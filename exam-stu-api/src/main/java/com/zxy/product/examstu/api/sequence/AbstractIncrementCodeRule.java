package com.zxy.product.examstu.api.sequence;


/**
 * 自增序列规则
 * <AUTHOR>
 * @date 2017年11月11日
 */
public abstract class AbstractIncrementCodeRule extends AbstractCodeRule {

    /** 为当前规则指定前缀 */
    private String prefix = "";
    /** 为当前规则指定类型,根据类型存储 */
    private String type = "";
    /** 格式长度,这种自增序号默认长度为4 */
    private int length = 4;

    public String getPrefix() { return prefix; }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }
}
