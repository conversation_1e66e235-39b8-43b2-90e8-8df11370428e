package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.Member;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @user tianjun
 * @date 16/6/13
 */
@RemoteService(timeout=3000000)
public interface MemberService {

    /** 根据员工编号以及四级授权节点查找授权范围内的用户记录 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findGrantByMemberNames(List<String> memberNames, List<String> organizationIds);

    /** 根据员工编号查找用户记录 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findByMemberNames(List<String> memberNames);


    /** 根据员工编号查找用户记录 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findByNames(List<String> memberNames);

    /** 根据id查询人员信息 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Member getMember(String id);

}