package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.ProfessionLevel;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


@RemoteService(timeout = 60000)
public interface ProfessionLevelService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ProfessionLevel getCloudLevel(Integer examRegion, String id);
}
