package com.zxy.product.examstu.api.sequence;

/**
 * 编号规则抽象，实现格式化编码方法
 * <AUTHOR>
 * @date 2017年11月11日
 */
public abstract class AbstractCodeRule implements CodeRule {

    /**
     * 置顶长度和前缀，格式化序列编码，不足长度前面补0，超出长度抛出异常
     * @param seq 序列号
     * @param length 长度
     * @param prefix 前缀
     * @return 格式化之后的字符串
     */
    protected String formatterCode(Long seq, int length, String prefix) {
        String str = String.valueOf(seq);
        int len = str.length();
        StringBuilder bld = new StringBuilder();
        bld.append(str);

        if(len > length){
            throw new RuntimeException("编号长度已超出规定");
        }

        //前面补"0"
        for(int i=0; i<length-len; i++){
            bld.insert(0, "0");
        }
        bld.insert(0, prefix);

        return bld.toString();
    }
}
