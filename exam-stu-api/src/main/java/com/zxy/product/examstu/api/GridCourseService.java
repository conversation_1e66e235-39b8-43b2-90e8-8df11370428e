package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.GridCourse;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * <AUTHOR>
 */
@RemoteService(timeout = 70000)
public interface GridCourseService {

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<GridCourse> findGridCourseByExamId(Integer examRegion, String examId);
}
