package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;


@RemoteService(timeout=300000)
public interface StrongBaseService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    boolean authType(Integer examRegion, String id);

    /**
     * 考试组内的正式考试共享允许考试次数，每次考试（仅认证考试），消耗一次考试次数，直到剩余允许考试次数为0后，不再允许继续考试，若不填写则为不限制考试次数；
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Boolean examAgain(Integer examRegion, String examId, String memberId);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Object> findFrontExamList(Integer examRegion, String subId, String contentId, Integer businessType, String currentUserId);


    /**
     * 校验考试组报名次数
     * @param currentUserId 当前用户id
     * @param examId 考试id
     * @return true 允许报名 false 不允许报名
     */
    @Transactional
    boolean checkExamGroupSignUp(Integer examRegion, String currentUserId, String examId);
}
