package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.AnswerRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@RemoteService
public interface AnswerRecordStuService {

    @Transactional
    int[] batchUpdateAnswerRecord(Integer examRegion,List<AnswerRecord> answerRecords, String examId);

    @Transactional
    Integer deleteReadingAnswer(Integer examRegion,List<String> shouldDeleteAnswerIds, String examId);
}
