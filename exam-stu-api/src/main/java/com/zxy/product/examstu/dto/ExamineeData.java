package com.zxy.product.examstu.dto;

import java.io.Serializable;

/**
 * 考生信息(IHR接口专用).
 *
 * <AUTHOR>
 */
public class ExamineeData implements Serializable {

  private static final long serialVersionUID = -1742669008083313925L;

  /**
   * 考生姓名
   */
  private String examineeName;
  /**
   * 成绩
   */
  private String score;

  /**
   * 交卷时间
   */
  private String examintionEndTime;
  /**
   * 状态
   */
  private String status;
  /**
   * 扩展字段
   */
  private String inputExt;

  public String getExamineeName() {
    return examineeName;
  }

  public void setExamineeName(String examineeName) {
    this.examineeName = examineeName;
  }

  public String getScore() {
    return score;
  }

  public void setScore(String score) {
    this.score = score;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getInputExt() {
    return inputExt;
  }

  public void setInputExt(String inputExt) {
    this.inputExt = inputExt;
  }

  public String getExamintionEndTime() {
    return examintionEndTime;
  }

  public void setExamintionEndTime(String examintionEndTime) {
    this.examintionEndTime = examintionEndTime;
  }

  @Override
  public String toString() {
    return "ExamineeData{" +
        "examineeName='" + examineeName + '\'' +
        ", score='" + score + '\'' +
        ", examintionEndTime='" + examintionEndTime + '\'' +
        ", status='" + status + '\'' +
        ", inputExt='" + inputExt + '\'' +
        '}';
  }
}
