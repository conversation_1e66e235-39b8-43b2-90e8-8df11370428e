package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.AnswerRecord;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.PaperInstance;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout=90000)
public interface ExamRecordService {
    @Transactional
    Integer calculateExamTimes(Integer examRegion, String id, String memberId);

    @Transactional
    String updateBeforeRecordBeNoCurrent(Integer examRegion, ExamRecord examRecord);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord getNewestRecord(Integer examRegion, String examId, String memberId);

    /**
     * 生成考试记录
     * @param examId
     * @param paperInstanceId
     * @param memberId
     * @param status
     * @return
     */
    ExamRecord insert(
            Integer examRegion,
            String examId,
            String paperInstanceId,
            String memberId,
            Integer status,
            Optional<Long> startTime,
            Optional<Long> endTime,
            Optional<Integer> paperSortRule,
            Optional<Integer> examedTimes,
            Optional<Integer> personalCode
    );

    /**
     * 感觉受众对象 试卷实例 批量生成 考试记录
     * @param examId
     * @param memberIds
     * @return
     */
    @Transactional
    List<ExamRecord> insert(Integer examRegion, String examId, List<String> memberIds);

    String update(
            Integer examRegion,
            String examRecordId,
            Optional<Long> startTime,
            Optional<Long> endTime,
            Optional<Integer> status,
            Optional<Long> submitTime,
            Integer isReset,
            Optional<String> paperInstanceId,
            Integer exceptionOrder,
            Optional<Long> lastSubmitTime,
            Optional<String> orderContent,
            Optional<Integer> personalCode,
            String examId
    );

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord getSimple(Integer examRegion, String examRecordId, String examId);

    @Transactional
    void updateExamRecordOrderContent(Integer examRegion, String examRecordId, String orderJson, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<ExamRecord> getExamRecordForCourseStudy(Integer examRegion, String id, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord getExamRecordSubmitTime(Integer examRegion, String memberId, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> findExamRecordMemberIds(Integer examRegion, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<AnswerRecord> findInculdeQuestionAnswers(Integer examRegion, List<AnswerRecord> fullAnswerRecords);

    @Transactional
    void batchInsertExamRecord(Integer examRegion, String examId, List<String> memberIds, List<PaperInstance> paperInstances);

    @Transactional
    void batchOtherInsertExamRecord(Integer examRegion, String examId, List<String> memberIds, List<PaperInstance> paperInstances);

    @Transactional
    void updateOrderContentOfNoStartExamRecords(Integer examRegion, String examId, Integer paperSortRule);

    @Transactional
    void deleteExamRecordBySenderToCenterExam(Integer examRegion, String examId);

    @Transactional
    void doUpdateException(Integer examRegion);

    @Transactional
    void checkExamRecord(Integer examRegion, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    HashMap<String, Integer> doExamJoinMemberAndTimes(Integer examRegion, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord getCurrentRecord(Integer examRegion, String examId, String memberId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord get(Integer examRegion, String id, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> findUnfinishedRecord(Integer examRegion, String examId, Integer page);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer findUnfinishedRecordCount(Integer examRegion, String examId);

    @Transactional
    String addExaminee(Integer examRegion, String examId, List<String> ids);

    @Transactional
    ExamRecord delete(Integer examRegion, String id, String examId, String memberId);

    @Transactional
    ExamRecord delayExamRecordEndTime(Integer examRegion, String examRecordId, Integer time, String examId);

    /**
     * 作废成绩（只作废及格的记录）
     * @param examRecordId
     * @param examId
     * @return
     */
    @Transactional
    void nullifyExamRecord(Integer examRegion, String examRecordId, String examId);

    /**
     * 强基计划证书作废（作废发证前所有的考试记录）
     * @param memberId
     * @param examId
     * @return
     */
    @Transactional
    void certificateRecordNullify(Integer examRegion, String memberId, String examId);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRecord> findExamRecordByMemberIdsAndExamId(Integer examRegion, List<String> memberIds, String examId);


    @Transactional
    String decreaseSwitchTimes(Integer examRegion, String examRecordId, Integer times, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ExamRecord> findPagedResult(Integer examRegion, Integer page, Integer pageSize, String examId, String memberId);


    /**
     * 查询该学员的考试次数和该次数中是否及格过
     * @param memberId
     * @param examId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRecord> getMemberExamRecordScore(Integer examRegion, String memberId, String examId);



}
