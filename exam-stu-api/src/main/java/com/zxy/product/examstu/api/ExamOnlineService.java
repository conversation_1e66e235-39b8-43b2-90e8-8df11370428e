package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.ExamOnlineLog;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 考试限流限频Service
 * <AUTHOR>
 * @date 2025年03月13日 12:10
 */
@RemoteService
public interface ExamOnlineService {

    /**
     * 查询考试限流水OPTIONAL对象
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     * @return 根据条件查询的考试限流流水OPTIONAL对象
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Optional<ExamOnlineLog> doSingleOnlineLogOpt( Integer examRegion, String examId, String memberId );

    /**
     * 添加考试限流前置流水
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     * @param expireTime 过期时间
     * @return 添加的对象
     */
    @Transactional(rollbackFor = Exception.class)
    ExamOnlineLog insertOnlineLog( Integer examRegion, String examId, String memberId, Long expireTime );


    /**
     * 释放考试流水
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     */
    @Transactional(rollbackFor = Exception.class)
    void releaseOnlineLog( Integer examRegion, String examId, String memberId );

    /**
     * 查询考试未过期流水数据
     * @param examRegion 考试所属数据库
     * @return 查询考试未过期流水数据
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Integer doSelectCount( Integer examRegion );

    /**
     * 清理考试无效数据
     * @param examRegion 考试所属数据库
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void cleanExamOnline( Integer examRegion );

}
