package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.AnswerRecordProcess;
import org.jooq.impl.TableImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout=500000)
public interface AnswerRecordProcessService {

   List<String> answerSubmit(Integer examRegion,String memberId,List<AnswerRecordProcess> answerRecordList);

   Integer answerRecordCount(Integer examRegion, String memberId, String examRecordId);

   Integer fullAnswerSubmit(Integer examRegion, String memberId, String examRecordId, List<AnswerRecordProcess> fullAnswerRecordList);

   List<AnswerRecordProcess> getListByExamRecordId(Integer examRegion,String memberId,String examRecordId, TableImpl<?> targetTable);

   @Transactional
   void cleanLastWeekData(Integer examRegion);
}
