package com.zxy.product.examstu.util;

import org.jooq.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据集处理工具类
 *
 * <AUTHOR>
 * @date 2024年04月24日 16:10
 */
public class DatasetProcessing {
    private static final Logger logger= LoggerFactory.getLogger(DatasetProcessing.class);

    /**
     * Copy目标对象
     * @param source 源对象
     * @param targetClass 目标类
     * @param <T>    源泛型引用
     * @param <V>    目标泛型引用
     * @return 目标对象
     */
    public static <T, V> V copyProperty(T source, Class<V> targetClass) {
        V target = null;
        try{
            target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
        }catch ( InstantiationException | IllegalAccessException e){
            logger.error("Copy目标对象发生异常,源对象{},目标类{},异常信息",source,targetClass,e);
        }
        return target;
    }

    /**
     * 默认值填充
     * @param targetValue 目标值
     * @param defaultValue 默认值
     * @param <T> 泛型引用
     * @return 目标值
     */
    public static <T> T disposeValue(T targetValue, T defaultValue){
        return Optional.ofNullable(targetValue).orElse(defaultValue);
    }

    /**
     * 单个对象返回
     * @param targetCollect 目标对象集合
     * @param <T> 泛型引用
     * @return 目标对象
     */
    public static<T> T buildSingleValue(List<T> targetCollect) {
        return !CollectionUtils.isEmpty(targetCollect)?targetCollect.get(0):null;
    }

    /**
     * 转化集合属性
     * @param source 源集合
     * @param mapper 流式Function
     * @param <T> 源集合泛型引用
     * @param <R> 返回集合泛型引用
     * @return 转化的数据集合
     */
    public static <T, R> List<R> convertCollectField(Collection<T> source,
                                                     Function<T, R> mapper) {
        return source.parallelStream()
                .map(mapper)
                .collect(Collectors.toList());
    }

    /**
     * JOOQ单表记录流
     * @param context JOOQ上下文
     * @param jooqField JOOQ查询属性
     * @param targetTable JOOQ查询表
     * @param condition JOOQ查询条件
     * @param selectType 查询类型 1去重 2不去重
     * @return JOOQ记录流
     */
    public static SelectConditionStep<Record> singleJooqRecord(DSLContext context,
                                                               Collection<Field<?>> jooqField,
                                                               Table<?> targetTable,
                                                               Collection<Condition> condition,
                                                               Integer selectType){
        return Optional.ofNullable(selectType)
                .filter(ew1->Objects.equals(1,ew1))
                .map(ew2->context.selectDistinct(jooqField).from(targetTable).where(condition))
                .orElseGet(()->context.select(jooqField).from(targetTable).where(condition));
    }

    /**
     * JOOQ：构建属性查询集合
     * @param selectField JOOQ属性
     * @return 构建的JOOQ查询属性集合
     */
    public static  Collection<SelectField<?>> doSelect(SelectField<?> ... selectField){
        return Arrays.asList(selectField);
    }

    /**
     * JOOQ：查询Record记录流成员变量
     * @param selectType 查询类型 0普通 1去重
     * @param context JOOQ上下文
     * @param selectField JOOQ查询属性集合
     * @return 查询Record记录流成员变量
     */
    public static  SelectSelectStep<Record> doSelectStep(Integer selectType, DSLContext context,
                                                         Collection<SelectField<?>> selectField){
        return Optional.of(selectType)
                .filter(ew1->Objects.equals(1,ew1))
                .map(ew2->context.select(selectField))
                .orElseGet(()->context.selectDistinct(selectField));
    }


    /**
     * JOOQ：查询Record记录流成员变量
     * @param tagTable 目标表名
     * @param selectStep 查询属性集合流
     * @return 查询Record记录流成员变量
     */
    public static SelectJoinStep<Record> doSingleRecord(Table<?> tagTable, SelectSelectStep<Record> selectStep) {
        return  selectStep.from(tagTable);
    }

    /**
     * JOOQ单表数量
     * @param context JOOQ上下文
     * @param targetTable JOOQ查询表
     * @param condition JOOQ查询条件
     * @return JOOQ单表数量
     */
    public static int singleJooqCount(DSLContext context,
                                      Table<?> targetTable,
                                      Collection<Condition> condition){
        return context.selectCount().from(targetTable).where(condition).fetchOne(0, Integer.class);
    }
}
