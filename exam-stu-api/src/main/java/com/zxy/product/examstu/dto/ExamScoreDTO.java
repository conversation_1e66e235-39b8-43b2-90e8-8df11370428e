package com.zxy.product.examstu.dto;

import java.util.List;

/**
 * 学员考试成绩以及考试信息DTO.
 *
 * <AUTHOR>
 */
public class ExamScoreDTO {

  /**
   * 学员考试成绩以及考试信息
   */
  private List<ExamMemberData> data;
  /**
   * 每页条数
   */
  private int pageSize;
  /**
   * 当前页
   */
  private int currentPage;
  /**
   * 省公司代码
   */
  private String provinceCode;
  /**
   * 扩展字段
   */
  private String inputExt;

  public List<ExamMemberData> getData() {
    return data;
  }

  public void setData(List<ExamMemberData> data) {
    this.data = data;
  }

  public int getPageSize() {
    return pageSize;
  }

  public void setPageSize(int pageSize) {
    this.pageSize = pageSize;
  }

  public int getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(int currentPage) {
    this.currentPage = currentPage;
  }

  public String getProvinceCode() {
    return provinceCode;
  }

  public void setProvinceCode(String provinceCode) {
    this.provinceCode = provinceCode;
  }

  public String getInputExt() {
    return inputExt;
  }

  public void setInputExt(String inputExt) {
    this.inputExt = inputExt;
  }

  @Override
  public String toString() {
    return "ExamScoreDTO{" +
        "data=" + data +
        ", pageSize=" + pageSize +
        ", currentPage=" + currentPage +
        ", provinceCode='" + provinceCode + '\'' +
        ", inputExt='" + inputExt + '\'' +
        '}';
  }
}
