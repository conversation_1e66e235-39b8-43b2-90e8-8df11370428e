package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.QuestionAttrCopy;
import com.zxy.product.exam.entity.QuestionCopy;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout=500000)
public interface QuestionCopyStuService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, QuestionCopy> getRightAnswer(Integer examRegion, String memberId, String examId);


    boolean compareAnswerInfo(List<QuestionAttrCopy> questionAttrCopys,String answer,Integer type);

}
