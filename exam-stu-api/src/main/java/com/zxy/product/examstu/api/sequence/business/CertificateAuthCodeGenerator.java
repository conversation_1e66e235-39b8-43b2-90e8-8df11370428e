package com.zxy.product.examstu.api.sequence.business;

import com.zxy.common.base.annotation.RemoteService;

import java.util.List;
import java.util.Optional;

/**
 * 认证考试证书编号生成器
 * <AUTHOR>
 * @date 2017年11月11日
 */
@RemoteService(timeout = 90000)
public interface CertificateAuthCodeGenerator {

    /**
     * 根据规则生成流水号
     * @param level 等级：一位数，共计5级，即1~5
     * @param professionCode 专业：两位数，如06
     * @param subProfessionCode 子专业：两位数，如02
     * @param equipmentCode 设备型号：两位数，华为01、中兴02、爱立信03、诺西04、大唐05、贝尔06、普天07、思科08、Juniper09、烽火10
     * @param areaCode 考生区划代码：三位数区号
     * @param year 考试年份：四位数
     * @param numList
     */
    String getCode(Optional<Integer> level, Optional<String> professionCode, Optional<String> subProfessionCode,
                   Optional<String> equipmentCode, String areaCode, String year, List<String> numList) throws Exception;
}
