package com.zxy.product.examstu.dto.exam.online;

import java.io.Serializable;
import java.util.List;

/**
 * 回显前端的限流数据POJO
 * <AUTHOR>
 * @date 2025年02月17日 10:41
 */
public class EchoLimitRuleConfig implements Serializable {
    private static final long serialVersionUID = -6028055662216594606L;

    /**限流规则集合*/
    private List<FlowRuleConfig> rules;

    /**主开关 0关闭 1开启*/
    private Integer mainSwitch;

    public List<FlowRuleConfig> getRules() { return rules; }

    public void setRules(List<FlowRuleConfig> rules) { this.rules = rules; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    @Override
    public String toString() {
        return "EchoLimitRuleConfig{" +
                "rules=" + rules +
                ", mainSwitch=" + mainSwitch +
                '}';
    }


}
