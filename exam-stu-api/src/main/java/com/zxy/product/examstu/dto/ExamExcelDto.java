package com.zxy.product.examstu.dto;

import java.io.Serializable;

/**
 * pccw改造为Ftp方式后,用于写文件的对象
 * <AUTHOR>
 * @create 2024/9/13 15:03
 */
public class ExamExcelDto  implements Serializable {


    private static final long serialVersionUID = -1742669008083313925L;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 合格分数
     */
    private String qualifiedScore;

    /**
     * 考试状态
     */
    private String examStatus;

    /**
     * 考试id
     */
    private String examintionId;


    /**
     * 考试名称
     */
    private String examinationName;

    /**
     * 考试开始时间
     */
    private String startTime;

    /**
     * 考试结束时间
     */
    private String endTime;

    /**
     * 最后提交考试时间
     */
    private String examintionEndTime;

    /**
     * 考试分数
     */
    private String score;

    /**
     * 考试记录状态
     */
    private String examRecordStatus;

    /**
     * 考生姓名
     */
    private String examineeName;

    /**
     * 考生ihrCode
     */
    private String examineeId;

    /**
     * 省公司编码
     */
    private String provinceCode;


    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getQualifiedScore() {
        return qualifiedScore;
    }

    public void setQualifiedScore(String qualifiedScore) {
        this.qualifiedScore = qualifiedScore;
    }

    public String getExamStatus() {
        return examStatus;
    }

    public void setExamStatus(String examStatus) {
        this.examStatus = examStatus;
    }

    public String getExaminationName() {
        return examinationName;
    }

    public void setExaminationName(String examinationName) {
        this.examinationName = examinationName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getExamintionEndTime() {
        return examintionEndTime;
    }

    public void setExamintionEndTime(String examintionEndTime) {
        this.examintionEndTime = examintionEndTime;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getExamRecordStatus() {
        return examRecordStatus;
    }

    public void setExamRecordStatus(String examRecordStatus) {
        this.examRecordStatus = examRecordStatus;
    }

    public String getExamineeName() {
        return examineeName;
    }

    public void setExamineeName(String examineeName) {
        this.examineeName = examineeName;
    }

    public String getExamineeId() {
        return examineeId;
    }

    public void setExamineeId(String examineeId) {
        this.examineeId = examineeId;
    }

    public String getExamintionId() {
        return examintionId;
    }

    public void setExamintionId(String examintionId) {
        this.examintionId = examintionId;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
}
