package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.Exam;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService(timeout=700000)
public interface ExamService {


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Map<String, Object> findNewPersonCenterListByRegist(
		Integer examRegion,
		int page,
		int pageSize,
		Optional<String> name,
		Optional<Integer> type,
		String memberId,
		Optional<Integer> searchStatus,
		Optional<Integer> startTimeOrderBy,
		List<String> userIds,
		Optional<String> organizationId,
		Optional<String> year, boolean b,
		boolean pageSwitch,
		Optional<String> all);

	/**
	 * 获取基本信息
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Exam getSimpleData(Integer examRegion, String id);

	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Integer findArchiveExamRegistCount(Integer examRegion,
									   String currentUserId, Optional<Long> startTime, Optional<Long> endTime,
									   String examRegistStringTable, String examRecordStringTable);

	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Exam getCloudSignUpAndExamRecord(Integer examRegion, String string, String currentUserId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Exam getGridSignUpAndExamRecord(Integer examRegion, String string, String currentUserId);

	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Exam getSignUpAndExamRecord(Integer examRegion, String string, String currentUserId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	boolean isOtherModuleExam(Integer examRegion, String examId);


	/**
	 * 查询其他考试记录 （课程 专题 班级）
	 */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Map<String, Object> otherExamRecords(Integer examRegion, int page, int pageSize,
										 Optional<String> name,
										 Optional<Integer> sourceType,
										 List<String> userIds,
										 String memberId,
										 Optional<String> year,
										 Optional<Integer> startTimeOrderBy,
										 boolean pageSwitch);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findPersonCenterListByRegist(
			Integer examRegion,
			int page,
			int pageSize,
			Optional<String> name,
			Optional<Integer> type,
			String memberId,
			Optional<Integer> searchStatus,
			Optional<Integer> startTimeOrderBy,
			List<String> userIds,
			Optional<String> organizationId,
			Optional<String> year, boolean b
	);


	/** 查询所有的考试
	 * @param year */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findAllPersonCenterListByRegist(Integer examRegion, int page,
													  int pageSize,
													  Optional<String> name,
													  Optional<Integer> type,
													  String memberId,
													  Optional<Integer> searchStatus,
													  Optional<Integer> startTimeOrderBy,
													  List<String> userIds,
													  Optional<String> organizationId, Optional<String> year);




	/**
	 * 活动首页查询列表
	 * @return
	 */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findActivityList(
			Integer examRegion,
			int page,
			int pageSize,
			Optional<String> name,
			Optional<Integer> type,
			String memberId,
			Optional<Integer> searchStatus,
			String clientType,
			Optional<String> topicId
	);

	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Map<String,Object> findArchiveListByExamRegist(Integer examRegion, Integer page, Integer pageSize,
												   String currentUserId, Optional<Long> startTime, Optional<Long> endTime, List<String> userIds, String examRegistStringTable, String examRecordStringTable,boolean pageSwitch);


	@Transactional
	boolean isOverExamTime(Integer examRegion, String examId, String currentUserId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Exam> findExamByAudient(Integer examRegion, String[] split, String currentUserId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Exam> findAllArchivrList(Integer examRegion, Optional<Long> startTime, Optional<Long> endTime, String currentUserId, List<String> userIds, String examRegistStringTable, String examRecordStringTable);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Exam> findBasicByIdsByMemberId(Integer examRegion, String[] ids, String memberId);

	@Transactional
	Optional<Exam> getOptionalById(Integer examRegion, String id);



	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findAuthExamList(Integer examRegion, String organizationId, Integer page, Integer pageSize, Optional<String> proId,
									   Optional<String> subProId, Optional<String> levelId, Optional<String> equipmentId,
									   Optional<Integer> startTimeOrderBy, String memberId);



	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findTicketExamList(Integer examRegion, String organizationId, Integer page, Integer pageSize, Optional<Integer> startTimeOrderBy,
										 String memberId, Optional<String> year);



	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	Exam findTicketDetail(Integer examRegion, String examId, String memberId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findMyExamList(Integer examRegion, String organizationId, Integer page, Integer pageSize, Optional<Integer> startTimeOrderBy,
									 String currentUserId, Optional<String> year);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findAuditList(Integer examRegion, String organizationId, Integer page, Integer pageSize, Optional<Integer> startTimeOrderBy,
									String currentUserId);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findMyScoreList(Integer examRegion, String organizationId, Integer page, Integer pageSize, Optional<Integer> startTimeOrderBy,
									  String currentUserId, Optional<String> year);


	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Exam> findExamDay(Integer examRegion, String organizationId, Long dayStartTime, Long dayEndTime, String currentUserId);




}
