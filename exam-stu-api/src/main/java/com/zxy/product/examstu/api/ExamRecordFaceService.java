package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;

import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.ExamRecordFace;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: <EMAIL>
 * @Date: 2021/12/16 17:48
 * @Description:
 */
@RemoteService(timeout = 90000)
public interface ExamRecordFaceService {
    @Transactional
    List<ExamRecordFace> getListByRecordId(Integer examRegion, String memberId, String examId, String examRecordId, Integer type, Optional<Integer> status);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecordFace findReviewRecord(Integer examRegion, String currentUserId, String examId, String examRecordId);

    @Transactional
    void refreshFaceRecord(Integer examRegion, String examId, String memberId, String examRecordId);

    @Transactional
    ExamRecordFace faceInvigilation(Integer examRegion, String memberId, String examId, Optional<String> examRecordId, Integer type, String faceImageUrl, Integer status, Optional<String> respMsg);

    @Transactional
    ExamRecord updateExamRecordFaceStatus(Integer examRegion, String examId, String examRecordId, Integer faceStatus);

    @Transactional
    void frontReview(Integer examRegion, String memberId, String examId);

    @Transactional
    ExamRecord backReview(Integer examRegion, String examId, String examRecordId, Integer faceStatus);

    @Transactional
    String backReviewNew(Integer examRegion, String id, String examId, Optional<String> examRecordId ,Integer faceStatus);

    @Transactional
    Map<Integer, List<ExamRecordFace>> faceDetail(Integer examRegion, String memberId, String examId, Optional<String> examRecordId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecordFace upFaceStatus(Integer examRegion, String memberId, String examId, Optional<String> examRecordId, Optional<Integer> type);

    @Transactional
    ExamRecordFace insertOrUpdate(Integer examRegion,String memberId, String examId, Integer type, Integer status,Integer num, Optional<String> respMsg);

    @Transactional
    Map<Integer, List<ExamRecordFace>> faceDetail2(Integer examRegion,String memberId, String examId,Integer... type);

}
