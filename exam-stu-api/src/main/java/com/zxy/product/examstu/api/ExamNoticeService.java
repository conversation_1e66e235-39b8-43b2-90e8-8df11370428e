package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.ExamNotice;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 *
 */
@RemoteService(timeout = 300000)
public interface ExamNoticeService {

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	ExamNotice get(String id);

	/** 保存消息通知  memberIds:对于催办等特殊场景的消息通知，接收通知的用户id是用户选择的*/
	@Transactional
    ExamNotice insert(Integer businessType, String businessId, Integer noticeType,
            String createMemberId, String templetCode, Optional<Integer> noticeUser,
            Optional<String> noticeUserText, Optional<String> noticeUserContent, Optional<String> memberIds);

	/** 保存消息通知 ，直接使用模板和参数发送消息*/
	@Transactional
	ExamNotice insert(Integer businessType, String businessId, Integer noticeType,
	        String organizationId, String templetCode, Optional<String[]> contentParams, Optional<String> memberIds);

	/**  批量发送不同的消息通知 */
	@Transactional
	List<ExamNotice> batchInsert(List<ExamNotice> examNotices);
}
