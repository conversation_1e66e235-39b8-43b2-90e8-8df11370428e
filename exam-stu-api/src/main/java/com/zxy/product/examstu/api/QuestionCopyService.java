package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.Question;
import com.zxy.product.exam.entity.QuestionCopy;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@RemoteService(timeout=300000)
public interface QuestionCopyService {

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<QuestionCopy> findQuestionsByPaperId(Integer examRegion,String id, String examRecordId, String examId);

	@Transactional
	List<QuestionCopy> findQuestionCopysByExamId(String id);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<QuestionCopy> findQuestionsByPaperAndExamRecord(Integer examRegion, String paperInstanceId, String examRecordId, String examId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<Question> findQuestionsTopError(Integer examRegion, List<String> questionDepotList);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<Question> findAnswerRecord(Integer examRegion, String examRecordId, String examId);
}
