package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.PersonalTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Optional;

/**
 *
 * ClassName: PersonalTemplateService <br/>
 * Reason: 个人认证信息模板服务类<br/>
 * date: 2017年10月19日 下午3:23:29 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@RemoteService(timeout=180000)
public interface PersonalTemplateService {
	 /**
	  *
	  * insert:新增. <br/>
	  *
	  * <AUTHOR>
	  * @param personalTemplate
	  * @return
	  * @since JDK 1.8
	  * date: 2017年10月19日 下午4:08:46 <br/>
	  */
	 @Transactional
	 PersonalTemplate insert(
			 Integer examRegion, PersonalTemplate personalTemplate
	    );

	 /**
	  *
	  * get:查询 <br/>
	  *
	  * <AUTHOR>
	 * @param examId
	  * @param isShield 是否加密
	  * @return
	  * @since JDK 1.8
	  * date: 2017年10月19日 下午4:09:50 <br/>
	  */
	  @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	  HashMap<String, Object> get(Integer examRegion, Optional<String> examId, String memberId,boolean isShield);

	  /**
	   *
	   * update:修改 <br/>
	   *
	   * <AUTHOR>
	   * @return
	   * @since JDK 1.8
	   * date: 2017年10月19日 下午4:13:13 <br/>
	   */
	  @Transactional
	  PersonalTemplate update(
			  Integer examRegion,
			  String id,
			  String professionId,
			   String subProfessionId,
			    String equipmentTypeId,
			    String workDepart,
			    String workTime,
			    Integer   isGroupExpert,
			    Integer   isProvinExpert,
			  String otherExamAppraisal,
			   String awardSituation,
			     String crossCondition,
			     Optional<String> applyLevel,
			     Optional<String> applyProfession,
			     Optional<String> applySubProfession,
			     Optional<String> applySupplier
			  );
}
