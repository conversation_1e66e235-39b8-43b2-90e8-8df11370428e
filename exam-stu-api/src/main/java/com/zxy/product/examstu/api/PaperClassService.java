package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.PaperClass;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout = 300000)
public interface PaperClassService {


	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PaperClass getSimplePaperClassInfo(Integer examRegion, String paperClassId);

}
