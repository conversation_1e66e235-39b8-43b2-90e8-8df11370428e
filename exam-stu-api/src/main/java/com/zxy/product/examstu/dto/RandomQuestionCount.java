package com.zxy.product.examstu.dto;

import java.io.Serializable;

/**
 * 随机组卷-各种类型，难度试题的数量
 * <AUTHOR>
 *
 */
public class RandomQuestionCount implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 2615847300819664641L;
    
    private Integer questionType;
    
    private Integer high;
    
    private Integer middle;
    
    private Integer low;

    public Integer getQuestionType() {
        return questionType;
    }

    public Integer getHigh() {
        return high;
    }

    public Integer getMiddle() {
        return middle;
    }

    public Integer getLow() {
        return low;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public void setHigh(Integer high) {
        this.high = high;
    }

    public void setMiddle(Integer middle) {
        this.middle = middle;
    }

    public void setLow(Integer low) {
        this.low = low;
    }
    
    
    
    
    

}
