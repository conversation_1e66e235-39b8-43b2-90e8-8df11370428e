package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.Profession;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;



@RemoteService(timeout = 60000)
public interface ProfessionService {


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Profession getCloudProfession(Integer examRegion, String id);
}
