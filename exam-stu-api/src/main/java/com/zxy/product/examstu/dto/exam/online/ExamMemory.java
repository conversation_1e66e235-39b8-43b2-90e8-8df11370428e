package com.zxy.product.examstu.dto.exam.online;

import java.io.Serializable;

/**
 * 限流限频：考试内存对象
 * <AUTHOR>
 * @date 2025年03月13日 6:12
 */
public class ExamMemory implements Serializable {
    private static final long serialVersionUID = 6528977854050442075L;

    /**限流限频主开关*/
    private Boolean mainSwitch;

    /**限流限频模块开关*/
    private Boolean applicationSwitch;

    /**限流限频最大在线开关*/
    private Boolean maxOnlineSwitch;

    /**限流限频最大在线*/
    private Integer maxOnline;

    public Boolean getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Boolean mainSwitch) { this.mainSwitch = mainSwitch; }

    public Boolean getApplicationSwitch() { return applicationSwitch; }

    public void setApplicationSwitch(Boolean applicationSwitch) { this.applicationSwitch = applicationSwitch; }

    public Boolean getMaxOnlineSwitch() { return maxOnlineSwitch; }

    public void setMaxOnlineSwitch(Boolean maxOnlineSwitch) { this.maxOnlineSwitch = maxOnlineSwitch; }

    public Integer getMaxOnline() { return maxOnline; }

    public void setMaxOnline(Integer maxOnline) { this.maxOnline = maxOnline; }

    @Override
    public String toString() {
        return "ExamMemory{" +
                "mainSwitch=" + mainSwitch +
                ", applicationSwitch=" + applicationSwitch +
                ", maxOnlineSwitch=" + maxOnlineSwitch +
                ", maxOnline=" + maxOnline +
                '}';
    }
}
