package com.zxy.product.examstu.content;

/**
 * @user tianjun 消息常量
 */
public class MessageTypeContent {

    /**
     * 系统管理 1
     * 人资模块(HR) 2
     * 考试 3
     * 课程模块(COURSE) 4
     *
     * question 01 paper 02 catalog 03 exam 04 examroom 05 signup 06 examRecord 07 submitPaper 10,activity 13
     *
     * insert 01 delete 02 update 03 publish 04 cancelPublish 05
     */

    public static final int EXAM_RECORD_INSERT_AUDIENCE = 20204;
    public static final int EXAM_RECORD_DELETE_AUDIENCE = 20205;

    public static final int EXAM_QUESTION_INSERT = 30101;
    public static final int EXAM_QUESTION_DELETE = 30102;
    public static final int EXAM_QUESTION_UPDATE = 30103;
    public static final int EXAM_QUESTION_PUBLISH = 30104;
    public static final int EXAM_QUESTION_CANCEL = 30105;
    public static final int EXAM_QUESTION_ERROR_RATE_INSERT = 30106;

    public static final int EXAM_PAPERCLASS_INSERT = 30201;
    public static final int EXAM_PAPERCLASS_DELETE = 30202;
    public static final int EXAM_PAPERCLASS_UPDATE = 30203;
    public static final int EXAM_PAPERCLASS_UPDATE_STATUS = 30204;
    public static final int EXAM_PAPERCLASS_SYNC = 30203;

    public static final int EXAM_QUESTION_DEPOT_INSERT = 30301;
    public static final int EXAM_QUESTION_DEPOT_DELETE = 30302;
    public static final int EXAM_QUESTION_DEPOT_UPDATE = 30303;
    public static final int EXAM_QUESTION_DEPOT_UPDATE_STATE = 30304;

    public static final int EXAM_EXAM_INSERT = 30401;
    public static final int EXAM_EXAM_DELETE = 30402;
    public static final int EXAM_EXAM_UPDATE = 30403;
    public static final int EXAM_CHANGE_TIME_STARTING = 30410;
    public static final int EXAM_TIME_TO_CACHE_PAPER = 30404;
    public static final int EXAM_EXAMS_PUBLISHING = 30405;
    public static final int EXAM_EXAMS_PUBLISHED = 30409;
    public static final int EXAM_EXAMS_CANCEL = 30407;
    public static final int EXAM_EXAMS_STATUS_UPDATE = 30408;
    public static final int EXAM_EXAM_ALL_STATUS_UPDATE=30410;//人工智能相关 考试状态部分变更推送
    public static final int EXAM_DO_CERTIFICATE=30411;//证书自动发放
    public static final int EXAM_DO_FORCE_SUBMIT_OVER_TIME_PAPER=30412;//系统自动强制交卷
    public static final int EXAM_DO_SIGNUP_COUNT=30413;//更新考试报名数
    public static final int EXAM_DO_JOIN_MEMBER_AND_TIMES=30414;//更新考试参考人数，人次
    public static final int EXAM_RECORD_NULLIFY = 30415;//考试成绩作废
    public static final int CERTIFICATE_NULLIFY = 30416;//强基计划考试证书作废

    public static final int EXAM_EXAMROOM_INSERT = 30501;
    public static final int EXAM_EXAMROOM_DELETE = 30502;
    public static final int EXAM_EXAMROOM_UPDATE = 30503;
    public static final int EXAM_EXAMROOM_CANCEL_PUBLISH = 30504;
    public static final int EXAM_EXAMROOM_PUBLISH = 30505;

    public static final int EXAM_SIGNUP_INSERT = 30601;
    public static final int EXAM_SIGNUP_DELETE = 30602;
    public static final int EXAM_SIGNUP_UPDATE = 30603;
    public static final int EXAM_SIGNUP_AUDIT_PASS = 30604;
    public static final int EXAM_SIGNUP_AUDIT_REFUSE = 30605;
    public static final int EXAM_EXAM_RECORD_INSERT = 30701;
    public static final int EXAM_EXAM_RECORD_DELETE = 30702;
    public static final int EXAM_EXAM_RECORD_UPDATE = 30703;
    /**
     * // 交卷异步消息 该消息接受头为 IDS
     */
	public static final int EXAM_EXAM_RECORD_SUBMIT = 30704;
    public static final int EXAM_ADD_USER_TO_EXAM = 30705;
    public static final int EXAM_RECORD_RESET = 30706;
    public static final int EXAM_RECORD_UPDATE_STARTING = 30710;
    public static final int EXAM_TIME_EXPAND = 30707;//延时交卷


    //考试状态异常
	public static final int EXAM_EXAM_RECODD_UPDATE_STATUS = 30708;


    public static final int AUDIENCE_ITEM_INSERT = 30901;
    public static final int AUDIENCE_ITEM_RECALCULATION = 30903;

    public static final int EXAM_ASYN_AUDIENCE_MEMBER_INSERT = 30902;

    public static final int SUBMIT_PAPER = 31001;

    public static final int EXAM_RETEST_INSERT = 31101;
    public static final int EXAM_RETEST_UPDATE = 31102;
    public static final int EXAM_RETEST_DELETE = 31103;

    public static final int EXAM_ANSWER_RECORD_UPDATE = 31201;

    public static final int EXAM_MARK_PAPER_INSERT = 31301;

	public static final int EXAM_RESEARCH_ACTIVITY_INSERT = 31401;
	public static final int EXAM_RESEARCH_ACTIVITY_UPDATE = 31402;
	public static final int EXAM_RESEARCH_ACTIVITY_DELETE = 31403;
	public static final int EXAM_RESEARCH_ACTIVITY_PUBLISHING = 31404; // 调研发布动作
	public static final int EXAM_RESEARCH_ACTIVITY_CANCEL = 31405;
	public static final int EXAM_RESEARCH_ACTIVITY_STATUS_UPDATE = 31406;
	public static final int EXAM_RESEARCH_ACTIVITY_PUBLISHED = 31407; //调研发布完成动作，主要是发布完了将researchRecord计算完成之后发
    public static final int EXAM_RESEARCH_ACTIVITY_VIEW_DETAIL = 31408; // 进入调研详情页面
    public static final int EXAM_RESEARCH_ACTIVITY_ALL_STATUS_UPDATE=31409;//人工智能相关消息类型 状态变更

	public static final int EXAM_DIMENSION_QUESTION_INSERT = 31501;
	public static final int EXAM_DIMENSION_QUESTION_UPDATE = 31502;
	public static final int EXAM_DIMENSION_QUESTION_DELETE = 31503;

	public static final int EXAM_DIMENSION_INSERT = 31601;
	public static final int EXAM_DIMENSION_UPDATE = 31602;
	public static final int EXAM_DIMENSION_DELETE = 31603;

	public static final int ACTIVITY_INSERT = 31701;
	public static final int ACTIVITY_UPDATE = 31702;
	public static final int ACTIVITY_DELETE = 31703;

	public static final int EXAM_RESEARCH_ACTIVITY_ADD_MEMBER = 31801;

	public static final int EXAM_QUESTIONARY_ANSWER_RECORD_INSERT = 31901; // 用户提交调研问卷
	public static final int EXAM_QUESTIONARY_ANSWER_ENTRY = 31902; // 用户进入调研答题

	public static final int OTHER_MODULE_EXAM_RECORD_UPDATE_STATUS = 30709;

	/**
	 * 发布后 重新预加载考试试卷缓存
	 */
	public static final int EXAM_RELOAD_PAPER_CACHE = 30406;

	// 话题、标签关联消息
    public static final int EXAM_BUSINEESS_TOPIC_INSERT = 30801; // 新增话题、标签
    public static final int EXAM_BUSINEESS_TOPIC_DELETE = 30802; // 删除话题、标签

    public static final int DELETE_EXAM_DATA = 30803; // 考试删除信息
    public static final int DELETE_CERTIFICATE_DATA = 30804; // 证书删除信息


    // notice 通知消息类型
    public static final int NOTICE_INSERT = 32001; // 消息通知，落地examNotice表，根据消息内容发送
    public static final int NOTICE_PARAMS_INSERT = 32002; // 消息通知，落地examNotice表，根据模板和参数发送
    public static final int NOTICE_BATCH_INSERT = 32003; // 消息通知，批量落地examNotice表

    public static final int NOTICE_INSERT_STU = 32004; // 学院端发送消息插入管理端examNotice表



    /****考试过程，同步mongodb数据到DB****/
    //进入考试，创建记录
	public static final int EXAM_RECORD_CREATE_IN_ENTER_EXAM_TO_MONGO = 30720;
	//提交试卷，controller更新提交时间
	public static final int EXAM_RECORD_SUBMIT_FOR_UPDATE = 30721;
	//提交试卷第一个异步 更新答题记录 考试记录
	public static final int ANSWER_RECORD_UPDATE_FOR_SUBMIT = 30722;
	//确认考试记录状态
	public static final int EXAM_RECORD_CONFIRM = 30723;
	//创建待办
	public static final int CREATE_TO_DO = 30724;
	//更新考试记录超时异常
	public static final int EXAM_RECORD_BE_EXCEPTION = 30725;
	//
	public static final int EXAM_EXAM_RECORD_INSERT_ASYNC = 30726;
	//报名通过
	public static final int EXAM_SIGNUP_PASS = 30727;
	//报名被拒绝
	public static final int EXAM_SIGNUP_REFUSE = 30728;
	//转换非报名非指定考试
	public static final int EXAM_CHANGE_TO_ORDINARY = 30729;
	//确认成绩
	public static final int EXAM_RECORD_SCORE_RESULT = 30730;
	//指定考试转非指定考试，删除为未开考过的记录
	public static final int EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER = 30731;
	//更改评卷配置
	public static final int UPDATE_MARK_CONFIG = 30732;

	//监听生成完受众对象之后，业务执行发布动作
    public static final int BUSINESS_PUBLISH_AFTER_GENERATE = 31002;

    //更改人员信息，加入群组等
    public static final int EXAM_ASYN_AUDIENCE_MEMBER_UPDATE = 30904;
    //删除人员 受众人员删除
    public static final int EXAM_ASYN_AUDIENCE_MEMBER_DELETE = 30905;

    // 新增监考老师
    public static final int EXAM_INVIGILATOR_INSERT = 32101;
    // 新增监考老师授权
    public static final int EXAM_INVIGILATOR_GRANT_INSERT = 32102;

    public static final int EXAM_BUSINEESS_TOPIC_DELETE_NEW = 32502; // 删除话题、标签

    //申请复核通知
    public static final int FACE_EXAM_REVIEW_NOTICE = 30398;

    //考试过程调用人脸识别
    public static final int EXAM_FACE_RECOGNITION = 33000;
    //推送考试学习计划 insert
    public static final int EXAM_STUDY_PLAN_CONFIG_INSERT = 24001;
    //推送考试学习计划 revoke
    public static final int EXAM_STUDY_PLAN_CONFIG_REVOKE = 24002;
    //推送考试学习计划 update
    public static final int EXAM_STUDY_PLAN_UPDATE = 24003;
    //推送考试学习计划 update
    public static final int EXAM_STUDY_PLAN_CONFIG_UPDATE = 24041;

    //推送调研学习计划 insert
    public static final int EXAM_STUDY_PLAN_CONFIG_ACTIVITY_INSERT = 24021;
    //推送调研学习计划 revoke
    public static final int EXAM_STUDY_PLAN_CONFIG_ACTIVITY_REVOKE = 24022;
    //推送调研学习计划 update
    public static final int EXAM_ACTIVITY_STUDY_PLAN_UPDATE = 24023;
    //学习风格测评结果
    public static final int EXAM_LEARNING_STYLE_TEST_RESULT = 24131;

    // stu的更新未开始的考试记录的试题顺序
    public static final int EXAM_STU_UPDATE_RECORD_ORDER = 30622;
    // stu的报名转非报名修改报名记录数据
    public static final int EXAM_STU_CHANGE_APPLICANT_EXAM = 30623;
    // stu的推送转非推送，删除待考试的考试记录
    public static final int EXAM_STU_DELETE_RECORD = 30624;
    // stu的评卷老师修改，删除不存在的旧评卷老师todo数据
    public static final int EXAM_STU_DELETE_TO_DO = 30625;
    // 考试结束后，检查考试记录有无问题
    public static final int EXAM_STU_CHECK_EXAM_RECORD = 30626;
    // stu处理考试交卷
    public static final int SUBMIT_PAPER_STU = 30627;
    public static final int EXAM_ANSWER_RECORD_UPDATE_STU = 30628;
    // stu的消息新增
    public static final int EXAM_STU_NOTICE_INSERT = 30629;
    public static final int EXAM_STU_NOTICE_PARAMS_INSERT = 30630;
    public static final int EXAM_STU_NOTICE_BATCH_INSERT = 30631;
    // stu的消息新增 处理考试评卷
    public static final int EXAM_STU_ANSWER_RECORD_UPDATE = 30632;
    // 更新删除分库todo表
    public static final int EXAM_STU_TODO_UPDATE = 30633;
    public static final int EXAM_STU_TODO_DELETE = 30634;
    //评卷完后，更新exam_record
    public static final int EXAM_RECORD_STU_UPDATE = 30635;
    //新增人员自动补增考试记录
    public static final int EXAM_RECORD_STU_INSERT = 30636;

    public static final int EXAM_STU_TODO_UPDATE_ENTITY = 30637;
    public static final int EXAM_STU_TODO_UPDATE_AUDITED = 30638;


    /**限流前置通知MQ  |  限流后置通知MQ */
    public static final int EXAM_LIMIT_VIEW=40000;
    public static final int EXAM_LIMIT_RELEASE=40001;
    public static final int EXAM_DISTRIBUTE_LIMIT_VIEW=40002;
    public static final int EXAM_DISTRIBUTE_LIMIT_RELEASE=40003;
}
