package com.zxy.product.examstu.dto.exam.online;

import java.io.Serializable;

/**
 * 考试MQ入参数据体
 * <AUTHOR>
 * @date 2025年02月17日 14:22
 */
public class MsgParameter implements Serializable {
    private static final long serialVersionUID = 3533952877158572749L;

    /**用户归属数据库（南库与北库）*/
    private Integer examRegion;

    /**当前用户Id*/
    private String memberId;

    /**业务类型*/
    private String businessId;

    /**考试开始时间*/
    private Long examStartTime;

    /**考试结束时间*/
    private Long examPersistentTime;

    public Integer getExamRegion() { return examRegion; }

    public void setExamRegion(Integer examRegion) { this.examRegion = examRegion; }

    public String getMemberId() { return memberId; }

    public void setMemberId(String memberId) { this.memberId = memberId; }

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public Long getExamStartTime() { return examStartTime; }

    public void setExamStartTime(Long examStartTime) { this.examStartTime = examStartTime; }

    public Long getExamPersistentTime() { return examPersistentTime; }

    public void setExamPersistentTime(Long examPersistentTime) { this.examPersistentTime = examPersistentTime; }

    @Override
    public String toString() {
        return "MsgParameter{" +
                "memberId='" + memberId + '\'' +
                ", businessId='" + businessId + '\'' +
                ", examStartTime=" + examStartTime +
                ", examPersistentTime=" + examPersistentTime +
                '}';
    }
}
