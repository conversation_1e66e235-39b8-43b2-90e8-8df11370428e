package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.PaperInstance;
import com.zxy.product.exam.entity.PaperInstanceQuestionCopy;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService(timeout=10800000)
public interface PaperInstanceService {


    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    PaperInstance get(Integer examRegion,String id, String examId);

    @Transactional
	String createNewQuestionOrder(Integer examRegion, Optional<PaperInstance> paperInstance, String paperInstanceId, Integer paperSortRule);
    /**
     * 根据examId随机查询一个paper_instance
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    PaperInstance getWithRandomByExamId(Integer examRegion, String examId);

    List<PaperInstanceQuestionCopy> findPaperInstanceQuestionCopiesByPaperId(Integer examRegion,String paperInstanceId, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    PaperInstance getNewInstance(Integer examRegion, String paperInstanceId, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    PaperInstance getSimpleData(Integer examRegion, String id);

    /**
     * 查询考试所有的试卷实例
     * @param examId
     * @return
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<PaperInstance> findByExamId(Integer examRegion,String...examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<PaperInstance> findNewPaperByExamId(Integer examRegion,String...examId);

}
