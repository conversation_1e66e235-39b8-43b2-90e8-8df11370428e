package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.AudienceItem;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService(timeout=700000)
public interface AudienceItemService {

    /**
     * 插入audienceItem 数据，实体类需包含 joinId, joinName, joinType字段，
     * 如果有 则直接更改 referenceCount, 如果没有则执行新增操作
     * @param audienceItems
     * @return
     */
    @Transactional
    List<AudienceItem>  batchInsert(List<AudienceItem> audienceItems, Integer businessType, String businessId, boolean isPublish);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Optional<AudienceItem> getByJoin(Integer joinType, String joinId);
}
