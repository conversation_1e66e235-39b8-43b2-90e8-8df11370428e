package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService(timeout=300000)
public interface SignUpService {


    /**
     * 批量修改
     * @param idList
     * @param status
     * @param uri
     * @return
     */
    @Transactional
    List<SignUp> update(
            Integer examRegion,
            List<String> idList,
            Integer status,
            String currentUserId,
            Optional<Integer> noticeUser,
            Optional<String> noticeUserText,
            Optional<String> noticeUserContent, String uri);

    /**
     * 单独修改
     * @param id
     * @param status
     * @return
     */
    @Transactional
    SignUp update(Integer examRegion, String id, Integer status, String memberId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    SignUp getByExamIdAndMemberId(Integer examRegion, String examId, String memberId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    CloudSignup getByExamIdAndMemberIdForCloudExamInterface(Integer examRegion, String examId, String memberId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    GridSignup getByExamIdAndMemberIdForGridExamInterface(Integer examRegion, String examId, String memberId);

    @Transactional
    SignUp ordinaryInsert(Integer examRegion, String examId, String memberId,int preApprovalStatus);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<SignUp> findSignUpingRecord();

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Optional<SignUp> getOptional(Integer examRegion,String id, String memberId);

    @Transactional
    Integer countSignUp(String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<SignUp> findByMemberId(Integer examRegion, String memberId);

    @Transactional
    Map<String, Object> signUpAuth(Integer examRegion, SignUpAuth signUpAuth, Optional<String> organizationId, String companyId,int preApprovalStatus);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Exam overlapTime(Integer examRegion, String examId, String currentUserId, String organizationId);

    /**
     * 获取报名详情
     * @param id
     * @return
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    SignUp get(Integer examRegion, String id);



    @Transactional
    Map<String, Object> insertCloudSignup(Integer examRegion, CloudSignup cloudSignup);

    @Transactional
    Map<String, Object> insertGridSignup(Integer examRegion, GridSignup gridSignup, Boolean finishAllCourse);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Map<String, Object> getCloudStatus(Integer examRegion, String currentUserId, List<String> asList);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Member getMember(Integer examRegion,String memberId);

    @Transactional
    void changeApplicantExam(Integer examRegion, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Integer countSignUpByExamType(Integer examRegion, Integer type, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    String getMemberIdsAboutExam(Integer examRegion, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Boolean isSignUpPass(Integer examRegion, String memberId, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<String> approveMemberIds(Integer examRegion, String examId, Integer page);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Integer approveMemberCount(Integer examRegion, String examId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<SignUp> getSignUpsByMemberIdsAndExamId(Integer examRegion, List<String> memberIds, String examId);

    /** 获取本年度剩余报名次数 */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Integer getSignUpCount(Integer examRegion, String lastOrganizationId, String memberId);
}
