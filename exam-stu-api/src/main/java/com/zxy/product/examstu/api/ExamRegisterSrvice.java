package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRegist;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService(timeout = 90000)
public interface ExamRegisterSrvice {

    /** 根据人员查找认证考试参与记录*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> getExamCertificationByMemberId(Integer examRegion, String memberId,
            Optional<Integer> optionalInteger);


    /** 根据memberId和examId查找examRegist*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRegist getExamRegistByMemberIdAndExamId(Integer examRegion, String memberId, String examId);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> getExamRegistList(Integer examRegion, String currentUserId, List<String> examIds);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> getNewenergyScoreList(Integer examRegion, String currentUserId, List<String> examIds);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRegist getNewenergyScore(Integer examRegion, String currentUserId, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRegist getExamRegisterByExamIdAndMemberId(Integer examRegion, String currentUserId, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String getExamRecordIdByExamIdAndMemberId(Integer examRegion, String currentUserId, String string);

    /**
     * 考试同步后查询最高分使用，只查询必要字段
     * 2019-12-11
     * @param memberId
     * @param examId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRegist getExamRegisterForCourseStudy(Integer examRegion, String memberId, String examId);

    /** 根据人员查找移动云认证考试参与记录*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> getCloudExamCertificationByMemberId(Integer examRegion, String string, Optional<Integer> optionalInteger);

    /** 根据人员查找网格长考试参与记录*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> getGridExamCertificationByMemberId(Integer examRegion, String string, Optional<Integer> optionalInteger);

    /** 云改专区考试列表
     * @param memberId */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Exam> getCloudExamList(String memberId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> getPersonalExamIds();

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> getQuestionDepotListByExamId(String examId);

    @Transactional
    boolean moreThanOneTimes(Integer examRegion, String examId, String memberId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer getExamRegisterCount(Integer examRegion, Long currentTime, String stringYear);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ExamRegist> findExamRegisterPage(Integer examRegion, int page, Long currentTime, String stringYear);

    @Transactional
    void updateExamRegistCertificate(Integer examRegion, List<String> examRegistersIds, String stringYear);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer getPassCount(Integer examRegion, String examId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer calculateExamJoinNum(Integer examRegion, String examId);

}

