package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.CloudSignup;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.SignUp;
import com.zxy.product.exam.entity.SignUpAuth;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 *
 * ClassName: SignUpAuthService <br/>
 * Reason: 报名认证服务类 <br/>
 * date: 2017年10月19日 下午3:25:43 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@RemoteService(timeout = 300000)
public interface SignUpAuthService {

	/**
	 *
	 * cancelSign:取消报名 <br/>
	 ** @since JDK 1.8
	 * date: 2017年11月3日 下午3:57:42 <br/>
	 */
	@Transactional
	SignUp cancelSign(Integer examRegion, String examId, Integer status, String memberId);

	/**
	 *
	 * get:获取报名认证信息. <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @return
	 * @since JDK 1.8
	 * date: 2017年10月19日 下午7:33:13 <br/>
	 */
	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	SignUpAuth get(Integer examRegion, String id,String memberId);


	/** 通过报名记录id查找认证信息记录 */
	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	SignUpAuth findBySignup(Integer examRegion, String signUpId);

	/** 移动云考试-通过报名记录id查找认证信息记录 */
	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	CloudSignup findCloudBySignup(Integer examRegion, String cloudSignupId);

	@Transactional
    SignUpAuth insertSignUpAuth(SignUpAuth signUpAuth);


	/**
	 *
	 * findAuditList:查询报名审核列表 <br/>
	 *
	 * <AUTHOR>
	 * @param memberId
	 * @return
	 * @since JDK 1.8
	 * date: 2017年10月21日 上午11:20:09 <br/>
	 */
	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PagedResult<Exam> findAuditList(Integer examRegion, String memberId,Integer page,
									Integer pageSize,Optional<Integer> startTimeOrderBy);


}
