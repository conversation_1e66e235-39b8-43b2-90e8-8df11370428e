package com.zxy.product.examstu.dto;

import java.io.Serializable;

/**
 * 考试信息(IHR接口专用).
 *
 * <AUTHOR>
 */
public class ExaminationData implements Serializable {

  private static final long serialVersionUID = 1152797893598362102L;

  /**
   * 关联课程id.
   */
  private String courseId;

  /**
   * 课程名称.
   */
  private String courseName;

  /**
   * 考试名称
   */
  private String examinationName;
  /**
   * 创建单位
   */
  private String examinationUnit;
  /**
   * 开始时间
   */
  private String startTime;
  /**
   * 结束时间
   */
  private String endTime;
  /**
   * 合格分数
   */
  private String qualifiedScore;
  /**
   * 状态
   */
  private String status;
  /**
   * 扩展字段
   */
  private String inputExt;

  public String getExaminationName() {
    return examinationName;
  }

  public void setExaminationName(String examinationName) {
    this.examinationName = examinationName;
  }

  public String getExaminationUnit() {
    return examinationUnit;
  }

  public void setExaminationUnit(String examinationUnit) {
    this.examinationUnit = examinationUnit;
  }

  public String getStartTime() {
    return startTime;
  }

  public void setStartTime(String startTime) {
    this.startTime = startTime;
  }

  public String getEndTime() {
    return endTime;
  }

  public void setEndTime(String endTime) {
    this.endTime = endTime;
  }

  public String getQualifiedScore() {
    return qualifiedScore;
  }

  public void setQualifiedScore(String qualifiedScore) {
    this.qualifiedScore = qualifiedScore;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getInputExt() {
    return inputExt;
  }

  public void setInputExt(String inputExt) {
    this.inputExt = inputExt;
  }

  public String getCourseId() {
    return courseId;
  }

  public void setCourseId(String courseId) {
    this.courseId = courseId;
  }

  public String getCourseName() {
    return courseName;
  }

  public void setCourseName(String courseName) {
    this.courseName = courseName;
  }

  @Override
  public String toString() {
    return "ExaminationData{" +
        "courseId='" + courseId + '\'' +
        ", courseName='" + courseName + '\'' +
        ", examinationName='" + examinationName + '\'' +
        ", examinationUnit='" + examinationUnit + '\'' +
        ", startTime='" + startTime + '\'' +
        ", endTime='" + endTime + '\'' +
        ", qualifiedScore='" + qualifiedScore + '\'' +
        ", status='" + status + '\'' +
        ", inputExt='" + inputExt + '\'' +
        '}';
  }
}
