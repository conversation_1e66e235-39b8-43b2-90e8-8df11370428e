package com.zxy.product.examstu.api.sequence.business;

import com.zxy.common.base.annotation.RemoteService;

/**
 * 普通考试证书编号生成器
 * <AUTHOR>
 * @date 2017年11月28日
 */
@RemoteService(timeout = 90000)
public interface CertificateNormalCodeGenerator {

    /**
     * 根据规则生成流水号
     * @param typeCode 证书类型：考试-001
     * @param date 具体到年月日，格式：20170101
     * @param orgCode 即考试归属部门编码
     */
    String getCode(String typeCode, String date, String orgCode) throws Exception;
}
