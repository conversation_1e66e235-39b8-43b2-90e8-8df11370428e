package com.zxy.product.examstu.util;

import org.springframework.util.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class EncryptUtil {
    final static String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    final static String AES_KEY = "d8cg8gVakEq9Agup";

    /**
     * 解密
     * @param sSrc 加密后的base 64 code
     * @param decryptKey 解密密钥
     * @return 解密后的内容
     */
    public static String Decrypt(String sSrc, String decryptKey) throws Exception {
        if (StringUtils.isEmpty(sSrc)){
            return sSrc;
        }
        try {
            if (StringUtils.isEmpty(decryptKey)) {
                decryptKey = AES_KEY;
            }
            byte[] encryptBytes = new BASE64Decoder().decodeBuffer(sSrc);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
            byte[] decryptBytes = cipher.doFinal(encryptBytes);
            return new String(decryptBytes);
        } catch (Exception e) {
            throw new Exception("sSrc=" + sSrc + ",key=" + decryptKey);
        }
    }

    /**
     * AES加密为base 64 code
     * @param content 待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的base 64 code
     */
    public static String aesEncrypt(String content, String encryptKey) {
        if (StringUtils.isEmpty(encryptKey)) {
            encryptKey = AES_KEY;
        }
        String result;
        try {
            result = new BASE64Encoder().encode(aesEncryptToBytes(content, encryptKey));
        } catch (Exception e) {
            throw new RuntimeException("sSrc=" + content + ",key=" + encryptKey);
        }
        return result.replace("\n", "");
    }

    private static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
    }
}
