package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;

import com.zxy.product.exam.entity.ExamRegist;

/**
 *
 * ClassName: AfterAssessmentService <br/>
 * Reason: 后评估服务类 <br/>
 * date: 2017年11月7日 上午10:46:43 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@RemoteService(timeout=3000000)
public interface AfterAssessmentService {

	    /**
	     * 前端考试个人后评估
	     * @param memberId
	     * @return
	     */
	    ExamRegist frontExamPersonalDetailList(Integer examRegion, String examId, String memberId);


}
