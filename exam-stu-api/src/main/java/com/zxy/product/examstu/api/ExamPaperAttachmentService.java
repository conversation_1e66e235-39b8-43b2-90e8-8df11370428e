package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.ExamPaperAttachment;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@RemoteService()
public interface ExamPaperAttachmentService {

  List<ExamPaperAttachment> getPaperListByExamId(String examId);

  Optional<ExamPaperAttachment> getByPaperInstanceId(String paperInstanceId);

}
