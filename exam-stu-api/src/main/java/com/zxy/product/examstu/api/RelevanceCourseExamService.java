package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.RelevanceCourseExam;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RemoteService
public interface RelevanceCourseExamService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<RelevanceCourseExam> findRelevanceCourseExamList(Integer examRegion, String examId);

}
