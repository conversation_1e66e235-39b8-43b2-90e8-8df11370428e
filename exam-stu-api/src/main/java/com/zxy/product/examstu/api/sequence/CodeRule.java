package com.zxy.product.examstu.api.sequence;

import com.zxy.common.base.annotation.RemoteService;

import java.util.Optional;

/**
 * 编码规则
 * <AUTHOR>
 * @date 2017年11月11日
 */
@RemoteService(timeout=90000)
public interface CodeRule {

    /**
     * 根据前缀得到格式化之后的序列号字符串
     * @param prefix 前缀
     * @return 格式化之后的序列号字符串
     * @throws Exception
     */
    String getCode(Optional<String> prefix) throws Exception;

    /**
     * 根据前缀得到格式化之后的序列号字符串，支持自定义初次回调
     * @param callback 获取DB已有最大序列号的回调实现
     * @param prefix 前缀
     * @return 格式化之后的序列号字符串
     * @throws Exception
     */
    String getCode(CodeMaxCallback callback, Optional<String> prefix) throws Exception;
}
