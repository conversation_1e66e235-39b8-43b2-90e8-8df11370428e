package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.ExamNotice;
import com.zxy.product.exam.entity.MarkConfig;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RemoteService
public interface MarkConfigService {

//    @Transactional
//    List<MarkConfig> insert(String examId, List<MarkConfig> markConfigs, Integer anonymityMark, List<ExamNotice> examNotices);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<MarkConfig> find(Integer examRegion, String examId, String memberId);

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<MarkConfig> find(Integer examRegion, String examId);


    @Transactional
	boolean validateMarkPaper(Integer examRegion, String string, String currentUserId, String examId);

    @Transactional
    boolean validateAuditPaper(Integer examRegion, String string, String currentUserId, String examId);


}
