package com.zxy.product.examstu.content;

import com.zxy.common.base.exception.Code;


/**
 * <AUTHOR>
 *
 */
public enum ErrorCode implements Code{

	ImportQuestionOptionsError(30101, "import options of question error"),
	ImportQuestionAnswerErrorRange(30102, "import answer of question error range"),
	ImportQuestionAnswerHaveSpace(30528, "import answer of question have space"), // 答案里面有空格
	ImportSentenceCompeletionWithChinesebracket(30103, "import sentenceCompetion with chinese bracket"),
	ImportSentenceCompletionWithOutEnglishBracket(30104, "import sentenceCompetion without english bracket"),
	ImportContentOverSize(30122, "import question content over size"),
	ImportSentenceAnswerError(30105, "import sentence completion answer error"),
	ImportSentenceAnswerHaveCHNSG(30526, "import sentence completion answer have CHN SG"),// 填空题答案里有中文丨
	ImportQuestionScoreDecimalError(30106, "import question score decimal error"),
	ImportQuestionScoreOverError(30110, "import question score over error"),
	ImportSingleQuestionErrorAnswer(30107, "import single question answer error"),
	ImportMultipleQuestionErrorAnswer(30127, "import multiple question answer error"),	// 多选题答案异常
	ImportMultipleQuestionAnswerAtLeast2(30527, "import multiple question answer at least 2"),	// 多选题答案至少2个
	ImportSortingQuestionErrorAnswer(30128, "import sorting question answer error"),	// 排序题答案异常
	ImportQuestionAnswerRepeatError(30129,"import question answer repeat error"),		// 答案重复异常
	ImportQuestionIndexError(30130,"import index of question error"),						// 序号格式错误
	ImportQuestionScoreError(30118, "import question score error"), // 评估问卷导入-分数格式不正确
	ImportQuestionScoreNotMatchOptionsError(30119, "import question score and　options do not match error"),// 评估问卷导入-分数与选项数量不匹配
	ImportQuestionOptionsGtLimit(30131, "import options of gt limit"), // 试题选项超过最大长度
	ImportQuestionTypeError(30108, "import question type error"),
	ImportQuestionDimensionNotExists(30134, "dimension not exists"),
	ImportQuestionDepotGrantError(30123, "import question depot grant error"),
	ImportQuestionDepotNotExistError(30124, "import question depot not exist error"),
	ImportQuestionOrganizationNotExistError(30132, "import question organization not exist error"),			// 试题部门编码不存在或不在授权范围内
	ImportQuestionOrganizationGrantError(30125, "import question organization grant error"),				// 试题部门编码不在授权范围内
	ImportQuestionOrganizationMismatchingError(30133, "import question organization mismatching error"),	// 部门编码与试题目录编码不匹配
	EXAM_NAME_EXISTS(30109, "exam name exists"),
	CanNotRefuseSignUp(30111, "can not refuse sign up record, cause examing"),
	OverExam(30112, "the exam is over"),
	ErrorViewScoreDetail(30113, "error to view score detail"),
	CanNotPublishExamError(30114, "can not publish exam error"),
	CanNotExamMore(30115, "can not exam more"),
	CanNotDeleteExamRefrenceError(30116, "can not delete exam  cause refrence error"),
	ExamStatusError(30117, "exam status error"),
	IsNotAudient(30120, "is not audient"),
	GetDecryptKeyError(30121, "get decryptkey error"),

	HasSignUpBeCancel(30136, "has signup be cancel"),
    ExamNotAuditTime(30137, "Not in the audit time"), // 不在审核时间范围

	QuestionClassesCanNotBeNull(30201,"question classes can not be null"),
    GetPaperError(30205, "get paper error"),
    PaperTacticCanNotBeNull(30202,"paper tactics can not be null"),
    TacticOrQuestionCanNotBeNull(30203,"paper tactics can not be null"),
    CanNotDeleteQuestionWithRefToPaper(30204, "can not delete question with reference to paper error"),

    CODE_ALREADY_EXISTS(30301, "code already exists"),
    RemoveQuestionDepotRefrenceError(30302, "remove question depot refrence error"),
    HasSubDepotError(30303, "has sub depot error"),
    RefrenceWithExamError(30308, "refrence with exam error"),

    ImportQuestionDepotNameError(30401, "import question depot name error"),
    ImportQuestionDepotCodeError(30402, "import question depot name error"),
    ImportQuestionDepotParentCodeError(30403, "import queston depot parent code error"),
    ImportQuestionDepotParentCodeNoGrantError(30404, "import queston depot parent code  grant error"),
    ImportQuestionDepotNameLengthError(30405, "import queston depot name length  error"),

    EXAMROOM_STATUS_MUST_BE_UNPUBLISH(30505, "examroom status must be unpublish"),
    EXAMROOM_STATUS_MUST_BE_PUBLISH(30504, "examroom status must be publish"),
    UNKNOW_EXAM_TYPE(30506, "unknow exam type"),
    AUDIENCE_ITEM_JSON_ERROR(30507, "audienceItem json format error"),
	NO_EXAM_RECORD_FOR_MEMBER(30508, "no exam record for member"),
    CAN_NOT_REPEAT_SIGNUP(30509, "can not repeat signup"),
    EXAM_STATUS_ERROR(30510, "exam status error"),
    SUBMIT_PAPER_ERROR(30511, "submit paper error"),
    ADD_USER_TO_EXAM_ERROR(30512, "add user to exam error"),
    RESEARCH_ANSWER_ALREADY_EXIST(30513, "research answer already exist"),
    ResearchHadFinished(30514, "research  has finished "),
    ResearchNoStart(30515, " research no start"),
    ResearchRequestUrlNoFull(30516, "research request url no full"),
    ResearchHasNoDimensions(30517, "Research has no dimensions"),
    ResearchSubmitRepeatedly(30518, "Can not submit repeatedly"),
    ResearchHasNoAudience(30519, "Research has no audiences"),
    ResearchNotPublish(30520, "Research not publish"),
    ResearchRecordError(30521, "research record error"),
    ResearchHasNoQuestions(30522, "Research has no questions"),
    ResearchInSummary(30523, "Research in summary"),

    ExamInitExamRecordDataError(30601, "init exam record data error"),

    ActivityRecommendGtLimit(30701, "Activity recommend number greater than limit"),

    TemplateError(30801, "template error"),

    ExamNullError(30126, "exam null error"),

    ExamApplicantNoPass(30138, "exam signup error"),

	OrgCanNotDeleteSinceExamReleated(30901, "can not delete, cause this org has exam releated"),
	OrgCanNotDeleteSincePaperClassReleated(30902, "can not delete, cause this org has paper class releated"),
	OrgCanNotDeleteSinceQuestionReleated(30903, "can not delete, cause this org has question releated"),
	OrgCanNotDeleteSinceQuestionDepotReleated(30904, "can not delete, cause this org has question depot releated"),
	OrgCanNotDeleteSinceResearchReleated(30905, "can not delete, cause this org has research releated"),

	MarkPaperError(30206, "mark paper error"),

	WebSocketSessionOpenError(30139, "web socket session open error"),

	ImportEmptyData(30140, "import empty data"),

	ImportQuestionDiffcultError(30141, "import question diffcult error"),

	HadMarkedError(30207, "had marked error"),

	NoMarkPermit(30208, "no mark permit"),

	SubmitMarkNoPermitError(30209, "submit mark no permit error"),

	ImportJudgeAnswerError(30135, "import judge answer error"),

	AlreadyMarkPaperError(30211, "already mark paper error"),

	ImportQuestionDepotCodeLengthError(30411, "import question depot code length error"),

	ExamPasswordOrPersonalCodeError(30150, "password or personalcode error"),

	ExamRecordStatusRequired(39011, "exam record status is required"), // 考试记录参数缺失

	ExamRecordScoreDetailError(39012, "exam record score detail error"), // 考试记录查看详情错误

    // 证书发放-导入
	CertificateImprotExceedMax(31001, "template data to long"),
	CertificateScoreError(31002, "import question score error"), // 分数格式错误
    CertificateProfessionNotExists(31003, "import profession not exists"), // 专业不存在
    CertificateSubNotMatchProfession(31004, "import subProfession not match profession"), // 子专业与专业不匹配
    CertificateProfessionLevelNotExists(31005, "import professionLevel not exists"), // 子专业下没有该等级
    CertificateEquipmentTypeNotExists(31006, "import equipmentType not exists"), // 设备类型不存在
    CertificateMemberNotExists(31007, "import member not exists"), // 人员不存在
    CertificateMemberExists(31008, "import certificate member exists"),// 该用户已拥有该证书
    CertificateCodeGeneratError(31009, "import certificate code generate error"),// 证书编号生成失败
    CertificateCannotToRegisterMember(31010, "import member not exists"), // 不能为代维人员导入证书
    CertificateMemberNotGrant(31011, "import member not grant"), // 人员不在授权范围内

    CertificateDoesNotExist(31012, "Certificate does not exist"), // 证书不存在
    MemberNameNotExist(31013, "member name not exist"), // 员工编号不存在

    // 监考管理-导入
    InvigilatorImprotExceedMax(31101, "template data to long"),
    InvigilatorMemberNotExists(31102, "import member not exists"), // 人员不存在
    InvigilatorBatchNoExams(31103, "import exam not exists"), // 该批次下没有考试记录
    InvigilatorOrganizationNotExists(31104, "import organization not exists"), // 组织不存在或不在您权限范围内
    InvigilatorExambatchNotMatched(31105, "import organization not exists"), // 批次不匹配
    InvigilatorNotExists(31106, "invigilator not exists"), // 已经被取消监考老师
    InvigilatorMemberNotGrant(31107, "import member not exists"), // 人员不在您权限范围内
    NoDataInTheGridExamOfThisLevel(31108, "no data in the grid exam of this level"), // 该级别网格考试无数据

    RegistrationHasBeenRejected(30412, "Registration has been rejected" ), // 报名已被拒绝
    ExamTimeConflicts(30413, "Exam time conflicts" ), // 考试存在交叉时间
    TheRegistrationPeriodHasPassed(30415, "The registration period has passed" ), // 已过报名时间
    AuthSignupCancel(30524, "Auth Signup is not allowed to cancel" ), //待审核状态不允许取消

    ExamStatusIsPublishing(30210, "Exam Status is publishing"), // 考试处于发布中
    UpdateExamStartTimeError(30212, "Update Exam StartTime Error"), // 不支持修改进入考试开始时间的年份
    ResearchQuestionaryIsPublishing(30525, "Research Questionary Status is publishing"), // 调研处于发布中
    ExamNoStart(30602, "exam no start"), // 考试未开始
    ExamIsOver(30603, "exam is over"), // 考试未开始
    DoNotMeetTheExamConditions(30604, "exam is over"), // 不满足考试条件
    TheAuthenticationInformationHasBeenModified(30605, "The authentication information has been modified"), // 认证信息已被修改
    ThisLevelHasData(30606, "This Level Has Data"), // 该级别已有数据
    GridAuditParameterError(30607, "Grid Audit Parameter Error"), // 参数错误
    MemberFullNameNoMatch(30608, "Member Full Name No Match"), // 员工姓名和员工编号不匹配
    TheCertificationInformationOfThisLevelCanOnlyBeModifiedOnce(30609, "The certification information of this level can only be modified once"), // 该学员该等级认证信息仅可修改一次
    NumberOfUploadedFilesExceeded(30610, "Number of uploaded files exceeded"), // 超出上传文件数量限制
    TimeFormatError(30611, "Time format error"), // 时间格式错误
    GridFileExportError(30612, "GridFileExportError"), // 审核材料下载失败
    ExamRecordNotExists(30613, "examRecord not exists"), // 考试记录不存在
    CurrentIrrevocable(30501,"Current irrevocable"), //当前不可撤销
    CurrentlyUnpublished(30502,"Currently unpublished"), //当前不可发布
    CurrentlyUnUpdate(30503,"Currently update"), //当前不可修改
    ExamsDonExist(30544,"Exams don't exist"), //考试不存在
    NoCertificateIsConfiguredForTheExam(30545,"No certificate is configured for the exam"), //考试未配置证书
    CertificateAlreadyExists(30546,"Certificate already exists"), //证书已存在
    EmployeesAreNotPartOfTheTestIsAudience(30547,"Employees are not part of the test's audience"),//员工不在该场考试的受众范围内

    OriginalExamTimeEmptyNotAllowedChange(30548,"原考试时间为空不允许修改"),
    CurrentTimeGreaterThanOriginalRecordEndTimeNotAllowedChange(30549,"当前时间大于原纪录结束时间,不允许修改"),
    CurrentTimeIntervalNotAllowedModifyStartTime(30550,"当前时间在考试区间内,不允许修改开始时间"),
    EndTimeCanNotLessThanOrEqualStartTime(30551,"考试结束时间不能小于等于考试开始时间"),
    CurrentTimeIntervalEndTimeLessThanOrEqualOriginalStartTime(30552,"当前时间在考试区间内,结束时间必须大于原纪录开始时间"),

    ExamHadAlreadyStartedCannotModified(30553,"当前考试已经开始,无法修改"),
    SystemAutomaticallyMarkingInternDoNotNeedConfigureInitialMarking(30554,"填空题为系统自动阅卷初评,无需配置初评阅卷人"),

    MaximumNumberRevisionsHasBeenReached(30555,"该学员修改次数已达上限"),
    NoCertificateSameLevelHasBeenIssuedCannotImported(30556,"没有发放同等级证书，无法导入"),
    UnableImportWithoutObtainingCertificateSupplementaryStudyArea(30558,"未获得补学专区证书，无法导入"),
    DataConsistentCannotModified(30559,"导入数据完全一致，无法导入"),

    ThereIsNoExamToday(30601,"今日无考试"),
    GenerateExamPaperError(40001,"生成考试试卷异常,请联系管理员"),
    NoSignUpCount(40005,"没有报名次数"),

            ;

    private final int code;

    ErrorCode(int code, String desc) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }


}
