package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.SignupRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService(timeout=30000)
public interface SignUpRecordService {

    /** 查找报名历史记录集合 */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<SignupRecord> findList(Integer examRegion, String memberId,
            Optional<String> examId);

}
