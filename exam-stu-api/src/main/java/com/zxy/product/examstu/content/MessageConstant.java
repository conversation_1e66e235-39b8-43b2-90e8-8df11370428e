package com.zxy.product.examstu.content;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息模板code
 * <AUTHOR>
 *
 */
public class MessageConstant {

    //消息类型 -- 站内信
    public static final int MESSAGE_TYPE_INNER = 1;
    //消息类型 -- 邮件
    public static final int MESSAGE_TYPE_EMAIL = 2;
    //消息类型 -- app
    public static final int MESSAGE_TYPE_APP = 3;
    //消息类型 -- 短信
    public static final int MESSAGE_TYPE_SHORT = 4;

    //消息业务类型 -- 知识
    public static final int TYPE_KNOWLEDGE = 1;
    //消息业务类型  -- 课程
    public static final int TYPE_COURSE = 2;
    //消息业务类型 -- 专题
    public static final int TYPE_SUBJECT = 3;
    //消息业务类型 -- 考试
    public static final int TYPE_EXAM = 4;
    //消息业务类型 -- 调研
    public static final int TYPE_RESEARCH = 5;
    //消息业务类型 -- 培训
    public static final int TYPE_TRAIN = 6;
    //消息业务类型 -- 问吧
    public static final int TYPE_BAR = 7;
    //消息业务类型 -- 登录
    public static final int TYPE_LOGIN = 8;
    //消息业务类型 -- 微课大赛
    public static final int TYPE_COMPETITION = 9;
    //消息业务类型 -- 积分商城
    public static final int TYPE_MALL = 10;
    //消息业务类型 -- 学习
    public static final int TYPE_STUDY = 11;
    //消息业务类型 -- 幕客
    public static final int TYPE_MOOC = 12;
    //消息业务类型 -- 直播
    public static final int TYPE_LIVE = 13;
    //消息业务类型 -- 班级
    public static final int TYPE_CLASS = 14;
    //消息业务类型 -- 账号
    public static final int TYPE_ACCOUNT = 15;
    //消息业务类型 -- 监控
    public static final int TYPE_MONITOR = 16;

    //是否发送开关 -- 关闭
    public static final int SEND_STATUS_CLOSE = 2;
    //是否发送开关 -- 打开
    public static final int SEND_STATUS_OPEN = 1;

    //新增账号成功
    public static final String ACCOUNT_ADD_SUCCESS = "account_add_success";
    //密码修改
    public static final String ACCUONT_PASSWORD_UPDATE = "accuont_password_update";
    //讨论回复被举报
    public static final String BAR_DISCUSS_ACCUSE = "bar_discuss_accuse";
    //讨论回复审核通过
    public static final String BAR_DISCUSS_AUDIT_PASS = "bar_discuss_audit_pass";
    //讨论回复审核未通过
    public static final String BAR_DISCUSS_AUDIT_REFUSE = "bar_discuss_audit_refuse";
    //讨论被加精/置顶
    public static final String BAR_DISCUSS_ESSENCE_TOP = "bar_discuss_essence_top";
    //讨论被点赞
    public static final String BAR_DISCUSS_PARISE = "bar_discuss_parise";
    //问题/文章被加精/置顶
    public static final String BAR_ESSENCE_TOP = "bar_essence_top";
    //申请成为专家审核通过
    public static final String BAR_EXPERT_AUDIT_PASS = "bar_expert_audit_pass";
    //申请成为专家审核拒绝
    public static final String BAR_EXPERT_AUDIT_REFUSE = "bar_expert_audit_refuse";
    //专家擅长话题下问题的回复
    public static final String BAR_EXPERT_INVITE = "bar_expert_invite";
    //提问/文章被举报
    public static final String BAR_QUESTION_ACCUSE = "bar_question_accuse";
    //提问/发文审核通过
    public static final String BAR_QUESTION_AUDIT_PASS = "bar_question_audit_pass";
    //  提问/发文审核拒绝
    public static final String BAR_QUESTION_AUDIT_REFUSE = "bar_question_audit_refuse";
    //考试评卷
    public static final String CLASS_EVALUATION = "class_evaluation";
    //考试阅卷安排
    public static final String CLASS_EVALUATION_PLAN = "class_evaluation_plan";
    //作业评审处理
    public static final String CLASS_HOMEWORK_DEAL = "class_homework_deal";
    //作业评审人
    public static final String CLASS_HOMEWORK_PLAN = "class_homework_plan";
    //需求方评估
    public static final String CLASS_NEED_EVALUATE = "class_need_evaluate";
    //需求方计划执行提醒
    public static final String CLASS_PLAN_EXECUTE = "class_plan_execute";
    //班级预定申请
    public static final String CLASS_RESERVE_APPLY = "class_reserve_apply";
    //预定审核通知通过
    public static final String CLASS_RESERVE_AUDIT_PASS = "class_reserve_audit_pass";
    //预定审核通知拒绝
    public static final String CLASS_RESERVE_AUDIT_REFUSE = "class_reserve_audit_refuse";
    //学员满意度评估
    public static final String CLASS_SATISFACTION = "class_satisfaction";
    //培训报名审核通过
    public static final String CLASS_SIGN_AUDIT_PASS = "class_sign_audit_pass";
    //培训报名审核拒绝
    public static final String CLASS_SIGN_AUDIT_REFUSE = "class_sign_audit_refuse";
    //培训报名通知
    public static final String CLASS_SIGN_UP = "class_sign_up";
    //调研问卷
    public static final String CLASS_TRAIN_QUESITION = "class_train_quesition";
    //课程讨论被举报
    public static final String COURSE_ACCUSE = "course_accuse";
    //课程讨论举报处理
    public static final String COURSE_ACCUSE_DEAL = "course_accuse_deal";
    //课程讨论审核
    public static final String COURSE_DISCUSS_AUDIT = "course_discuss_audit";
    //课程讨论被设置精品/置顶
    public static final String COURSE_ESSENCE_TOP = "course_essence_top";
    //考试评卷安排通知
    public static final String COURSE_EXAM_ARRANGE = "course_exam_arrange";
    //考试评卷
    public static final String COURSE_EXAM_EVALUATION = "course_exam_evaluation";
    //作业评审人通知
    public static final String COURSE_HOMEWORK_REVIEWER = "course_homework_reviewer";
    //学员作业提交通知
    public static final String COURSE_HOMEWORK_SUBMIT = "course_homework_submit";
    //课程讨论被点赞
    public static final String COURSE_PRAISE = "course_praise";
    //课程发布通知
    public static final String COURSE_PUBLISH = "course_publish";
    //课程讨论被回复
    public static final String COURSE_REPLY = "course_reply";
    //课程更新通知
    public static final String COURSE_UPDATE = "course_update";
    //课程催学通知
    public static final String COURSE_URGED = "course_urged";
    //动态密码通知
    public static final String DYNAMIC_PASSWORD = "dynamic_password";
    //考试审核通知通过
    public static final String EXAM_AUDIT_PASS = "exam_audit_pass";
    //考试审核通知通过(人脸检测考试)
    public static final String EXAM_FACE_MONITOR_AUDIT_PASS = "exam_face_monitor_audit_pass";
    //考试审核通知拒绝
    public static final String EXAM_AUDIT_REFUSE = "exam_audit_refuse";
    //考试撤销
    public static final String EXAM_CALCEL = "exam_calcel";
    //延时通知
    public static final String EXAM_DELAY = "exam_delay";
    //考试评卷
    public static final String EXAM_EVALUATION_DEL = "exam_evaluation_del";
    //考试阅卷安排
    public static final String EXAM_MARK_PLAN = "exam_mark_plan";
    //考试发布
    public static final String EXAM_PUBLISH = "exam_publish";
    //考试报名通知
    public static final String EXAM_SIGN_UP = "exam_sign_up";
    //考试催办
    public static final String EXAM_URGE = "exam_urge";
    //(人脸监考)考试催办
    public static final String EXAM_URGE_FACE_MONITOR = "exam_urge_face_monitor";
    //密码修改
    public static final String FORGET_PASSWORD_FIND = "forget_password_find";
    //知识审核通过
    public static final String KNOWLEDGE_AUDIT_PASS = "knowledge_audit_pass";
    //知识审核拒绝
    public static final String KNOWLEDGE_AUDIT_REFUSE = "knowledge_audit_refuse";
    //知识讨论被举报
    public static final String KNOWLEDGE_DISCUSS_ACCUSE = "knowledge_discuss_accuse";
    //知识讨论审核通过
    public static final String KNOWLEDGE_DISCUSS_AUDIT_PASS = "knowledge_discuss_audit_pass";
    //知识讨论审核拒绝
    public static final String KNOWLEDGE_DISCUSS_AUDIT_REFUSE = "knowledge_discuss_audit_refuse";
    //知识讨论被点赞
    public static final String KNOWLEDGE_DISCUSS_PARISE = "knowledge_discuss_parise";
    //知识讨论被回复
    public static final String KNOWLEDGE_DISCUSS_REPLY = "knowledge_discuss_reply";
    //知识讨论被设为精品/置顶
    public static final String KNOWLEDGE_ESSENCE_TOP = "knowledge_essence_top";
    //考试评卷处理
    public static final String LIVE_EXAM_DEAL = "live_exam_deal";
    //考试评卷安排通知
    public static final String LIVE_EXAM_PLAN = "live_exam_plan";
    // 考试成绩
    public static final String EXAM_SCORE = "exam_score";
    //直播发布
    public static final String LIVE_PUBLISH = "live_publish";
    //直播预约
    public static final String LIVE_SUBSCRIBE = "live_subscribe";
    //授权
    public static final String MONITOR_IMPOWER = "monitor_impower";
    //同步监控
    public static final String MONITOR_SYNC = "monitor_sync";
    //白名单监控
    public static final String MONITOR_WHITE = "monitor_white";
    //幕客讨论举报处理
    public static final String MOOC_ACCUSE_DEAL = "mooc_accuse_deal";
    //幕客报名审核通过
    public static final String MOOC_AUDIT_PASS = "mooc_audit_pass";
    //幕客报名审核拒绝
    public static final String MOOC_AUDIT_REFUSE = "mooc_audit_refuse";
    //幕客讨论被举报
    public static final String MOOC_DISCUSS_ACCUSE = "mooc_discuss_accuse";
    //幕客讨论审核
    public static final String MOOC_DISCUSS_AUDIT = "mooc_discuss_audit";
    //幕客讨论被点赞
    public static final String MOOC_DISCUSS_PRAISE = "mooc_discuss_praise";
    //幕客讨论被设为精品/置顶
    public static final String MOOC_ESSENCE_TOP = "mooc_essence_top";
    //考试评卷
    public static final String MOOC_EVALUATION = "mooc_evaluation";
    //考试评卷安排通知
    public static final String MOOC_EXAM_PLAN = "mooc_exam_plan";
    //作业评审处理
    public static final String MOOC_HOMEWORK_DEL = "mooc_homework_del";
    //作业互评
    public static final String MOOC_HOMEWORK_EACH = "mooc_homework_each";
    //作业评审人设置
    public static final String MOOC_HOMEWORK_REVIEWER = "mooc_homework_reviewer";
    //作业自评
    public static final String MOOC_HOMEWORK_SELF = "mooc_homework_self";
    //幕客发布
    public static final String MOOC_PUBLISH = "mooc_publish";
    //幕客讨论被回复
    public static final String MOOC_REPLY = "mooc_reply";
    //幕客学习阶段
    public static final String MOOC_STUDY_REMIND = "mooc_study_remind";
    //密码修改
    public static final String PHONE_PASSWORD_CHANGE = "phone_password_change";
    //验证码
    public static final String PHONE_VERIFICATION = "phone_verification";
    //注册申请通知
    public static final String REGISTER_APPLY = "register_apply";
    //注册结果通知失败
    public static final String REGISTER_FAIL = "register_fail";
    //注册结果通知成功
    public static final String REGISTER_SUCCESS = "register_success";
    //调研发布
    public static final String RESEARCH_PUBLISH = "research_publish";
    //调研催办
    public static final String RESEARCH_URGE = "research_urge";
    //学习推送
    public static final String STUDY_PUSH = "study_push";
    //专题讨论举报处理
    public static final String SUBJECT_ACCUSE_DEAL = "subject_accuse_deal";
    //专题讨论被举报
    public static final String SUBJECT_DISCUSS_ACCUSE = "subject_discuss_accuse";
    //专题讨论审核
    public static final String SUBJECT_DISCUSS_AUDIT = "subject_discuss_audit";
    //专题讨论被点赞
    public static final String SUBJECT_DISCUSS_PRAISE = "subject_discuss_praise";
    //专题讨论被加精/置顶
    public static final String SUBJECT_ESSENCE_TOP = "subject_essence_top";
    //考试评卷安排通知
    public static final String SUBJECT_EXAM_EVALUATION = "subject_exam_evaluation";
    //考试评卷
    public static final String SUBJECT_EXAM_SUBMIT = "subject_exam_submit";
    //作业评审处理
    public static final String SUBJECT_HOMEWORD_DEAL = "subject_homeword_deal";
    //作业评审人设置
    public static final String SUBJECT_HOMEWORK_REVIEWER = "subject_homework_reviewer";
    //专题发布
    public static final String SUBJECT_PUBLISH = "subject_publish";
    //专题讨论被回复
    public static final String SUBJECT_REPLY = "subject_reply";
    //专题重新发布
    public static final String SUBJECT_UPDATE = "subject_update";
    // 考试证书发放
    public static final String EXAM_CERTIFICATE_ISSUE = "exam_certificate_issue";
    // 监考通知
    public static final String INVIGILATOR_NOTICE = "invigilator_notice";
    // 考试证书自动发放
    public static final String EXAM_CERTIFICATE_ISSUE_AUTO = "exam_certificate_issue_auto";
    // 证书手工发放
    public static final String EXAM_CERTIFICATE_ISSUE_IMPORT = "exam_certificate_issue_import";
    //申请复核通知
    public static final String FACE_EXAM_REVIEW = "face_exam_review";
    // 人脸智能监考(通知监考员)
    public static final String FACE_MONITOR_INVIGILATOR_NOTICE = "face_monitor_invigilator_notice";


    //设置属于@我类型的通知:个人的讨论和回复的相关消息都属于@我
    public static final List<String> AT_ME_LIST = new ArrayList<>();

    static{
        AT_ME_LIST.add(BAR_DISCUSS_ACCUSE);
        AT_ME_LIST.add(BAR_DISCUSS_AUDIT_PASS);
        AT_ME_LIST.add(BAR_DISCUSS_AUDIT_REFUSE);
        AT_ME_LIST.add(BAR_DISCUSS_ESSENCE_TOP);
        AT_ME_LIST.add(BAR_DISCUSS_PARISE);
        AT_ME_LIST.add(BAR_ESSENCE_TOP);
        AT_ME_LIST.add(BAR_EXPERT_INVITE);
        AT_ME_LIST.add(BAR_QUESTION_ACCUSE);
        AT_ME_LIST.add(BAR_QUESTION_AUDIT_PASS);
        AT_ME_LIST.add(BAR_QUESTION_AUDIT_REFUSE);
        AT_ME_LIST.add(COURSE_ACCUSE);
        AT_ME_LIST.add(COURSE_DISCUSS_AUDIT);
        AT_ME_LIST.add(COURSE_ESSENCE_TOP);
        AT_ME_LIST.add(COURSE_PRAISE);
        AT_ME_LIST.add(COURSE_REPLY);
        AT_ME_LIST.add(KNOWLEDGE_DISCUSS_ACCUSE);
        AT_ME_LIST.add(KNOWLEDGE_DISCUSS_AUDIT_PASS);
        AT_ME_LIST.add(KNOWLEDGE_DISCUSS_AUDIT_REFUSE);
        AT_ME_LIST.add(KNOWLEDGE_DISCUSS_PARISE);
        AT_ME_LIST.add(KNOWLEDGE_DISCUSS_REPLY);
        AT_ME_LIST.add(KNOWLEDGE_ESSENCE_TOP);
        AT_ME_LIST.add(MOOC_DISCUSS_ACCUSE);
        AT_ME_LIST.add(MOOC_DISCUSS_AUDIT);
        AT_ME_LIST.add(MOOC_DISCUSS_PRAISE);
        AT_ME_LIST.add(MOOC_ESSENCE_TOP);
        AT_ME_LIST.add(SUBJECT_DISCUSS_ACCUSE);
        AT_ME_LIST.add(SUBJECT_DISCUSS_AUDIT);
        AT_ME_LIST.add(SUBJECT_DISCUSS_PRAISE);
        AT_ME_LIST.add(SUBJECT_ESSENCE_TOP);
        AT_ME_LIST.add(SUBJECT_REPLY);
    }
}
