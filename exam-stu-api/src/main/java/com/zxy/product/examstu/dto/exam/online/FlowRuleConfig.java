package com.zxy.product.examstu.dto.exam.online;

import java.io.Serializable;

/**
 * 系统模块配置的限流数据POJO
 * <AUTHOR>
 * @date 2025年02月17日 10:43
 */
public class FlowRuleConfig implements Serializable {
    private static final long serialVersionUID = 4827146028472563148L;

    /**应用名*/
    private String applicationName;

    /**模块级别开关 0关闭 1开启*/
    private Integer mainSwitch;

    /**限流资源|业务类型*/
    private Integer businessType;

    /**限流规则POJO*/
    private Object flowRule;

    public String getApplicationName() { return applicationName; }

    public void setApplicationName(String applicationName) { this.applicationName = applicationName; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public Object getFlowRule() { return flowRule; }

    public void setFlowRule(Object flowRule) { this.flowRule = flowRule; }

    @Override
    public String toString() {
        return "FlowRuleConfig{" +
                "applicationName='" + applicationName + '\'' +
                ", mainSwitch=" + mainSwitch +
                ", businessType=" + businessType +
                ", flowRule=" + flowRule +
                '}';
    }


}
