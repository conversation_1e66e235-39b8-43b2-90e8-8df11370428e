package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.*;
import org.jooq.impl.TableImpl;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout=90000)
public interface ExamStuRecordService {

    /**
     * @param examRegion
     * @param examId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ExamRecord getNewestRecord(Integer examRegion, String examId, String memberId);

    @Transactional
    ExamRecord insert(Integer examRegion,String examId,
                      String paperInstanceId,String memberId,
                      Integer status,Optional<Long> startTime,
                      Optional<Long> endTime, Optional<Integer> paperSortRule,
                      Optional<Integer> examedTimes,Optional<Integer> personalCode);

    @Transactional
    Integer calculateExamTimes(Integer examRegion,String id, String memberId);

    @Transactional
    String update(Integer examRegion,String examRecordId,
            Optional<Long> startTime,Optional<Long> endTime,
            Optional<Integer> status, Optional<Long> submitTime,
            Integer isReset, Optional<String> paperInstanceId,
            Integer exceptionOrder,Optional<Long> lastSubmitTime,
            Optional<String> orderContent,Optional<Integer> personalCode,
            String examId
    );

    @Transactional
    ExamRecord submitPaper(Integer examRegion,String memberId,
            String examRecordId, Integer clientType,
            ExamRecord.SubmitType submitType, Long submitTime, String userId,
            Integer noAnswerCount, Integer answeredCount,
            Integer submitDetailType, String clientVersion,
            String examId);


    @Transactional
    void doForceSubmitOverTimePaper(Integer examRegion);

    @Transactional
    void forceSubmitPaperById(Integer examRegion, String examId, String memberId, String id);

    @Transactional
    ExamRecord normalForceSubmitPaper(Integer examRegion,String examId, String userId);

    @Transactional
    ExamRecord getExamRecord(Integer examRegion,String examRecordId, String examId);

    @Transactional
    Integer updateExamRecord(Integer examRegion,String examId, String examRecordId, Long lastSubmitTime, Integer noAnswerCount, Integer answeredCount, Integer clientType, String clientVersion, String ipAddr);

    @Transactional
    Integer updateExamRecordInConfirm(Integer examRegion,String id, Integer status, Integer isFinished, Integer score, String examId,
                              Integer answeredCount, Integer noAnswerCount, Integer rightCount);
    @Transactional
    Integer updateExamRecordToBeOver(Integer examRegion,ExamRecord examRecord);

    @Transactional
    Integer updateExamRecord(Integer examRegion, String id,
                             Integer status, Integer isFinished,
                             Integer score, Integer rightCount,
                             String examId);

    @Transactional
    Integer batchInsert(Integer examRegion, String examId,List<ExamRecord> inserts);
}
