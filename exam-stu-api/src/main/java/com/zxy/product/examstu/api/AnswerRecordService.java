package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.AnswerRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@RemoteService
public interface AnswerRecordService {

    /**
     * 根据考试记录查找答题记录
     * @param id
     * @param examId
     * @return
     */
    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    List<AnswerRecord> findByExamRecordId(Integer examRegion, String id, String examId);


    @Transactional
    AnswerRecord updateScore(Integer examRegion, AnswerRecord answerRecord);

    /**
     * 答题记录 包含试题信息
     *
     * @param examRegion
     * @param examRecordId
     * @param examId
     * @return
     */
    @Transactional
    List<AnswerRecord> findIncludeQuestionByExamRecordId(Integer examRegion,String examRecordId, String examId);

    @Transactional
    void batchUpdate(Integer examRegion, String examId,List<AnswerRecord> answerRecords);

}
