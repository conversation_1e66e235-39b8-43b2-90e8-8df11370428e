package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.exam.entity.AudienceItem;
import com.zxy.product.exam.entity.AudienceObject;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@RemoteService(timeout=700000)
public interface AudienceObjectService {

	@Transactional
	List<AudienceObject> batchInsert(int type, String targetId, List<AudienceItem> audienceItems, boolean isPublish);

	@Transactional
	boolean isAudient(Integer examRegion, String targetId, String memberId);
}
