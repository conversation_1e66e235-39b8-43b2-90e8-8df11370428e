package com.zxy.product.examstu.content;

/**
 * 限流常量类
 * <AUTHOR>
 * @date 2024年12月12日 17:41
 */
public class ExamLimitConstants {
    /**用户白名单*/
    public static String HumanWhiteMemberIds = "human-white-member-ids";

    /**数据过期标记*/
    public static Long ExpiredTag =0L;

    /**限流资源是否过期 0未过期  1已过期*/
    public static Integer Normal=0;
    public static Integer Overdue=1;

    /**限流规则开关*/
    public static Integer FlowLimitDisable=0;
    public static Integer FlowLimitEnable=1;

    /**限流回显Map数据Key And Value*/
    public static String EchoResultKey="result";
    public static String EchoResultSuccess="true";
    public static String EchoResultFalse="false";

    /**redisKey相关存储*/
    public static String CacheKeyApplication = "exam-stu";
    public static String CacheKeyModule = "limit-num";
    public static String CacheKeyOfSystem="system";
    public static String CacheKeyRuleConfigSuffix="rule-config-key-FLOW_LIMIT_CONFIG";
    public static String CacheKeyOnline="flow-limit-max-online";
    public static Integer CacheKeyExpireOnline=60*60*12;

    /**默认令牌||令牌续期时间*/
    public static Long DefaultTokenTime=10*60*1000L;
}
