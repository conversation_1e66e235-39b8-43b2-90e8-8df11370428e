package com.zxy.product.examstu.content;

import java.util.HashMap;
import java.util.Map;

public enum DataSourceEnum {
    NORTH(1,"north"),SOUTH(2,"south");
    private String name;
    private Integer type;

    DataSourceEnum(String name) {
        this.name = name;
    }

    public static Map<Integer, String> dataSourceMap = new HashMap<>(18);

    DataSourceEnum(Integer type, String name){
        this.name = name;
        this.type = type;
    }

    static {
        for (DataSourceEnum type : values()) {
            dataSourceMap.put(type.getType(), type.getName());
        }
    }

    public static String getName(Integer type){
        return dataSourceMap.get(type);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
