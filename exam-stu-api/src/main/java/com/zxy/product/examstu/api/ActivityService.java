package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.Activity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@RemoteService(timeout=500000)
public interface ActivityService {

    @Transactional
    Activity insert(
    	String coverId,
    	String organizationId,
    	String name,
    	Long startTime,
    	Long endTime,
    	Integer duration,
        Integer passScore,
        Integer type,
        String description,
        String targetId
    );

    @Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
    Activity getByTargetId(String targetId,int type);

}
