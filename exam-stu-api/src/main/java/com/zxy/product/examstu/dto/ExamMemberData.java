package com.zxy.product.examstu.dto;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExamMemberData {

  /**
   * 考生编号
   */
  private String examineeId;
  /**
   * 考试编号
   */
  private String examintionId;
  /**
   * 考生数据
   */
  private List<ExamineeData> examineeData;

  /**
   * 考试信息数据
   */
  private List<ExaminationData> examinationData;
  /**
   * 扩展字段
   */
  private String inputExt;

  public String getExamineeId() {
    return examineeId;
  }

  public void setExamineeId(String examineeId) {
    this.examineeId = examineeId;
  }

  public String getExamintionId() {
    return examintionId;
  }

  public void setExamintionId(String examintionId) {
    this.examintionId = examintionId;
  }

  public List<ExamineeData> getExamineeData() {
    return examineeData;
  }

  public void setExamineeData(List<ExamineeData> examineeData) {
    this.examineeData = examineeData;
  }

  public List<ExaminationData> getExaminationData() {
    return examinationData;
  }

  public void setExaminationData(List<ExaminationData> examinationData) {
    this.examinationData = examinationData;
  }

  public String getInputExt() {
    return inputExt;
  }

  public void setInputExt(String inputExt) {
    this.inputExt = inputExt;
  }

  @Override
  public String toString() {
    return "ExamMemberData{" +
        "examineeId='" + examineeId + '\'' +
        ", examintionId='" + examintionId + '\'' +
        ", examineeData=" + examineeData +
        ", examinationData=" + examinationData +
        ", inputExt='" + inputExt + '\'' +
        '}';
  }
}
