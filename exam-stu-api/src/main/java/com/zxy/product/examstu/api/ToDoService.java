package com.zxy.product.examstu.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.ToDo;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService
public interface ToDoService {

	@Transactional
	List<ToDo> insert(Integer examRegion,String targetId, List<String> memberIds, int type, Map<String, Integer> memberMap);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<ToDo> find(Integer examRegion, String memberId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<ToDo> findMarkPapers(String currentUserId);

	@Transactional
	String updateSubmitTime(String examRecordId, String memberId);

	@Transactional
	String updateAuditTime(Integer examRegion,String examRecordId, String memberId);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	PagedResult<ToDo> findPageForMarkPapers(
		Integer page,
		Integer pageSize,
		String currentUserId,
		Optional<String> name,
		Optional<Integer> wait
	);

	@Transactional(readOnly=true,propagation = Propagation.SUPPORTS)
	List<ToDo> findNoFinishedToDoByMemberIds(List<String> receiverIds);

	@Transactional
	String deleteByTargetId(Integer examRegion, String string, String currentUserId, String examId);

	@Transactional
	void deleteToDoByUpdateMarkConfig(Integer examRegion, String examId, List<String> memberIds);


	@Transactional
	List<ToDo> findNoFinishedToDoByMemberIds(Integer examRegion, List<String> receiverIds);

	@Transactional
	void updateToDo(Integer examRegion,List<ToDo> toDos);

	@Transactional
	void deleteToDo(Integer examRegion,List<ToDo> toDos);

	@Transactional
	void update(Integer examRegion,List<ToDo> toDos);

}
