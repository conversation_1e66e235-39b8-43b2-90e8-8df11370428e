package com.zxy.product.examstu.dto.exam.online;

import java.io.Serializable;

/**
 * 限流规则：仅最大在线相关POJO
 * <AUTHOR>
 * @date 2024年12月25日 14:10
 */
public class OnlineFlowRule  implements Serializable {
    private static final long serialVersionUID = -1771429834402278786L;

    /**最大在线人数*/
    private Integer maxOnline;

    /**最大在线人数开关*/
    private Boolean maxOnlineSwitchType;

    public Integer getMaxOnline() { return maxOnline; }

    public void setMaxOnline(Integer maxOnline) { this.maxOnline = maxOnline; }

    public Boolean getMaxOnlineSwitchType() { return maxOnlineSwitchType; }

    public void setMaxOnlineSwitchType(Boolean maxOnlineSwitchType) { this.maxOnlineSwitchType = maxOnlineSwitchType; }

    @Override
    public String toString() {
        return "OnlineFlowRule{" +
                "maxOnline=" + maxOnline +
                ", maxOnlineSwitchType=" + maxOnlineSwitchType +
                '}';
    }

}
