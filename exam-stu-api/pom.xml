<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zxy.product</groupId>
        <artifactId>exam-stu</artifactId>
        <version>cmu-9.6.0</version>
    </parent>
    <artifactId>exam-stu-api</artifactId>
    
    <distributionManagement>
        <snapshotRepository>
            <id>m2.zhixueyun.com</id>
            <name>zhixueyun</name>
            <url>https://m2.zhixueyun.com/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>m2.zhixueyun.com</id>
            <name>zhixueyun</name>
            <url>https://m2.zhixueyun.com/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
    <dependencies>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>api-parent</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-api</artifactId>
            <version>${version}</version>
        </dependency>
    </dependencies>
</project>