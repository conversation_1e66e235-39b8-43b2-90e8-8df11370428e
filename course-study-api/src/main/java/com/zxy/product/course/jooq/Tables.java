/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq;


import com.zxy.product.course.jooq.tables.*;

import javax.annotation.Generated;


/**
 * Convenience access to all tables in course-study
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>course-study.t_activity_banner</code>.
     */
    public static final ActivityBanner ACTIVITY_BANNER = ActivityBanner.ACTIVITY_BANNER;

    /**
     * 网大CHBN知识赋能行动
     */
    public static final ActivityChbn ACTIVITY_CHBN = ActivityChbn.ACTIVITY_CHBN;

    /**
     * The table <code>course-study.t_annual_bill_2019</code>.
     */
    public static final AnnualBill_2019 ANNUAL_BILL_2019 = AnnualBill_2019.ANNUAL_BILL_2019;

    /**
     * 2021年度账单
     */
    public static final AnnualBill_2021 ANNUAL_BILL_2021 = AnnualBill_2021.ANNUAL_BILL_2021;

    /**
     * The table <code>course-study.t_annual_bill_askbarcomment</code>.
     */
    public static final AnnualBillAskbarcomment ANNUAL_BILL_ASKBARCOMMENT = AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT;

    /**
     * The table <code>course-study.t_annual_bill_coursecomment</code>.
     */
    public static final AnnualBillCoursecomment ANNUAL_BILL_COURSECOMMENT = AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT;

    /**
     * The table <code>course-study.t_annual_bill_exam</code>.
     */
    public static final AnnualBillExam ANNUAL_BILL_EXAM = AnnualBillExam.ANNUAL_BILL_EXAM;

    /**
     * The table <code>course-study.t_annual_bill_studyday</code>.
     */
    public static final AnnualBillStudyday ANNUAL_BILL_STUDYDAY = AnnualBillStudyday.ANNUAL_BILL_STUDYDAY;

    /**
     * 受众项
     */
    public static final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public static final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 受众对象表
     */
    public static final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * The table <code>course-study.t_bill_config</code>.
     */
    public static final BillConfig BILL_CONFIG = BillConfig.BILL_CONFIG;

    /**
     * The table <code>course-study.t_bill_config_lost</code>.
     */
    public static final BillConfigLost BILL_CONFIG_LOST = BillConfigLost.BILL_CONFIG_LOST;


    /**
     * 证书与业务关联表
     */
    public static final BusinessCertificate BUSINESS_CERTIFICATE = BusinessCertificate.BUSINESS_CERTIFICATE;

    /**
     * 资源话题表
     */
    public static final BusinessTopic BUSINESS_TOPIC = BusinessTopic.BUSINESS_TOPIC;

    /**
     * 证书记录表
     */
    public static final CertificateRecord CERTIFICATE_RECORD = CertificateRecord.CERTIFICATE_RECORD;

    /**
     * chbn证书记录表
     */
    public static final CertificateRecordChbn CERTIFICATE_RECORD_CHBN = CertificateRecordChbn.CERTIFICATE_RECORD_CHBN;

    /**
     * 参赛作品附件表
     */
    public static final CompeteCourseAttachment COMPETE_COURSE_ATTACHMENT = CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT;

    /**
     * 参赛作品节资源(课件)
     */
    public static final CompeteCourseChapterSection COMPETE_COURSE_CHAPTER_SECTION = CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION;

    /**
     * 参赛作品表
     */
    public static final CompeteCourseInfo COMPETE_COURSE_INFO = CompeteCourseInfo.COMPETE_COURSE_INFO;

    /**
     * 参赛作品投票信息表
     */
    public static final CompeteCourseVote COMPETE_COURSE_VOTE = CompeteCourseVote.COMPETE_COURSE_VOTE;

    /**
     * 讲师单位表
     */
    public static final CompeteLecturerCompany COMPETE_LECTURER_COMPANY = CompeteLecturerCompany.COMPETE_LECTURER_COMPANY;

    /**
     * 课程附件
     */
    public static final CourseAttachment COURSE_ATTACHMENT = CourseAttachment.COURSE_ATTACHMENT;

    /**
     * 课程目录表
     */
    public static final CourseCategory COURSE_CATEGORY = CourseCategory.COURSE_CATEGORY;

    /**
     * 用户证书领取记录
     */
    public static final CourseCertificateRecord COURSE_CERTIFICATE_RECORD = CourseCertificateRecord.COURSE_CERTIFICATE_RECORD;

    /**
     * 课程章节（阶段）
     */
    public static final CourseChapter COURSE_CHAPTER = CourseChapter.COURSE_CHAPTER;

    /**
     * 课程章节-满意度问卷模板关联表
     */
    public static final CourseChapterQuestionnaire COURSE_CHAPTER_QUESTIONNAIRE = CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE;

    /**
     * 课程节资源(课件)
     */
    public static final CourseChapterSection COURSE_CHAPTER_SECTION = CourseChapterSection.COURSE_CHAPTER_SECTION;

    /**
     * 网大新动能通用课程表
     */
    public static final CourseCurrency COURSE_CURRENCY = CourseCurrency.COURSE_CURRENCY;

    /**
     * The table <code>course-study.t_course_exception</code>.
     */
    public static final CourseException COURSE_EXCEPTION = CourseException.COURSE_EXCEPTION;

    /**
     * 课程信息表
     */
    public static final CourseInfo COURSE_INFO = CourseInfo.COURSE_INFO;

    /**
     * 课程详情-目录中间表
     */
    public static final CourseInfoCategory COURSE_INFO_CATEGORY = com.zxy.product.course.jooq.tables.CourseInfoCategory.COURSE_INFO_CATEGORY;

    /**
     * 课程举报表
     */
    public static final CourseInform COURSE_INFORM = CourseInform.COURSE_INFORM;

    /**
     * 课程笔记表
     */
    public static final CourseNote COURSE_NOTE = CourseNote.COURSE_NOTE;

    /**
     * 专题相册
     */
    public static final CoursePhoto COURSE_PHOTO = CoursePhoto.COURSE_PHOTO;

    /**
     * 课程满意度问卷记录表
     */
    public static final CourseQuestionnaireRecord COURSE_QUESTIONNAIRE_RECORD = CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD;

    /**
     * 推荐课程表
     */
    public static final CourseRecommend COURSE_RECOMMEND = CourseRecommend.COURSE_RECOMMEND;

    /**
     * The table <code>course-study.t_course_record</code>.
     */
    public static final CourseRecord COURSE_RECORD = CourseRecord.COURSE_RECORD;

    /**
     * 课程注册表
     */
    public static final CourseRegister COURSE_REGISTER = CourseRegister.COURSE_REGISTER;

    /**
     * 课程评分表
     */
    public static final CourseScore COURSE_SCORE = CourseScore.COURSE_SCORE;

    /**
     * 用户提交任务附件
     */
    public static final CourseSectionProgressAttachment COURSE_SECTION_PROGRESS_ATTACHMENT = CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT;

    /**
     * 标准课件的每一节
     */
    public static final CourseSectionScorm COURSE_SECTION_SCORM = CourseSectionScorm.COURSE_SECTION_SCORM;

    /**
     * The table <code>course-study.t_course_section_scorm_progress</code>.
     */
    public static final CourseSectionScormProgress COURSE_SECTION_SCORM_PROGRESS = CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS;

    /**
     * 课程节学习日志(流水)，记录单次学习动作
     */
    public static final CourseSectionStudyLog COURSE_SECTION_STUDY_LOG = CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG;

    /**
     * The table <code>course-study.t_course_section_study_log_ah</code>.
     */
    public static final CourseSectionStudyLogAh COURSE_SECTION_STUDY_LOG_AH = CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH;

    /**
     * The table <code>course-study.t_course_section_study_log_ah_day</code>.
     */
    public static final CourseSectionStudyLogAhDay COURSE_SECTION_STUDY_LOG_AH_DAY = CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_bj</code>.
     */
    public static final CourseSectionStudyLogBj COURSE_SECTION_STUDY_LOG_BJ = CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ;

    /**
     * The table <code>course-study.t_course_section_study_log_bj_day</code>.
     */
    public static final CourseSectionStudyLogBjDay COURSE_SECTION_STUDY_LOG_BJ_DAY = CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_cm</code>.
     */
    public static final CourseSectionStudyLogCm COURSE_SECTION_STUDY_LOG_CM = CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM;

    /**
     * The table <code>course-study.t_course_section_study_log_cm_day</code>.
     */
    public static final CourseSectionStudyLogCmDay COURSE_SECTION_STUDY_LOG_CM_DAY = CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_cq</code>.
     */
    public static final CourseSectionStudyLogCq COURSE_SECTION_STUDY_LOG_CQ = CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ;

    /**
     * The table <code>course-study.t_course_section_study_log_cq_day</code>.
     */
    public static final CourseSectionStudyLogCqDay COURSE_SECTION_STUDY_LOG_CQ_DAY = CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_eb</code>.
     */
    public static final CourseSectionStudyLogEb COURSE_SECTION_STUDY_LOG_EB = CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB;

    /**
     * The table <code>course-study.t_course_section_study_log_eb_day</code>.
     */
    public static final CourseSectionStudyLogEbDay COURSE_SECTION_STUDY_LOG_EB_DAY = CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_fj</code>.
     */
    public static final CourseSectionStudyLogFj COURSE_SECTION_STUDY_LOG_FJ = CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ;

    /**
     * The table <code>course-study.t_course_section_study_log_fj_day</code>.
     */
    public static final CourseSectionStudyLogFjDay COURSE_SECTION_STUDY_LOG_FJ_DAY = CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gd</code>.
     */
    public static final CourseSectionStudyLogGd COURSE_SECTION_STUDY_LOG_GD = CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD;

    /**
     * The table <code>course-study.t_course_section_study_log_gd_day</code>.
     */
    public static final CourseSectionStudyLogGdDay COURSE_SECTION_STUDY_LOG_GD_DAY = CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gs</code>.
     */
    public static final CourseSectionStudyLogGs COURSE_SECTION_STUDY_LOG_GS = CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS;

    /**
     * The table <code>course-study.t_course_section_study_log_gs_day</code>.
     */
    public static final CourseSectionStudyLogGsDay COURSE_SECTION_STUDY_LOG_GS_DAY = CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gx</code>.
     */
    public static final CourseSectionStudyLogGx COURSE_SECTION_STUDY_LOG_GX = CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX;

    /**
     * The table <code>course-study.t_course_section_study_log_gx_day</code>.
     */
    public static final CourseSectionStudyLogGxDay COURSE_SECTION_STUDY_LOG_GX_DAY = CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gz</code>.
     */
    public static final CourseSectionStudyLogGz COURSE_SECTION_STUDY_LOG_GZ = CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ;

    /**
     * The table <code>course-study.t_course_section_study_log_gz_day</code>.
     */
    public static final CourseSectionStudyLogGzDay COURSE_SECTION_STUDY_LOG_GZ_DAY = CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hb</code>.
     */
    public static final CourseSectionStudyLogHb COURSE_SECTION_STUDY_LOG_HB = CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB;

    /**
     * The table <code>course-study.t_course_section_study_log_hb_day</code>.
     */
    public static final CourseSectionStudyLogHbDay COURSE_SECTION_STUDY_LOG_HB_DAY = CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hl</code>.
     */
    public static final CourseSectionStudyLogHl COURSE_SECTION_STUDY_LOG_HL = CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL;

    /**
     * The table <code>course-study.t_course_section_study_log_hl_day</code>.
     */
    public static final CourseSectionStudyLogHlDay COURSE_SECTION_STUDY_LOG_HL_DAY = CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hn</code>.
     */
    public static final CourseSectionStudyLogHn COURSE_SECTION_STUDY_LOG_HN = CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN;

    /**
     * The table <code>course-study.t_course_section_study_log_hn_day</code>.
     */
    public static final CourseSectionStudyLogHnDay COURSE_SECTION_STUDY_LOG_HN_DAY = CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_jl</code>.
     */
    public static final CourseSectionStudyLogJl COURSE_SECTION_STUDY_LOG_JL = CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL;

    /**
     * The table <code>course-study.t_course_section_study_log_jl_day</code>.
     */
    public static final CourseSectionStudyLogJlDay COURSE_SECTION_STUDY_LOG_JL_DAY = CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_js</code>.
     */
    public static final CourseSectionStudyLogJs COURSE_SECTION_STUDY_LOG_JS = CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS;

    /**
     * The table <code>course-study.t_course_section_study_log_js_day</code>.
     */
    public static final CourseSectionStudyLogJsDay COURSE_SECTION_STUDY_LOG_JS_DAY = CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_jx</code>.
     */
    public static final CourseSectionStudyLogJx COURSE_SECTION_STUDY_LOG_JX = CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX;

    /**
     * The table <code>course-study.t_course_section_study_log_jx_day</code>.
     */
    public static final CourseSectionStudyLogJxDay COURSE_SECTION_STUDY_LOG_JX_DAY = CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_ln</code>.
     */
    public static final CourseSectionStudyLogLn COURSE_SECTION_STUDY_LOG_LN = CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN;

    /**
     * The table <code>course-study.t_course_section_study_log_ln_day</code>.
     */
    public static final CourseSectionStudyLogLnDay COURSE_SECTION_STUDY_LOG_LN_DAY = CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_nm</code>.
     */
    public static final CourseSectionStudyLogNm COURSE_SECTION_STUDY_LOG_NM = CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM;

    /**
     * The table <code>course-study.t_course_section_study_log_nm_day</code>.
     */
    public static final CourseSectionStudyLogNmDay COURSE_SECTION_STUDY_LOG_NM_DAY = CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_nx</code>.
     */
    public static final CourseSectionStudyLogNx COURSE_SECTION_STUDY_LOG_NX = CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX;

    /**
     * The table <code>course-study.t_course_section_study_log_nx_day</code>.
     */
    public static final CourseSectionStudyLogNxDay COURSE_SECTION_STUDY_LOG_NX_DAY = CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_other</code>.
     */
    public static final CourseSectionStudyLogOther COURSE_SECTION_STUDY_LOG_OTHER = CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER;

    /**
     * The table <code>course-study.t_course_section_study_log_other_day</code>.
     */
    public static final CourseSectionStudyLogOtherDay COURSE_SECTION_STUDY_LOG_OTHER_DAY = CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_qh</code>.
     */
    public static final CourseSectionStudyLogQh COURSE_SECTION_STUDY_LOG_QH = CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH;

    /**
     * The table <code>course-study.t_course_section_study_log_qh_day</code>.
     */
    public static final CourseSectionStudyLogQhDay COURSE_SECTION_STUDY_LOG_QH_DAY = CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_qo</code>.
     */
    public static final CourseSectionStudyLogQo COURSE_SECTION_STUDY_LOG_QO = CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO;

    /**
     * The table <code>course-study.t_course_section_study_log_qo_day</code>.
     */
    public static final CourseSectionStudyLogQoDay COURSE_SECTION_STUDY_LOG_QO_DAY = CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sc</code>.
     */
    public static final CourseSectionStudyLogSc COURSE_SECTION_STUDY_LOG_SC = CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC;

    /**
     * The table <code>course-study.t_course_section_study_log_sc_day</code>.
     */
    public static final CourseSectionStudyLogScDay COURSE_SECTION_STUDY_LOG_SC_DAY = CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sd</code>.
     */
    public static final CourseSectionStudyLogSd COURSE_SECTION_STUDY_LOG_SD = CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD;

    /**
     * The table <code>course-study.t_course_section_study_log_sd_day</code>.
     */
    public static final CourseSectionStudyLogSdDay COURSE_SECTION_STUDY_LOG_SD_DAY = CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sh</code>.
     */
    public static final CourseSectionStudyLogSh COURSE_SECTION_STUDY_LOG_SH = CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH;

    /**
     * The table <code>course-study.t_course_section_study_log_sh_day</code>.
     */
    public static final CourseSectionStudyLogShDay COURSE_SECTION_STUDY_LOG_SH_DAY = CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sn</code>.
     */
    public static final CourseSectionStudyLogSn COURSE_SECTION_STUDY_LOG_SN = CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN;

    /**
     * The table <code>course-study.t_course_section_study_log_sn_day</code>.
     */
    public static final CourseSectionStudyLogSnDay COURSE_SECTION_STUDY_LOG_SN_DAY = CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sx</code>.
     */
    public static final CourseSectionStudyLogSx COURSE_SECTION_STUDY_LOG_SX = CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX;

    /**
     * The table <code>course-study.t_course_section_study_log_sx_day</code>.
     */
    public static final CourseSectionStudyLogSxDay COURSE_SECTION_STUDY_LOG_SX_DAY = CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_tj</code>.
     */
    public static final CourseSectionStudyLogTj COURSE_SECTION_STUDY_LOG_TJ = CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ;

    /**
     * The table <code>course-study.t_course_section_study_log_tj_day</code>.
     */
    public static final CourseSectionStudyLogTjDay COURSE_SECTION_STUDY_LOG_TJ_DAY = CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xj</code>.
     */
    public static final CourseSectionStudyLogXj COURSE_SECTION_STUDY_LOG_XJ = CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ;

    /**
     * The table <code>course-study.t_course_section_study_log_xj_day</code>.
     */
    public static final CourseSectionStudyLogXjDay COURSE_SECTION_STUDY_LOG_XJ_DAY = CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xn</code>.
     */
    public static final CourseSectionStudyLogXn COURSE_SECTION_STUDY_LOG_XN = CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN;

    /**
     * The table <code>course-study.t_course_section_study_log_xn_day</code>.
     */
    public static final CourseSectionStudyLogXnDay COURSE_SECTION_STUDY_LOG_XN_DAY = CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xz</code>.
     */
    public static final CourseSectionStudyLogXz COURSE_SECTION_STUDY_LOG_XZ = CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ;

    /**
     * The table <code>course-study.t_course_section_study_log_xz_day</code>.
     */
    public static final CourseSectionStudyLogXzDay COURSE_SECTION_STUDY_LOG_XZ_DAY = CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_yn</code>.
     */
    public static final CourseSectionStudyLogYn COURSE_SECTION_STUDY_LOG_YN = CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN;

    /**
     * The table <code>course-study.t_course_section_study_log_yn_day</code>.
     */
    public static final CourseSectionStudyLogYnDay COURSE_SECTION_STUDY_LOG_YN_DAY = CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zgtt</code>.
     */
    public static final CourseSectionStudyLogZgtt COURSE_SECTION_STUDY_LOG_ZGTT = CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT;

    /**
     * The table <code>course-study.t_course_section_study_log_zgtt_day</code>.
     */
    public static final CourseSectionStudyLogZgttDay COURSE_SECTION_STUDY_LOG_ZGTT_DAY = CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zj</code>.
     */
    public static final CourseSectionStudyLogZj COURSE_SECTION_STUDY_LOG_ZJ = CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ;

    /**
     * The table <code>course-study.t_course_section_study_log_zj_day</code>.
     */
    public static final CourseSectionStudyLogZjDay COURSE_SECTION_STUDY_LOG_ZJ_DAY = CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zx</code>.
     */
    public static final CourseSectionStudyLogZx COURSE_SECTION_STUDY_LOG_ZX = CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX;

    /**
     * The table <code>course-study.t_course_section_study_log_zx_day</code>.
     */
    public static final CourseSectionStudyLogZxDay COURSE_SECTION_STUDY_LOG_ZX_DAY = CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY;

    /**
     * 课程节学习进度
     */
    public static final CourseSectionStudyProgress COURSE_SECTION_STUDY_PROGRESS = CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS;

    /**
     * CHBN活动课程章节进度表
     */
    public static final CourseSectionStudyProgressChbnc COURSE_SECTION_STUDY_PROGRESS_CHBNC = CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC;

    /**
     * CHBN活动专题章节进度表
     */
    public static final CourseSectionStudyProgressChbns COURSE_SECTION_STUDY_PROGRESS_CHBNS = CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS;

    /**
     * 课程节学习进度(2020反复倡廉课程)
     */
    public static final CourseSectionStudyProgressFfclc COURSE_SECTION_STUDY_PROGRESS_FFCLC = CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC;

    /**
     * 课程节学习进度(2020反复倡廉专题)
     */
    public static final CourseSectionStudyProgressFfcls COURSE_SECTION_STUDY_PROGRESS_FFCLS = CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS;

    /**
     * 课程节学习进度(2022反复倡廉专题)
     */
    public static final CourseSectionStudyProgressFfcls_2022 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022 = CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022;


    /**
     * 课程节学习进度(新动能重塑培训专题)
     */
    public static final CourseSectionStudyProgressRts COURSE_SECTION_STUDY_PROGRESS_RTS = CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS;

    /**
     * 课程节学习进度(新动能课程)
     */
    public static final CourseSectionStudyProgressXdnc COURSE_SECTION_STUDY_PROGRESS_XDNC = CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC;

    /**
     * 课程节学习进度(新动能专题)
     */
    public static final CourseSectionStudyProgressXdns COURSE_SECTION_STUDY_PROGRESS_XDNS = CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS;

    /**
     * 智慧中台活动专题章节进度表
     */
    public static final CourseSectionStudyProgressZhzts COURSE_SECTION_STUDY_PROGRESS_ZHZTS = CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS;

    /**
     * 课程序列记录，用于累计生成课程编码的序列号
     */
    public static final CourseSequence COURSE_SEQUENCE = CourseSequence.COURSE_SEQUENCE;

    /**
     * 课程上架通知表
     */
    public static final CourseShelves COURSE_SHELVES = CourseShelves.COURSE_SHELVES;

    /**
     * The table <code>course-study.t_course_study_process_2017</code>.
     */
    public static final CourseStudyProcess_2017 COURSE_STUDY_PROCESS_2017 = CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017;

    /**
     * 课程学习进度表
     */
    public static final CourseStudyProgress COURSE_STUDY_PROGRESS = CourseStudyProgress.COURSE_STUDY_PROGRESS;

    /**
     * The table <code>course-study.t_course_study_progress_ah</code>.
     */
    public static final CourseStudyProgressAh COURSE_STUDY_PROGRESS_AH = CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH;

    /**
     * The table <code>course-study.t_course_study_progress_bj</code>.
     */
    public static final CourseStudyProgressBj COURSE_STUDY_PROGRESS_BJ = CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ;

    /**
     * The table <code>course-study.t_course_study_progress_cm</code>.
     */
    public static final CourseStudyProgressCm COURSE_STUDY_PROGRESS_CM = CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM;

    /**
     * The table <code>course-study.t_course_study_progress_cq</code>.
     */
    public static final CourseStudyProgressCq COURSE_STUDY_PROGRESS_CQ = CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ;

    /**
     * The table <code>course-study.t_course_study_progress_eb</code>.
     */
    public static final CourseStudyProgressEb COURSE_STUDY_PROGRESS_EB = CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB;

    /**
     * The table <code>course-study.t_course_study_progress_fj</code>.
     */
    public static final CourseStudyProgressFj COURSE_STUDY_PROGRESS_FJ = CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ;

    /**
     * The table <code>course-study.t_course_study_progress_gd</code>.
     */
    public static final CourseStudyProgressGd COURSE_STUDY_PROGRESS_GD = CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD;

    /**
     * The table <code>course-study.t_course_study_progress_gs</code>.
     */
    public static final CourseStudyProgressGs COURSE_STUDY_PROGRESS_GS = CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS;

    /**
     * The table <code>course-study.t_course_study_progress_gx</code>.
     */
    public static final CourseStudyProgressGx COURSE_STUDY_PROGRESS_GX = CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX;

    /**
     * The table <code>course-study.t_course_study_progress_gz</code>.
     */
    public static final CourseStudyProgressGz COURSE_STUDY_PROGRESS_GZ = CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ;

    /**
     * The table <code>course-study.t_course_study_progress_hb</code>.
     */
    public static final CourseStudyProgressHb COURSE_STUDY_PROGRESS_HB = CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB;

    /**
     * The table <code>course-study.t_course_study_progress_hl</code>.
     */
    public static final CourseStudyProgressHl COURSE_STUDY_PROGRESS_HL = CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL;

    /**
     * The table <code>course-study.t_course_study_progress_hn</code>.
     */
    public static final CourseStudyProgressHn COURSE_STUDY_PROGRESS_HN = CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN;

    /**
     * The table <code>course-study.t_course_study_progress_jl</code>.
     */
    public static final CourseStudyProgressJl COURSE_STUDY_PROGRESS_JL = CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL;

    /**
     * The table <code>course-study.t_course_study_progress_js</code>.
     */
    public static final CourseStudyProgressJs COURSE_STUDY_PROGRESS_JS = CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS;

    /**
     * The table <code>course-study.t_course_study_progress_jx</code>.
     */
    public static final CourseStudyProgressJx COURSE_STUDY_PROGRESS_JX = CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX;

    /**
     * The table <code>course-study.t_course_study_progress_ln</code>.
     */
    public static final CourseStudyProgressLn COURSE_STUDY_PROGRESS_LN = CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN;

    /**
     * The table <code>course-study.t_course_study_progress_nm</code>.
     */
    public static final CourseStudyProgressNm COURSE_STUDY_PROGRESS_NM = CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM;

    /**
     * The table <code>course-study.t_course_study_progress_nx</code>.
     */
    public static final CourseStudyProgressNx COURSE_STUDY_PROGRESS_NX = CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX;

    /**
     * The table <code>course-study.t_course_study_progress_other</code>.
     */
    public static final CourseStudyProgressOther COURSE_STUDY_PROGRESS_OTHER = CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER;

    /**
     * The table <code>course-study.t_course_study_progress_qh</code>.
     */
    public static final CourseStudyProgressQh COURSE_STUDY_PROGRESS_QH = CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH;

    /**
     * The table <code>course-study.t_course_study_progress_qo</code>.
     */
    public static final CourseStudyProgressQo COURSE_STUDY_PROGRESS_QO = CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO;

    /**
     * The table <code>course-study.t_course_study_progress_sc</code>.
     */
    public static final CourseStudyProgressSc COURSE_STUDY_PROGRESS_SC = CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC;

    /**
     * The table <code>course-study.t_course_study_progress_sd</code>.
     */
    public static final CourseStudyProgressSd COURSE_STUDY_PROGRESS_SD = CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD;

    /**
     * The table <code>course-study.t_course_study_progress_sh</code>.
     */
    public static final CourseStudyProgressSh COURSE_STUDY_PROGRESS_SH = CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH;

    /**
     * The table <code>course-study.t_course_study_progress_sn</code>.
     */
    public static final CourseStudyProgressSn COURSE_STUDY_PROGRESS_SN = CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN;

    /**
     * The table <code>course-study.t_course_study_progress_sx</code>.
     */
    public static final CourseStudyProgressSx COURSE_STUDY_PROGRESS_SX = CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX;

    /**
     * The table <code>course-study.t_course_study_progress_tj</code>.
     */
    public static final CourseStudyProgressTj COURSE_STUDY_PROGRESS_TJ = CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ;

    /**
     * The table <code>course-study.t_course_study_progress_xj</code>.
     */
    public static final CourseStudyProgressXj COURSE_STUDY_PROGRESS_XJ = CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ;

    /**
     * The table <code>course-study.t_course_study_progress_xn</code>.
     */
    public static final CourseStudyProgressXn COURSE_STUDY_PROGRESS_XN = CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN;

    /**
     * The table <code>course-study.t_course_study_progress_xz</code>.
     */
    public static final CourseStudyProgressXz COURSE_STUDY_PROGRESS_XZ = CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ;

    /**
     * The table <code>course-study.t_course_study_progress_yn</code>.
     */
    public static final CourseStudyProgressYn COURSE_STUDY_PROGRESS_YN = CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN;

    /**
     * The table <code>course-study.t_course_study_progress_zgtt</code>.
     */
    public static final CourseStudyProgressZgtt COURSE_STUDY_PROGRESS_ZGTT = CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT;

    /**
     * The table <code>course-study.t_course_study_progress_zj</code>.
     */
    public static final CourseStudyProgressZj COURSE_STUDY_PROGRESS_ZJ = CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ;

    /**
     * The table <code>course-study.t_course_study_progress_zx</code>.
     */
    public static final CourseStudyProgressZx COURSE_STUDY_PROGRESS_ZX = CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX;

    /**
     * 业务关联话题
     */
    public static final CourseTopic COURSE_TOPIC = CourseTopic.COURSE_TOPIC;

    /**
     * The table <code>course-study.t_course_version</code>.
     */
    public static final CourseVersion COURSE_VERSION = CourseVersion.COURSE_VERSION;

    /**
     * The table <code>course-study.t_dba_analyze_table_index</code>.
     */
    public static final DbaAnalyzeTableIndex DBA_ANALYZE_TABLE_INDEX = DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX;

    /**
     * 党建分类
     */
    public static final DjClassify DJ_CLASSIFY = DjClassify.DJ_CLASSIFY;

    /**
     * 党建资源
     */
    public static final DjResource DJ_RESOURCE = DjResource.DJ_RESOURCE;

    /**
     * The table <code>course-study.t_exam</code>.
     */
    public static final Exam EXAM = Exam.EXAM;

    /**
     * The table <code>course-study.t_exam_record</code>.
     */
    public static final ExamRecord EXAM_RECORD = ExamRecord.EXAM_RECORD;

    /**
     * 直播关联业务表
     */
    public static final GenseeBusiness GENSEE_BUSINESS = GenseeBusiness.GENSEE_BUSINESS;

    /**
     * 直播业务参与进度
     */
    public static final GenseeBusinessProgress GENSEE_BUSINESS_PROGRESS = GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS;

    /**
     * 直播关联讲师
     */
    public static final GenseeLecturer GENSEE_LECTURER = GenseeLecturer.GENSEE_LECTURER;

    /**
     * 直播用户订阅表
     */
    public static final GenseeSubscription GENSEE_SUBSCRIPTION = GenseeSubscription.GENSEE_SUBSCRIPTION;

    /**
     * 直播关联话题
     */
    public static final GenseeTopic GENSEE_TOPIC = GenseeTopic.GENSEE_TOPIC;

    /**
     * 用户参加直播表
     */
    public static final GenseeUserAccess GENSEE_USER_ACCESS = GenseeUserAccess.GENSEE_USER_ACCESS;

    /**
     * 直播历史记录表
     */
    public static final GenseeUserJoinHistory GENSEE_USER_JOIN_HISTORY = GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY;

    /**
     * 直播表
     */
    public static final GenseeWebCast GENSEE_WEB_CAST = GenseeWebCast.GENSEE_WEB_CAST;

    /**
     * The table <code>course-study.t_grant_detail</code>.
     */
    public static final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

    /**
     * The table <code>course-study.t_job</code>.
     */
    public static final Job JOB = Job.JOB;

    /**
     * 知识目录表
     */
    public static final KnowledgeCategory KNOWLEDGE_CATEGORY = KnowledgeCategory.KNOWLEDGE_CATEGORY;

    /**
     * 知识下载记录，用于统计下载人数
     */
    public static final KnowledgeDownRecord KNOWLEDGE_DOWN_RECORD = KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD;

    /**
     * 知识库
     */
    public static final KnowledgeInfo KNOWLEDGE_INFO = KnowledgeInfo.KNOWLEDGE_INFO;

    /**
     * 知识月度排行榜
     */
    public static final KnowledgeMonthList KNOWLEDGE_MONTH_LIST = KnowledgeMonthList.KNOWLEDGE_MONTH_LIST;

    /**
     * 知识话题表
     */
    public static final KnowledgeTopic KNOWLEDGE_TOPIC = KnowledgeTopic.KNOWLEDGE_TOPIC;

    /**
     * 知识使用记录，用于统计使用人数
     */
    public static final KnowledgeUseRecord KNOWLEDGE_USE_RECORD = KnowledgeUseRecord.KNOWLEDGE_USE_RECORD;

    /**
     * 知识查看记录，用于统计浏览人数
     */
    public static final KnowledgeViewRecord KNOWLEDGE_VIEW_RECORD = KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD;

    /**
     * The table <code>course-study.t_member</code>.
     */
    public static final Member MEMBER = Member.MEMBER;

    /**
     * 用户月度学习汇总
     */
    public static final MemberCourseMonth MEMBER_COURSE_MONTH = MemberCourseMonth.MEMBER_COURSE_MONTH;

    /**
     * The table <code>course-study.t_member_detail</code>.
     */
    public static final MemberDetail MEMBER_DETAIL = MemberDetail.MEMBER_DETAIL;

    /**
     * 月度知识访问排行
     */
    public static final MemberKnowledgeMonth MEMBER_KNOWLEDGE_MONTH = MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH;

    /**
     * 党员信息（原样同步党建云数据）
     */
    public static final MemberParty MEMBER_PARTY = MemberParty.MEMBER_PARTY;

    /**
     * 学习资源汇总表
     */
    public static final MemberStatistics MEMBER_STATISTICS = MemberStatistics.MEMBER_STATISTICS;

    /**
     * 学习资源汇总表
     */
    public static final MemberStatisticsArchives MEMBER_STATISTICS_ARCHIVES = MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES;

    /**
     * course-study模块通知表
     */
    public static final Notice NOTICE = Notice.NOTICE;

    /**
     * 面授导入信息表
     */
    public static final OfflineClass OFFLINE_CLASS = OfflineClass.OFFLINE_CLASS;

    /**
     * 线下课程调查问卷表
     */
    public static final OfflineCourseQuestionnaire OFFLINE_COURSE_QUESTIONNAIRE = OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE;

    /**
     * 线下课程调查问卷章节表
     */
    public static final OfflineCourseQuestionnaireChapter OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER = OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER;

    /**
     * 线下课程满意度问答记录表
     */
    public static final OfflineQuestionnaireAnswer OFFLINE_QUESTIONNAIRE_ANSWER = OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER;

    /**
     * 在线课程满意度问答记录表
     */
    public static final OnlineQuestionnaireAnswer ONLINE_QUESTIONNAIRE_ANSWER = OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER;

    /**
     * The table <code>course-study.t_organization</code>.
     */
    public static final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>course-study.t_organization_detail</code>.
     */
    public static final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * The table <code>course-study.t_org_table_count</code>.
     */
    public static final OrgTableCount ORG_TABLE_COUNT = OrgTableCount.ORG_TABLE_COUNT;

    /**
     * 党校活跃度人天
     */
    public static final PartyActivityMemberDay PARTY_ACTIVITY_MEMBER_DAY = PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY;

    /**
     * 党校活跃度人月
     */
    public static final PartyActivityMemberMonth PARTY_ACTIVITY_MEMBER_MONTH = PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH;

    /**
     * 党校活跃度组织天
     */
    public static final PartyActivityOrgDay PARTY_ACTIVITY_ORG_DAY = PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY;

    /**
     * 党校活跃度组织月
     */
    public static final PartyActivityOrgMonth PARTY_ACTIVITY_ORG_MONTH = PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH;

    /**
     * 党校活跃度组织年
     */
    public static final PartyActivityOrgYear PARTY_ACTIVITY_ORG_YEAR = PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR;

    /**
     * 智慧云屏业务配置表
     */
    public static final PartyBusinessConfiguration PARTY_BUSINESS_CONFIGURATION = PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION;

    /**
     * 党校数据记录
     */
    public static final PartyData PARTY_DATA = PartyData.PARTY_DATA;

    /**
     * 党校热词配置
     */
    public static final PartyHotTopic PARTY_HOT_TOPIC = PartyHotTopic.PARTY_HOT_TOPIC;

    /**
     * 党校热词管理
     */
    public static final PartyHotTopicManage PARTY_HOT_TOPIC_MANAGE = PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE;

    /**
     * 智慧云屏可以查询全集团数据的人员配置表
     */
    public static final PartyLeader PARTY_LEADER = PartyLeader.PARTY_LEADER;

    /**
     * 党组织（原样同步党建云数据）
     */
    public static final PartyOrganization PARTY_ORGANIZATION = PartyOrganization.PARTY_ORGANIZATION;

    /**
     * 智慧云屏党组织与行政组织对应关系表
     */
    public static final PartyOrganizationRelationships PARTY_ORGANIZATION_RELATIONSHIPS = PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS;

    /**
     * 智慧云屏为您推荐
     */
    public static final PartyRecommendation PARTY_RECOMMENDATION = PartyRecommendation.PARTY_RECOMMENDATION;

    /**
     * 智慧云屏推荐结果表
     */
    public static final PartyRecommendResult PARTY_RECOMMEND_RESULT = PartyRecommendResult.PARTY_RECOMMEND_RESULT;

    /**
     * 党建云屏推荐接口失败应急课程
     */
    public static final PartyRecommendSpare PARTY_RECOMMEND_SPARE = PartyRecommendSpare.PARTY_RECOMMEND_SPARE;

    /**
     * 党校学习数据汇总（按天）
     */
    public static final PartyStudySummaryDay PARTY_STUDY_SUMMARY_DAY = PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY;

    /**
     * 党校学习数据汇总（按月）
     */
    public static final PartyStudySummaryMonth PARTY_STUDY_SUMMARY_MONTH = PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH;

    /**
     * 党校首页课程分类表
     */
    public static final PartyTopic PARTY_TOPIC = PartyTopic.PARTY_TOPIC;

    /**
     * PCCW progress数据同步需要同步的组织数据
     */
    public static final PccwOrganizationConfig PCCW_ORGANIZATION_CONFIG = PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG;

    /**
     * PCCW HR接口调用结果
     */
    public static final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;

    /**
     * PCCW progress数据同步失败的id记录
     */
    public static final PccwResultBusiness PCCW_RESULT_BUSINESS = PccwResultBusiness.PCCW_RESULT_BUSINESS;

    /**
     * PCCW HR接口调用结果
     */
    public static final PccwResultErrorHistory PCCW_RESULT_ERROR_HISTORY = PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY;

    /**
     * 个人年度学习账单
     */
    public static final PersonYearBill PERSON_YEAR_BILL = PersonYearBill.PERSON_YEAR_BILL;

    /**
     * The table <code>course-study.t_position</code>.
     */
    public static final Position POSITION = Position.POSITION;

    /**
     * The table <code>course-study.t_position_old</code>.
     */
    public static final PositionOld POSITION_OLD = PositionOld.POSITION_OLD;

    /**
     * 满意度问卷模板表
     */
    public static final QuestionnaireMould QUESTIONNAIRE_MOULD = QuestionnaireMould.QUESTIONNAIRE_MOULD;

    /**
     * 满意度问卷模板-问题关联表
     */
    public static final QuestionnaireMouldQuestion QUESTIONNAIRE_MOULD_QUESTION = QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION;

    /**
     * 满意度问卷问题字典表
     */
    public static final QuestionnaireQuestion QUESTIONNAIRE_QUESTION = QuestionnaireQuestion.QUESTIONNAIRE_QUESTION;

    /**
     * 重塑培训计划-报名流水表
     */
    public static final RemodelingEntryStudyLog REMODELING_ENTRY_STUDY_LOG = RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG;

    /**
     * 重塑培训计划-报名进度表
     */
    public static final RemodelingEntryStudyProgress REMODELING_ENTRY_STUDY_PROGRESS = RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS;

    /**
     * 重塑培训计划-外部在线课程与网大业务关联关系表
     */
    public static final RemodelingExternalCourseBusiness REMODELING_EXTERNAL_COURSE_BUSINESS = RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS;

    /**
     * 重塑培训计划-外部在线课程学习数据回传详情表
     */
    public static final RemodelingExternalCourseStudyDetail REMODELING_EXTERNAL_COURSE_STUDY_DETAIL = RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL;

    /**
     * 重塑培训计划-外部考试数据回传详情表
     */
    public static final RemodelingExternalExamDetail REMODELING_EXTERNAL_EXAM_DETAIL = RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL;

    /**
     * 重塑培训计划-标注学员是否在外部平台学习某章节，满意度问卷使用
     */
    public static final RemodelingExternalPassbackBusiness REMODELING_EXTERNAL_PASSBACK_BUSINESS = RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS;

    /**
     * 重塑培训计划-在线课程链接与专区关联关系表
     */
    public static final RemodelingInternalCourseBusiness REMODELING_INTERNAL_COURSE_BUSINESS = RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS;

    /**
     * 重塑培训计划-角色信息表
     */
    public static final RemodelingRoleDetail REMODELING_ROLE_DETAIL = RemodelingRoleDetail.REMODELING_ROLE_DETAIL;

    /**
     * 重塑专区证书固定颁发时间配置表
     */
    public static final RemodelingRoleIssueTimeConfig REMODELING_ROLE_ISSUE_TIME_CONFIG = com.zxy.product.course.jooq.tables.RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG;

    /**
     * 重复专题学习进度记录备份
     */
    public static final RepeatCourseSectionStudyProgress REPEAT_COURSE_SECTION_STUDY_PROGRESS = RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS;

    /**
     * 分表配置表
     */
    public static final ShardingConfig SHARDING_CONFIG = ShardingConfig.SHARDING_CONFIG;

    /**
     * 学习log表按部门分表配置表
     */
    public static final SplitLogConfig SPLIT_LOG_CONFIG = SplitLogConfig.SPLIT_LOG_CONFIG;

    /**
     * The table <code>course-study.t_split_log_time</code>.
     */
    public static final SplitLogTime SPLIT_LOG_TIME = SplitLogTime.SPLIT_LOG_TIME;

    /**
     * 按省分表配置表
     */
    public static final SplitTableConfig SPLIT_TABLE_CONFIG = SplitTableConfig.SPLIT_TABLE_CONFIG;

    /**
     * The table <code>course-study.t_split_table_count</code>.
     */
    public static final SplitTableCount SPLIT_TABLE_COUNT = SplitTableCount.SPLIT_TABLE_COUNT;

    /**
     * The table <code>course-study.t_split_table_time</code>.
     */
    public static final SplitTableTime SPLIT_TABLE_TIME = SplitTableTime.SPLIT_TABLE_TIME;

    /**
     * 学习活动配置表
     */
    public static final StudyActivityConfig STUDY_ACTIVITY_CONFIG = StudyActivityConfig.STUDY_ACTIVITY_CONFIG;

    /**
     * 学习体会表
     */
    public static final StudyExperience STUDY_EXPERIENCE = StudyExperience.STUDY_EXPERIENCE;

    /**
     * 学习推送受众对象表(连接推送表和受众项表)
     */
    public static final StudyPushAudienceObject STUDY_PUSH_AUDIENCE_OBJECT = StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT;

    /**
     * 学习推送基本信息表
     */
    public static final StudyPushInfo STUDY_PUSH_INFO = StudyPushInfo.STUDY_PUSH_INFO;

    /**
     * 学习推送对象表
     */
    public static final StudyPushObject STUDY_PUSH_OBJECT = StudyPushObject.STUDY_PUSH_OBJECT;

    /**
     * 推送记录表
     */
    public static final StudyPushRecord STUDY_PUSH_RECORD = StudyPushRecord.STUDY_PUSH_RECORD;

    /**
     * 推送上架通知表
     */
    public static final StudyPushShelves STUDY_PUSH_SHELVES = StudyPushShelves.STUDY_PUSH_SHELVES;

    /**
     * The table <code>course-study.t_study_record_2017</code>.
     */
    public static final StudyRecord_2017 STUDY_RECORD_2017 = StudyRecord_2017.STUDY_RECORD_2017;

    /**
     * 专题任务信息
     */
    public static final StudyTask STUDY_TASK = StudyTask.STUDY_TASK;

    /**
     * 任务附件
     */
    public static final StudyTaskAttachment STUDY_TASK_ATTACHMENT = StudyTaskAttachment.STUDY_TASK_ATTACHMENT;

    /**
     * 任务审核人列表
     */
    public static final StudyTaskAuditMember STUDY_TASK_AUDIT_MEMBER = StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER;

    /**
     * 专题banner广告
     */
    public static final SubjectAdvertising SUBJECT_ADVERTISING = SubjectAdvertising.SUBJECT_ADVERTISING;

    /**
     * The table <code>course-study.t_subject_course</code>.
     */
    public static final SubjectCourse SUBJECT_COURSE = SubjectCourse.SUBJECT_COURSE;

    /**
     * The table <code>course-study.t_subject_direction</code>.
     */
    public static final SubjectDirection SUBJECT_DIRECTION = SubjectDirection.SUBJECT_DIRECTION;

    /**
     * 重塑专题人员证书黑名单
     */
    public static final SubjectMemberBlacklist SUBJECT_MEMBER_BLACKLIST = SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST;

    /**
     * The table <code>course-study.t_subject_problem</code>.
     */
    public static final SubjectProblem SUBJECT_PROBLEM = SubjectProblem.SUBJECT_PROBLEM;

    /**
     * The table <code>course-study.t_subject_rank</code>.
     */
    public static final SubjectRank SUBJECT_RANK = SubjectRank.SUBJECT_RANK;

    /**
     * 专题推荐表
     */
    public static final SubjectRecommend SUBJECT_RECOMMEND = SubjectRecommend.SUBJECT_RECOMMEND;

    /**
     * 新动能专题角色默认评论表
     */
    public static final SubjectRoleComment SUBJECT_ROLE_COMMENT = SubjectRoleComment.SUBJECT_ROLE_COMMENT;

    /**
     * The table <code>course-study.t_subject_role_detail</code>.
     */
    public static final SubjectRoleDetail SUBJECT_ROLE_DETAIL = SubjectRoleDetail.SUBJECT_ROLE_DETAIL;

    /**
     * 专题资源学习日志(流水)，记录单次学习动作
     */
    public static final SubjectSectionStudyLog SUBJECT_SECTION_STUDY_LOG = SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG;

    /**
     * The table <code>course-study.t_subject_section_study_log_ah</code>.
     */
    public static final SubjectSectionStudyLogAh SUBJECT_SECTION_STUDY_LOG_AH = SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH;

    /**
     * The table <code>course-study.t_subject_section_study_log_bj</code>.
     */
    public static final SubjectSectionStudyLogBj SUBJECT_SECTION_STUDY_LOG_BJ = SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_cm</code>.
     */
    public static final SubjectSectionStudyLogCm SUBJECT_SECTION_STUDY_LOG_CM = SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM;

    /**
     * The table <code>course-study.t_subject_section_study_log_cq</code>.
     */
    public static final SubjectSectionStudyLogCq SUBJECT_SECTION_STUDY_LOG_CQ = SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ;

    /**
     * The table <code>course-study.t_subject_section_study_log_eb</code>.
     */
    public static final SubjectSectionStudyLogEb SUBJECT_SECTION_STUDY_LOG_EB = SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB;

    /**
     * The table <code>course-study.t_subject_section_study_log_fj</code>.
     */
    public static final SubjectSectionStudyLogFj SUBJECT_SECTION_STUDY_LOG_FJ = SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_gd</code>.
     */
    public static final SubjectSectionStudyLogGd SUBJECT_SECTION_STUDY_LOG_GD = SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD;

    /**
     * The table <code>course-study.t_subject_section_study_log_gs</code>.
     */
    public static final SubjectSectionStudyLogGs SUBJECT_SECTION_STUDY_LOG_GS = SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS;

    /**
     * The table <code>course-study.t_subject_section_study_log_gx</code>.
     */
    public static final SubjectSectionStudyLogGx SUBJECT_SECTION_STUDY_LOG_GX = SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX;

    /**
     * The table <code>course-study.t_subject_section_study_log_gz</code>.
     */
    public static final SubjectSectionStudyLogGz SUBJECT_SECTION_STUDY_LOG_GZ = SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ;

    /**
     * The table <code>course-study.t_subject_section_study_log_hb</code>.
     */
    public static final SubjectSectionStudyLogHb SUBJECT_SECTION_STUDY_LOG_HB = SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB;

    /**
     * The table <code>course-study.t_subject_section_study_log_hl</code>.
     */
    public static final SubjectSectionStudyLogHl SUBJECT_SECTION_STUDY_LOG_HL = SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL;

    /**
     * The table <code>course-study.t_subject_section_study_log_hn</code>.
     */
    public static final SubjectSectionStudyLogHn SUBJECT_SECTION_STUDY_LOG_HN = SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN;

    /**
     * The table <code>course-study.t_subject_section_study_log_jl</code>.
     */
    public static final SubjectSectionStudyLogJl SUBJECT_SECTION_STUDY_LOG_JL = SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL;

    /**
     * The table <code>course-study.t_subject_section_study_log_js</code>.
     */
    public static final SubjectSectionStudyLogJs SUBJECT_SECTION_STUDY_LOG_JS = SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS;

    /**
     * The table <code>course-study.t_subject_section_study_log_jx</code>.
     */
    public static final SubjectSectionStudyLogJx SUBJECT_SECTION_STUDY_LOG_JX = SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX;

    /**
     * The table <code>course-study.t_subject_section_study_log_ln</code>.
     */
    public static final SubjectSectionStudyLogLn SUBJECT_SECTION_STUDY_LOG_LN = SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN;

    /**
     * The table <code>course-study.t_subject_section_study_log_nm</code>.
     */
    public static final SubjectSectionStudyLogNm SUBJECT_SECTION_STUDY_LOG_NM = SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM;

    /**
     * The table <code>course-study.t_subject_section_study_log_nx</code>.
     */
    public static final SubjectSectionStudyLogNx SUBJECT_SECTION_STUDY_LOG_NX = SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX;

    /**
     * The table <code>course-study.t_subject_section_study_log_other</code>.
     */
    public static final SubjectSectionStudyLogOther SUBJECT_SECTION_STUDY_LOG_OTHER = SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER;

    /**
     * The table <code>course-study.t_subject_section_study_log_qh</code>.
     */
    public static final SubjectSectionStudyLogQh SUBJECT_SECTION_STUDY_LOG_QH = SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH;

    /**
     * The table <code>course-study.t_subject_section_study_log_qo</code>.
     */
    public static final SubjectSectionStudyLogQo SUBJECT_SECTION_STUDY_LOG_QO = SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO;

    /**
     * The table <code>course-study.t_subject_section_study_log_sc</code>.
     */
    public static final SubjectSectionStudyLogSc SUBJECT_SECTION_STUDY_LOG_SC = SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC;

    /**
     * The table <code>course-study.t_subject_section_study_log_sd</code>.
     */
    public static final SubjectSectionStudyLogSd SUBJECT_SECTION_STUDY_LOG_SD = SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD;

    /**
     * The table <code>course-study.t_subject_section_study_log_sh</code>.
     */
    public static final SubjectSectionStudyLogSh SUBJECT_SECTION_STUDY_LOG_SH = SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH;

    /**
     * The table <code>course-study.t_subject_section_study_log_sn</code>.
     */
    public static final SubjectSectionStudyLogSn SUBJECT_SECTION_STUDY_LOG_SN = SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN;

    /**
     * The table <code>course-study.t_subject_section_study_log_sx</code>.
     */
    public static final SubjectSectionStudyLogSx SUBJECT_SECTION_STUDY_LOG_SX = SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX;

    /**
     * The table <code>course-study.t_subject_section_study_log_tj</code>.
     */
    public static final SubjectSectionStudyLogTj SUBJECT_SECTION_STUDY_LOG_TJ = SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_xj</code>.
     */
    public static final SubjectSectionStudyLogXj SUBJECT_SECTION_STUDY_LOG_XJ = SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_xn</code>.
     */
    public static final SubjectSectionStudyLogXn SUBJECT_SECTION_STUDY_LOG_XN = SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN;

    /**
     * The table <code>course-study.t_subject_section_study_log_xz</code>.
     */
    public static final SubjectSectionStudyLogXz SUBJECT_SECTION_STUDY_LOG_XZ = SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ;

    /**
     * The table <code>course-study.t_subject_section_study_log_yn</code>.
     */
    public static final SubjectSectionStudyLogYn SUBJECT_SECTION_STUDY_LOG_YN = SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN;

    /**
     * The table <code>course-study.t_subject_section_study_log_zgtt</code>.
     */
    public static final SubjectSectionStudyLogZgtt SUBJECT_SECTION_STUDY_LOG_ZGTT = SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT;

    /**
     * The table <code>course-study.t_subject_section_study_log_zj</code>.
     */
    public static final SubjectSectionStudyLogZj SUBJECT_SECTION_STUDY_LOG_ZJ = SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_zx</code>.
     */
    public static final SubjectSectionStudyLogZx SUBJECT_SECTION_STUDY_LOG_ZX = SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX;

    /**
     * The table <code>course-study.t_subject_study_day_exception</code>.
     */
    public static final SubjectStudyDayException SUBJECT_STUDY_DAY_EXCEPTION = SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION;

    /**
     * The table <code>course-study.t_subject_study_log_ah_day</code>.
     */
    public static final SubjectStudyLogAhDay SUBJECT_STUDY_LOG_AH_DAY = SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_bj_day</code>.
     */
    public static final SubjectStudyLogBjDay SUBJECT_STUDY_LOG_BJ_DAY = SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_cm_day</code>.
     */
    public static final SubjectStudyLogCmDay SUBJECT_STUDY_LOG_CM_DAY = SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_cq_day</code>.
     */
    public static final SubjectStudyLogCqDay SUBJECT_STUDY_LOG_CQ_DAY = SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_eb_day</code>.
     */
    public static final SubjectStudyLogEbDay SUBJECT_STUDY_LOG_EB_DAY = SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_exception</code>.
     */
    public static final SubjectStudyLogException SUBJECT_STUDY_LOG_EXCEPTION = SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION;

    /**
     * The table <code>course-study.t_subject_study_log_fj_day</code>.
     */
    public static final SubjectStudyLogFjDay SUBJECT_STUDY_LOG_FJ_DAY = SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gd_day</code>.
     */
    public static final SubjectStudyLogGdDay SUBJECT_STUDY_LOG_GD_DAY = SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gs_day</code>.
     */
    public static final SubjectStudyLogGsDay SUBJECT_STUDY_LOG_GS_DAY = SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gx_day</code>.
     */
    public static final SubjectStudyLogGxDay SUBJECT_STUDY_LOG_GX_DAY = SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gz_day</code>.
     */
    public static final SubjectStudyLogGzDay SUBJECT_STUDY_LOG_GZ_DAY = SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hb_day</code>.
     */
    public static final SubjectStudyLogHbDay SUBJECT_STUDY_LOG_HB_DAY = SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hl_day</code>.
     */
    public static final SubjectStudyLogHlDay SUBJECT_STUDY_LOG_HL_DAY = SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hn_day</code>.
     */
    public static final SubjectStudyLogHnDay SUBJECT_STUDY_LOG_HN_DAY = SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_jl_day</code>.
     */
    public static final SubjectStudyLogJlDay SUBJECT_STUDY_LOG_JL_DAY = SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_js_day</code>.
     */
    public static final SubjectStudyLogJsDay SUBJECT_STUDY_LOG_JS_DAY = SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_jx_day</code>.
     */
    public static final SubjectStudyLogJxDay SUBJECT_STUDY_LOG_JX_DAY = SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_ln_day</code>.
     */
    public static final SubjectStudyLogLnDay SUBJECT_STUDY_LOG_LN_DAY = SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_nm_day</code>.
     */
    public static final SubjectStudyLogNmDay SUBJECT_STUDY_LOG_NM_DAY = SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_nx_day</code>.
     */
    public static final SubjectStudyLogNxDay SUBJECT_STUDY_LOG_NX_DAY = SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_other_day</code>.
     */
    public static final SubjectStudyLogOtherDay SUBJECT_STUDY_LOG_OTHER_DAY = SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_qh_day</code>.
     */
    public static final SubjectStudyLogQhDay SUBJECT_STUDY_LOG_QH_DAY = SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_qo_day</code>.
     */
    public static final SubjectStudyLogQoDay SUBJECT_STUDY_LOG_QO_DAY = SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sc_day</code>.
     */
    public static final SubjectStudyLogScDay SUBJECT_STUDY_LOG_SC_DAY = SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sd_day</code>.
     */
    public static final SubjectStudyLogSdDay SUBJECT_STUDY_LOG_SD_DAY = SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sh_day</code>.
     */
    public static final SubjectStudyLogShDay SUBJECT_STUDY_LOG_SH_DAY = SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sn_day</code>.
     */
    public static final SubjectStudyLogSnDay SUBJECT_STUDY_LOG_SN_DAY = SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sx_day</code>.
     */
    public static final SubjectStudyLogSxDay SUBJECT_STUDY_LOG_SX_DAY = SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_tj_day</code>.
     */
    public static final SubjectStudyLogTjDay SUBJECT_STUDY_LOG_TJ_DAY = SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xj_day</code>.
     */
    public static final SubjectStudyLogXjDay SUBJECT_STUDY_LOG_XJ_DAY = SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xn_day</code>.
     */
    public static final SubjectStudyLogXnDay SUBJECT_STUDY_LOG_XN_DAY = SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xz_day</code>.
     */
    public static final SubjectStudyLogXzDay SUBJECT_STUDY_LOG_XZ_DAY = SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_yn_day</code>.
     */
    public static final SubjectStudyLogYnDay SUBJECT_STUDY_LOG_YN_DAY = SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zgtt_day</code>.
     */
    public static final SubjectStudyLogZgttDay SUBJECT_STUDY_LOG_ZGTT_DAY = SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zj_day</code>.
     */
    public static final SubjectStudyLogZjDay SUBJECT_STUDY_LOG_ZJ_DAY = SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zx_day</code>.
     */
    public static final SubjectStudyLogZxDay SUBJECT_STUDY_LOG_ZX_DAY = SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY;

    /**
     * 专题文字区域
     */
    public static final SubjectTextArea SUBJECT_TEXT_AREA = SubjectTextArea.SUBJECT_TEXT_AREA;

    /**
     * 专题年度账单
     */
    public static final SubjectYearBill SUBJECT_YEAR_BILL = SubjectYearBill.SUBJECT_YEAR_BILL;

    /**
     * The table <code>course-study.t_summary</code>.
     */
    public static final Summary SUMMARY = Summary.SUMMARY;

    /**
     * 供应商表
     */
    public static final Supplier SUPPLIER = Supplier.SUPPLIER;

    /**
     * 临时用户记录
     */
    public static final TempMember TEMP_MEMBER = TempMember.TEMP_MEMBER;

    /**
     * 专题数据无法自动修复数据表
     */
    public static final TempRepairCourse TEMP_REPAIR_COURSE = TempRepairCourse.TEMP_REPAIR_COURSE;

    /**
     * The table <code>course-study.t_temp_repair_subject</code>.
     */
    public static final TempRepairSubject TEMP_REPAIR_SUBJECT = TempRepairSubject.TEMP_REPAIR_SUBJECT;

    /**
     * 超过24小时时长交叉章节log备份
     */
    public static final TempSectionStudyLogGt_24 TEMP_SECTION_STUDY_LOG_GT_24 = TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24;

    /**
     * 专题Log重复流水删除备份
     */
    public static final TempSubjectSectionStudyLog TEMP_SUBJECT_SECTION_STUDY_LOG = TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG;

    /**
     * 专题班
     */
    public static final Thematic THEMATIC = Thematic.THEMATIC;

    /**
     * 专题班附件
     */
    public static final ThematicAttachment THEMATIC_ATTACHMENT = ThematicAttachment.THEMATIC_ATTACHMENT;

    /**
     * 专题班主题章节
     */
    public static final ThematicChapter THEMATIC_CHAPTER = ThematicChapter.THEMATIC_CHAPTER;

    /**
     * 专题班课程
     */
    public static final ThematicChapterSection THEMATIC_CHAPTER_SECTION = ThematicChapterSection.THEMATIC_CHAPTER_SECTION;

    /**
     * 专题班用户
     */
    public static final ThematicMember THEMATIC_MEMBER = ThematicMember.THEMATIC_MEMBER;

    /**
     * 专题班公告
     */
    public static final ThematicNotice THEMATIC_NOTICE = ThematicNotice.THEMATIC_NOTICE;

    /**
     * 专题班班务
     */
    public static final ThematicWork THEMATIC_WORK = ThematicWork.THEMATIC_WORK;

    /**
     * 第三方接口调用记录表
     */
    public static final ThirdPartyCallRecord THIRD_PARTY_CALL_RECORD = ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD;

    /**
     * 第三方课程表
     */
    public static final ThirdPartyCourseInfo THIRD_PARTY_COURSE_INFO = ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO;

    /**
     * 第三方课程学习记录进度表
     */
    public static final ThirdPartyCourseStudyProgress THIRD_PARTY_COURSE_STUDY_PROGRESS = ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS;

    /**
     * The table <code>course-study.t_topic</code>.
     */
    public static final Topic TOPIC = Topic.TOPIC;

    /**
     * The table <code>course-study.t_topic_object</code>.
     */
    public static final TopicObject TOPIC_OBJECT = TopicObject.TOPIC_OBJECT;

    /**
     * 白名单登陆异常记录
     */
    public static final WhiteRecord WHITE_RECORD = WhiteRecord.WHITE_RECORD;
    /**
     * 课程节学习进度(2022反复倡廉课程)
     */
    public static final CourseSectionStudyProgressFfclc_2022 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022;

    /**
     * 课程清单表（党建云屏使用）
     */
    public static final CourseInfoDjyp COURSE_INFO_DJYP = com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP;

    public static final MultidimensionalScoring MULTIDIMENSIONAL_SCORING = com.zxy.product.course.jooq.tables.MultidimensionalScoring.MULTIDIMENSIONAL_SCORING;

    /**
     * 多维度评分关系表
     */
    public static final MultidimensionalScoringSubject MULTIDIMENSIONAL_SCORING_SUBJECT = com.zxy.product.course.jooq.tables.MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT;

    /**
     * 学员评分表0
     */
    public static final MultidimensionalStudentScoreSheet_00 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00;

    /**
     * 学员评分表1
     */
    public static final MultidimensionalStudentScoreSheet_01 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01;

    /**
     * 学员评分表2
     */
    public static final MultidimensionalStudentScoreSheet_02 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02;

    /**
     * 学员评分表3
     */
    public static final MultidimensionalStudentScoreSheet_03 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03;

    /**
     * 学员评分表4
     */
    public static final MultidimensionalStudentScoreSheet_04 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04;

    /**
     * 学员评分表5
     */
    public static final MultidimensionalStudentScoreSheet_05 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05;

    /**
     * 学员评分表6
     */
    public static final MultidimensionalStudentScoreSheet_06 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06;

    /**
     * 学员评分表7
     */
    public static final MultidimensionalStudentScoreSheet_07 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07;

    /**
     * 学员评分表8
     */
    public static final MultidimensionalStudentScoreSheet_08 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08;

    /**
     * 学员评分表9
     */
    public static final MultidimensionalStudentScoreSheet_09 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09;

    /**
     * 学习活动-任务关联表
     */
    public static final CourseStudyPlanConfig COURSE_STUDY_PLAN_CONFIG = com.zxy.product.course.jooq.tables.CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG;

    /**
     * 课程红船审核记录表
     */
    public static final CourseRedShipAuditDetail COURSE_RED_SHIP_AUDIT_DETAIL = com.zxy.product.course.jooq.tables.CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL;

    /**
     * 课程红船审核版本号记录表
     */
    public static final CourseRedShipAuditVersion COURSE_RED_SHIP_AUDIT_VERSION = com.zxy.product.course.jooq.tables.CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION;

    /**
     * 课程红船审核-章节记录表
     */
    public static final CourseRedShipAuditChapterSection COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION = com.zxy.product.course.jooq.tables.CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION;

    /** 智能播报
     */
    public static final IntelligentBroadcast INTELLIGENT_BROADCAST = com.zxy.product.course.jooq.tables.IntelligentBroadcast.INTELLIGENT_BROADCAST;
    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_00 COURSE_STUDY_PROGRESS_ARCHIVED_00 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_01 COURSE_STUDY_PROGRESS_ARCHIVED_01 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_02 COURSE_STUDY_PROGRESS_ARCHIVED_02 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_03 COURSE_STUDY_PROGRESS_ARCHIVED_03 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_04 COURSE_STUDY_PROGRESS_ARCHIVED_04 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_05 COURSE_STUDY_PROGRESS_ARCHIVED_05 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_06 COURSE_STUDY_PROGRESS_ARCHIVED_06 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_07 COURSE_STUDY_PROGRESS_ARCHIVED_07 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_08 COURSE_STUDY_PROGRESS_ARCHIVED_08 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_09 COURSE_STUDY_PROGRESS_ARCHIVED_09 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_10 COURSE_STUDY_PROGRESS_ARCHIVED_10 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_11 COURSE_STUDY_PROGRESS_ARCHIVED_11 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_12 COURSE_STUDY_PROGRESS_ARCHIVED_12 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_13 COURSE_STUDY_PROGRESS_ARCHIVED_13 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_14 COURSE_STUDY_PROGRESS_ARCHIVED_14 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_15 COURSE_STUDY_PROGRESS_ARCHIVED_15 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_16 COURSE_STUDY_PROGRESS_ARCHIVED_16 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_17 COURSE_STUDY_PROGRESS_ARCHIVED_17 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_18 COURSE_STUDY_PROGRESS_ARCHIVED_18 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18;

    /**
     * course_study_progress已归档数据表
     */
    public static final CourseStudyProgressArchived_19 COURSE_STUDY_PROGRESS_ARCHIVED_19 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19;

    /**
     * 删除记录表
     */
    public static final DeleteDataCourse DELETE_DATA_COURSE = com.zxy.product.course.jooq.tables.DeleteDataCourse.DELETE_DATA_COURSE;

    /**
     * 智能笔记表
     */
    public static final IntelligentNote INTELLIGENT_NOTE = com.zxy.product.course.jooq.tables.IntelligentNote.INTELLIGENT_NOTE;

    /**
     * 智能笔记标签表
     */
    public static final IntelligentNoteBookmark INTELLIGENT_NOTE_BOOKMARK = com.zxy.product.course.jooq.tables.IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK;

    /**
     * 专题管理员表
     */
    public static final SubjectTopicManager SUBJECT_TOPIC_MANAGER = com.zxy.product.course.jooq.tables.SubjectTopicManager.SUBJECT_TOPIC_MANAGER;

    /**
     * 字幕表
     */
    public static final Caption CAPTION = com.zxy.product.course.jooq.tables.Caption.CAPTION;


    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressAh COURSE_SECTION_STUDY_PROGRESS_AH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressBj COURSE_SECTION_STUDY_PROGRESS_BJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressCm COURSE_SECTION_STUDY_PROGRESS_CM = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressCq COURSE_SECTION_STUDY_PROGRESS_CQ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressEb COURSE_SECTION_STUDY_PROGRESS_EB = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressFj COURSE_SECTION_STUDY_PROGRESS_FJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressGd COURSE_SECTION_STUDY_PROGRESS_GD = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressGs COURSE_SECTION_STUDY_PROGRESS_GS = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressGx COURSE_SECTION_STUDY_PROGRESS_GX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressGz COURSE_SECTION_STUDY_PROGRESS_GZ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressHb COURSE_SECTION_STUDY_PROGRESS_HB = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressHl COURSE_SECTION_STUDY_PROGRESS_HL = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressHn COURSE_SECTION_STUDY_PROGRESS_HN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressJl COURSE_SECTION_STUDY_PROGRESS_JL = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressJs COURSE_SECTION_STUDY_PROGRESS_JS = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressJx COURSE_SECTION_STUDY_PROGRESS_JX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressLn COURSE_SECTION_STUDY_PROGRESS_LN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressNm COURSE_SECTION_STUDY_PROGRESS_NM = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressNx COURSE_SECTION_STUDY_PROGRESS_NX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressOther COURSE_SECTION_STUDY_PROGRESS_OTHER = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressQh COURSE_SECTION_STUDY_PROGRESS_QH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressQo COURSE_SECTION_STUDY_PROGRESS_QO = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressSc COURSE_SECTION_STUDY_PROGRESS_SC = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressSd COURSE_SECTION_STUDY_PROGRESS_SD = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressSh COURSE_SECTION_STUDY_PROGRESS_SH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressSn COURSE_SECTION_STUDY_PROGRESS_SN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressSx COURSE_SECTION_STUDY_PROGRESS_SX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressTj COURSE_SECTION_STUDY_PROGRESS_TJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressXj COURSE_SECTION_STUDY_PROGRESS_XJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressXn COURSE_SECTION_STUDY_PROGRESS_XN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressXz COURSE_SECTION_STUDY_PROGRESS_XZ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressYn COURSE_SECTION_STUDY_PROGRESS_YN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressZgtt COURSE_SECTION_STUDY_PROGRESS_ZGTT = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressZj COURSE_SECTION_STUDY_PROGRESS_ZJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ;

    /**
     * 课程节学习进度分表
     */
    public static final CourseSectionStudyProgressZx COURSE_SECTION_STUDY_PROGRESS_ZX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX;

    /**
     * 2022年度账单
     */
    public static final AnnualBill_2022 ANNUAL_BILL_2022 = com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022;
    /**
     * 课程节学习进度(2023反复倡廉课程)
     */
    public static final CourseSectionStudyProgressFfclc_2023 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023;

    /**
     * 课程节学习进度(2023反复倡廉专题)
     */
    public static final CourseSectionStudyProgressFfcls_2023 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023;


    /**
     * 业务应急表
     */
    public static final BusinessEmergency BUSINESS_EMERGENCY = com.zxy.product.course.jooq.tables.BusinessEmergency.BUSINESS_EMERGENCY;

    /**
     * 分组信息表
     */
    public static final AuthenticatedGroup AUTHENTICATED_GROUP = com.zxy.product.course.jooq.tables.AuthenticatedGroup.AUTHENTICATED_GROUP;

    /**
     * 认证专区表
     */
    public static final AuthenticatedZone AUTHENTICATED_ZONE = com.zxy.product.course.jooq.tables.AuthenticatedZone.AUTHENTICATED_ZONE;

    /**
     * 认证专区-分组关系表
     */
    public static final AuthenticatedZoneGroup AUTHENTICATED_ZONE_GROUP = com.zxy.product.course.jooq.tables.AuthenticatedZoneGroup.AUTHENTICATED_ZONE_GROUP;

    /**
     * 子认证表
     */
    public static final SubAuthenticated SUB_AUTHENTICATED = com.zxy.product.course.jooq.tables.SubAuthenticated.SUB_AUTHENTICATED;

    /**
     * 子认证-学员-证书记录表
     */
    public static final SubAuthenticatedCertificateRecord SUB_AUTHENTICATED_CERTIFICATE_RECORD = com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD;

    /**
     * 子认证内容配置表
     */
    public static final SubAuthenticatedContentConfigure SUB_AUTHENTICATED_CONTENT_CONFIGURE = com.zxy.product.course.jooq.tables.SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE;

    /**
     * 子认证-学员学习进度表
     */
    public static final SubAuthenticatedCourseProgress SUB_AUTHENTICATED_COURSE_PROGRESS = com.zxy.product.course.jooq.tables.SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS;

    /**
     * 子认证-学员-资料审核、举证材料记录表
     */
    public static final SubAuthenticatedResourceAuditRecord SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD = com.zxy.product.course.jooq.tables.SubAuthenticatedResourceAuditRecord.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD;

    /**
     * 子认证-在线学习组-学习内容关联关系表
     */
    public static final SubAuthenticatedStudyOnline SUB_AUTHENTICATED_STUDY_ONLINE = com.zxy.product.course.jooq.tables.SubAuthenticatedStudyOnline.SUB_AUTHENTICATED_STUDY_ONLINE;

    /**
     * 子认证-维度表
     */
    public static final SubAuthenticatedDimension SUB_AUTHENTICATED_DIMENSION = com.zxy.product.course.jooq.tables.SubAuthenticatedDimension.SUB_AUTHENTICATED_DIMENSION;

    /**
     * 子认证-学员-维度点亮记录表
     */
    public static final SubAuthenticatedMemberDimension SUB_AUTHENTICATED_MEMBER_DIMENSION = com.zxy.product.course.jooq.tables.SubAuthenticatedMemberDimension.SUB_AUTHENTICATED_MEMBER_DIMENSION;
    /**
     * 子认证注册表
     */
    public static final SubAuthenticatedRegister SUB_AUTHENTICATED_REGISTER = com.zxy.product.course.jooq.tables.SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER;

    /**
     * 报表红名单管理表
     */
    public static final ReportRedlist REPORT_REDLIST = com.zxy.product.course.jooq.tables.ReportRedlist.REPORT_REDLIST;


    /**
     * 直播用户签到表
     */
    public static final GenseeSignIn GENSEE_SIGN_IN = com.zxy.product.course.jooq.tables.GenseeSignIn.GENSEE_SIGN_IN;

    /**
     * 个人创建短视频明细表
     */
    public static final IndividualShortVideoDetails INDIVIDUAL_SHORT_VIDEO_DETAILS = com.zxy.product.course.jooq.tables.IndividualShortVideoDetails.INDIVIDUAL_SHORT_VIDEO_DETAILS;

    /**
     * 个人创建短视频汇总表
     */
    public static final IndividualShortVideoSummary INDIVIDUAL_SHORT_VIDEO_SUMMARY = com.zxy.product.course.jooq.tables.IndividualShortVideoSummary.INDIVIDUAL_SHORT_VIDEO_SUMMARY;

    /**
     * 个人短视频学习明细表
     */
    public static final PersonalShortVideoLearningDetails PERSONAL_SHORT_VIDEO_LEARNING_DETAILS = com.zxy.product.course.jooq.tables.PersonalShortVideoLearningDetails.PERSONAL_SHORT_VIDEO_LEARNING_DETAILS;

    /**
     * 短视频建设明细表
     */
    public static final ShortVideoCreateDetails SHORT_VIDEO_CREATE_DETAILS = com.zxy.product.course.jooq.tables.ShortVideoCreateDetails.SHORT_VIDEO_CREATE_DETAILS;

    /**
     * 短视频报表
     */
    public static final ShortVideoReport SHORT_VIDEO_REPORT = com.zxy.product.course.jooq.tables.ShortVideoReport.SHORT_VIDEO_REPORT;

    /**
     * 咪咕直播观看记录表
     */
    public static final MiguUserAccess MIGU_USER_ACCESS = com.zxy.product.course.jooq.tables.MiguUserAccess.MIGU_USER_ACCESS;

    /**
     * 咪咕短视频跳转频道配置
     */
    public static final MiguConfig MIGU_CONFIG = com.zxy.product.course.jooq.tables.MiguConfig.MIGU_CONFIG;

    /**
     * 咪咕直播附件表
     */
    public static final MiguAttachment MIGU_ATTACHMENT = com.zxy.product.course.jooq.tables.MiguAttachment.MIGU_ATTACHMENT;


    /**
     * 课程打点表
     */
    public static final CourseMark COURSE_MARK = com.zxy.product.course.jooq.tables.CourseMark.COURSE_MARK;

    /**
     * 虚拟空间直播表
     */
    public static final LiveVirtualSpace LIVE_VIRTUAL_SPACE = com.zxy.product.course.jooq.tables.LiveVirtualSpace.LIVE_VIRTUAL_SPACE;


    /**
     * 智能笔记点赞表
     */
    public static final NotePraise NOTE_PRAISE = com.zxy.product.course.jooq.tables.NotePraise.NOTE_PRAISE;


    /**
     * 直播分享表
     */
    public static final GenseeShare GENSEE_SHARE = com.zxy.product.course.jooq.tables.GenseeShare.GENSEE_SHARE;

    /**
     * 红船知识审核表
     */
    public static final KnowledgeRedShipAudit KNOWLEDGE_RED_SHIP_AUDIT = com.zxy.product.course.jooq.tables.KnowledgeRedShipAudit.KNOWLEDGE_RED_SHIP_AUDIT;

    /**
     * 个人短视频每日时长表-短视频人课天表
     */
    public static final ShortVideoLogDay SHORT_VIDEO_LOG_DAY = com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY;

    /**
     * 2023年度账单
     */
    public static final AnnualBill_2023 ANNUAL_BILL_2023 = com.zxy.product.course.jooq.tables.AnnualBill_2023.ANNUAL_BILL_2023;
    /**
     * 课程节学习进度(2024反复倡廉课程)
     */
    public static final CourseSectionStudyProgressFfclc_2024 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024;

    /**
     * 课程节学习进度(2024反复倡廉专题)
     */
    public static final CourseSectionStudyProgressFfcls_2024 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024;

    /**
     * 党建群组日志表（党建云屏使用）
     */
    public static final DjLog DJ_LOG = com.zxy.product.course.jooq.tables.DjLog.DJ_LOG;

    /**
     * 直播签到活动
     */
    public static final SignIn SIGN_IN = com.zxy.product.course.jooq.tables.SignIn.SIGN_IN;

    /**
     * The table <code>course-study.t_member_course_hours</code>.
     */
    public static final MemberCourseHours MEMBER_COURSE_HOURS = com.zxy.product.course.jooq.tables.MemberCourseHours.MEMBER_COURSE_HOURS;


    /**
     * 智能播报使用次数
     */
    public static final BroadcastCount BROADCAST_COUNT = com.zxy.product.course.jooq.tables.BroadcastCount.BROADCAST_COUNT;


    /**
     * 党校banner表
     */
    public static final PartyBanner PARTY_BANNER = com.zxy.product.course.jooq.tables.PartyBanner.PARTY_BANNER;

    /**
     * 党校党校示范培训配置表
     */
    public static final PartyDemonstrationTrainingConfig PARTY_DEMONSTRATION_TRAINING_CONFIG = com.zxy.product.course.jooq.tables.PartyDemonstrationTrainingConfig.PARTY_DEMONSTRATION_TRAINING_CONFIG;

    /**
     * 党校重点专题配置表
     */
    public static final PartyFocusConfig PARTY_FOCUS_CONFIG = com.zxy.product.course.jooq.tables.PartyFocusConfig.PARTY_FOCUS_CONFIG;

    /**
     * 党校动态配置表
     */
    public static final PartyDynamicNewsConfig PARTY_DYNAMIC_NEWS_CONFIG = com.zxy.product.course.jooq.tables.PartyDynamicNewsConfig.PARTY_DYNAMIC_NEWS_CONFIG;

    /**
     * 数智导师 模型问题表
     */
    public static final AiQuestion AI_QUESTION = com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION;

    /**
     * 数智导师 模型答案表
     */
    public static final AiAnswer AI_ANSWER = com.zxy.product.course.jooq.tables.AiAnswer.AI_ANSWER;

    /**
     * 数智导师 模型反馈表
     */
    public static final AiFeedback AI_FEEDBACK = com.zxy.product.course.jooq.tables.AiFeedback.AI_FEEDBACK;

    /**
     * 数智导师 模型关联表
     */
    public static final AiMentor AI_MENTOR = com.zxy.product.course.jooq.tables.AiMentor.AI_MENTOR;

    /**
     * 数智导师 模型预设实例表
     */
    public static final AiPresetExample AI_PRESET_EXAMPLE = com.zxy.product.course.jooq.tables.AiPresetExample.AI_PRESET_EXAMPLE;

    /**
     * 高标党建-约课审核
     */
    public static final GbCourseAudit GB_COURSE_AUDIT = com.zxy.product.course.jooq.tables.GbCourseAudit.GB_COURSE_AUDIT;

    /**
     * 高标党建-课程分类
     */
    public static final GbCourseClassification GB_COURSE_CLASSIFICATION = com.zxy.product.course.jooq.tables.GbCourseClassification.GB_COURSE_CLASSIFICATION;

    /**
     * 高标党建-在线课程配置
     */
    public static final GbCourseConfiguration GB_COURSE_CONFIGURATION = com.zxy.product.course.jooq.tables.GbCourseConfiguration.GB_COURSE_CONFIGURATION;

    /**
     * 高标党建-课程库
     */
    public static final GbCourseLibrary GB_COURSE_LIBRARY = com.zxy.product.course.jooq.tables.GbCourseLibrary.GB_COURSE_LIBRARY;

    /**
     * 高标党建-关联关系表
     */
    public static final GbCourseMiddle GB_COURSE_MIDDLE = com.zxy.product.course.jooq.tables.GbCourseMiddle.GB_COURSE_MIDDLE;

    /**
     * 高标党建-约课记录
     */
    public static final GbCourseRecord GB_COURSE_RECORD = com.zxy.product.course.jooq.tables.GbCourseRecord.GB_COURSE_RECORD;

    /**
     * 高标党建-讲师库
     */
    public static final GbLecturerLibrary GB_LECTURER_LIBRARY = com.zxy.product.course.jooq.tables.GbLecturerLibrary.GB_LECTURER_LIBRARY;

    /**
     * 高标党建-管理员分类表
     */
    public static final GbMember GB_MEMBER = com.zxy.product.course.jooq.tables.GbMember.GB_MEMBER;
    /**
     * The table <code>course-study.t_short_video_operation</code>.
     */
    public static final ShortVideoOperation SHORT_VIDEO_OPERATION = com.zxy.product.course.jooq.tables.ShortVideoOperation.SHORT_VIDEO_OPERATION;

    /**
     * The table <code>course-study.t_short_video_operation_group</code>.
     */
    public static final ShortVideoOperationGroup SHORT_VIDEO_OPERATION_GROUP = com.zxy.product.course.jooq.tables.ShortVideoOperationGroup.SHORT_VIDEO_OPERATION_GROUP;

    /**
     * The table <code>course-study.t_short_video_operation_integral</code>.
     */
    public static final ShortVideoOperationIntegral SHORT_VIDEO_OPERATION_INTEGRAL = com.zxy.product.course.jooq.tables.ShortVideoOperationIntegral.SHORT_VIDEO_OPERATION_INTEGRAL;

    /**
     * The table <code>course-study.t_short_video_operation_member</code>.
     */
    public static final ShortVideoOperationMember SHORT_VIDEO_OPERATION_MEMBER = com.zxy.product.course.jooq.tables.ShortVideoOperationMember.SHORT_VIDEO_OPERATION_MEMBER;

    /**
     * The table <code>course-study.t_short_video_operation_pick_question</code>.
     */
    public static final ShortVideoOperationPickQuestion SHORT_VIDEO_OPERATION_PICK_QUESTION = com.zxy.product.course.jooq.tables.ShortVideoOperationPickQuestion.SHORT_VIDEO_OPERATION_PICK_QUESTION;

    /**
     * The table <code>course-study.t_short_video_operation_question</code>.
     */
    public static final ShortVideoOperationQuestion SHORT_VIDEO_OPERATION_QUESTION = com.zxy.product.course.jooq.tables.ShortVideoOperationQuestion.SHORT_VIDEO_OPERATION_QUESTION;


    /**
     * 专题-专题计划关联表
     */
    public static final SubjectPlanRelated SUBJECT_PLAN_RELATED = com.zxy.product.course.jooq.tables.SubjectPlanRelated.SUBJECT_PLAN_RELATED;

    /**
     * 数智导师-官方笔记表
     */
    public static final CoursewareNote COURSEWARE_NOTE = com.zxy.product.course.jooq.tables.CoursewareNote.COURSEWARE_NOTE;

    /**
     * 课件笔记审核表
     */
    public static final CoursewareNoteAudit COURSEWARE_NOTE_AUDIT = com.zxy.product.course.jooq.tables.CoursewareNoteAudit.COURSEWARE_NOTE_AUDIT;

    /**
     * 数智导师-官方笔记版本表
     */
    public static final CoursewareNoteVersion COURSEWARE_NOTE_VERSION = com.zxy.product.course.jooq.tables.CoursewareNoteVersion.COURSEWARE_NOTE_VERSION;

    /**
     * 课程笔记-（九天传输）
     */
    public static final CourseMainNote COURSE_MAIN_NOTE = com.zxy.product.course.jooq.tables.CourseMainNote.COURSE_MAIN_NOTE;

    /**
     * 课程版本笔记-（九天传输）
     */
    public static final CourseMainNoteVersion COURSE_MAIN_NOTE_VERSION = com.zxy.product.course.jooq.tables.CourseMainNoteVersion.COURSE_MAIN_NOTE_VERSION;

    /**
     * 课程笔记审核-（九天传输）
     */
    public static final CourseMainNoteAudit COURSE_MAIN_NOTE_AUDIT = com.zxy.product.course.jooq.tables.CourseMainNoteAudit.COURSE_MAIN_NOTE_AUDIT;

    /**
     * The table <code>course-study.t_subject_rank_year</code>.
     */
    public static final SubjectRankYear SUBJECT_RANK_YEAR = SubjectRankYear.SUBJECT_RANK_YEAR;

    /**
     * ishow外部请求记录
     */
    public static final IshowSdkRequest ISHOW_SDK_REQUEST = com.zxy.product.course.jooq.tables.IshowSdkRequest.ISHOW_SDK_REQUEST;

    /**
     * ishow学习记录
     */
    public static final IshowStudyRecord ISHOW_STUDY_RECORD = com.zxy.product.course.jooq.tables.IshowStudyRecord.ISHOW_STUDY_RECORD;
    /**
     * 学习助手应用管理
     */
    public static final ApplicationConfig APPLICATION_CONFIG = ApplicationConfig.APPLICATION_CONFIG;

    /**
     * 能力信息表
     */
    public static final Ability ABILITY = com.zxy.product.course.jooq.tables.Ability.ABILITY;

    /**
     * 能力内容关联表
     */
    public static final AbilityBusiness ABILITY_BUSINESS = com.zxy.product.course.jooq.tables.AbilityBusiness.ABILITY_BUSINESS;

    /**
     * 学习地图章关联能力表
     */
    public static final CourseAbility COURSE_ABILITY = com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY;

    /**
     * 课程/专题-学情分析分析报告管理员 - 关联表
     */
    public static final StudyReportAnalysisManagers STUDY_REPORT_ANALYSIS_MANAGERS = com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS;

    /**
     * 红色展馆-存储对应平台学习数据
     */
    public static final CourseRen COURSE_REN = com.zxy.product.course.jooq.tables.CourseRen.COURSE_REN;

    /**
     * 红色展馆-存储对应平台学习数据
     */
    public static final CourseRenFeedback COURSE_REN_FEEDBACK = com.zxy.product.course.jooq.tables.CourseRenFeedback.COURSE_REN_FEEDBACK;

    /**
     * 数智导师——同步表
     */
    public static final AiSynchronous AI_SYNCHRONOUS = com.zxy.product.course.jooq.tables.AiSynchronous.AI_SYNCHRONOUS;

    /**
     * The table <code>course-study.t_course_online_log</code>.
     */
    public static final CourseOnlineLog COURSE_ONLINE_LOG = com.zxy.product.course.jooq.tables.CourseOnlineLog.COURSE_ONLINE_LOG;

    /**
     * 数智导师回调表
     */
    public static final DigitalMentorCallback DIGITAL_MENTOR_CALLBACK = com.zxy.product.course.jooq.tables.DigitalMentorCallback.DIGITAL_MENTOR_CALLBACK;

    /**
     * 网上党校动态表
     */
    public static final PartySchoolNews PARTY_SCHOOL_NEWS = com.zxy.product.course.jooq.tables.PartySchoolNews.PARTY_SCHOOL_NEWS;
    /**
     * 意见反馈
     */
    public static final CourseFeedback COURSE_FEEDBACK = CourseFeedback.COURSE_FEEDBACK;

    /**
     * 学习助手知识表
     */
    public static final CourseKnowledge COURSE_KNOWLEDGE = CourseKnowledge.COURSE_KNOWLEDGE;

    /**
     * 推荐问题管理表
     */
    public static final CourseQuestionRecommend COURSE_QUESTION_RECOMMEND = CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND;

    /**
     * 回答问题时间差
     */
    public static final ChatTimeRecord CHAT_TIME_RECORD = com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD;
    /**
     * 子认证-维度导入表-临时表
     */
    public static final SubAuthenticatedTmp SUB_AUTHENTICATED_TMP = com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP;
}
