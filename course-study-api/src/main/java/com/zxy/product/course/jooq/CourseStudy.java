/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq;

import com.zxy.product.course.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudy extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study</code>
     */
    public static final CourseStudy COURSE_STUDY_SCHEMA = new CourseStudy();

    /**
     * The table <code>course-study.t_activity_banner</code>.
     */
    public final ActivityBanner ACTIVITY_BANNER = ActivityBanner.ACTIVITY_BANNER;

    /**
     * 网大CHBN知识赋能行动
     */
    public final ActivityChbn ACTIVITY_CHBN = ActivityChbn.ACTIVITY_CHBN;

    /**
     * The table <code>course-study.t_annual_bill_2019</code>.
     */
    public final AnnualBill_2019 ANNUAL_BILL_2019 = AnnualBill_2019.ANNUAL_BILL_2019;

    /**
     * 2021年度账单
     */
    public final AnnualBill_2021 ANNUAL_BILL_2021 = AnnualBill_2021.ANNUAL_BILL_2021;

    /**
     * The table <code>course-study.t_annual_bill_askbarcomment</code>.
     */
    public final AnnualBillAskbarcomment ANNUAL_BILL_ASKBARCOMMENT = AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT;

    /**
     * The table <code>course-study.t_annual_bill_coursecomment</code>.
     */
    public final AnnualBillCoursecomment ANNUAL_BILL_COURSECOMMENT = AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT;

    /**
     * The table <code>course-study.t_annual_bill_exam</code>.
     */
    public final AnnualBillExam ANNUAL_BILL_EXAM = AnnualBillExam.ANNUAL_BILL_EXAM;

    /**
     * The table <code>course-study.t_annual_bill_studyday</code>.
     */
    public final AnnualBillStudyday ANNUAL_BILL_STUDYDAY = AnnualBillStudyday.ANNUAL_BILL_STUDYDAY;

    /**
     * 受众项
     */
    public final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 受众对象表
     */
    public final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * The table <code>course-study.t_bill_config</code>.
     */
    public final BillConfig BILL_CONFIG = BillConfig.BILL_CONFIG;

    /**
     * The table <code>course-study.t_bill_config_lost</code>.
     */
    public final BillConfigLost BILL_CONFIG_LOST = BillConfigLost.BILL_CONFIG_LOST;


    /**
     * 证书与业务关联表
     */
    public final BusinessCertificate BUSINESS_CERTIFICATE = BusinessCertificate.BUSINESS_CERTIFICATE;

    /**
     * 资源话题表
     */
    public final BusinessTopic BUSINESS_TOPIC = BusinessTopic.BUSINESS_TOPIC;

    /**
     * 证书记录表
     */
    public final CertificateRecord CERTIFICATE_RECORD = CertificateRecord.CERTIFICATE_RECORD;

    /**
     * chbn证书记录表
     */
    public final CertificateRecordChbn CERTIFICATE_RECORD_CHBN = CertificateRecordChbn.CERTIFICATE_RECORD_CHBN;

    /**
     * 参赛作品附件表
     */
    public final CompeteCourseAttachment COMPETE_COURSE_ATTACHMENT = CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT;

    /**
     * 参赛作品节资源(课件)
     */
    public final CompeteCourseChapterSection COMPETE_COURSE_CHAPTER_SECTION = CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION;

    /**
     * 参赛作品表
     */
    public final CompeteCourseInfo COMPETE_COURSE_INFO = CompeteCourseInfo.COMPETE_COURSE_INFO;

    /**
     * 参赛作品投票信息表
     */
    public final CompeteCourseVote COMPETE_COURSE_VOTE = CompeteCourseVote.COMPETE_COURSE_VOTE;

    /**
     * 讲师单位表
     */
    public final CompeteLecturerCompany COMPETE_LECTURER_COMPANY = CompeteLecturerCompany.COMPETE_LECTURER_COMPANY;

    /**
     * 课程附件
     */
    public final CourseAttachment COURSE_ATTACHMENT = CourseAttachment.COURSE_ATTACHMENT;

    /**
     * 课程目录表
     */
    public final CourseCategory COURSE_CATEGORY = CourseCategory.COURSE_CATEGORY;

    /**
     * 用户证书领取记录
     */
    public final CourseCertificateRecord COURSE_CERTIFICATE_RECORD = CourseCertificateRecord.COURSE_CERTIFICATE_RECORD;

    /**
     * 课程章节（阶段）
     */
    public final CourseChapter COURSE_CHAPTER = CourseChapter.COURSE_CHAPTER;

    /**
     * 课程章节-满意度问卷模板关联表
     */
    public final CourseChapterQuestionnaire COURSE_CHAPTER_QUESTIONNAIRE = CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE;

    /**
     * 课程节资源(课件)
     */
    public final CourseChapterSection COURSE_CHAPTER_SECTION = CourseChapterSection.COURSE_CHAPTER_SECTION;

    /**
     * 网大新动能通用课程表
     */
    public final CourseCurrency COURSE_CURRENCY = CourseCurrency.COURSE_CURRENCY;

    /**
     * The table <code>course-study.t_course_exception</code>.
     */
    public final CourseException COURSE_EXCEPTION = CourseException.COURSE_EXCEPTION;

    /**
     * 课程信息表
     */
    public final CourseInfo COURSE_INFO = CourseInfo.COURSE_INFO;

    /**
     * 课程举报表
     */
    public final CourseInform COURSE_INFORM = CourseInform.COURSE_INFORM;


    /**
     * 课程详情-目录中间表
     */
    public final CourseInfoCategory COURSE_INFO_CATEGORY = com.zxy.product.course.jooq.tables.CourseInfoCategory.COURSE_INFO_CATEGORY;


    /**
     * 课程笔记表
     */
    public final CourseNote COURSE_NOTE = CourseNote.COURSE_NOTE;

    /**
     * 专题相册
     */
    public final CoursePhoto COURSE_PHOTO = CoursePhoto.COURSE_PHOTO;

    /**
     * 课程满意度问卷记录表
     */
    public final CourseQuestionnaireRecord COURSE_QUESTIONNAIRE_RECORD = CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD;

    /**
     * 推荐课程表
     */
    public final CourseRecommend COURSE_RECOMMEND = CourseRecommend.COURSE_RECOMMEND;

    /**
     * The table <code>course-study.t_course_record</code>.
     */
    public final CourseRecord COURSE_RECORD = CourseRecord.COURSE_RECORD;

    /**
     * 课程注册表
     */
    public final CourseRegister COURSE_REGISTER = CourseRegister.COURSE_REGISTER;

    /**
     * 课程评分表
     */
    public final CourseScore COURSE_SCORE = CourseScore.COURSE_SCORE;

    /**
     * 用户提交任务附件
     */
    public final CourseSectionProgressAttachment COURSE_SECTION_PROGRESS_ATTACHMENT = CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT;

    /**
     * 标准课件的每一节
     */
    public final CourseSectionScorm COURSE_SECTION_SCORM = CourseSectionScorm.COURSE_SECTION_SCORM;

    /**
     * The table <code>course-study.t_course_section_scorm_progress</code>.
     */
    public final CourseSectionScormProgress COURSE_SECTION_SCORM_PROGRESS = CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS;

    /**
     * 课程节学习日志(流水)，记录单次学习动作
     */
    public final CourseSectionStudyLog COURSE_SECTION_STUDY_LOG = CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG;

    /**
     * The table <code>course-study.t_course_section_study_log_ah</code>.
     */
    public final CourseSectionStudyLogAh COURSE_SECTION_STUDY_LOG_AH = CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH;

    /**
     * The table <code>course-study.t_course_section_study_log_ah_day</code>.
     */
    public final CourseSectionStudyLogAhDay COURSE_SECTION_STUDY_LOG_AH_DAY = CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_bj</code>.
     */
    public final CourseSectionStudyLogBj COURSE_SECTION_STUDY_LOG_BJ = CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ;

    /**
     * The table <code>course-study.t_course_section_study_log_bj_day</code>.
     */
    public final CourseSectionStudyLogBjDay COURSE_SECTION_STUDY_LOG_BJ_DAY = CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_cm</code>.
     */
    public final CourseSectionStudyLogCm COURSE_SECTION_STUDY_LOG_CM = CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM;

    /**
     * The table <code>course-study.t_course_section_study_log_cm_day</code>.
     */
    public final CourseSectionStudyLogCmDay COURSE_SECTION_STUDY_LOG_CM_DAY = CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_cq</code>.
     */
    public final CourseSectionStudyLogCq COURSE_SECTION_STUDY_LOG_CQ = CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ;

    /**
     * The table <code>course-study.t_course_section_study_log_cq_day</code>.
     */
    public final CourseSectionStudyLogCqDay COURSE_SECTION_STUDY_LOG_CQ_DAY = CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_eb</code>.
     */
    public final CourseSectionStudyLogEb COURSE_SECTION_STUDY_LOG_EB = CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB;

    /**
     * The table <code>course-study.t_course_section_study_log_eb_day</code>.
     */
    public final CourseSectionStudyLogEbDay COURSE_SECTION_STUDY_LOG_EB_DAY = CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_fj</code>.
     */
    public final CourseSectionStudyLogFj COURSE_SECTION_STUDY_LOG_FJ = CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ;

    /**
     * The table <code>course-study.t_course_section_study_log_fj_day</code>.
     */
    public final CourseSectionStudyLogFjDay COURSE_SECTION_STUDY_LOG_FJ_DAY = CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gd</code>.
     */
    public final CourseSectionStudyLogGd COURSE_SECTION_STUDY_LOG_GD = CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD;

    /**
     * The table <code>course-study.t_course_section_study_log_gd_day</code>.
     */
    public final CourseSectionStudyLogGdDay COURSE_SECTION_STUDY_LOG_GD_DAY = CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gs</code>.
     */
    public final CourseSectionStudyLogGs COURSE_SECTION_STUDY_LOG_GS = CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS;

    /**
     * The table <code>course-study.t_course_section_study_log_gs_day</code>.
     */
    public final CourseSectionStudyLogGsDay COURSE_SECTION_STUDY_LOG_GS_DAY = CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gx</code>.
     */
    public final CourseSectionStudyLogGx COURSE_SECTION_STUDY_LOG_GX = CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX;

    /**
     * The table <code>course-study.t_course_section_study_log_gx_day</code>.
     */
    public final CourseSectionStudyLogGxDay COURSE_SECTION_STUDY_LOG_GX_DAY = CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_gz</code>.
     */
    public final CourseSectionStudyLogGz COURSE_SECTION_STUDY_LOG_GZ = CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ;

    /**
     * The table <code>course-study.t_course_section_study_log_gz_day</code>.
     */
    public final CourseSectionStudyLogGzDay COURSE_SECTION_STUDY_LOG_GZ_DAY = CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hb</code>.
     */
    public final CourseSectionStudyLogHb COURSE_SECTION_STUDY_LOG_HB = CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB;

    /**
     * The table <code>course-study.t_course_section_study_log_hb_day</code>.
     */
    public final CourseSectionStudyLogHbDay COURSE_SECTION_STUDY_LOG_HB_DAY = CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hl</code>.
     */
    public final CourseSectionStudyLogHl COURSE_SECTION_STUDY_LOG_HL = CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL;

    /**
     * The table <code>course-study.t_course_section_study_log_hl_day</code>.
     */
    public final CourseSectionStudyLogHlDay COURSE_SECTION_STUDY_LOG_HL_DAY = CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_hn</code>.
     */
    public final CourseSectionStudyLogHn COURSE_SECTION_STUDY_LOG_HN = CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN;

    /**
     * The table <code>course-study.t_course_section_study_log_hn_day</code>.
     */
    public final CourseSectionStudyLogHnDay COURSE_SECTION_STUDY_LOG_HN_DAY = CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_jl</code>.
     */
    public final CourseSectionStudyLogJl COURSE_SECTION_STUDY_LOG_JL = CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL;

    /**
     * The table <code>course-study.t_course_section_study_log_jl_day</code>.
     */
    public final CourseSectionStudyLogJlDay COURSE_SECTION_STUDY_LOG_JL_DAY = CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_js</code>.
     */
    public final CourseSectionStudyLogJs COURSE_SECTION_STUDY_LOG_JS = CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS;

    /**
     * The table <code>course-study.t_course_section_study_log_js_day</code>.
     */
    public final CourseSectionStudyLogJsDay COURSE_SECTION_STUDY_LOG_JS_DAY = CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_jx</code>.
     */
    public final CourseSectionStudyLogJx COURSE_SECTION_STUDY_LOG_JX = CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX;

    /**
     * The table <code>course-study.t_course_section_study_log_jx_day</code>.
     */
    public final CourseSectionStudyLogJxDay COURSE_SECTION_STUDY_LOG_JX_DAY = CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_ln</code>.
     */
    public final CourseSectionStudyLogLn COURSE_SECTION_STUDY_LOG_LN = CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN;

    /**
     * The table <code>course-study.t_course_section_study_log_ln_day</code>.
     */
    public final CourseSectionStudyLogLnDay COURSE_SECTION_STUDY_LOG_LN_DAY = CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_nm</code>.
     */
    public final CourseSectionStudyLogNm COURSE_SECTION_STUDY_LOG_NM = CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM;

    /**
     * The table <code>course-study.t_course_section_study_log_nm_day</code>.
     */
    public final CourseSectionStudyLogNmDay COURSE_SECTION_STUDY_LOG_NM_DAY = CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_nx</code>.
     */
    public final CourseSectionStudyLogNx COURSE_SECTION_STUDY_LOG_NX = CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX;

    /**
     * The table <code>course-study.t_course_section_study_log_nx_day</code>.
     */
    public final CourseSectionStudyLogNxDay COURSE_SECTION_STUDY_LOG_NX_DAY = CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_other</code>.
     */
    public final CourseSectionStudyLogOther COURSE_SECTION_STUDY_LOG_OTHER = CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER;

    /**
     * The table <code>course-study.t_course_section_study_log_other_day</code>.
     */
    public final CourseSectionStudyLogOtherDay COURSE_SECTION_STUDY_LOG_OTHER_DAY = CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_qh</code>.
     */
    public final CourseSectionStudyLogQh COURSE_SECTION_STUDY_LOG_QH = CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH;

    /**
     * The table <code>course-study.t_course_section_study_log_qh_day</code>.
     */
    public final CourseSectionStudyLogQhDay COURSE_SECTION_STUDY_LOG_QH_DAY = CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_qo</code>.
     */
    public final CourseSectionStudyLogQo COURSE_SECTION_STUDY_LOG_QO = CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO;

    /**
     * The table <code>course-study.t_course_section_study_log_qo_day</code>.
     */
    public final CourseSectionStudyLogQoDay COURSE_SECTION_STUDY_LOG_QO_DAY = CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sc</code>.
     */
    public final CourseSectionStudyLogSc COURSE_SECTION_STUDY_LOG_SC = CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC;

    /**
     * The table <code>course-study.t_course_section_study_log_sc_day</code>.
     */
    public final CourseSectionStudyLogScDay COURSE_SECTION_STUDY_LOG_SC_DAY = CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sd</code>.
     */
    public final CourseSectionStudyLogSd COURSE_SECTION_STUDY_LOG_SD = CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD;

    /**
     * The table <code>course-study.t_course_section_study_log_sd_day</code>.
     */
    public final CourseSectionStudyLogSdDay COURSE_SECTION_STUDY_LOG_SD_DAY = CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sh</code>.
     */
    public final CourseSectionStudyLogSh COURSE_SECTION_STUDY_LOG_SH = CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH;

    /**
     * The table <code>course-study.t_course_section_study_log_sh_day</code>.
     */
    public final CourseSectionStudyLogShDay COURSE_SECTION_STUDY_LOG_SH_DAY = CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sn</code>.
     */
    public final CourseSectionStudyLogSn COURSE_SECTION_STUDY_LOG_SN = CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN;

    /**
     * The table <code>course-study.t_course_section_study_log_sn_day</code>.
     */
    public final CourseSectionStudyLogSnDay COURSE_SECTION_STUDY_LOG_SN_DAY = CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_sx</code>.
     */
    public final CourseSectionStudyLogSx COURSE_SECTION_STUDY_LOG_SX = CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX;

    /**
     * The table <code>course-study.t_course_section_study_log_sx_day</code>.
     */
    public final CourseSectionStudyLogSxDay COURSE_SECTION_STUDY_LOG_SX_DAY = CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_tj</code>.
     */
    public final CourseSectionStudyLogTj COURSE_SECTION_STUDY_LOG_TJ = CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ;

    /**
     * The table <code>course-study.t_course_section_study_log_tj_day</code>.
     */
    public final CourseSectionStudyLogTjDay COURSE_SECTION_STUDY_LOG_TJ_DAY = CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xj</code>.
     */
    public final CourseSectionStudyLogXj COURSE_SECTION_STUDY_LOG_XJ = CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ;

    /**
     * The table <code>course-study.t_course_section_study_log_xj_day</code>.
     */
    public final CourseSectionStudyLogXjDay COURSE_SECTION_STUDY_LOG_XJ_DAY = CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xn</code>.
     */
    public final CourseSectionStudyLogXn COURSE_SECTION_STUDY_LOG_XN = CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN;

    /**
     * The table <code>course-study.t_course_section_study_log_xn_day</code>.
     */
    public final CourseSectionStudyLogXnDay COURSE_SECTION_STUDY_LOG_XN_DAY = CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_xz</code>.
     */
    public final CourseSectionStudyLogXz COURSE_SECTION_STUDY_LOG_XZ = CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ;

    /**
     * The table <code>course-study.t_course_section_study_log_xz_day</code>.
     */
    public final CourseSectionStudyLogXzDay COURSE_SECTION_STUDY_LOG_XZ_DAY = CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_yn</code>.
     */
    public final CourseSectionStudyLogYn COURSE_SECTION_STUDY_LOG_YN = CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN;

    /**
     * The table <code>course-study.t_course_section_study_log_yn_day</code>.
     */
    public final CourseSectionStudyLogYnDay COURSE_SECTION_STUDY_LOG_YN_DAY = CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zgtt</code>.
     */
    public final CourseSectionStudyLogZgtt COURSE_SECTION_STUDY_LOG_ZGTT = CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT;

    /**
     * The table <code>course-study.t_course_section_study_log_zgtt_day</code>.
     */
    public final CourseSectionStudyLogZgttDay COURSE_SECTION_STUDY_LOG_ZGTT_DAY = CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zj</code>.
     */
    public final CourseSectionStudyLogZj COURSE_SECTION_STUDY_LOG_ZJ = CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ;

    /**
     * The table <code>course-study.t_course_section_study_log_zj_day</code>.
     */
    public final CourseSectionStudyLogZjDay COURSE_SECTION_STUDY_LOG_ZJ_DAY = CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY;

    /**
     * The table <code>course-study.t_course_section_study_log_zx</code>.
     */
    public final CourseSectionStudyLogZx COURSE_SECTION_STUDY_LOG_ZX = CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX;

    /**
     * The table <code>course-study.t_course_section_study_log_zx_day</code>.
     */
    public final CourseSectionStudyLogZxDay COURSE_SECTION_STUDY_LOG_ZX_DAY = CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY;

    /**
     * 课程节学习进度
     */
    public final CourseSectionStudyProgress COURSE_SECTION_STUDY_PROGRESS = CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS;

    /**
     * CHBN活动课程章节进度表
     */
    public final CourseSectionStudyProgressChbnc COURSE_SECTION_STUDY_PROGRESS_CHBNC = CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC;

    /**
     * CHBN活动专题章节进度表
     */
    public final CourseSectionStudyProgressChbns COURSE_SECTION_STUDY_PROGRESS_CHBNS = CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS;

    /**
     * 课程节学习进度(2020反复倡廉课程)
     */
    public final CourseSectionStudyProgressFfclc COURSE_SECTION_STUDY_PROGRESS_FFCLC = CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC;

    /**
     * 课程节学习进度(2020反复倡廉专题)
     */
    public final CourseSectionStudyProgressFfcls COURSE_SECTION_STUDY_PROGRESS_FFCLS = CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS;

    /**
     * 课程节学习进度(2022反复倡廉专题)
     */
    public final CourseSectionStudyProgressFfcls_2022 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022 = CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022;


    /**
     * 课程节学习进度(新动能重塑培训专题)
     */
    public final CourseSectionStudyProgressRts COURSE_SECTION_STUDY_PROGRESS_RTS = CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS;

    /**
     * 课程节学习进度(新动能课程)
     */
    public final CourseSectionStudyProgressXdnc COURSE_SECTION_STUDY_PROGRESS_XDNC = CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC;

    /**
     * 课程节学习进度(新动能专题)
     */
    public final CourseSectionStudyProgressXdns COURSE_SECTION_STUDY_PROGRESS_XDNS = CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS;

    /**
     * 智慧中台活动专题章节进度表
     */
    public final CourseSectionStudyProgressZhzts COURSE_SECTION_STUDY_PROGRESS_ZHZTS = CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS;

    /**
     * 课程序列记录，用于累计生成课程编码的序列号
     */
    public final CourseSequence COURSE_SEQUENCE = CourseSequence.COURSE_SEQUENCE;

    /**
     * 课程上架通知表
     */
    public final CourseShelves COURSE_SHELVES = CourseShelves.COURSE_SHELVES;

    /**
     * The table <code>course-study.t_course_study_process_2017</code>.
     */
    public final CourseStudyProcess_2017 COURSE_STUDY_PROCESS_2017 = CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017;

    /**
     * 课程学习进度表
     */
    public final CourseStudyProgress COURSE_STUDY_PROGRESS = CourseStudyProgress.COURSE_STUDY_PROGRESS;

    /**
     * The table <code>course-study.t_course_study_progress_ah</code>.
     */
    public final CourseStudyProgressAh COURSE_STUDY_PROGRESS_AH = CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH;

    /**
     * The table <code>course-study.t_course_study_progress_bj</code>.
     */
    public final CourseStudyProgressBj COURSE_STUDY_PROGRESS_BJ = CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ;

    /**
     * The table <code>course-study.t_course_study_progress_cm</code>.
     */
    public final CourseStudyProgressCm COURSE_STUDY_PROGRESS_CM = CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM;

    /**
     * The table <code>course-study.t_course_study_progress_cq</code>.
     */
    public final CourseStudyProgressCq COURSE_STUDY_PROGRESS_CQ = CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ;

    /**
     * The table <code>course-study.t_course_study_progress_eb</code>.
     */
    public final CourseStudyProgressEb COURSE_STUDY_PROGRESS_EB = CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB;

    /**
     * The table <code>course-study.t_course_study_progress_fj</code>.
     */
    public final CourseStudyProgressFj COURSE_STUDY_PROGRESS_FJ = CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ;

    /**
     * The table <code>course-study.t_course_study_progress_gd</code>.
     */
    public final CourseStudyProgressGd COURSE_STUDY_PROGRESS_GD = CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD;

    /**
     * The table <code>course-study.t_course_study_progress_gs</code>.
     */
    public final CourseStudyProgressGs COURSE_STUDY_PROGRESS_GS = CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS;

    /**
     * The table <code>course-study.t_course_study_progress_gx</code>.
     */
    public final CourseStudyProgressGx COURSE_STUDY_PROGRESS_GX = CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX;

    /**
     * The table <code>course-study.t_course_study_progress_gz</code>.
     */
    public final CourseStudyProgressGz COURSE_STUDY_PROGRESS_GZ = CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ;

    /**
     * The table <code>course-study.t_course_study_progress_hb</code>.
     */
    public final CourseStudyProgressHb COURSE_STUDY_PROGRESS_HB = CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB;

    /**
     * The table <code>course-study.t_course_study_progress_hl</code>.
     */
    public final CourseStudyProgressHl COURSE_STUDY_PROGRESS_HL = CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL;

    /**
     * The table <code>course-study.t_course_study_progress_hn</code>.
     */
    public final CourseStudyProgressHn COURSE_STUDY_PROGRESS_HN = CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN;

    /**
     * The table <code>course-study.t_course_study_progress_jl</code>.
     */
    public final CourseStudyProgressJl COURSE_STUDY_PROGRESS_JL = CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL;

    /**
     * The table <code>course-study.t_course_study_progress_js</code>.
     */
    public final CourseStudyProgressJs COURSE_STUDY_PROGRESS_JS = CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS;

    /**
     * The table <code>course-study.t_course_study_progress_jx</code>.
     */
    public final CourseStudyProgressJx COURSE_STUDY_PROGRESS_JX = CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX;

    /**
     * The table <code>course-study.t_course_study_progress_ln</code>.
     */
    public final CourseStudyProgressLn COURSE_STUDY_PROGRESS_LN = CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN;

    /**
     * The table <code>course-study.t_course_study_progress_nm</code>.
     */
    public final CourseStudyProgressNm COURSE_STUDY_PROGRESS_NM = CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM;

    /**
     * The table <code>course-study.t_course_study_progress_nx</code>.
     */
    public final CourseStudyProgressNx COURSE_STUDY_PROGRESS_NX = CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX;

    /**
     * The table <code>course-study.t_course_study_progress_other</code>.
     */
    public final CourseStudyProgressOther COURSE_STUDY_PROGRESS_OTHER = CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER;

    /**
     * The table <code>course-study.t_course_study_progress_qh</code>.
     */
    public final CourseStudyProgressQh COURSE_STUDY_PROGRESS_QH = CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH;

    /**
     * The table <code>course-study.t_course_study_progress_qo</code>.
     */
    public final CourseStudyProgressQo COURSE_STUDY_PROGRESS_QO = CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO;

    /**
     * The table <code>course-study.t_course_study_progress_sc</code>.
     */
    public final CourseStudyProgressSc COURSE_STUDY_PROGRESS_SC = CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC;

    /**
     * The table <code>course-study.t_course_study_progress_sd</code>.
     */
    public final CourseStudyProgressSd COURSE_STUDY_PROGRESS_SD = CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD;

    /**
     * The table <code>course-study.t_course_study_progress_sh</code>.
     */
    public final CourseStudyProgressSh COURSE_STUDY_PROGRESS_SH = CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH;

    /**
     * The table <code>course-study.t_course_study_progress_sn</code>.
     */
    public final CourseStudyProgressSn COURSE_STUDY_PROGRESS_SN = CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN;

    /**
     * The table <code>course-study.t_course_study_progress_sx</code>.
     */
    public final CourseStudyProgressSx COURSE_STUDY_PROGRESS_SX = CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX;

    /**
     * The table <code>course-study.t_course_study_progress_tj</code>.
     */
    public final CourseStudyProgressTj COURSE_STUDY_PROGRESS_TJ = CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ;

    /**
     * The table <code>course-study.t_course_study_progress_xj</code>.
     */
    public final CourseStudyProgressXj COURSE_STUDY_PROGRESS_XJ = CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ;

    /**
     * The table <code>course-study.t_course_study_progress_xn</code>.
     */
    public final CourseStudyProgressXn COURSE_STUDY_PROGRESS_XN = CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN;

    /**
     * The table <code>course-study.t_course_study_progress_xz</code>.
     */
    public final CourseStudyProgressXz COURSE_STUDY_PROGRESS_XZ = CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ;

    /**
     * The table <code>course-study.t_course_study_progress_yn</code>.
     */
    public final CourseStudyProgressYn COURSE_STUDY_PROGRESS_YN = CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN;

    /**
     * The table <code>course-study.t_course_study_progress_zgtt</code>.
     */
    public final CourseStudyProgressZgtt COURSE_STUDY_PROGRESS_ZGTT = CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT;

    /**
     * The table <code>course-study.t_course_study_progress_zj</code>.
     */
    public final CourseStudyProgressZj COURSE_STUDY_PROGRESS_ZJ = CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ;

    /**
     * The table <code>course-study.t_course_study_progress_zx</code>.
     */
    public final CourseStudyProgressZx COURSE_STUDY_PROGRESS_ZX = CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX;

    /**
     * 业务关联话题
     */
    public final CourseTopic COURSE_TOPIC = CourseTopic.COURSE_TOPIC;

    /**
     * The table <code>course-study.t_course_version</code>.
     */
    public final CourseVersion COURSE_VERSION = CourseVersion.COURSE_VERSION;

    /**
     * The table <code>course-study.t_dba_analyze_table_index</code>.
     */
    public final DbaAnalyzeTableIndex DBA_ANALYZE_TABLE_INDEX = DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX;

    /**
     * 党建分类
     */
    public final DjClassify DJ_CLASSIFY = DjClassify.DJ_CLASSIFY;

    /**
     * 党建资源
     */
    public final DjResource DJ_RESOURCE = DjResource.DJ_RESOURCE;

    /**
     * The table <code>course-study.t_exam</code>.
     */
    public final Exam EXAM = Exam.EXAM;

    /**
     * The table <code>course-study.t_exam_record</code>.
     */
    public final ExamRecord EXAM_RECORD = ExamRecord.EXAM_RECORD;

    /**
     * 直播关联业务表
     */
    public final GenseeBusiness GENSEE_BUSINESS = GenseeBusiness.GENSEE_BUSINESS;

    /**
     * 直播业务参与进度
     */
    public final GenseeBusinessProgress GENSEE_BUSINESS_PROGRESS = GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS;

    /**
     * 直播关联讲师
     */
    public final GenseeLecturer GENSEE_LECTURER = GenseeLecturer.GENSEE_LECTURER;

    /**
     * 直播用户订阅表
     */
    public final GenseeSubscription GENSEE_SUBSCRIPTION = GenseeSubscription.GENSEE_SUBSCRIPTION;

    /**
     * 直播关联话题
     */
    public final GenseeTopic GENSEE_TOPIC = GenseeTopic.GENSEE_TOPIC;

    /**
     * 用户参加直播表
     */
    public final GenseeUserAccess GENSEE_USER_ACCESS = GenseeUserAccess.GENSEE_USER_ACCESS;

    /**
     * 直播历史记录表
     */
    public final GenseeUserJoinHistory GENSEE_USER_JOIN_HISTORY = GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY;

    /**
     * 直播表
     */
    public final GenseeWebCast GENSEE_WEB_CAST = GenseeWebCast.GENSEE_WEB_CAST;

    /**
     * The table <code>course-study.t_grant_detail</code>.
     */
    public final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

    /**
     * The table <code>course-study.t_job</code>.
     */
    public final Job JOB = Job.JOB;

    /**
     * 知识目录表
     */
    public final KnowledgeCategory KNOWLEDGE_CATEGORY = KnowledgeCategory.KNOWLEDGE_CATEGORY;

    /**
     * 知识下载记录，用于统计下载人数
     */
    public final KnowledgeDownRecord KNOWLEDGE_DOWN_RECORD = KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD;

    /**
     * 知识库
     */
    public final KnowledgeInfo KNOWLEDGE_INFO = KnowledgeInfo.KNOWLEDGE_INFO;

    /**
     * 知识月度排行榜
     */
    public final KnowledgeMonthList KNOWLEDGE_MONTH_LIST = KnowledgeMonthList.KNOWLEDGE_MONTH_LIST;

    /**
     * 知识话题表
     */
    public final KnowledgeTopic KNOWLEDGE_TOPIC = KnowledgeTopic.KNOWLEDGE_TOPIC;

    /**
     * 知识使用记录，用于统计使用人数
     */
    public final KnowledgeUseRecord KNOWLEDGE_USE_RECORD = KnowledgeUseRecord.KNOWLEDGE_USE_RECORD;

    /**
     * 知识查看记录，用于统计浏览人数
     */
    public final KnowledgeViewRecord KNOWLEDGE_VIEW_RECORD = KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD;

    /**
     * The table <code>course-study.t_member</code>.
     */
    public final Member MEMBER = Member.MEMBER;

    /**
     * 用户月度学习汇总
     */
    public final MemberCourseMonth MEMBER_COURSE_MONTH = MemberCourseMonth.MEMBER_COURSE_MONTH;

    /**
     * The table <code>course-study.t_member_detail</code>.
     */
    public final MemberDetail MEMBER_DETAIL = MemberDetail.MEMBER_DETAIL;

    /**
     * 月度知识访问排行
     */
    public final MemberKnowledgeMonth MEMBER_KNOWLEDGE_MONTH = MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH;

    /**
     * 党员信息（原样同步党建云数据）
     */
    public final MemberParty MEMBER_PARTY = MemberParty.MEMBER_PARTY;

    /**
     * 学习资源汇总表
     */
    public final MemberStatistics MEMBER_STATISTICS = MemberStatistics.MEMBER_STATISTICS;

    /**
     * 学习资源汇总表
     */
    public final MemberStatisticsArchives MEMBER_STATISTICS_ARCHIVES = MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES;

    /**
     * course-study模块通知表
     */
    public final Notice NOTICE = Notice.NOTICE;

    /**
     * 面授导入信息表
     */
    public final OfflineClass OFFLINE_CLASS = OfflineClass.OFFLINE_CLASS;

    /**
     * 线下课程调查问卷表
     */
    public final OfflineCourseQuestionnaire OFFLINE_COURSE_QUESTIONNAIRE = OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE;

    /**
     * 线下课程调查问卷章节表
     */
    public final OfflineCourseQuestionnaireChapter OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER = OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER;

    /**
     * 线下课程满意度问答记录表
     */
    public final OfflineQuestionnaireAnswer OFFLINE_QUESTIONNAIRE_ANSWER = OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER;

    /**
     * 在线课程满意度问答记录表
     */
    public final OnlineQuestionnaireAnswer ONLINE_QUESTIONNAIRE_ANSWER = OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER;

    /**
     * The table <code>course-study.t_organization</code>.
     */
    public final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>course-study.t_organization_detail</code>.
     */
    public final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * The table <code>course-study.t_org_table_count</code>.
     */
    public final OrgTableCount ORG_TABLE_COUNT = OrgTableCount.ORG_TABLE_COUNT;

    /**
     * 党校活跃度人天
     */
    public final PartyActivityMemberDay PARTY_ACTIVITY_MEMBER_DAY = PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY;

    /**
     * 党校活跃度人月
     */
    public final PartyActivityMemberMonth PARTY_ACTIVITY_MEMBER_MONTH = PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH;

    /**
     * 党校活跃度组织天
     */
    public final PartyActivityOrgDay PARTY_ACTIVITY_ORG_DAY = PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY;

    /**
     * 党校活跃度组织月
     */
    public final PartyActivityOrgMonth PARTY_ACTIVITY_ORG_MONTH = PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH;

    /**
     * 党校活跃度组织年
     */
    public final PartyActivityOrgYear PARTY_ACTIVITY_ORG_YEAR = PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR;

    /**
     * 智慧云屏业务配置表
     */
    public final PartyBusinessConfiguration PARTY_BUSINESS_CONFIGURATION = PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION;

    /**
     * 党校数据记录
     */
    public final PartyData PARTY_DATA = PartyData.PARTY_DATA;

    /**
     * 党校热词配置
     */
    public final PartyHotTopic PARTY_HOT_TOPIC = PartyHotTopic.PARTY_HOT_TOPIC;

    /**
     * 党校热词管理
     */
    public final PartyHotTopicManage PARTY_HOT_TOPIC_MANAGE = PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE;

    /**
     * 智慧云屏可以查询全集团数据的人员配置表
     */
    public final PartyLeader PARTY_LEADER = PartyLeader.PARTY_LEADER;

    /**
     * 党组织（原样同步党建云数据）
     */
    public final PartyOrganization PARTY_ORGANIZATION = PartyOrganization.PARTY_ORGANIZATION;

    /**
     * 智慧云屏党组织与行政组织对应关系表
     */
    public final PartyOrganizationRelationships PARTY_ORGANIZATION_RELATIONSHIPS = PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS;

    /**
     * 智慧云屏为您推荐
     */
    public final PartyRecommendation PARTY_RECOMMENDATION = PartyRecommendation.PARTY_RECOMMENDATION;

    /**
     * 智慧云屏推荐结果表
     */
    public final PartyRecommendResult PARTY_RECOMMEND_RESULT = PartyRecommendResult.PARTY_RECOMMEND_RESULT;

    /**
     * 党建云屏推荐接口失败应急课程
     */
    public final PartyRecommendSpare PARTY_RECOMMEND_SPARE = PartyRecommendSpare.PARTY_RECOMMEND_SPARE;

    /**
     * 党校学习数据汇总（按天）
     */
    public final PartyStudySummaryDay PARTY_STUDY_SUMMARY_DAY = PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY;

    /**
     * 党校学习数据汇总（按月）
     */
    public final PartyStudySummaryMonth PARTY_STUDY_SUMMARY_MONTH = PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH;

    /**
     * 党校首页课程分类表
     */
    public final PartyTopic PARTY_TOPIC = PartyTopic.PARTY_TOPIC;

    /**
     * PCCW progress数据同步需要同步的组织数据
     */
    public final PccwOrganizationConfig PCCW_ORGANIZATION_CONFIG = PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG;

    /**
     * PCCW HR接口调用结果
     */
    public final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;

    /**
     * PCCW progress数据同步失败的id记录
     */
    public final PccwResultBusiness PCCW_RESULT_BUSINESS = PccwResultBusiness.PCCW_RESULT_BUSINESS;

    /**
     * PCCW HR接口调用结果
     */
    public final PccwResultErrorHistory PCCW_RESULT_ERROR_HISTORY = PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY;

    /**
     * 个人年度学习账单
     */
    public final PersonYearBill PERSON_YEAR_BILL = PersonYearBill.PERSON_YEAR_BILL;

    /**
     * The table <code>course-study.t_position</code>.
     */
    public final Position POSITION = Position.POSITION;

    /**
     * The table <code>course-study.t_position_old</code>.
     */
    public final PositionOld POSITION_OLD = PositionOld.POSITION_OLD;

    /**
     * 满意度问卷模板表
     */
    public final QuestionnaireMould QUESTIONNAIRE_MOULD = QuestionnaireMould.QUESTIONNAIRE_MOULD;

    /**
     * 满意度问卷模板-问题关联表
     */
    public final QuestionnaireMouldQuestion QUESTIONNAIRE_MOULD_QUESTION = QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION;

    /**
     * 满意度问卷问题字典表
     */
    public final QuestionnaireQuestion QUESTIONNAIRE_QUESTION = QuestionnaireQuestion.QUESTIONNAIRE_QUESTION;

    /**
     * 重塑培训计划-报名流水表
     */
    public final RemodelingEntryStudyLog REMODELING_ENTRY_STUDY_LOG = RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG;

    /**
     * 重塑培训计划-报名进度表
     */
    public final RemodelingEntryStudyProgress REMODELING_ENTRY_STUDY_PROGRESS = RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS;

    /**
     * 重塑培训计划-外部在线课程与网大业务关联关系表
     */
    public final RemodelingExternalCourseBusiness REMODELING_EXTERNAL_COURSE_BUSINESS = RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS;

    /**
     * 重塑培训计划-外部在线课程学习数据回传详情表
     */
    public final RemodelingExternalCourseStudyDetail REMODELING_EXTERNAL_COURSE_STUDY_DETAIL = RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL;

    /**
     * 重塑培训计划-外部考试数据回传详情表
     */
    public final RemodelingExternalExamDetail REMODELING_EXTERNAL_EXAM_DETAIL = RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL;

    /**
     * 重塑培训计划-标注学员是否在外部平台学习某章节，满意度问卷使用
     */
    public final RemodelingExternalPassbackBusiness REMODELING_EXTERNAL_PASSBACK_BUSINESS = RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS;

    /**
     * 重塑培训计划-在线课程链接与专区关联关系表
     */
    public final RemodelingInternalCourseBusiness REMODELING_INTERNAL_COURSE_BUSINESS = RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS;

    /**
     * 重塑培训计划-角色信息表
     */
    public final RemodelingRoleDetail REMODELING_ROLE_DETAIL = RemodelingRoleDetail.REMODELING_ROLE_DETAIL;

    /**
     * 重塑专区证书固定颁发时间配置表
     */
    public final RemodelingRoleIssueTimeConfig REMODELING_ROLE_ISSUE_TIME_CONFIG = com.zxy.product.course.jooq.tables.RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG;


    /**
     * 重复专题学习进度记录备份
     */
    public final RepeatCourseSectionStudyProgress REPEAT_COURSE_SECTION_STUDY_PROGRESS = RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS;

    /**
     * 分表配置表
     */
    public final ShardingConfig SHARDING_CONFIG = ShardingConfig.SHARDING_CONFIG;

    /**
     * 学习log表按部门分表配置表
     */
    public final SplitLogConfig SPLIT_LOG_CONFIG = SplitLogConfig.SPLIT_LOG_CONFIG;

    /**
     * The table <code>course-study.t_split_log_time</code>.
     */
    public final SplitLogTime SPLIT_LOG_TIME = SplitLogTime.SPLIT_LOG_TIME;

    /**
     * 按省分表配置表
     */
    public final SplitTableConfig SPLIT_TABLE_CONFIG = SplitTableConfig.SPLIT_TABLE_CONFIG;

    /**
     * The table <code>course-study.t_split_table_count</code>.
     */
    public final SplitTableCount SPLIT_TABLE_COUNT = SplitTableCount.SPLIT_TABLE_COUNT;

    /**
     * The table <code>course-study.t_split_table_time</code>.
     */
    public final SplitTableTime SPLIT_TABLE_TIME = SplitTableTime.SPLIT_TABLE_TIME;

    /**
     * 学习活动配置表
     */
    public final StudyActivityConfig STUDY_ACTIVITY_CONFIG = StudyActivityConfig.STUDY_ACTIVITY_CONFIG;

    /**
     * 学习体会表
     */
    public final StudyExperience STUDY_EXPERIENCE = StudyExperience.STUDY_EXPERIENCE;

    /**
     * 学习推送受众对象表(连接推送表和受众项表)
     */
    public final StudyPushAudienceObject STUDY_PUSH_AUDIENCE_OBJECT = StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT;

    /**
     * 学习推送基本信息表
     */
    public final StudyPushInfo STUDY_PUSH_INFO = StudyPushInfo.STUDY_PUSH_INFO;

    /**
     * 学习推送对象表
     */
    public final StudyPushObject STUDY_PUSH_OBJECT = StudyPushObject.STUDY_PUSH_OBJECT;

    /**
     * 推送记录表
     */
    public final StudyPushRecord STUDY_PUSH_RECORD = StudyPushRecord.STUDY_PUSH_RECORD;

    /**
     * 推送上架通知表
     */
    public final StudyPushShelves STUDY_PUSH_SHELVES = StudyPushShelves.STUDY_PUSH_SHELVES;

    /**
     * The table <code>course-study.t_study_record_2017</code>.
     */
    public final StudyRecord_2017 STUDY_RECORD_2017 = StudyRecord_2017.STUDY_RECORD_2017;

    /**
     * 专题任务信息
     */
    public final StudyTask STUDY_TASK = StudyTask.STUDY_TASK;

    /**
     * 任务附件
     */
    public final StudyTaskAttachment STUDY_TASK_ATTACHMENT = StudyTaskAttachment.STUDY_TASK_ATTACHMENT;

    /**
     * 任务审核人列表
     */
    public final StudyTaskAuditMember STUDY_TASK_AUDIT_MEMBER = StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER;

    /**
     * 专题banner广告
     */
    public final SubjectAdvertising SUBJECT_ADVERTISING = SubjectAdvertising.SUBJECT_ADVERTISING;

    /**
     * The table <code>course-study.t_subject_course</code>.
     */
    public final SubjectCourse SUBJECT_COURSE = SubjectCourse.SUBJECT_COURSE;

    /**
     * The table <code>course-study.t_subject_direction</code>.
     */
    public final SubjectDirection SUBJECT_DIRECTION = SubjectDirection.SUBJECT_DIRECTION;

    /**
     * 重塑专题人员证书黑名单
     */
    public final SubjectMemberBlacklist SUBJECT_MEMBER_BLACKLIST = SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST;

    /**
     * The table <code>course-study.t_subject_problem</code>.
     */
    public final SubjectProblem SUBJECT_PROBLEM = SubjectProblem.SUBJECT_PROBLEM;

    /**
     * The table <code>course-study.t_subject_rank</code>.
     */
    public final SubjectRank SUBJECT_RANK = SubjectRank.SUBJECT_RANK;

    /**
     * 专题推荐表
     */
    public final SubjectRecommend SUBJECT_RECOMMEND = SubjectRecommend.SUBJECT_RECOMMEND;

    /**
     * 新动能专题角色默认评论表
     */
    public final SubjectRoleComment SUBJECT_ROLE_COMMENT = SubjectRoleComment.SUBJECT_ROLE_COMMENT;

    /**
     * The table <code>course-study.t_subject_role_detail</code>.
     */
    public final SubjectRoleDetail SUBJECT_ROLE_DETAIL = SubjectRoleDetail.SUBJECT_ROLE_DETAIL;

    /**
     * 专题资源学习日志(流水)，记录单次学习动作
     */
    public final SubjectSectionStudyLog SUBJECT_SECTION_STUDY_LOG = SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG;

    /**
     * The table <code>course-study.t_subject_section_study_log_ah</code>.
     */
    public final SubjectSectionStudyLogAh SUBJECT_SECTION_STUDY_LOG_AH = SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH;

    /**
     * The table <code>course-study.t_subject_section_study_log_bj</code>.
     */
    public final SubjectSectionStudyLogBj SUBJECT_SECTION_STUDY_LOG_BJ = SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_cm</code>.
     */
    public final SubjectSectionStudyLogCm SUBJECT_SECTION_STUDY_LOG_CM = SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM;

    /**
     * The table <code>course-study.t_subject_section_study_log_cq</code>.
     */
    public final SubjectSectionStudyLogCq SUBJECT_SECTION_STUDY_LOG_CQ = SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ;

    /**
     * The table <code>course-study.t_subject_section_study_log_eb</code>.
     */
    public final SubjectSectionStudyLogEb SUBJECT_SECTION_STUDY_LOG_EB = SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB;

    /**
     * The table <code>course-study.t_subject_section_study_log_fj</code>.
     */
    public final SubjectSectionStudyLogFj SUBJECT_SECTION_STUDY_LOG_FJ = SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_gd</code>.
     */
    public final SubjectSectionStudyLogGd SUBJECT_SECTION_STUDY_LOG_GD = SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD;

    /**
     * The table <code>course-study.t_subject_section_study_log_gs</code>.
     */
    public final SubjectSectionStudyLogGs SUBJECT_SECTION_STUDY_LOG_GS = SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS;

    /**
     * The table <code>course-study.t_subject_section_study_log_gx</code>.
     */
    public final SubjectSectionStudyLogGx SUBJECT_SECTION_STUDY_LOG_GX = SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX;

    /**
     * The table <code>course-study.t_subject_section_study_log_gz</code>.
     */
    public final SubjectSectionStudyLogGz SUBJECT_SECTION_STUDY_LOG_GZ = SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ;

    /**
     * The table <code>course-study.t_subject_section_study_log_hb</code>.
     */
    public final SubjectSectionStudyLogHb SUBJECT_SECTION_STUDY_LOG_HB = SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB;

    /**
     * The table <code>course-study.t_subject_section_study_log_hl</code>.
     */
    public final SubjectSectionStudyLogHl SUBJECT_SECTION_STUDY_LOG_HL = SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL;

    /**
     * The table <code>course-study.t_subject_section_study_log_hn</code>.
     */
    public final SubjectSectionStudyLogHn SUBJECT_SECTION_STUDY_LOG_HN = SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN;

    /**
     * The table <code>course-study.t_subject_section_study_log_jl</code>.
     */
    public final SubjectSectionStudyLogJl SUBJECT_SECTION_STUDY_LOG_JL = SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL;

    /**
     * The table <code>course-study.t_subject_section_study_log_js</code>.
     */
    public final SubjectSectionStudyLogJs SUBJECT_SECTION_STUDY_LOG_JS = SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS;

    /**
     * The table <code>course-study.t_subject_section_study_log_jx</code>.
     */
    public final SubjectSectionStudyLogJx SUBJECT_SECTION_STUDY_LOG_JX = SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX;

    /**
     * The table <code>course-study.t_subject_section_study_log_ln</code>.
     */
    public final SubjectSectionStudyLogLn SUBJECT_SECTION_STUDY_LOG_LN = SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN;

    /**
     * The table <code>course-study.t_subject_section_study_log_nm</code>.
     */
    public final SubjectSectionStudyLogNm SUBJECT_SECTION_STUDY_LOG_NM = SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM;

    /**
     * The table <code>course-study.t_subject_section_study_log_nx</code>.
     */
    public final SubjectSectionStudyLogNx SUBJECT_SECTION_STUDY_LOG_NX = SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX;

    /**
     * The table <code>course-study.t_subject_section_study_log_other</code>.
     */
    public final SubjectSectionStudyLogOther SUBJECT_SECTION_STUDY_LOG_OTHER = SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER;

    /**
     * The table <code>course-study.t_subject_section_study_log_qh</code>.
     */
    public final SubjectSectionStudyLogQh SUBJECT_SECTION_STUDY_LOG_QH = SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH;

    /**
     * The table <code>course-study.t_subject_section_study_log_qo</code>.
     */
    public final SubjectSectionStudyLogQo SUBJECT_SECTION_STUDY_LOG_QO = SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO;

    /**
     * The table <code>course-study.t_subject_section_study_log_sc</code>.
     */
    public final SubjectSectionStudyLogSc SUBJECT_SECTION_STUDY_LOG_SC = SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC;

    /**
     * The table <code>course-study.t_subject_section_study_log_sd</code>.
     */
    public final SubjectSectionStudyLogSd SUBJECT_SECTION_STUDY_LOG_SD = SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD;

    /**
     * The table <code>course-study.t_subject_section_study_log_sh</code>.
     */
    public final SubjectSectionStudyLogSh SUBJECT_SECTION_STUDY_LOG_SH = SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH;

    /**
     * The table <code>course-study.t_subject_section_study_log_sn</code>.
     */
    public final SubjectSectionStudyLogSn SUBJECT_SECTION_STUDY_LOG_SN = SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN;

    /**
     * The table <code>course-study.t_subject_section_study_log_sx</code>.
     */
    public final SubjectSectionStudyLogSx SUBJECT_SECTION_STUDY_LOG_SX = SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX;

    /**
     * The table <code>course-study.t_subject_section_study_log_tj</code>.
     */
    public final SubjectSectionStudyLogTj SUBJECT_SECTION_STUDY_LOG_TJ = SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_xj</code>.
     */
    public final SubjectSectionStudyLogXj SUBJECT_SECTION_STUDY_LOG_XJ = SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_xn</code>.
     */
    public final SubjectSectionStudyLogXn SUBJECT_SECTION_STUDY_LOG_XN = SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN;

    /**
     * The table <code>course-study.t_subject_section_study_log_xz</code>.
     */
    public final SubjectSectionStudyLogXz SUBJECT_SECTION_STUDY_LOG_XZ = SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ;

    /**
     * The table <code>course-study.t_subject_section_study_log_yn</code>.
     */
    public final SubjectSectionStudyLogYn SUBJECT_SECTION_STUDY_LOG_YN = SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN;

    /**
     * The table <code>course-study.t_subject_section_study_log_zgtt</code>.
     */
    public final SubjectSectionStudyLogZgtt SUBJECT_SECTION_STUDY_LOG_ZGTT = SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT;

    /**
     * The table <code>course-study.t_subject_section_study_log_zj</code>.
     */
    public final SubjectSectionStudyLogZj SUBJECT_SECTION_STUDY_LOG_ZJ = SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ;

    /**
     * The table <code>course-study.t_subject_section_study_log_zx</code>.
     */
    public final SubjectSectionStudyLogZx SUBJECT_SECTION_STUDY_LOG_ZX = SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX;

    /**
     * The table <code>course-study.t_subject_study_day_exception</code>.
     */
    public final SubjectStudyDayException SUBJECT_STUDY_DAY_EXCEPTION = SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION;

    /**
     * The table <code>course-study.t_subject_study_log_ah_day</code>.
     */
    public final SubjectStudyLogAhDay SUBJECT_STUDY_LOG_AH_DAY = SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_bj_day</code>.
     */
    public final SubjectStudyLogBjDay SUBJECT_STUDY_LOG_BJ_DAY = SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_cm_day</code>.
     */
    public final SubjectStudyLogCmDay SUBJECT_STUDY_LOG_CM_DAY = SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_cq_day</code>.
     */
    public final SubjectStudyLogCqDay SUBJECT_STUDY_LOG_CQ_DAY = SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_eb_day</code>.
     */
    public final SubjectStudyLogEbDay SUBJECT_STUDY_LOG_EB_DAY = SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_exception</code>.
     */
    public final SubjectStudyLogException SUBJECT_STUDY_LOG_EXCEPTION = SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION;

    /**
     * The table <code>course-study.t_subject_study_log_fj_day</code>.
     */
    public final SubjectStudyLogFjDay SUBJECT_STUDY_LOG_FJ_DAY = SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gd_day</code>.
     */
    public final SubjectStudyLogGdDay SUBJECT_STUDY_LOG_GD_DAY = SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gs_day</code>.
     */
    public final SubjectStudyLogGsDay SUBJECT_STUDY_LOG_GS_DAY = SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gx_day</code>.
     */
    public final SubjectStudyLogGxDay SUBJECT_STUDY_LOG_GX_DAY = SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_gz_day</code>.
     */
    public final SubjectStudyLogGzDay SUBJECT_STUDY_LOG_GZ_DAY = SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hb_day</code>.
     */
    public final SubjectStudyLogHbDay SUBJECT_STUDY_LOG_HB_DAY = SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hl_day</code>.
     */
    public final SubjectStudyLogHlDay SUBJECT_STUDY_LOG_HL_DAY = SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_hn_day</code>.
     */
    public final SubjectStudyLogHnDay SUBJECT_STUDY_LOG_HN_DAY = SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_jl_day</code>.
     */
    public final SubjectStudyLogJlDay SUBJECT_STUDY_LOG_JL_DAY = SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_js_day</code>.
     */
    public final SubjectStudyLogJsDay SUBJECT_STUDY_LOG_JS_DAY = SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_jx_day</code>.
     */
    public final SubjectStudyLogJxDay SUBJECT_STUDY_LOG_JX_DAY = SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_ln_day</code>.
     */
    public final SubjectStudyLogLnDay SUBJECT_STUDY_LOG_LN_DAY = SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_nm_day</code>.
     */
    public final SubjectStudyLogNmDay SUBJECT_STUDY_LOG_NM_DAY = SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_nx_day</code>.
     */
    public final SubjectStudyLogNxDay SUBJECT_STUDY_LOG_NX_DAY = SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_other_day</code>.
     */
    public final SubjectStudyLogOtherDay SUBJECT_STUDY_LOG_OTHER_DAY = SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_qh_day</code>.
     */
    public final SubjectStudyLogQhDay SUBJECT_STUDY_LOG_QH_DAY = SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_qo_day</code>.
     */
    public final SubjectStudyLogQoDay SUBJECT_STUDY_LOG_QO_DAY = SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sc_day</code>.
     */
    public final SubjectStudyLogScDay SUBJECT_STUDY_LOG_SC_DAY = SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sd_day</code>.
     */
    public final SubjectStudyLogSdDay SUBJECT_STUDY_LOG_SD_DAY = SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sh_day</code>.
     */
    public final SubjectStudyLogShDay SUBJECT_STUDY_LOG_SH_DAY = SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sn_day</code>.
     */
    public final SubjectStudyLogSnDay SUBJECT_STUDY_LOG_SN_DAY = SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_sx_day</code>.
     */
    public final SubjectStudyLogSxDay SUBJECT_STUDY_LOG_SX_DAY = SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_tj_day</code>.
     */
    public final SubjectStudyLogTjDay SUBJECT_STUDY_LOG_TJ_DAY = SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xj_day</code>.
     */
    public final SubjectStudyLogXjDay SUBJECT_STUDY_LOG_XJ_DAY = SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xn_day</code>.
     */
    public final SubjectStudyLogXnDay SUBJECT_STUDY_LOG_XN_DAY = SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_xz_day</code>.
     */
    public final SubjectStudyLogXzDay SUBJECT_STUDY_LOG_XZ_DAY = SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_yn_day</code>.
     */
    public final SubjectStudyLogYnDay SUBJECT_STUDY_LOG_YN_DAY = SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zgtt_day</code>.
     */
    public final SubjectStudyLogZgttDay SUBJECT_STUDY_LOG_ZGTT_DAY = SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zj_day</code>.
     */
    public final SubjectStudyLogZjDay SUBJECT_STUDY_LOG_ZJ_DAY = SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY;

    /**
     * The table <code>course-study.t_subject_study_log_zx_day</code>.
     */
    public final SubjectStudyLogZxDay SUBJECT_STUDY_LOG_ZX_DAY = SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY;

    /**
     * 专题文字区域
     */
    public final SubjectTextArea SUBJECT_TEXT_AREA = SubjectTextArea.SUBJECT_TEXT_AREA;

    /**
     * 专题年度账单
     */
    public final SubjectYearBill SUBJECT_YEAR_BILL = SubjectYearBill.SUBJECT_YEAR_BILL;

    /**
     * The table <code>course-study.t_summary</code>.
     */
    public final Summary SUMMARY = Summary.SUMMARY;

    /**
     * 供应商表
     */
    public final Supplier SUPPLIER = Supplier.SUPPLIER;

    /**
     * 临时用户记录
     */
    public final TempMember TEMP_MEMBER = TempMember.TEMP_MEMBER;

    /**
     * 专题数据无法自动修复数据表
     */
    public final TempRepairCourse TEMP_REPAIR_COURSE = TempRepairCourse.TEMP_REPAIR_COURSE;

    /**
     * The table <code>course-study.t_temp_repair_subject</code>.
     */
    public final TempRepairSubject TEMP_REPAIR_SUBJECT = TempRepairSubject.TEMP_REPAIR_SUBJECT;

    /**
     * 超过24小时时长交叉章节log备份
     */
    public final TempSectionStudyLogGt_24 TEMP_SECTION_STUDY_LOG_GT_24 = TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24;

    /**
     * 专题Log重复流水删除备份
     */
    public final TempSubjectSectionStudyLog TEMP_SUBJECT_SECTION_STUDY_LOG = TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG;

    /**
     * 专题班
     */
    public final Thematic THEMATIC = Thematic.THEMATIC;

    /**
     * 专题班附件
     */
    public final ThematicAttachment THEMATIC_ATTACHMENT = ThematicAttachment.THEMATIC_ATTACHMENT;

    /**
     * 专题班主题章节
     */
    public final ThematicChapter THEMATIC_CHAPTER = ThematicChapter.THEMATIC_CHAPTER;

    /**
     * 专题班课程
     */
    public final ThematicChapterSection THEMATIC_CHAPTER_SECTION = ThematicChapterSection.THEMATIC_CHAPTER_SECTION;

    /**
     * 专题班用户
     */
    public final ThematicMember THEMATIC_MEMBER = ThematicMember.THEMATIC_MEMBER;

    /**
     * 专题班公告
     */
    public final ThematicNotice THEMATIC_NOTICE = ThematicNotice.THEMATIC_NOTICE;

    /**
     * 专题班班务
     */
    public final ThematicWork THEMATIC_WORK = ThematicWork.THEMATIC_WORK;

    /**
     * 第三方接口调用记录表
     */
    public final ThirdPartyCallRecord THIRD_PARTY_CALL_RECORD = ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD;

    /**
     * 第三方课程表
     */
    public final ThirdPartyCourseInfo THIRD_PARTY_COURSE_INFO = ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO;

    /**
     * 第三方课程学习记录进度表
     */
    public final ThirdPartyCourseStudyProgress THIRD_PARTY_COURSE_STUDY_PROGRESS = ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS;

    /**
     * The table <code>course-study.t_topic</code>.
     */
    public final Topic TOPIC = Topic.TOPIC;

    /**
     * The table <code>course-study.t_topic_object</code>.
     */
    public final TopicObject TOPIC_OBJECT = TopicObject.TOPIC_OBJECT;

    /**
     * 白名单登陆异常记录
     */
    public final WhiteRecord WHITE_RECORD = WhiteRecord.WHITE_RECORD;

    /**
     * 课程节学习进度(2022反复倡廉课程)
     */
    public final CourseSectionStudyProgressFfclc_2022 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022;

    /**
     * 课程清单表（党建云屏使用）
     */
    public final CourseInfoDjyp COURSE_INFO_DJYP = com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP;
    /**
     * 多维度评分表
     */
    public final MultidimensionalScoring MULTIDIMENSIONAL_SCORING = com.zxy.product.course.jooq.tables.MultidimensionalScoring.MULTIDIMENSIONAL_SCORING;

    /**
     * 学员评分表
     */
    public final MultidimensionalScoringSubject MULTIDIMENSIONAL_SCORING_SUBJECT = com.zxy.product.course.jooq.tables.MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT;

    /**
     * 学员评分表0
     */
    public final MultidimensionalStudentScoreSheet_00 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00;

    /**
     * 学员评分表1
     */
    public final MultidimensionalStudentScoreSheet_01 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01;

    /**
     * 学员评分表2
     */
    public final MultidimensionalStudentScoreSheet_02 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02;

    /**
     * 学员评分表3
     */
    public final MultidimensionalStudentScoreSheet_03 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03;

    /**
     * 学员评分表4
     */
    public final MultidimensionalStudentScoreSheet_04 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04;

    /**
     * 学员评分表5
     */
    public final MultidimensionalStudentScoreSheet_05 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05;

    /**
     * 学员评分表6
     */
    public final MultidimensionalStudentScoreSheet_06 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06;

    /**
     * 学员评分表7
     */
    public final MultidimensionalStudentScoreSheet_07 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07;

    /**
     * 学员评分表8
     */
    public final MultidimensionalStudentScoreSheet_08 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08;

    /**
     * 学员评分表9
     */
    public final MultidimensionalStudentScoreSheet_09 MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09 = com.zxy.product.course.jooq.tables.MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09;

    /**
     * 学习活动-任务关联表
     */
    public final CourseStudyPlanConfig COURSE_STUDY_PLAN_CONFIG = com.zxy.product.course.jooq.tables.CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG;
    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_00 COURSE_STUDY_PROGRESS_ARCHIVED_00 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_01 COURSE_STUDY_PROGRESS_ARCHIVED_01 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_02 COURSE_STUDY_PROGRESS_ARCHIVED_02 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_03 COURSE_STUDY_PROGRESS_ARCHIVED_03 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_04 COURSE_STUDY_PROGRESS_ARCHIVED_04 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_05 COURSE_STUDY_PROGRESS_ARCHIVED_05 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_06 COURSE_STUDY_PROGRESS_ARCHIVED_06 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_07 COURSE_STUDY_PROGRESS_ARCHIVED_07 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_08 COURSE_STUDY_PROGRESS_ARCHIVED_08 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_09 COURSE_STUDY_PROGRESS_ARCHIVED_09 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_10 COURSE_STUDY_PROGRESS_ARCHIVED_10 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_11 COURSE_STUDY_PROGRESS_ARCHIVED_11 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_12 COURSE_STUDY_PROGRESS_ARCHIVED_12 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_13 COURSE_STUDY_PROGRESS_ARCHIVED_13 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_14 COURSE_STUDY_PROGRESS_ARCHIVED_14 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_15 COURSE_STUDY_PROGRESS_ARCHIVED_15 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_16 COURSE_STUDY_PROGRESS_ARCHIVED_16 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_17 COURSE_STUDY_PROGRESS_ARCHIVED_17 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_18 COURSE_STUDY_PROGRESS_ARCHIVED_18 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18;

    /**
     * course_study_progress已归档数据表
     */
    public final CourseStudyProgressArchived_19 COURSE_STUDY_PROGRESS_ARCHIVED_19 = com.zxy.product.course.jooq.tables.CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19;


    /**
     * 智能播报
     */
    public final IntelligentBroadcast INTELLIGENT_BROADCAST = com.zxy.product.course.jooq.tables.IntelligentBroadcast.INTELLIGENT_BROADCAST;

    /**
     * 专题管理员表
     */
    public final SubjectTopicManager SUBJECT_TOPIC_MANAGER = com.zxy.product.course.jooq.tables.SubjectTopicManager.SUBJECT_TOPIC_MANAGER;

    /**
     * 智能笔记表
     */
    public final IntelligentNote INTELLIGENT_NOTE = com.zxy.product.course.jooq.tables.IntelligentNote.INTELLIGENT_NOTE;

    /**
     * 智能笔记标签表
     */
    public final IntelligentNoteBookmark INTELLIGENT_NOTE_BOOKMARK = com.zxy.product.course.jooq.tables.IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK;

    /**
     * 字幕表
     */
    public final Caption CAPTION = com.zxy.product.course.jooq.tables.Caption.CAPTION;

    /**
     * 课程红船审核记录表
     */
    public final CourseRedShipAuditDetail COURSE_RED_SHIP_AUDIT_DETAIL = com.zxy.product.course.jooq.tables.CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL;

    /**
     * 课程红船审核版本号记录表
     */
    public final CourseRedShipAuditVersion COURSE_RED_SHIP_AUDIT_VERSION = com.zxy.product.course.jooq.tables.CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION;

    /**
     * 课程红船审核-章节记录表
     */
    public final CourseRedShipAuditChapterSection COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION = com.zxy.product.course.jooq.tables.CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION;
    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressAh COURSE_SECTION_STUDY_PROGRESS_AH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressBj COURSE_SECTION_STUDY_PROGRESS_BJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressCm COURSE_SECTION_STUDY_PROGRESS_CM = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressCq COURSE_SECTION_STUDY_PROGRESS_CQ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressEb COURSE_SECTION_STUDY_PROGRESS_EB = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressFj COURSE_SECTION_STUDY_PROGRESS_FJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressGd COURSE_SECTION_STUDY_PROGRESS_GD = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressGs COURSE_SECTION_STUDY_PROGRESS_GS = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressGx COURSE_SECTION_STUDY_PROGRESS_GX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressGz COURSE_SECTION_STUDY_PROGRESS_GZ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressHb COURSE_SECTION_STUDY_PROGRESS_HB = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressHl COURSE_SECTION_STUDY_PROGRESS_HL = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressHn COURSE_SECTION_STUDY_PROGRESS_HN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressJl COURSE_SECTION_STUDY_PROGRESS_JL = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressJs COURSE_SECTION_STUDY_PROGRESS_JS = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressJx COURSE_SECTION_STUDY_PROGRESS_JX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressLn COURSE_SECTION_STUDY_PROGRESS_LN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressNm COURSE_SECTION_STUDY_PROGRESS_NM = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressNx COURSE_SECTION_STUDY_PROGRESS_NX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressOther COURSE_SECTION_STUDY_PROGRESS_OTHER = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressQh COURSE_SECTION_STUDY_PROGRESS_QH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressQo COURSE_SECTION_STUDY_PROGRESS_QO = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressSc COURSE_SECTION_STUDY_PROGRESS_SC = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressSd COURSE_SECTION_STUDY_PROGRESS_SD = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressSh COURSE_SECTION_STUDY_PROGRESS_SH = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressSn COURSE_SECTION_STUDY_PROGRESS_SN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressSx COURSE_SECTION_STUDY_PROGRESS_SX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressTj COURSE_SECTION_STUDY_PROGRESS_TJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressXj COURSE_SECTION_STUDY_PROGRESS_XJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressXn COURSE_SECTION_STUDY_PROGRESS_XN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressXz COURSE_SECTION_STUDY_PROGRESS_XZ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressYn COURSE_SECTION_STUDY_PROGRESS_YN = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressZgtt COURSE_SECTION_STUDY_PROGRESS_ZGTT = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressZj COURSE_SECTION_STUDY_PROGRESS_ZJ = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ;

    /**
     * 课程节学习进度分表
     */
    public final CourseSectionStudyProgressZx COURSE_SECTION_STUDY_PROGRESS_ZX = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX;

    /**
     * 删除记录表
     */
    public final DeleteDataCourse DELETE_DATA_COURSE = com.zxy.product.course.jooq.tables.DeleteDataCourse.DELETE_DATA_COURSE;

    /**
     * 2022年度账单
     */
    public final AnnualBill_2022 ANNUAL_BILL_2022 = com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022;

    /**
     * 分组信息表
     */
    public final AuthenticatedGroup AUTHENTICATED_GROUP = com.zxy.product.course.jooq.tables.AuthenticatedGroup.AUTHENTICATED_GROUP;

    /**
     * 认证专区表
     */
    public final AuthenticatedZone AUTHENTICATED_ZONE = com.zxy.product.course.jooq.tables.AuthenticatedZone.AUTHENTICATED_ZONE;

    /**
     * 认证专区-分组关系表
     */
    public final AuthenticatedZoneGroup AUTHENTICATED_ZONE_GROUP = com.zxy.product.course.jooq.tables.AuthenticatedZoneGroup.AUTHENTICATED_ZONE_GROUP;

    /**
     * 子认证表
     */
    public final SubAuthenticated SUB_AUTHENTICATED = com.zxy.product.course.jooq.tables.SubAuthenticated.SUB_AUTHENTICATED;

    /**
     * 子认证-学员-证书记录表
     */
    public final SubAuthenticatedCertificateRecord SUB_AUTHENTICATED_CERTIFICATE_RECORD = com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD;

    /**
     * 子认证内容配置表
     */
    public final SubAuthenticatedContentConfigure SUB_AUTHENTICATED_CONTENT_CONFIGURE = com.zxy.product.course.jooq.tables.SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE;

    /**
     * 子认证-学员学习进度表
     */
    public final SubAuthenticatedCourseProgress SUB_AUTHENTICATED_COURSE_PROGRESS = com.zxy.product.course.jooq.tables.SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS;

    /**
     * 子认证-学员-资料审核、举证材料记录表
     */
    public final SubAuthenticatedResourceAuditRecord SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD = com.zxy.product.course.jooq.tables.SubAuthenticatedResourceAuditRecord.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD;

    /**
     * 子认证-在线学习组-学习内容关联关系表
     */
    public final SubAuthenticatedStudyOnline SUB_AUTHENTICATED_STUDY_ONLINE = com.zxy.product.course.jooq.tables.SubAuthenticatedStudyOnline.SUB_AUTHENTICATED_STUDY_ONLINE;


    /**
     * 子认证-维度表
     */
    public final SubAuthenticatedDimension SUB_AUTHENTICATED_DIMENSION = com.zxy.product.course.jooq.tables.SubAuthenticatedDimension.SUB_AUTHENTICATED_DIMENSION;

    /**
     * 子认证-学员-维度点亮记录表
     */
    public final SubAuthenticatedMemberDimension SUB_AUTHENTICATED_MEMBER_DIMENSION = com.zxy.product.course.jooq.tables.SubAuthenticatedMemberDimension.SUB_AUTHENTICATED_MEMBER_DIMENSION;


    /**
     * 子认证注册表
     */
    public final SubAuthenticatedRegister SUB_AUTHENTICATED_REGISTER = com.zxy.product.course.jooq.tables.SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER;


    /**
     * 业务应急表
     */
    public final BusinessEmergency BUSINESS_EMERGENCY = com.zxy.product.course.jooq.tables.BusinessEmergency.BUSINESS_EMERGENCY;
    /**
     * 课程节学习进度(2023反复倡廉课程)
     */
    public final CourseSectionStudyProgressFfclc_2023 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023;

    /**
     * 课程节学习进度(2023反复倡廉专题)
     */
    public final CourseSectionStudyProgressFfcls_2023 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023;


    /**
     * 报表红名单管理表
     */
    public final ReportRedlist REPORT_REDLIST = com.zxy.product.course.jooq.tables.ReportRedlist.REPORT_REDLIST;


    /**
     * 个人创建短视频明细表
     */
    public final IndividualShortVideoDetails INDIVIDUAL_SHORT_VIDEO_DETAILS = com.zxy.product.course.jooq.tables.IndividualShortVideoDetails.INDIVIDUAL_SHORT_VIDEO_DETAILS;

    /**
     * 个人创建短视频汇总表
     */
    public final IndividualShortVideoSummary INDIVIDUAL_SHORT_VIDEO_SUMMARY = com.zxy.product.course.jooq.tables.IndividualShortVideoSummary.INDIVIDUAL_SHORT_VIDEO_SUMMARY;

    /**
     * 个人短视频学习明细表
     */
    public final PersonalShortVideoLearningDetails PERSONAL_SHORT_VIDEO_LEARNING_DETAILS = com.zxy.product.course.jooq.tables.PersonalShortVideoLearningDetails.PERSONAL_SHORT_VIDEO_LEARNING_DETAILS;

    /**
     * 短视频建设明细表
     */
    public final ShortVideoCreateDetails SHORT_VIDEO_CREATE_DETAILS = com.zxy.product.course.jooq.tables.ShortVideoCreateDetails.SHORT_VIDEO_CREATE_DETAILS;

    /**
     * 短视频报表
     */
    public final ShortVideoReport SHORT_VIDEO_REPORT = com.zxy.product.course.jooq.tables.ShortVideoReport.SHORT_VIDEO_REPORT;

    /**
     * 直播用户签到表 新增
     */
    public final GenseeSignIn GENSEE_SIGN_IN = com.zxy.product.course.jooq.tables.GenseeSignIn.GENSEE_SIGN_IN;

    /**
     * 咪咕直播观看记录表
     */
    public final MiguUserAccess MIGU_USER_ACCESS = com.zxy.product.course.jooq.tables.MiguUserAccess.MIGU_USER_ACCESS;


    /**
     * 咪咕短视频跳转频道配置
     */
    public final MiguConfig MIGU_CONFIG = com.zxy.product.course.jooq.tables.MiguConfig.MIGU_CONFIG;


    /**
     * 课程打点表
     */
    public final CourseMark COURSE_MARK = com.zxy.product.course.jooq.tables.CourseMark.COURSE_MARK;

    /**
     * 虚拟空间直播表
     */
    public final LiveVirtualSpace LIVE_VIRTUAL_SPACE = com.zxy.product.course.jooq.tables.LiveVirtualSpace.LIVE_VIRTUAL_SPACE;

    /**
     * 咪咕直播附件
     */
    public final MiguAttachment MIGU_ATTACHMENT = com.zxy.product.course.jooq.tables.MiguAttachment.MIGU_ATTACHMENT;

    /**
     * 直播分享表
     */
    public final GenseeShare GENSEE_SHARE = com.zxy.product.course.jooq.tables.GenseeShare.GENSEE_SHARE;

    /**
     * 个人短视频每日时长表-短视频人课天表
     */
    public final ShortVideoLogDay SHORT_VIDEO_LOG_DAY = com.zxy.product.course.jooq.tables.ShortVideoLogDay.SHORT_VIDEO_LOG_DAY;

    /**
     * 红船知识审核表
     */
    public final KnowledgeRedShipAudit KNOWLEDGE_RED_SHIP_AUDIT = com.zxy.product.course.jooq.tables.KnowledgeRedShipAudit.KNOWLEDGE_RED_SHIP_AUDIT;


    /**
     * 智能笔记点赞表
     */
    public final NotePraise NOTE_PRAISE = com.zxy.product.course.jooq.tables.NotePraise.NOTE_PRAISE;

    /**
     * 2023年度账单
     */
    public final AnnualBill_2023 ANNUAL_BILL_2023 = com.zxy.product.course.jooq.tables.AnnualBill_2023.ANNUAL_BILL_2023;


    /**
     * 课程节学习进度(2024反复倡廉课程)
     */
    public final CourseSectionStudyProgressFfclc_2024 COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024;

    /**
     * 课程节学习进度(2024反复倡廉专题)
     */
    public final CourseSectionStudyProgressFfcls_2024 COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024 = com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024;

    /**
     * 党建群组日志表（党建云屏使用）
     */
    public final DjLog DJ_LOG = com.zxy.product.course.jooq.tables.DjLog.DJ_LOG;

    /**
     * 直播签到活动
     */
    public final SignIn SIGN_IN = com.zxy.product.course.jooq.tables.SignIn.SIGN_IN;

    /**
     * 智能播报使用次数
     */
    public final BroadcastCount BROADCAST_COUNT = com.zxy.product.course.jooq.tables.BroadcastCount.BROADCAST_COUNT;

    /**
     * The table <code>course-study.t_member_course_hours</code>.
     */
    public final MemberCourseHours MEMBER_COURSE_HOURS = com.zxy.product.course.jooq.tables.MemberCourseHours.MEMBER_COURSE_HOURS;

    /**
     * 党校banner表
     */
    public final PartyBanner PARTY_BANNER = com.zxy.product.course.jooq.tables.PartyBanner.PARTY_BANNER;
    /**
     * 数智导师 模型问题表
     */
    public final AiQuestion AI_QUESTION = com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION;

    /**
     * 数智导师 模型答案表
     */
    public final AiAnswer AI_ANSWER = com.zxy.product.course.jooq.tables.AiAnswer.AI_ANSWER;

    /**
     * 数智导师 模型反馈表
     */
    public final AiFeedback AI_FEEDBACK = com.zxy.product.course.jooq.tables.AiFeedback.AI_FEEDBACK;

    /**
     * 数智导师 模型关联表
     */
    public final AiMentor AI_MENTOR = com.zxy.product.course.jooq.tables.AiMentor.AI_MENTOR;

    /**
     * 数智导师 模型预设实例表
     */
    public final AiPresetExample AI_PRESET_EXAMPLE = com.zxy.product.course.jooq.tables.AiPresetExample.AI_PRESET_EXAMPLE;



    /**
     * 党校党校示范培训配置表
     */
    public final PartyDemonstrationTrainingConfig PARTY_DEMONSTRATION_TRAINING_CONFIG = com.zxy.product.course.jooq.tables.PartyDemonstrationTrainingConfig.PARTY_DEMONSTRATION_TRAINING_CONFIG;

    /**
     * 党校重点专题配置表
     */
    public final PartyFocusConfig PARTY_FOCUS_CONFIG = com.zxy.product.course.jooq.tables.PartyFocusConfig.PARTY_FOCUS_CONFIG;

    /**
     * 党校动态配置表
     */
    public final PartyDynamicNewsConfig PARTY_DYNAMIC_NEWS_CONFIG = com.zxy.product.course.jooq.tables.PartyDynamicNewsConfig.PARTY_DYNAMIC_NEWS_CONFIG;

    /**
     * 高标党建-约课审核
     */
    public final GbCourseAudit GB_COURSE_AUDIT = com.zxy.product.course.jooq.tables.GbCourseAudit.GB_COURSE_AUDIT;

    /**
     * 高标党建-课程分类
     */
    public final GbCourseClassification GB_COURSE_CLASSIFICATION = com.zxy.product.course.jooq.tables.GbCourseClassification.GB_COURSE_CLASSIFICATION;

    /**
     * 高标党建-在线课程配置
     */
    public final GbCourseConfiguration GB_COURSE_CONFIGURATION = com.zxy.product.course.jooq.tables.GbCourseConfiguration.GB_COURSE_CONFIGURATION;

    /**
     * 高标党建-课程库
     */
    public final GbCourseLibrary GB_COURSE_LIBRARY = com.zxy.product.course.jooq.tables.GbCourseLibrary.GB_COURSE_LIBRARY;

    /**
     * 高标党建-关联关系表
     */
    public final GbCourseMiddle GB_COURSE_MIDDLE = com.zxy.product.course.jooq.tables.GbCourseMiddle.GB_COURSE_MIDDLE;

    /**
     * 高标党建-约课记录
     */
    public final GbCourseRecord GB_COURSE_RECORD = com.zxy.product.course.jooq.tables.GbCourseRecord.GB_COURSE_RECORD;

    /**
     * 高标党建-讲师库
     */
    public final GbLecturerLibrary GB_LECTURER_LIBRARY = com.zxy.product.course.jooq.tables.GbLecturerLibrary.GB_LECTURER_LIBRARY;

    /**
     * 高标党建-管理员分类表
     */
    public final GbMember GB_MEMBER = com.zxy.product.course.jooq.tables.GbMember.GB_MEMBER;

    /**
     * 专题-专题计划关联表
     */
    public final SubjectPlanRelated SUBJECT_PLAN_RELATED = com.zxy.product.course.jooq.tables.SubjectPlanRelated.SUBJECT_PLAN_RELATED;

    /**
     * 数智导师-官方笔记表
     */
    public final CoursewareNote COURSEWARE_NOTE = com.zxy.product.course.jooq.tables.CoursewareNote.COURSEWARE_NOTE;

    /**
     * 课件笔记审核表
     */
    public final CoursewareNoteAudit COURSEWARE_NOTE_AUDIT = com.zxy.product.course.jooq.tables.CoursewareNoteAudit.COURSEWARE_NOTE_AUDIT;

    /**
     * 数智导师-官方笔记版本表
     */
    public final CoursewareNoteVersion COURSEWARE_NOTE_VERSION = com.zxy.product.course.jooq.tables.CoursewareNoteVersion.COURSEWARE_NOTE_VERSION;

    /**
     * 课程笔记-（九天传输）
     */
    public final CourseMainNote COURSE_MAIN_NOTE = com.zxy.product.course.jooq.tables.CourseMainNote.COURSE_MAIN_NOTE;

    /**
     * 课程版本笔记-（九天传输）
     */
    public final CourseMainNoteVersion COURSE_MAIN_NOTE_VERSION = com.zxy.product.course.jooq.tables.CourseMainNoteVersion.COURSE_MAIN_NOTE_VERSION;

    /**
     * 课程笔记审核-（九天传输）
     */
    public final CourseMainNoteAudit COURSE_MAIN_NOTE_AUDIT = com.zxy.product.course.jooq.tables.CourseMainNoteAudit.COURSE_MAIN_NOTE_AUDIT;

    /**
     * The table <code>course-study.t_subject_rank_year</code>.
     */
    public final SubjectRankYear SUBJECT_RANK_YEAR = com.zxy.product.course.jooq.tables.SubjectRankYear.SUBJECT_RANK_YEAR;



    /**
     * The table <code>course-study.t_short_video_operation</code>.
     */
    public final ShortVideoOperation SHORT_VIDEO_OPERATION = com.zxy.product.course.jooq.tables.ShortVideoOperation.SHORT_VIDEO_OPERATION;

    /**
     * The table <code>course-study.t_short_video_operation_group</code>.
     */
    public final ShortVideoOperationGroup SHORT_VIDEO_OPERATION_GROUP = com.zxy.product.course.jooq.tables.ShortVideoOperationGroup.SHORT_VIDEO_OPERATION_GROUP;

    /**
     * The table <code>course-study.t_short_video_operation_integral</code>.
     */
    public final ShortVideoOperationIntegral SHORT_VIDEO_OPERATION_INTEGRAL = com.zxy.product.course.jooq.tables.ShortVideoOperationIntegral.SHORT_VIDEO_OPERATION_INTEGRAL;

    /**
     * The table <code>course-study.t_short_video_operation_member</code>.
     */
    public final ShortVideoOperationMember SHORT_VIDEO_OPERATION_MEMBER = com.zxy.product.course.jooq.tables.ShortVideoOperationMember.SHORT_VIDEO_OPERATION_MEMBER;

    /**
     * The table <code>course-study.t_short_video_operation_pick_question</code>.
     */
    public final ShortVideoOperationPickQuestion SHORT_VIDEO_OPERATION_PICK_QUESTION = com.zxy.product.course.jooq.tables.ShortVideoOperationPickQuestion.SHORT_VIDEO_OPERATION_PICK_QUESTION;

    /**
     * The table <code>course-study.t_short_video_operation_question</code>.
     */
    public final ShortVideoOperationQuestion SHORT_VIDEO_OPERATION_QUESTION = com.zxy.product.course.jooq.tables.ShortVideoOperationQuestion.SHORT_VIDEO_OPERATION_QUESTION;

    /**
     * ishow外部请求记录
     */
    public final IshowSdkRequest ISHOW_SDK_REQUEST = com.zxy.product.course.jooq.tables.IshowSdkRequest.ISHOW_SDK_REQUEST;

    /**
     * ishow学习记录
     */
    public final IshowStudyRecord ISHOW_STUDY_RECORD = com.zxy.product.course.jooq.tables.IshowStudyRecord.ISHOW_STUDY_RECORD;

    /**
     * The table <code>course-study.t_study_report_analysis_managers</code>.
     */
    public final StudyReportAnalysisManagers STUDY_REPORT_ANALYSIS_MANAGERS = com.zxy.product.course.jooq.tables.StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS;

    /**
     * 学习助手应用管理
     */
    public final ApplicationConfig APPLICATION_CONFIG = com.zxy.product.course.jooq.tables.ApplicationConfig.APPLICATION_CONFIG;

    /**
     * 能力信息表
     */
    public final Ability ABILITY = com.zxy.product.course.jooq.tables.Ability.ABILITY;

    /**
     * 能力内容关联表
     */
    public final AbilityBusiness ABILITY_BUSINESS = com.zxy.product.course.jooq.tables.AbilityBusiness.ABILITY_BUSINESS;
    /**
     * 学习地图章关联能力表
     */
    public final CourseAbility COURSE_ABILITY = com.zxy.product.course.jooq.tables.CourseAbility.COURSE_ABILITY;

    /**
     * 红色展馆-存储对应平台学习数据
     */
    public final CourseRen COURSE_REN = com.zxy.product.course.jooq.tables.CourseRen.COURSE_REN;

    /**
     * 红色展馆-存储对应平台学习数据
     */
    public final CourseRenFeedback COURSE_REN_FEEDBACK = com.zxy.product.course.jooq.tables.CourseRenFeedback.COURSE_REN_FEEDBACK;


    /**
     * 数智导师——同步表
     */
    public final AiSynchronous AI_SYNCHRONOUS = com.zxy.product.course.jooq.tables.AiSynchronous.AI_SYNCHRONOUS;

    /**
     * 课程在线——限流限频表
     */
    public final CourseOnlineLog COURSE_ONLINE_LOG = com.zxy.product.course.jooq.tables.CourseOnlineLog.COURSE_ONLINE_LOG;
    /**
     * 意见反馈
     */
    public final CourseFeedback COURSE_FEEDBACK = com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK;

    /**
     * 学习助手知识表
     */
    public final CourseKnowledge COURSE_KNOWLEDGE = com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE;

    /**
     * 推荐问题管理表
     */
    public final CourseQuestionRecommend COURSE_QUESTION_RECOMMEND = com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND;

    /**
     * 回答问题时间差
     */
    public final ChatTimeRecord CHAT_TIME_RECORD = com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD;

    /**
     * 数智导师回调表
     */
    public final DigitalMentorCallback DIGITAL_MENTOR_CALLBACK = com.zxy.product.course.jooq.tables.DigitalMentorCallback.DIGITAL_MENTOR_CALLBACK;

    /**
     * 网上党校动态表
     */
    public final PartySchoolNews PARTY_SCHOOL_NEWS = com.zxy.product.course.jooq.tables.PartySchoolNews.PARTY_SCHOOL_NEWS;

    /**
     * 子认证-维度导入表-临时表
     */
    public final SubAuthenticatedTmp SUB_AUTHENTICATED_TMP = com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP;
    /**
     * No further instances allowed
     */
    private CourseStudy() {
        super("course-study", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
            ActivityBanner.ACTIVITY_BANNER,
            ActivityChbn.ACTIVITY_CHBN,
            AnnualBill_2019.ANNUAL_BILL_2019,
            AnnualBill_2021.ANNUAL_BILL_2021,
            AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT,
            AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT,
            AnnualBillExam.ANNUAL_BILL_EXAM,
            AnnualBillStudyday.ANNUAL_BILL_STUDYDAY,
            AudienceItem.AUDIENCE_ITEM,
            AudienceMember.AUDIENCE_MEMBER,
            AudienceObject.AUDIENCE_OBJECT,
            BillConfig.BILL_CONFIG,
            BillConfigLost.BILL_CONFIG_LOST,
            BusinessCertificate.BUSINESS_CERTIFICATE,
            BusinessTopic.BUSINESS_TOPIC,
            CertificateRecord.CERTIFICATE_RECORD,
            CertificateRecordChbn.CERTIFICATE_RECORD_CHBN,
            CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT,
            CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION,
            CompeteCourseInfo.COMPETE_COURSE_INFO,
            CompeteCourseVote.COMPETE_COURSE_VOTE,
            CompeteLecturerCompany.COMPETE_LECTURER_COMPANY,
            CourseAttachment.COURSE_ATTACHMENT,
            CourseCategory.COURSE_CATEGORY,
            CourseCertificateRecord.COURSE_CERTIFICATE_RECORD,
            CourseChapter.COURSE_CHAPTER,
            CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE,
            CourseChapterSection.COURSE_CHAPTER_SECTION,
            CourseCurrency.COURSE_CURRENCY,
            CourseException.COURSE_EXCEPTION,
            CourseInfo.COURSE_INFO,
            CourseInfoCategory.COURSE_INFO_CATEGORY,
            CourseInform.COURSE_INFORM,
            CourseNote.COURSE_NOTE,
            CoursePhoto.COURSE_PHOTO,
            CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD,
            CourseRecommend.COURSE_RECOMMEND,
            CourseRecord.COURSE_RECORD,
            CourseRegister.COURSE_REGISTER,
            CourseScore.COURSE_SCORE,
            CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT,
            CourseSectionScorm.COURSE_SECTION_SCORM,
            CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS,
            CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG,
            CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH,
            CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY,
            CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ,
            CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY,
            CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM,
            CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY,
            CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ,
            CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY,
            CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB,
            CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY,
            CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ,
            CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY,
            CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD,
            CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY,
            CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS,
            CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY,
            CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX,
            CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY,
            CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ,
            CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY,
            CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB,
            CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY,
            CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL,
            CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY,
            CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN,
            CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY,
            CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL,
            CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY,
            CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS,
            CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY,
            CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX,
            CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY,
            CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN,
            CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY,
            CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM,
            CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY,
            CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX,
            CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY,
            CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER,
            CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY,
            CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH,
            CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY,
            CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO,
            CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY,
            CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC,
            CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY,
            CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD,
            CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY,
            CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH,
            CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY,
            CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN,
            CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY,
            CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX,
            CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY,
            CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ,
            CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY,
            CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ,
            CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY,
            CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN,
            CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY,
            CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ,
            CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY,
            CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN,
            CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY,
            CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT,
            CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY,
            CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ,
            CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY,
            CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX,
            CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY,
            CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS,
            CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC,
            CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS,
            CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC,
            CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS,
            CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022,
            CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS,
            CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC,
            CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS,
            CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS,
            CourseSequence.COURSE_SEQUENCE,
            CourseShelves.COURSE_SHELVES,
            CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017,
            CourseStudyProgress.COURSE_STUDY_PROGRESS,
            CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH,
            CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ,
            CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM,
            CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ,
            CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB,
            CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ,
            CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD,
            CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS,
            CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX,
            CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ,
            CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB,
            CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL,
            CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN,
            CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL,
            CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS,
            CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX,
            CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN,
            CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM,
            CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX,
            CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER,
            CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH,
            CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO,
            CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC,
            CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD,
            CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH,
            CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN,
            CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX,
            CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ,
            CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ,
            CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN,
            CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ,
            CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN,
            CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT,
            CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ,
            CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX,
            CourseTopic.COURSE_TOPIC,
            CourseVersion.COURSE_VERSION,
            DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX,
            DjClassify.DJ_CLASSIFY,
            DjResource.DJ_RESOURCE,
            Exam.EXAM,
            ExamRecord.EXAM_RECORD,
            GenseeBusiness.GENSEE_BUSINESS,
            GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS,
            GenseeLecturer.GENSEE_LECTURER,
            GenseeSubscription.GENSEE_SUBSCRIPTION,
            GenseeTopic.GENSEE_TOPIC,
            GenseeUserAccess.GENSEE_USER_ACCESS,
            GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY,
            GenseeWebCast.GENSEE_WEB_CAST,
            GrantDetail.GRANT_DETAIL,
            Job.JOB,
            KnowledgeCategory.KNOWLEDGE_CATEGORY,
            KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD,
            KnowledgeInfo.KNOWLEDGE_INFO,
            KnowledgeMonthList.KNOWLEDGE_MONTH_LIST,
            KnowledgeTopic.KNOWLEDGE_TOPIC,
            KnowledgeUseRecord.KNOWLEDGE_USE_RECORD,
            KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD,
            Member.MEMBER,
            MemberCourseMonth.MEMBER_COURSE_MONTH,
            MemberDetail.MEMBER_DETAIL,
            MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH,
            MemberParty.MEMBER_PARTY,
            MemberStatistics.MEMBER_STATISTICS,
            MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES,
            Notice.NOTICE,
            OfflineClass.OFFLINE_CLASS,
            OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE,
            OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER,
            OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER,
            OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER,
            Organization.ORGANIZATION,
            OrganizationDetail.ORGANIZATION_DETAIL,
            OrgTableCount.ORG_TABLE_COUNT,
            PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY,
            PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH,
            PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY,
            PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH,
            PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR,
            PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION,
            PartyData.PARTY_DATA,
            PartyHotTopic.PARTY_HOT_TOPIC,
            PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE,
            PartyLeader.PARTY_LEADER,
            PartyOrganization.PARTY_ORGANIZATION,
            PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS,
            PartyRecommendation.PARTY_RECOMMENDATION,
            PartyRecommendResult.PARTY_RECOMMEND_RESULT,
            PartyRecommendSpare.PARTY_RECOMMEND_SPARE,
            PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY,
            PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH,
            PartyTopic.PARTY_TOPIC,
            PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG,
            PccwResult.PCCW_RESULT,
            PccwResultBusiness.PCCW_RESULT_BUSINESS,
            PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY,
            PersonYearBill.PERSON_YEAR_BILL,
            Position.POSITION,
            PositionOld.POSITION_OLD,
            QuestionnaireMould.QUESTIONNAIRE_MOULD,
            QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION,
            QuestionnaireQuestion.QUESTIONNAIRE_QUESTION,
            RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG,
            RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS,
            RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS,
            RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL,
            RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL,
            RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS,
            RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS,
            RemodelingRoleDetail.REMODELING_ROLE_DETAIL,
            RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG,
            RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS,
            ShardingConfig.SHARDING_CONFIG,
            SplitLogConfig.SPLIT_LOG_CONFIG,
            SplitLogTime.SPLIT_LOG_TIME,
            SplitTableConfig.SPLIT_TABLE_CONFIG,
            SplitTableCount.SPLIT_TABLE_COUNT,
            SplitTableTime.SPLIT_TABLE_TIME,
            StudyActivityConfig.STUDY_ACTIVITY_CONFIG,
            StudyExperience.STUDY_EXPERIENCE,
            StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT,
            StudyPushInfo.STUDY_PUSH_INFO,
            StudyPushObject.STUDY_PUSH_OBJECT,
            StudyPushRecord.STUDY_PUSH_RECORD,
            StudyPushShelves.STUDY_PUSH_SHELVES,
            StudyRecord_2017.STUDY_RECORD_2017,
            StudyTask.STUDY_TASK,
            StudyTaskAttachment.STUDY_TASK_ATTACHMENT,
            StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER,
            SubjectAdvertising.SUBJECT_ADVERTISING,
            SubjectCourse.SUBJECT_COURSE,
            SubjectDirection.SUBJECT_DIRECTION,
            SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST,
            SubjectProblem.SUBJECT_PROBLEM,
            SubjectRank.SUBJECT_RANK,
            SubjectRecommend.SUBJECT_RECOMMEND,
            SubjectRoleComment.SUBJECT_ROLE_COMMENT,
            SubjectRoleDetail.SUBJECT_ROLE_DETAIL,
            SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG,
            SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH,
            SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ,
            SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM,
            SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ,
            SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB,
            SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ,
            SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD,
            SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS,
            SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX,
            SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ,
            SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB,
            SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL,
            SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN,
            SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL,
            SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS,
            SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX,
            SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN,
            SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM,
            SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX,
            SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER,
            SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH,
            SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO,
            SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC,
            SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD,
            SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH,
            SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN,
            SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX,
            SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ,
            SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ,
            SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN,
            SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ,
            SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN,
            SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT,
            SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ,
            SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX,
            SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION,
            SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY,
            SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY,
            SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY,
            SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY,
            SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY,
            SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION,
            SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY,
            SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY,
            SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY,
            SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY,
            SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY,
            SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY,
            SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY,
            SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY,
            SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY,
            SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY,
            SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY,
            SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY,
            SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY,
            SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY,
            SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY,
            SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY,
            SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY,
            SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY,
            SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY,
            SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY,
            SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY,
            SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY,
            SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY,
            SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY,
            SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY,
            SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY,
            SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY,
            SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY,
            SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY,
            SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY,
            SubjectTextArea.SUBJECT_TEXT_AREA,
            SubjectYearBill.SUBJECT_YEAR_BILL,
            Summary.SUMMARY,
            Supplier.SUPPLIER,
            TempMember.TEMP_MEMBER,
            TempRepairCourse.TEMP_REPAIR_COURSE,
            TempRepairSubject.TEMP_REPAIR_SUBJECT,
            TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24,
            TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG,
            Thematic.THEMATIC,
            ThematicAttachment.THEMATIC_ATTACHMENT,
            ThematicChapter.THEMATIC_CHAPTER,
            ThematicChapterSection.THEMATIC_CHAPTER_SECTION,
            ThematicMember.THEMATIC_MEMBER,
            ThematicNotice.THEMATIC_NOTICE,
            ThematicWork.THEMATIC_WORK,
            ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD,
            ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO,
            ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS,
            Topic.TOPIC,
            TopicObject.TOPIC_OBJECT,
            WhiteRecord.WHITE_RECORD,
            CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022,
            CourseInfoDjyp.COURSE_INFO_DJYP,
            MultidimensionalScoring.MULTIDIMENSIONAL_SCORING,
            MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT,
            MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00,
            MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01,
            MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02,
            MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03,
            MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04,
            MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05,
            MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06,
            MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07,
            MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08,
            MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09,
            CourseInfoDjyp.COURSE_INFO_DJYP,
            CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG,
            IntelligentBroadcast.INTELLIGENT_BROADCAST,
            CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00,
            CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01,
            CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02,
            CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03,
            CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04,
            CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05,
            CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06,
            CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07,
            CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08,
            CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09,
            CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10,
            CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11,
            CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12,
            CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13,
            CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14,
            CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15,
            CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16,
            CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17,
            CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18,
            CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19,
            SubjectTopicManager.SUBJECT_TOPIC_MANAGER,
            IntelligentNote.INTELLIGENT_NOTE,
            IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK,
            Caption.CAPTION,
            CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL,
            CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION,
            CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION,
            CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG,
            ActivityBanner.ACTIVITY_BANNER,
            ActivityChbn.ACTIVITY_CHBN,
            AnnualBill_2019.ANNUAL_BILL_2019,
            AnnualBill_2021.ANNUAL_BILL_2021,
            AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT,
            AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT,
            AnnualBillExam.ANNUAL_BILL_EXAM,
            AnnualBillStudyday.ANNUAL_BILL_STUDYDAY,
            AudienceItem.AUDIENCE_ITEM,
            AudienceMember.AUDIENCE_MEMBER,
            AudienceObject.AUDIENCE_OBJECT,
            BillConfig.BILL_CONFIG,
            BillConfigLost.BILL_CONFIG_LOST,
            BusinessCertificate.BUSINESS_CERTIFICATE,
            BusinessTopic.BUSINESS_TOPIC,
            CertificateRecord.CERTIFICATE_RECORD,
            CertificateRecordChbn.CERTIFICATE_RECORD_CHBN,
            CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT,
            CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION,
            CompeteCourseInfo.COMPETE_COURSE_INFO,
            CompeteCourseVote.COMPETE_COURSE_VOTE,
            CompeteLecturerCompany.COMPETE_LECTURER_COMPANY,
            CourseAttachment.COURSE_ATTACHMENT,
            CourseCategory.COURSE_CATEGORY,
            CourseCertificateRecord.COURSE_CERTIFICATE_RECORD,
            CourseChapter.COURSE_CHAPTER,
            CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE,
            CourseChapterSection.COURSE_CHAPTER_SECTION,
            CourseCurrency.COURSE_CURRENCY,
            CourseException.COURSE_EXCEPTION,
            CourseInfo.COURSE_INFO,
            CourseInfoCategory.COURSE_INFO_CATEGORY,
            CourseInform.COURSE_INFORM,
            CourseNote.COURSE_NOTE,
            CoursePhoto.COURSE_PHOTO,
            CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD,
            CourseRecommend.COURSE_RECOMMEND,
            CourseRecord.COURSE_RECORD,
            CourseRegister.COURSE_REGISTER,
            CourseScore.COURSE_SCORE,
            CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT,
            CourseSectionScorm.COURSE_SECTION_SCORM,
            CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS,
            CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG,
            CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH,
            CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY,
            CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ,
            CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY,
            CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM,
            CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY,
            CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ,
            CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY,
            CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB,
            CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY,
            CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ,
            CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY,
            CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD,
            CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY,
            CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS,
            CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY,
            CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX,
            CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY,
            CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ,
            CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY,
            CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB,
            CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY,
            CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL,
            CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY,
            CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN,
            CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY,
            CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL,
            CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY,
            CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS,
            CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY,
            CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX,
            CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY,
            CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN,
            CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY,
            CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM,
            CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY,
            CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX,
            CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY,
            CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER,
            CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY,
            CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH,
            CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY,
            CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO,
            CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY,
            CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC,
            CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY,
            CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD,
            CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY,
            CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH,
            CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY,
            CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN,
            CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY,
            CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX,
            CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY,
            CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ,
            CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY,
            CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ,
            CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY,
            CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN,
            CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY,
            CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ,
            CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY,
            CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN,
            CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY,
            CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT,
            CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY,
            CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ,
            CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY,
            CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX,
            CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY,
            CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS,
            CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC,
            CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS,
            CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC,
            CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS,
            CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022,
            CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS,
            CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC,
            CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS,
            CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS,
            CourseSequence.COURSE_SEQUENCE,
            CourseShelves.COURSE_SHELVES,
            CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017,
            CourseStudyProgress.COURSE_STUDY_PROGRESS,
            CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH,
            CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ,
            CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM,
            CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ,
            CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB,
            CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ,
            CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD,
            CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS,
            CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX,
            CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ,
            CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB,
            CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL,
            CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN,
            CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL,
            CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS,
            CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX,
            CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN,
            CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM,
            CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX,
            CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER,
            CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH,
            CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO,
            CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC,
            CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD,
            CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH,
            CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN,
            CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX,
            CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ,
            CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ,
            CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN,
            CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ,
            CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN,
            CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT,
            CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ,
            CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX,
            CourseTopic.COURSE_TOPIC,
            CourseVersion.COURSE_VERSION,
            DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX,
            DjClassify.DJ_CLASSIFY,
            DjResource.DJ_RESOURCE,
            Exam.EXAM,
            ExamRecord.EXAM_RECORD,
            GenseeBusiness.GENSEE_BUSINESS,
            GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS,
            GenseeLecturer.GENSEE_LECTURER,
            GenseeSubscription.GENSEE_SUBSCRIPTION,
            GenseeTopic.GENSEE_TOPIC,
            GenseeUserAccess.GENSEE_USER_ACCESS,
            GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY,
            GenseeWebCast.GENSEE_WEB_CAST,
            GrantDetail.GRANT_DETAIL,
            Job.JOB,
            KnowledgeCategory.KNOWLEDGE_CATEGORY,
            KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD,
            KnowledgeInfo.KNOWLEDGE_INFO,
            KnowledgeMonthList.KNOWLEDGE_MONTH_LIST,
            KnowledgeTopic.KNOWLEDGE_TOPIC,
            KnowledgeUseRecord.KNOWLEDGE_USE_RECORD,
            KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD,
            Member.MEMBER,
            MemberCourseMonth.MEMBER_COURSE_MONTH,
            MemberDetail.MEMBER_DETAIL,
            MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH,
            MemberParty.MEMBER_PARTY,
            MemberStatistics.MEMBER_STATISTICS,
            MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES,
            Notice.NOTICE,
            OfflineClass.OFFLINE_CLASS,
            OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE,
            OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER,
            OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER,
            OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER,
            Organization.ORGANIZATION,
            OrganizationDetail.ORGANIZATION_DETAIL,
            OrgTableCount.ORG_TABLE_COUNT,
            PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY,
            PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH,
            PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY,
            PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH,
            PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR,
            PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION,
            PartyData.PARTY_DATA,
            PartyHotTopic.PARTY_HOT_TOPIC,
            PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE,
            PartyLeader.PARTY_LEADER,
            PartyOrganization.PARTY_ORGANIZATION,
            PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS,
            PartyRecommendation.PARTY_RECOMMENDATION,
            PartyRecommendResult.PARTY_RECOMMEND_RESULT,
            PartyRecommendSpare.PARTY_RECOMMEND_SPARE,
            PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY,
            PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH,
            PartyTopic.PARTY_TOPIC,
            PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG,
            PccwResult.PCCW_RESULT,
            PccwResultBusiness.PCCW_RESULT_BUSINESS,
            PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY,
            PersonYearBill.PERSON_YEAR_BILL,
            Position.POSITION,
            PositionOld.POSITION_OLD,
            QuestionnaireMould.QUESTIONNAIRE_MOULD,
            QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION,
            QuestionnaireQuestion.QUESTIONNAIRE_QUESTION,
            RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG,
            RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS,
            RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS,
            RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL,
            RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL,
            RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS,
            RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS,
            RemodelingRoleDetail.REMODELING_ROLE_DETAIL,
            RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG,
            RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS,
            ShardingConfig.SHARDING_CONFIG,
            SplitLogConfig.SPLIT_LOG_CONFIG,
            SplitLogTime.SPLIT_LOG_TIME,
            SplitTableConfig.SPLIT_TABLE_CONFIG,
            SplitTableCount.SPLIT_TABLE_COUNT,
            SplitTableTime.SPLIT_TABLE_TIME,
            StudyActivityConfig.STUDY_ACTIVITY_CONFIG,
            StudyExperience.STUDY_EXPERIENCE,
            StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT,
            StudyPushInfo.STUDY_PUSH_INFO,
            StudyPushObject.STUDY_PUSH_OBJECT,
            StudyPushRecord.STUDY_PUSH_RECORD,
            StudyPushShelves.STUDY_PUSH_SHELVES,
            StudyRecord_2017.STUDY_RECORD_2017,
            StudyTask.STUDY_TASK,
            StudyTaskAttachment.STUDY_TASK_ATTACHMENT,
            StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER,
            SubjectAdvertising.SUBJECT_ADVERTISING,
            SubjectCourse.SUBJECT_COURSE,
            SubjectDirection.SUBJECT_DIRECTION,
            SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST,
            SubjectProblem.SUBJECT_PROBLEM,
            SubjectRank.SUBJECT_RANK,
            SubjectRankYear.SUBJECT_RANK_YEAR,
            SubjectRecommend.SUBJECT_RECOMMEND,
            SubjectRoleComment.SUBJECT_ROLE_COMMENT,
            SubjectRoleDetail.SUBJECT_ROLE_DETAIL,
            SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG,
            SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH,
            SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ,
            SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM,
            SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ,
            SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB,
            SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ,
            SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD,
            SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS,
            SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX,
            SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ,
            SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB,
            SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL,
            SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN,
            SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL,
            SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS,
            SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX,
            SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN,
            SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM,
            SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX,
            SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER,
            SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH,
            SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO,
            SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC,
            SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD,
            SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH,
            SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN,
            SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX,
            SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ,
            SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ,
            SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN,
            SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ,
            SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN,
            SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT,
            SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ,
            SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX,
            SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION,
            SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY,
            SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY,
            SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY,
            SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY,
            SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY,
            SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION,
            SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY,
            SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY,
            SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY,
            SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY,
            SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY,
            SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY,
            SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY,
            SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY,
            SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY,
            SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY,
            SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY,
            SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY,
            SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY,
            SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY,
            SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY,
            SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY,
            SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY,
            SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY,
            SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY,
            SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY,
            SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY,
            SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY,
            SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY,
            SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY,
            SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY,
            SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY,
            SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY,
            SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY,
            SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY,
            SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY,
            SubjectTextArea.SUBJECT_TEXT_AREA,
            SubjectYearBill.SUBJECT_YEAR_BILL,
            Summary.SUMMARY,
            Supplier.SUPPLIER,
            TempMember.TEMP_MEMBER,
            TempRepairCourse.TEMP_REPAIR_COURSE,
            TempRepairSubject.TEMP_REPAIR_SUBJECT,
            TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24,
            TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG,
            Thematic.THEMATIC,
            ThematicAttachment.THEMATIC_ATTACHMENT,
            ThematicChapter.THEMATIC_CHAPTER,
            ThematicChapterSection.THEMATIC_CHAPTER_SECTION,
            ThematicMember.THEMATIC_MEMBER,
            ThematicNotice.THEMATIC_NOTICE,
            ThematicWork.THEMATIC_WORK,
            ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD,
            ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO,
            ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS,
            Topic.TOPIC,
            TopicObject.TOPIC_OBJECT,
            WhiteRecord.WHITE_RECORD,
            CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022,
            CourseInfoDjyp.COURSE_INFO_DJYP,
            MultidimensionalScoring.MULTIDIMENSIONAL_SCORING,
            MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT,
            MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00,
            MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01,
            MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02,
            MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03,
            MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04,
            MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05,
            MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06,
            MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07,
            MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08,
            MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09,
            CourseInfoDjyp.COURSE_INFO_DJYP,
            CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG,
            IntelligentBroadcast.INTELLIGENT_BROADCAST,
            CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00,
            CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01,
            CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02,
            CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03,
            CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04,
            CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05,
            CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06,
            CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07,
            CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08,
            CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09,
            CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10,
            CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11,
            CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12,
            CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13,
            CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14,
            CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15,
            CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16,
            CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17,
            CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18,
            CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19,
            SubjectTopicManager.SUBJECT_TOPIC_MANAGER,
            IntelligentNote.INTELLIGENT_NOTE,
            IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK,
            Caption.CAPTION,
            CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL,
            CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION,
            CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION,
            CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG,
            DeleteDataCourse.DELETE_DATA_COURSE,
            CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH,
            CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ,
            CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM,
            CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ,
            CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB,
            CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ,
            CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD,
            CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS,
            CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX,
            CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ,
            CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB,
            CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL,
            CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN,
            CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL,
            CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS,
            CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX,
            CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN,
            CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM,
            CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX,
            CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER,
            CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH,
            CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO,
            CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC,
            CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD,
            CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH,
            CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN,
            CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX,
            CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ,
            CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ,
            CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN,
            CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ,
            CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN,
            CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT,
            CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ,
            CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX,
            AnnualBill_2022.ANNUAL_BILL_2022,
            BusinessEmergency.BUSINESS_EMERGENCY,
            CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023,
            CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023,
            AuthenticatedGroup.AUTHENTICATED_GROUP,
            AuthenticatedZone.AUTHENTICATED_ZONE,
            AuthenticatedZoneGroup.AUTHENTICATED_ZONE_GROUP,
            SubAuthenticated.SUB_AUTHENTICATED,
            SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD,
            SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE,
            SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS,
            SubAuthenticatedResourceAuditRecord.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD,
            SubAuthenticatedStudyOnline.SUB_AUTHENTICATED_STUDY_ONLINE,
            SubAuthenticatedDimension.SUB_AUTHENTICATED_DIMENSION,
            SubAuthenticatedMemberDimension.SUB_AUTHENTICATED_MEMBER_DIMENSION,
            SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER,
            ReportRedlist.REPORT_REDLIST,
            SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER,
            GenseeSignIn.GENSEE_SIGN_IN,
            MiguUserAccess.MIGU_USER_ACCESS,
            ShortVideoReport.SHORT_VIDEO_REPORT,
            IndividualShortVideoDetails.INDIVIDUAL_SHORT_VIDEO_DETAILS,
            IndividualShortVideoSummary.INDIVIDUAL_SHORT_VIDEO_SUMMARY,
            PersonalShortVideoLearningDetails.PERSONAL_SHORT_VIDEO_LEARNING_DETAILS,
            ShortVideoCreateDetails.SHORT_VIDEO_CREATE_DETAILS,
            MiguConfig.MIGU_CONFIG,
            CourseMark.COURSE_MARK,
            MiguAttachment.MIGU_ATTACHMENT,
            LiveVirtualSpace.LIVE_VIRTUAL_SPACE,
            MiguAttachment.MIGU_ATTACHMENT,
            NotePraise.NOTE_PRAISE,
            GenseeShare.GENSEE_SHARE,
            MiguAttachment.MIGU_ATTACHMENT,
            LiveVirtualSpace.LIVE_VIRTUAL_SPACE,
            GenseeShare.GENSEE_SHARE,
            MiguAttachment.MIGU_ATTACHMENT,
            KnowledgeRedShipAudit.KNOWLEDGE_RED_SHIP_AUDIT,
            ShortVideoLogDay.SHORT_VIDEO_LOG_DAY,
            AnnualBill_2023.ANNUAL_BILL_2023,
            CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024,
            CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024,
            AiQuestion.AI_QUESTION,
            AiAnswer.AI_ANSWER,
            AiFeedback.AI_FEEDBACK,
            AiMentor.AI_MENTOR,
            AiPresetExample.AI_PRESET_EXAMPLE,
            SignIn.SIGN_IN,
            BroadcastCount.BROADCAST_COUNT,
            DjLog.DJ_LOG,
            SignIn.SIGN_IN,
            MemberCourseHours.MEMBER_COURSE_HOURS,
            PartyBanner.PARTY_BANNER,
            PartyDemonstrationTrainingConfig.PARTY_DEMONSTRATION_TRAINING_CONFIG,
            PartyFocusConfig.PARTY_FOCUS_CONFIG,
            PartyDynamicNewsConfig.PARTY_DYNAMIC_NEWS_CONFIG,
            GbCourseAudit.GB_COURSE_AUDIT,
            GbCourseClassification.GB_COURSE_CLASSIFICATION,
            GbCourseConfiguration.GB_COURSE_CONFIGURATION,
            GbCourseLibrary.GB_COURSE_LIBRARY,
            GbCourseMiddle.GB_COURSE_MIDDLE,
            GbCourseRecord.GB_COURSE_RECORD,
            GbLecturerLibrary.GB_LECTURER_LIBRARY,
            GbMember.GB_MEMBER,
            SubjectPlanRelated.SUBJECT_PLAN_RELATED,
            CoursewareNote.COURSEWARE_NOTE,
            CoursewareNoteAudit.COURSEWARE_NOTE_AUDIT,
            CoursewareNoteVersion.COURSEWARE_NOTE_VERSION,
            CourseMainNote.COURSE_MAIN_NOTE,
            CourseMainNoteVersion.COURSE_MAIN_NOTE_VERSION,
            CourseMainNoteAudit.COURSE_MAIN_NOTE_AUDIT,
            ShortVideoOperation.SHORT_VIDEO_OPERATION,
            ShortVideoOperationGroup.SHORT_VIDEO_OPERATION_GROUP,
            ShortVideoOperationIntegral.SHORT_VIDEO_OPERATION_INTEGRAL,
            ShortVideoOperationMember.SHORT_VIDEO_OPERATION_MEMBER,
            ShortVideoOperationPickQuestion.SHORT_VIDEO_OPERATION_PICK_QUESTION,
            ShortVideoOperationQuestion.SHORT_VIDEO_OPERATION_QUESTION,
            IshowSdkRequest.ISHOW_SDK_REQUEST,
            IshowStudyRecord.ISHOW_STUDY_RECORD,
            ApplicationConfig.APPLICATION_CONFIG,
            Ability.ABILITY,
            AbilityBusiness.ABILITY_BUSINESS,
            CourseAbility.COURSE_ABILITY,
            StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS,
            CourseRen.COURSE_REN,
            CourseRenFeedback.COURSE_REN_FEEDBACK, AiSynchronous.AI_SYNCHRONOUS, CourseOnlineLog.COURSE_ONLINE_LOG,
            DigitalMentorCallback.DIGITAL_MENTOR_CALLBACK,
            PartySchoolNews.PARTY_SCHOOL_NEWS,
            CourseRenFeedback.COURSE_REN_FEEDBACK, AiSynchronous.AI_SYNCHRONOUS, CourseOnlineLog.COURSE_ONLINE_LOG,
            CourseFeedback.COURSE_FEEDBACK,
            CourseKnowledge.COURSE_KNOWLEDGE,
            CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND,
            ChatTimeRecord.CHAT_TIME_RECORD,
            PartySchoolNews.PARTY_SCHOOL_NEWS,
            SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP
        );
    }
}
