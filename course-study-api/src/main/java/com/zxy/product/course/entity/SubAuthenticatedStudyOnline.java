package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedStudyOnlineEntity;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class SubAuthenticatedStudyOnline extends SubAuthenticatedStudyOnlineEntity {
    private static final long serialVersionUID = -6230083454508749623L;
    public static final Integer BUSINESS_TYPE_COURSE = 1;//课程
    public static final String BUSINESS_TYPE_COURSE_NAME = "课程";
    public static final String BUSINESS_TYPE_SUBJECT_NAME = "专题";
    public static final Integer BUSINESS_TYPE_SUBJECT = 2;//转推
    public static final Integer BUSINESS_TYPE_MOCK_EXAM = 1;//模拟考试
    public static final Integer BUSINESS_TYPE_AUTH_EXAM = 2;//认证考试

    private String businessName;
    private String businessCover;
    private String coverPath;
    private Long beginTime;
    private Integer studyTotalTime;
    private Integer status;

    public Long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    public Integer getStudyTotalTime() {
        return studyTotalTime;
    }

    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getBusinessCover() {
        return businessCover;
    }

    public void setBusinessCover(String businessCover) {
        this.businessCover = businessCover;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }
}
