package com.zxy.product.examstu.service.component.sequence.business;

import com.zxy.product.examstu.api.sequence.CodeMaxCallback;
import com.zxy.product.examstu.api.sequence.CodeRule;
import com.zxy.product.examstu.api.sequence.business.CertificateAuthCodeGenerator;
import com.zxy.product.examstu.api.sequence.business.CertificateNormalCodeGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 普通考试证书编号生成器实现类
 * <AUTHOR>
 * @date 2017年11月28日
 */
@Service
public class CertificateNormalCodeGeneratorSupport implements CertificateNormalCodeGenerator {

    private CodeRule codeRule;
    private CodeMaxCallback codeMaxCallback;

    @Autowired
    @Qualifier("certificateNormalCodeRule")
    public void setCodeRule(CodeRule codeRule){
        this.codeRule = codeRule;
    }

    @Autowired
    @Qualifier("certificateCodeMaxCallback")
    public void setCodeMaxCallback(CodeMaxCallback codeMaxCallback){
        this.codeMaxCallback = codeMaxCallback;
    }

    /**
     * 根据规则生成流水号
     * @param typeCode 证书类型：考试-001
     * @param time 具体到年月日，格式：20170101
     * @param orgCode 即考试归属部门编码
     */
    @Override
    public String getCode(String typeCode, String date, String orgCode) throws Exception {
        StringBuilder bld = new StringBuilder();
        bld.append(typeCode);
        bld.append(date);
        bld.append(orgCode);
        String code = codeRule.getCode(codeMaxCallback, Optional.ofNullable(bld.toString()));
        if (!StringUtils.isEmpty(code) && code.contains("-")) {
            code = codeRule.getCode(codeMaxCallback, Optional.ofNullable(bld.toString()));
        }
        return code;
    }
}
