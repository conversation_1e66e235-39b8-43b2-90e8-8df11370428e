package com.zxy.product.examstu.service.component.sequence.business;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.zxy.product.examstu.api.sequence.CodeMaxCallback;
import com.zxy.product.examstu.api.sequence.CodeRule;
import com.zxy.product.examstu.api.sequence.business.CertificateAuthCodeGenerator;
import org.springframework.util.StringUtils;

/**
 * 认证考试证书编号生成器实现类
 * <AUTHOR>
 * @date 2017年11月11日
 */
@Service
public class CertificateAuthCodeGeneratorSupport implements CertificateAuthCodeGenerator {

    private CodeRule codeRule;
    private CodeMaxCallback codeMaxCallback;

    @Autowired
    @Qualifier("certificateAuthCodeRule")
    public void setCodeRule(CodeRule codeRule){
        this.codeRule = codeRule;
    }

    @Autowired
    @Qualifier("certificateCodeMaxCallback")
    public void setCodeMaxCallback(CodeMaxCallback codeMaxCallback){
        this.codeMaxCallback = codeMaxCallback;
    }

    /**
     * 根据规则生成流水号
     * @param level 等级：一位数，共计5级，即1~5
     * @param professionCode 专业：两位数，如06
     * @param subProfessionCode 子专业：两位数，如02
     * @param equipmentCode 设备型号：两位数，华为01、中兴02、爱立信03、诺西04、大唐05、贝尔06、普天07、思科08、Juniper09、烽火10
     * @param areaCode 考生区划代码：三位数区号
     * @param year 考试年份：四位数
     */
    @Override
    public String getCode(Optional<Integer> level, Optional<String> professionCode, Optional<String> subProfessionCode,
                          Optional<String> equipmentCode, String areaCode, String year, List<String> numList) throws Exception {
        StringBuilder bld = new StringBuilder();
        level.ifPresent(l -> bld.append(String.valueOf(l)));
        professionCode.ifPresent(l -> bld.append(l));
        subProfessionCode.ifPresent(l -> bld.append(l));
        equipmentCode.ifPresent(l -> bld.append(l));
        bld.append(areaCode);
        bld.append(year);
        String code;
        while(true) {
            code = codeRule.getCode(codeMaxCallback, Optional.ofNullable(bld.toString()));
            if (!StringUtils.isEmpty(code) && code.contains("-")) {
                code = codeRule.getCode(codeMaxCallback, Optional.ofNullable(bld.toString()));
            }
            if (!numList.contains(code)){
                numList.add(code);
                return code;
            }
            System.out.println("time：" + System.currentTimeMillis() + " code：" + code);
        }

    }

}
