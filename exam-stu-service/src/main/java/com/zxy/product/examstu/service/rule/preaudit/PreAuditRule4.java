package com.zxy.product.examstu.service.rule.preaudit;

import static com.zxy.product.exam.jooq.Tables.CERTIFICATE_RECORD;
import static com.zxy.product.exam.jooq.Tables.PROFESSION_LEVEL;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.CertificateRecord;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.Profession;
import com.zxy.product.exam.entity.ProfessionLevel;

/**
 * Created by ouyang on 2017/11/1.
 * 规则4，可选择子专业内进行降级申报（较高等级证书在有效期内）或同级申报（证书在有效期内）
 */
@Component("preAuditRule4")
public class PreAuditRule4 implements PreAuditRule {

    private CommonDao<ProfessionLevel> professionLevelDao;

    private CommonDao<CertificateRecord> certificateRecordDao;


    @Autowired
    public void setProfessionLevelDao(CommonDao<ProfessionLevel> professionLevelDao) {
        this.professionLevelDao = professionLevelDao;
    }

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }


    @Override
    public boolean audit(Exam exam, String memberId) {
            //勾选了规则4，可选择子专业内进行降级申报（较高等级证书在有效期内）或同级申报（证书在有效期内）
            //这个考试的专业id，子专业id，等级id
            String professionId = exam.getProfessionId();
            String subProfessionId = exam.getSubProfessionId();
            String levelId = exam.getLevelId();

            String myProfession = "";
            String mySubProfession = "";
            int examLevel = 1;

            if (professionId != null && subProfessionId != null && levelId != null) {
                //这场考试的等级id对应的等级
                Optional<ProfessionLevel> professionLevel = professionLevelDao.getOptional(levelId);
                if (professionLevel.isPresent() && professionLevel.get().getLevel() != null) {
                    int level = professionLevel.get().getLevel().intValue();
                    boolean haveCertificate = validateCertificate(professionId, subProfessionId, level, memberId);
                    if (haveCertificate)
                        return true;

                 // 由于业务上专业的拆分，需要单独判断指定考试证书是否有效

                    // 考试是 监控-投诉-L2，证书是 监控-监控-L2，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_TS) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 监控-投诉-L3，证书是 监控-监控-L3，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_TS) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 监控-大核心监控-L2，证书是 监控-监控-L2，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DHXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 监控-大核心监控-L3，证书是 监控-监控-L3，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DHXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 监控-大无线监控-L2，证书是 监控-监控-L2，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DWXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 监控-大无线监控-L3，证书是 监控-监控-L3，可通过
                    if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DWXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 IP-CMNET-L2，证书是 数据-IP-L2，可通过
                    if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_CMNET) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 IP-CMNET-L3，证书是 数据-IP-L3，可通过
                    if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_CMNET) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 IP-IP承载网-L2，证书为 数据-IP-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_IP_CZW) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 IP-IP承载网-L3，证书为 数据-IP-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_IP_CZW) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 IT-IT数据库-L2，证书为 数据-IT数据库-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJK_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_SJK;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 IT-IT数据库-L3，证书为 数据-IT数据库-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJK_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_SJK;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 IT-ITX86及虚拟化-L2，证书为 数据-ITX86及虚拟化-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_X86) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_X86;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 IT-ITX86及虚拟化-L3，证书为 数据-ITX86及虚拟化-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_X86) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_X86;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 安全-安全-L2，证书为 安全-安全基础-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQJC;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 安全-安全-L3，证书为 安全-安全基础-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQJC;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 安全-安全-L2，证书为 安全-安全防护-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQFH;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 安全-安全-L3，证书为 安全-安全防护-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQFH;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 安全-安全-L2，证书为 安全-评估渗透-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_PGST;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 安全-安全-L3，证书为 安全-评估渗透-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_PGST;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 互联网-内容管理-L2，证书为 数据-互联网内容管理-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_HLW) && subProfessionId.equals(Profession.SUB_PROFESSION_HLWNRGL_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_HLWNRGL;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 互联网-内容管理-L3，证书为 数据-互联网内容管理-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_HLW) && subProfessionId.equals(Profession.SUB_PROFESSION_HLWNRGL_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_HLWNRGL;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 家客业务支撑-家客-L2，证书为 集客家客-家客-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_JKYWZC) && subProfessionId.equals(Profession.SUB_PROFESSION_JIA_KE_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JIA_KE;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 家客业务支撑-家客-L3，证书为 集客家客-家客-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_JKYWZC) && subProfessionId.equals(Profession.SUB_PROFESSION_JIA_KE_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JIA_KE;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 集客业务支撑-集客专线-L2，证书为 集客家客-集客-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_JKYWZC_2) && subProfessionId.equals(Profession.SUB_PROFESSION_JKZX) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JI_KE;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 集客业务支撑-集客专线-L3，证书为 集客家客-集客-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_JKYWZC_2) && subProfessionId.equals(Profession.SUB_PROFESSION_JKZX) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JI_KE;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 IT-IT开发-L3，证书为 IT-IT前端开发-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_QD;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 IT-IT开发-L3，证书为 IT-IT后端开发-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_HD;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 IT-IT开发-L2，证书为 IT-IT前端开发-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_QD;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 IT-IT开发-L2，证书为 IT-IT后端开发-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_HD;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 无线-LTE网优-L2，证书为 第三方无线-无线优化-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_LTE) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 无线-GSM网优-L2，证书为 第三方无线-无线优化-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_GSM) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 第三方无线-无线优化-L2，证书为 无线-LTE网优-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_LTE;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 第三方无线-无线优化-L2，证书为 无线-GSM网优-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_GSM;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 无线-LTE网优-L3，证书为 第三方无线-无线优化-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_LTE) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 无线-GSM网优-L3，证书为 第三方无线-无线优化-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_GSM) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 第三方无线-无线优化-L3，证书为 无线-LTE网优-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_LTE;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 第三方无线-无线优化-L3，证书为 无线-GSM网优-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_GSM;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }


                    // 考试是 IT-网络云-L3，证书为 云计算-网络云-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_YJS;mySubProfession = Profession.SUB_PROFESSION_YJS_WLY;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 IT-网络云-L2，证书为 云计算-网络云-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_YJS;mySubProfession = Profession.SUB_PROFESSION_YJS_WLY;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是 云计算-网络云-L3，证书为 IT-网络云-L3， 可通过
                    if (professionId.equals(Profession.PROFESSION_YJS) && subProfessionId.equals(Profession.SUB_PROFESSION_YJS_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_WLY;examLevel = ProfessionLevel.LEVEL_3;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }
                    // 考试是 云计算-网络云-L2，证书为 IT-网络云-L2， 可通过
                    if (professionId.equals(Profession.PROFESSION_YJS) && subProfessionId.equals(Profession.SUB_PROFESSION_YJS_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_WLY;examLevel = ProfessionLevel.LEVEL_2;
                        if(validateCertificate(myProfession, mySubProfession, examLevel, memberId))
                            return true;
                    }

                    // 考试是  IT-数据挖掘分析-L3，证书为 原“集中性能”（现“业务质量管理”）-数据挖掘分析-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJWJFX) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_YWZLGL;mySubProfession = Profession.SUB_PROFESSION_YWZLGL_SJWJFX;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是  IT-数据挖掘分析-L2，证书为 原“集中性能”（现“业务质量管理”）-数据挖掘分析-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJWJFX) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_YWZLGL;mySubProfession = Profession.SUB_PROFESSION_YWZLGL_SJWJFX;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是  核心网-4/5G融合-L3，证书为 核心网-PS域-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_PSY;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是  核心网-4/5G融合-L2，证书为 核心网-PS域-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_PSY;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 核心网-4/5G融合-L3，证书为 核心网-5GC-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_5GC;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 核心网-4/5G融合-L2，证书为 核心网-5GC-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_5GC;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 传输-设备SPN-L3，证书为 传输-设备PTN-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_PTN;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 传输-设备SPN-L2，证书为 传输-设备PTN-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_PTN;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L3，最高证书为 原“动环”（现“动力能源”）-高低压配电-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHGDY;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L2，最高证书为 原“动环”（现“动力能源”）-高低压配电-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHGDY;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L3,最高证书为 原“动环”（现“动力能源”）-交直流供电-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHJZL;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L2,最高证书为 原“动环”（现“动力能源”）-交直流供电-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHJZL;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L3 最高证书为 原“动环”（现“动力能源”）-发电系统-L3 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHFD;examLevel = ProfessionLevel.LEVEL_3;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                    // 考试是 原“动环”（现“动力能源”）-电源系统-L2 最高证书为 原“动环”（现“动力能源”）-发电系统-L2 可通过
                    if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                        myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHFD;examLevel = ProfessionLevel.LEVEL_2;
                        if (validateCertificate(myProfession,mySubProfession,examLevel,memberId))
                            return true;
                    }

                }
            }

        return false;
    }

    /**
     * 该专业-子专业下我的有效的证书的等级是否>=这次考试的等级级别
     * @param profession
     * @param subProfession
     * @param level
     * @param memberId
     * @return
     */
    private boolean validateCertificate(String profession, String subProfession, int level, String memberId) {

        // 我的有效证书等级list
        List<ProfessionLevel> ProfessionLevelList = certificateRecordDao.execute(e -> {
            return e.select(
                    Fields.start()
                    .add(PROFESSION_LEVEL.LEVEL)
                    .end()
                )
                .from(CERTIFICATE_RECORD)
                .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                .where(
                        CERTIFICATE_RECORD.MEMBER_ID.eq(memberId),
                        CERTIFICATE_RECORD.PROFESSION_ID.eq(profession),
                        CERTIFICATE_RECORD.SUB_PROFESSION_ID.eq(subProfession)
                        )
                .and(CERTIFICATE_RECORD.VALID_DATE.ge(System.currentTimeMillis())
                        .or(CERTIFICATE_RECORD.VALID_DATE.isNull())
                        )
                .fetch(r -> {
                    return r.into(ProfessionLevel.class);
                });
            });

        // 判断我的有效证书等级 是否 >= 当前考试的等级级别
        if (ProfessionLevelList != null && ProfessionLevelList.size() > 0) {
            for (int j = 0; j < ProfessionLevelList.size(); j++) {
               if(ProfessionLevelList.get(j) != null && ProfessionLevelList.get(j).getLevel() != null) {
                 if(level <= ProfessionLevelList.get(j).getLevel().intValue()) {
                     return true;
                  }
               }
            }
        }

        return false;
    }
}
