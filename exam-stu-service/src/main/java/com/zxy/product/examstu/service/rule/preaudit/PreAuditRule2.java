package com.zxy.product.examstu.service.rule.preaudit;

import static com.zxy.product.exam.jooq.Tables.CERTIFICATE_RECORD;

import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.CertificateRecord;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.Profession;
import com.zxy.product.exam.entity.ProfessionLevel;

/**
 * Created by ouyang on 2017/11/1.
 * 规则2，子专业内逐级申报，申报L2或L2以上等级学员应保证其已具备L1或L1以上等级（即比申报等级低一等级）的技能证书，
 * 且证书仍在有效期内
 */
@Component("preAuditRule2")
public class PreAuditRule2 implements PreAuditRule {

    private CommonDao<ProfessionLevel> professionLevelDao;

    private CommonDao<CertificateRecord> certificateRecordDao;

    @Autowired
    public void setProfessionLevelDao(CommonDao<ProfessionLevel> professionLevelDao) {
        this.professionLevelDao = professionLevelDao;
    }

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }


    @Override
    public boolean audit(Exam exam, String memberId) {
                    //勾选了规则2，子专业内逐级申报，且证书仍在有效期内
                    //这个考试的专业id，子专业id，等级id
                    String professionId = exam.getProfessionId();
                    String subProfessionId = exam.getSubProfessionId();
                    String levelId = exam.getLevelId();

                    String myProfession = "";
                    String mySubProfession = "";
                    String myLevel = "";
                    String thisLevel = "";

                    if (professionId != null && subProfessionId != null && levelId != null){

                        // 如果当前考试是 L1，直接通过
                        if (levelId.equals(ProfessionLevel.LEVEL_ID_1)) return true;

                        // 我需要的等级是该考试的 低一级
                        if (levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            thisLevel = ProfessionLevel.LEVEL_ID_1;
                        } else if (levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            thisLevel = ProfessionLevel.LEVEL_ID_2;
                        } else if (levelId.equals(ProfessionLevel.LEVEL_ID_4)) {
                            thisLevel = ProfessionLevel.LEVEL_ID_3;
                        } else {
                            thisLevel = ProfessionLevel.LEVEL_ID_4;
                        }

                        // 验证 我是否拥有这场考试对应的 专业-子专业-低一级等级 的有效证书
                        boolean haveCertificate = validateCertificate(professionId,subProfessionId,thisLevel,memberId);

                        if (haveCertificate) return true;

                     // 由于业务上专业的拆分，需要单独判断指定考试证书是否有效

                        // 考试是 监控-投诉-L2，证书为 监控-监控-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_TS) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 监控-大核心监控-L2，证书为 监控-监控-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DHXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 监控-大无线监控-L2，证书为 监控-监控-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DWXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 监控-投诉-L3，证书为 监控-监控-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_TS) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 监控-大核心监控-L3，证书为 监控-监控-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DHXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 监控-大无线监控-L3，证书为 监控-监控-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_JK) && subProfessionId.equals(Profession.SUB_PROFESSION_DWXJK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_JK;mySubProfession = Profession.SUB_PROFESSION_JK;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 IP-CMNET-L2，证书为 数据-IP-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_CMNET) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 IP-IP承载网-L2，证书为 数据-IP-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_IP_CZW) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 IP-CMNET-L3，证书为 数据-IP-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_CMNET) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 IP-IP承载网-L3，证书为 数据-IP-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IP) && subProfessionId.equals(Profession.SUB_PROFESSION_IP_CZW) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IP;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 IT-IT数据库-L2，证书为 数据-IT数据库-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJK_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_SJK;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 IT-IT数据库-L3，证书为 数据-IT数据库-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJK_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_SJK;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 IT-ITX86及虚拟化-L2，证书为 数据-ITX86及虚拟化-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_X86) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_X86;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 IT-ITX86及虚拟化-L3，证书为 数据-ITX86及虚拟化-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_X86) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_IT_X86;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 安全-安全-L2，证书为 安全-安全基础-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQJC;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 安全-安全-L3，证书为 安全-安全基础-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQJC;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 安全-安全-L2，证书为 安全-安全防护-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQFH;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 安全-安全-L2，证书为 安全-评估渗透-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_PGST;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 安全-安全-L3，证书为 安全-安全防护-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_AQFH;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 安全-安全-L3，证书为 安全-评估渗透-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_AQ) && subProfessionId.equals(Profession.SUB_PROFESSION_AQ) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_AQ;mySubProfession = Profession.SUB_PROFESSION_PGST;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 互联网-内容管理-L2，证书为 数据-互联网内容管理-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_HLW) && subProfessionId.equals(Profession.SUB_PROFESSION_HLWNRGL_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_HLWNRGL;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 互联网-内容管理-L3，证书为 数据-互联网内容管理-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_HLW) && subProfessionId.equals(Profession.SUB_PROFESSION_HLWNRGL_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_SJ;mySubProfession = Profession.SUB_PROFESSION_HLWNRGL;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 传输-设备PTN-L2，证书为 传输-设备通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_PTN) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备OTN-L2，证书为 传输-设备通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_OTN) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备PON-L2，证书为 传输-设备通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_PON) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备SPN-L2，证书为 传输-设备通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备PTN-L3，证书为 传输-设备通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_PTN) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备OTN-L3，证书为 传输-设备通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_OTN) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备PON-L3，证书为 传输-设备通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_PON) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备SPN-L3，证书为 传输-设备通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_SBTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 家客业务支撑-家客-L2，证书为 集客家客-家客-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_JKYWZC) && subProfessionId.equals(Profession.SUB_PROFESSION_JIA_KE_2) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JIA_KE;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 家客业务支撑-家客-L3，证书为 集客家客-家客-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_JKYWZC) && subProfessionId.equals(Profession.SUB_PROFESSION_JIA_KE_2) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JIA_KE;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 集客业务支撑-集客专线-L2，证书为 集客家客-集客-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_JKYWZC_2) && subProfessionId.equals(Profession.SUB_PROFESSION_JKZX) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JI_KE;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 集客业务支撑-集客专线-L3，证书为 集客家客-集客-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_JKYWZC_2) && subProfessionId.equals(Profession.SUB_PROFESSION_JKZX) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_JKJK;mySubProfession = Profession.SUB_PROFESSION_JI_KE;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 动环-高低压配电系统-L2，证书为 动环-通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHGDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-交直流供电系统-L2，证书为 动环-通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHJZL) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-发电系统-L2，证书为 动环-通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHFD) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-温控系统-L2，证书为 动环-通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHWK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-动环监控系统-L2，证书为 动环-通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHJK) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 动环-高低压配电系统-L3，证书为 动环-通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHGDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-交直流供电系统-L3，证书为 动环-通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHJZL) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-发电系统-L3，证书为 动环-通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHFD) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-温控系统-L3，证书为 动环-通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHWK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 动环-动环监控系统-L3，证书为 动环-通用-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHJK) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 无线-LTE网优-L2，证书为 第三方无线-无线优化-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_LTE) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 无线-GSM网优-L2，证书为 第三方无线-无线优化-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_GSM) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  第三方无线-无线优化-L2，证书为 无线-LTE网优-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_LTE;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  第三方无线-无线优化-L2，证书为 无线-GSM网优-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_GSM;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是 无线-LTE网优-L3，证书为 第三方无线-无线优化-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_LTE) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是 无线-GSM网优-L3，证书为 第三方无线-无线优化-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_WX) && subProfessionId.equals(Profession.SUB_PROFESSION_GSM) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DSFWX;mySubProfession = Profession.SUB_PROFESSION_WXYH;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  第三方无线-无线优化-L3，证书为 无线-LTE网优-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_LTE;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  第三方无线-无线优化-L3，证书为 无线-GSM网优-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DSFWX) && subProfessionId.equals(Profession.SUB_PROFESSION_WXYH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_WX;mySubProfession = Profession.SUB_PROFESSION_GSM;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是  IT-网络云-L3，证书为 云计算-网络云-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_YJS;mySubProfession = Profession.SUB_PROFESSION_YJS_WLY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  IT-网络云-L2，证书为 云计算-网络云-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_YJS;mySubProfession = Profession.SUB_PROFESSION_YJS_WLY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }


                        // 考试是  云计算-网络云-L3，证书为 IT-网络云-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_YJS) && subProfessionId.equals(Profession.SUB_PROFESSION_YJS_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_WLY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }
                        // 考试是  云计算-网络云-L2，证书为 IT-网络云-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_YJS) && subProfessionId.equals(Profession.SUB_PROFESSION_YJS_WLY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_IT;mySubProfession = Profession.SUB_PROFESSION_IT_WLY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是  IT-数据挖掘分析-L3，证书为 原“集中性能”（现“业务质量管理”）-数据挖掘分析-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJWJFX) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_YWZLGL;mySubProfession = Profession.SUB_PROFESSION_YWZLGL_SJWJFX;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是  IT-数据挖掘分析-L2，证书为 原“集中性能”（现“业务质量管理”）-数据挖掘分析-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_IT) && subProfessionId.equals(Profession.SUB_PROFESSION_IT_SJWJFX) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_YWZLGL;mySubProfession = Profession.SUB_PROFESSION_YWZLGL_SJWJFX;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是  核心网-4/5G融合-L3，证书为 核心网-PS域-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_PSY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是  核心网-4/5G融合-L2，证书为 核心网-PS域-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_PSY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 核心网-4/5G融合-L3，证书为 核心网-5GC-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_5GC;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 核心网-4/5G融合-L2，证书为 核心网-5GC-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_HXW) && subProfessionId.equals(Profession.SUB_PROFESSION_HXW_4G5GRH) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_HXW;mySubProfession = Profession.SUB_PROFESSION_HXW_5GC;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备SPN-L3，证书为 传输-设备PTN-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_PTN;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 传输-设备SPN-L2，证书为 传输-设备PTN-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_CS) && subProfessionId.equals(Profession.SUB_PROFESSION_SPN) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_CS;mySubProfession = Profession.SUB_PROFESSION_PTN;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L3，最高证书为 原“动环”（现“动力能源”）-高低压配电-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHGDY;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L2，最高证书为 原“动环”（现“动力能源”）-高低压配电-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHGDY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L3,最高证书为 原“动环”（现“动力能源”）-交直流供电-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHJZL;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L2,最高证书为 原“动环”（现“动力能源”）-交直流供电-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHJZL;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L3 最高证书为 原“动环”（现“动力能源”）-发电系统-L2 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_3)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHFD;myLevel = ProfessionLevel.LEVEL_ID_2;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L2 最高证书为 原“动环”（现“动力能源”）-发电系统-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHFD;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                        // 考试是 原“动环”（现“动力能源”）-电源系统-L2，最高证书为 原“动环”（现“动力能源”）-动环通用-L1 可通过
                        if (professionId.equals(Profession.PROFESSION_DH) && subProfessionId.equals(Profession.SUB_PROFESSION_DHDY) && levelId.equals(ProfessionLevel.LEVEL_ID_2)) {
                            myProfession = Profession.PROFESSION_DH;mySubProfession = Profession.SUB_PROFESSION_DHTY;myLevel = ProfessionLevel.LEVEL_ID_1;
                            if (validateCertificate(myProfession,mySubProfession,myLevel,memberId))
                                return true;
                        }

                    }

        return false;
    }

    /**
     * 验证我是否有对应的有效证书
     * @param myProfession
     * @param mySubProfession
     * @param myLevel
     * @return
     */
    private boolean validateCertificate(String myProfession, String mySubProfession, String myLevel, String memberId) {

        Integer count = certificateRecordDao.execute(e ->
             e.select(
                     DSL.count(CERTIFICATE_RECORD.ID)
                )
                .from(CERTIFICATE_RECORD)
                .where(
                        CERTIFICATE_RECORD.MEMBER_ID.eq(memberId),
                        CERTIFICATE_RECORD.PROFESSION_ID.eq(myProfession),
                        CERTIFICATE_RECORD.SUB_PROFESSION_ID.eq(mySubProfession),
                        CERTIFICATE_RECORD.PROFESSION_LEVEL_ID.eq(myLevel)
                        )
                .and(
                        (CERTIFICATE_RECORD.PROFESSION_LEVEL_ID.eq(ProfessionLevel.LEVEL_ID_1))
                        .or(CERTIFICATE_RECORD.VALID_DATE.ge(System.currentTimeMillis()))
                        )
                .fetchOne(DSL.count(CERTIFICATE_RECORD.ID))
            );

        return count > 0;
    }

}
