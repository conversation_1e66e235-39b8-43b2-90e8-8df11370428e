package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AudienceItemService;
import com.zxy.product.examstu.api.AudienceObjectService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.AudienceItem;
import com.zxy.product.exam.entity.AudienceObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 */
@Service
public class AudienceObjectServiceSupport implements AudienceObjectService {

    AudienceItemService audienceItemService;

    CommonDao<AudienceObject> dao;

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }


    @Autowired
    public void setDao(CommonDao<AudienceObject> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setAudienceItemService(AudienceItemService audienceItemService) {
        this.audienceItemService = audienceItemService;
    }


    @Override
    public List<AudienceObject> batchInsert(int type, String targetId, List<AudienceItem> audienceItems, boolean isPublish) {
        audienceItems = audienceItemService.batchInsert(audienceItems, type, targetId, isPublish);
        List<String> addItemsIds = new ArrayList<>();
        List<AudienceObject> audienceObjects = new ArrayList<>();
        audienceItems.forEach(item -> {
            AudienceObject obj = new AudienceObject();
            obj.setTargetId(targetId);
            obj.setItemId(item.getId());
            obj.setType(type);
            obj.forInsert();
            audienceObjects.add(obj);
            if(item.getIsAdd()) { // add items
                addItemsIds.add(item.getId());
            }
        });
        dao.insert(audienceObjects);
        messageSender.send(
                MessageTypeContent.AUDIENCE_ITEM_INSERT, addItemsIds,
                MessageHeaderContent.BUSINESS_ID, targetId,
                MessageHeaderContent.BUSINESS_TYPE, String.valueOf(type),
                MessageHeaderContent.PULISH, isPublish ? "1" : "0");
        return audienceObjects;
    }



	@Override
    @DataSource
	public boolean isAudient(Integer examRegion, String targetId, String memberId) {
		Integer count = dao.execute(e -> {
			return e.select(
					Fields.start()
	                .add(AUDIENCE_OBJECT.ID.count())
	                .end()
	               )
				.from(AUDIENCE_OBJECT)
				.leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
				.where(AUDIENCE_OBJECT.TARGET_ID.eq(targetId), AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
				.fetchOne().getValue(0, Integer.class);
		});

		return count > 0;
	}

}
