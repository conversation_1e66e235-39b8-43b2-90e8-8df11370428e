//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.zxy.product.examstu.service.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.MonitorConfig;
import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.zxy.common.rpc.spring.DubboServiceExporter;
import com.zxy.common.rpc.spring.config.RemoteServicePackageConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class RPCServerConfig {
    public RPCServerConfig() {
    }

    @Bean
    public DubboServiceExporter dubboServiceExporter(RemoteServicePackageConfig packages, Environment env) {
        DubboServiceExporter exporter = new DubboServiceExporter();
        exporter.setPackages(packages);
        exporter.setVersion((Integer)env.getProperty("dubbo.application.version", Integer.TYPE, 1));
        return exporter;
    }

    @Bean
    public ApplicationConfig applicationConfig(Environment env) {
        ApplicationConfig config = new ApplicationConfig();
        config.setName(env.getProperty("dubbo.application.name"));
        return config;
    }

    @Bean
    public ProtocolConfig protocolConfig(Environment env) {
        ProtocolConfig config = new ProtocolConfig();
        config.setName("dubbo");
        config.setSerialization("hessian3");
        config.setThreads((Integer)env.getProperty("dubbo.protocol.threads", Integer.class, 200));
        config.setPort((Integer)env.getProperty("dubbo.protocol.port", Integer.TYPE, 20880));
        if (env.containsProperty("dubbo.protocal.host")) {
            config.setHost(env.getProperty("dubbo.protocal.host"));
        }

        return config;
    }

    @Bean
    public RegistryConfig registryConfig(Environment environment) {
        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setAddress(environment.getProperty("dubbo.registry.address"));
        registryConfig.setUsername(environment.getProperty("dubbo.registry.username",String.class,null));
        registryConfig.setPassword(environment.getProperty("dubbo.registry.password",String.class,null));
        registryConfig.setClient(environment.getProperty("dubbo.registry.client",String.class,null));
        return registryConfig;
    }

    @Bean
    public MonitorConfig monitorConfig() {
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setProtocol("registry");
        return monitorConfig;
    }
}
