package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamRegisterSrvice;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class ExamRegisterSrviceSupport implements ExamRegisterSrvice{

    public final static int PAGE_SIZE = 2000;

    private CommonDao<ExamRegist> examRegistCommonDao;

    @Value("${network.organizationId}")
    private String orgId;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setExamRegistCommonDao(CommonDao<ExamRegist> examRegistCommonDao) {
        this.examRegistCommonDao = examRegistCommonDao;
    }

    @Override
    @DataSource
    public List<ExamRegist> getExamCertificationByMemberId(Integer examRegion, String memberId,
                                                           Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        String[] allExamRegistStringTable = ExamRegist.STRING_EXAM_REGIST_ALL;

        for (String examRegistStringTable : allExamRegistStringTable) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);

            List<ExamRegist> examRegistForYear = examRegistCommonDao.execute(e -> {
                com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                                .add(examRegistTable.field("f_id", String.class))
                                .add(examRegistTable.field("f_top_score", Integer.class))
                                .add(examRegistTable.field("f_top_score_record_id", String.class))
                                .add(examRegistTable.field("f_pass_status", Integer.class))
                                .add(examRegistTable.field("f_status", Integer.class))
                                .add(EXAM.NAME)
                                .add(PROFESSION.ID)
                                .add(PROFESSION.NAME)
                                .add(subProfessionTable.ID)
                                .add(subProfessionTable.NAME)
                                .add(PROFESSION_LEVEL.ID)
                                .add(PROFESSION_LEVEL.LEVEL_NAME)
                                .add(EQUIPMENT_TYPE.ID)
                                .add(EQUIPMENT_TYPE.NAME)
                                .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(EXAM.EQUIPMENT_TYPE_ID))
                        .leftJoin(PROFESSION).on(PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                        .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(EXAM.SUB_PROFESSION_ID))
                        .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(EXAM.ORGANIZATION_ID.eq(orgId))
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE, Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                            examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));

                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));
                            // 专业
                            Profession professions = new Profession();
                            professions.setId(r.getValue(PROFESSION.ID));
                            professions.setName(r.getValue(PROFESSION.NAME));
                            exam.setProfession(professions);
                            // 子专业
                            Profession subProfessions = new Profession();
                            subProfessions.setId(r.getValue(subProfessionTable.ID));
                            subProfessions.setName(r.getValue(subProfessionTable.NAME));
                            exam.setSubProfession(subProfessions);
                            // 等级
                            ProfessionLevel professionLevels = new ProfessionLevel();
                            professionLevels.setId(r.getValue(PROFESSION_LEVEL.ID));
                            professionLevels.setLevelName(r.getValue(PROFESSION_LEVEL.LEVEL_NAME));
                            exam.setLevel(professionLevels);
                            // 设备
                            EquipmentType equipmentTypes = new EquipmentType();
                            equipmentTypes.setId(r.getValue(EQUIPMENT_TYPE.ID));
                            equipmentTypes.setName(r.getValue(EQUIPMENT_TYPE.NAME));
                            exam.setEquipmentType(equipmentTypes);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
                });
            examRegistList.addAll(examRegistForYear);
        }
        return examRegistList;
    }

    @Override
    @DataSource
    public List<ExamRegist> getCloudExamCertificationByMemberId(Integer examRegion, String memberId,
            Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        String[] allExamRegistStringTable = ExamRegist.STRING_EXAM_REGIST_ALL;

        for (String examRegistStringTable : allExamRegistStringTable) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);

            List<ExamRegist> examRegistListForYear = examRegistCommonDao.execute(e -> {

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                        .add(examRegistTable.field("f_id", String.class))
                        .add(examRegistTable.field("f_status", Integer.class))
                        .add(examRegistTable.field("f_pass_status", Integer.class))
                        .add(EXAM.NAME)
                        .add(CLOUD_PROFESSION.ID)
                        .add(CLOUD_PROFESSION.NAME)
                        .add(CLOUD_LEVEL.ID)
                        .add(CLOUD_LEVEL.LEVEL_NAME)
                        .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                        .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.eq(Exam.EXAM_CLOUD_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));
                            // 专业
                            CloudProfession cloudProfession = new CloudProfession();
                            cloudProfession.setId(r.getValue(CLOUD_PROFESSION.ID));
                            cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                            exam.setCloudProfession(cloudProfession);
                            // 等级
                            CloudLevel cloudLevel = new CloudLevel();
                            cloudLevel.setId(r.getValue(CLOUD_LEVEL.ID));
                            cloudLevel.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
                            exam.setCloudLevel(cloudLevel);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
            });
            examRegistList.addAll(examRegistListForYear);
        }
        return examRegistList;
    }

    @Override
    @DataSource
    public List<ExamRegist> getGridExamCertificationByMemberId(Integer examRegion, String memberId,
            Optional<Integer> status) {

        List<ExamRegist> examRegistList = new ArrayList<ExamRegist>();

        for (String stringYear : ExamRegist.STRING_ONLY_YEAR) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(ExamRegist.STRING_EXAM_REGIST+"_"+stringYear);

            List<ExamRegist> examRegistListForYear = examRegistCommonDao.execute(e -> {

                List<Condition> conditions = Stream.of(
                        status.map(examRegistTable.field("f_status", Integer.class)::eq)
                        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

                return e.select(
                        Fields.start()
                        .add(examRegistTable.field("f_id", String.class))
                        .add(examRegistTable.field("f_status", Integer.class))
                        .add(examRegistTable.field("f_pass_status", Integer.class))
                        .add(EXAM.NAME)
                        .add(GRID_LEVEL.ID)
                        .add(GRID_LEVEL.LEVEL_NAME)
                        .end())
                        .from(examRegistTable)
                        .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(GRID_LEVEL).on(GRID_LEVEL.ID.eq(EXAM.LEVEL_ID))
                        .where(conditions)
                        .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED))
                        .and(examRegistTable.field("f_member_id", String.class).eq(memberId)
                                .and(EXAM.TYPE.eq(Exam.EXAM_GRID_TYPE)))
                        .fetch().stream().map(r -> {
                            ExamRegist examRegist = new ExamRegist();
                            examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                            examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                            examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                            Exam exam = new Exam();
                            exam.setName(r.getValue(EXAM.NAME));

                            // 等级
                            GridLevel gridLevel = new GridLevel();
                            gridLevel.setId(r.getValue(GRID_LEVEL.ID));
                            gridLevel.setLevelName(r.getValue(GRID_LEVEL.LEVEL_NAME));
                            exam.setGridLevel(gridLevel);
                            examRegist.setExam(exam);
                            return examRegist;
                        }).collect(Collectors.toList());
            });
            examRegistList.addAll(examRegistListForYear);
        }
        return examRegistList;
    }

    @Override
    public List<Exam> getCloudExamList(String memberId) {
        List<Exam> list = examRegistCommonDao.execute(e -> e.selectDistinct(
                Fields.start()
                .add(CLOUD_EXAM.ID)
                .add(EXAM.ID)
                .add(EXAM.NAME)
                .add(EXAM.APPLICANT_START_TIME)
                .add(EXAM.APPLICANT_END_TIME)
                .add(EXAM.START_TIME)
                .add(EXAM.END_TIME)
                .add(EXAM.DURATION)
                .add(EXAM.STATUS)
                .add(CLOUD_PROFESSION.ID)
                .add(CLOUD_PROFESSION.NAME)
                .add(CLOUD_LEVEL.ID)
                .add(CLOUD_LEVEL.LEVEL_NAME)
                .add(CLOUD_EXAM.NUM)
                .end())
                .from(CLOUD_EXAM)
                .leftJoin(EXAM).on(CLOUD_EXAM.EXAM_ID.eq(EXAM.ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID)).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                .fetch().stream().map(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                    exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                    exam.setStartTime(r.getValue(EXAM.START_TIME));
                    exam.setEndTime(r.getValue(EXAM.END_TIME));
                    exam.setDuration(r.getValue(EXAM.DURATION));
                    exam.setStatus(r.getValue(EXAM.STATUS));

                    exam.setNum(r.getValue(CLOUD_EXAM.NUM));
                    // 专业
                    CloudProfession cloudProfession = new CloudProfession();
                    cloudProfession.setId(r.getValue(CLOUD_PROFESSION.ID));
                    cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                    exam.setCloudProfession(cloudProfession);
                    // 等级
                    CloudLevel cloudLevel = new CloudLevel();
                    cloudLevel.setId(r.getValue(CLOUD_LEVEL.ID));
                    cloudLevel.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
                    exam.setCloudProfession(cloudProfession);
                    return exam;
                }).collect(Collectors.toList()));
//        1-1 中级客户模拟，1-2 中级客户正式，2-1 中级方案模拟，2-2 中级方案正式，3-1 中级交付模拟，3-2 中级交付正式'
        if(list.stream().filter(a -> a.getNum().equals("1-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("1-1")).collect(Collectors.toList());
        if(list.stream().filter(a -> a.getNum().equals("2-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("2-1")).collect(Collectors.toList());
        if(list.stream().filter(a -> a.getNum().equals("3-2")).findAny().orElse(null) == null)
            list = list.stream().filter(s->!s.getNum().equals("3-1")).collect(Collectors.toList());

        return list;
    }


    @Override
    @DataSource
    public ExamRegist getExamRegistByMemberIdAndExamId(Integer examRegion, String memberId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.fields())
                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(memberId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .limit(1)
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                    examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                    examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                    examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
                    examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                    examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                    examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                    examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));
                    examRegist.setCreateTime(r.getValue(examRegistTable.field("f_create_time", Long.class)));
                    examRegist.setType(r.getValue(examRegistTable.field("f_type", Integer.class)));
                    return examRegist;
                }));
        if (examRegistList != null && examRegistList.size() > 0)
            return examRegistList.get(0);
        return new ExamRegist();
    }

    @Override
    @DataSource
    public List<ExamRegist> getExamRegistList(Integer examRegion, String currentUserId, List<String> examIds) {

        List<ExamRegist> examRegistLists = new ArrayList<ExamRegist>();

        for (String examId : examIds) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

            List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                    Fields.start()
                    .add(examRegistTable.field("f_top_score", Integer.class))
                    .add(examRegistTable.field("f_exam_id", String.class))
                    .add(examRegistTable.field("f_status", Integer.class))
                    .add(EXAM.ID)
                    .add(EXAM.SHOW_ANSWER_RULE)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .add(EXAM.NAME)
                    .add(EXAM.START_TIME)
                    .add(EXAM.CREATE_TIME)
                    .end())
                    .from(examRegistTable)
                    .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                    .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                    .fetch(r -> {
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                        examRegist.setCreateTime(System.currentTimeMillis());
                        examRegist.setStartTime(r.getValue(EXAM.CREATE_TIME));
                        Exam exam = new Exam();
                        exam.setId(r.getValue(EXAM.ID));
                        exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                        exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
                        exam.setName(r.getValue(EXAM.NAME));
                        exam.setStartTime(r.getValue(EXAM.START_TIME));
                        examRegist.setExam(exam);
                        return examRegist;
                    }));

            examRegistLists.addAll(examRegistList);
        }

        examRegistLists = examRegistLists.stream().sorted(Comparator.comparing(ExamRegist::getStartTime).reversed()).collect(Collectors.toList());

        return examRegistLists;
    }



    @Override
    @DataSource
    public List<ExamRegist> getNewenergyScoreList(Integer examRegion, String currentUserId, List<String> examIds) {

        List<ExamRegist> examRegistLists = new ArrayList<ExamRegist>();

        for (String examId : examIds) {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

            List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                    Fields.start()
                    .add(examRegistTable.field("f_exam_id", String.class))
                    .add(examRegistTable.field("f_top_score", Integer.class))
                    .add(EXAM.NAME)
                    .end())
                    .from(examRegistTable)
                    .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                    .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                    .fetch(r -> {
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setExamName(r.getValue(EXAM.NAME));
                        Integer topScore = r.getValue(examRegistTable.field("f_top_score", Integer.class));
                        examRegist.setTopScore(topScore == null ? 0 : topScore);
                        return examRegist;
                    }));

            examRegistLists.addAll(examRegistList);
        }
        examRegistLists = examRegistLists.stream().sorted(Comparator.comparing(ExamRegist::getTopScore).reversed()).collect(Collectors.toList());
        return examRegistLists;
    }



    @Override
    @DataSource
    public ExamRegist getNewenergyScore(Integer examRegion, String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_exam_id", String.class))
                .add(examRegistTable.field("f_top_score", Integer.class))
                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                    examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                    return examRegist;
                }));

        return examRegistList != null && examRegistList.size() > 0 ? examRegistList.get(0) : new ExamRegist();
    }


    @Override
    @DataSource
    public ExamRegist getExamRegisterByExamIdAndMemberId(Integer examRegion, String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRegist> examRegistList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_id", String.class)).add(examRegistTable.field("f_exam_id", String.class))
                .add(examRecordTable.field("f_id", String.class)).add(examRecordTable.field("f_member_id", String.class))
                .add(examRecordTable.field("f_start_time", Long.class)).add(examRecordTable.field("f_submit_time", Long.class))
                .add(examRecordTable.field("f_score", Integer.class)).add(examRecordTable.field("f_status", Integer.class))
                .add(examRecordTable.field("f_paper_instance_id", String.class)).add(EXAM.DURATION)
                .add(examRecordTable.field("f_order_content", String.class))
                .end())
                .from(examRegistTable)
                .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                    examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));

                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                    examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                    examRegist.setExamRecord(examRecord);

                    Exam exam = new Exam();
                    exam.setDuration(r.getValue(EXAM.DURATION));
                    examRegist.setExam(exam);
                    return examRegist;
                }));

        return examRegistList != null && examRegistList.size() > 0 ? examRegistList.get(0) : null;
    }


    @Override
    @DataSource
    public String getExamRecordIdByExamIdAndMemberId(Integer examRegion, String currentUserId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<String> examRecordIdList = examRegistCommonDao.execute(e -> e.select(
                Fields.start()
                .add(examRegistTable.field("f_top_score_record_id", String.class))
                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(currentUserId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetch(examRegistTable.field("f_top_score_record_id", String.class)));

        return examRecordIdList != null && examRecordIdList.size() > 0 ? examRecordIdList.get(0) : null;
    }


    @Override
    @DataSource
    public ExamRegist getExamRegisterForCourseStudy(Integer examRegion, String memberId, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        return examRegistCommonDao.execute(x -> x.select(examRegistTable.field("f_top_score", Integer.class),
                examRegistTable.field("f_pass_status", Integer.class),
                examRegistTable.field("f_id", String.class)).from(examRegistTable)
                .where(examRegistTable.field("f_member_id", String.class).eq(memberId)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .limit(1)
                .fetchOptional(r -> {
                    ExamRegist er = new ExamRegist();
                    er.setTopScore(r.get(examRegistTable.field("f_top_score", Integer.class)));
                    er.setPassStatus(r.get(examRegistTable.field("f_pass_status", Integer.class)));
                    er.setId(r.get(examRegistTable.field("f_id", String.class)));
                    return er;
                }).orElse(null)
        );
    }

    @Override
    public List<String> getPersonalExamIds() {
        return examRegistCommonDao.execute(e -> e.selectDistinct(
                Fields.start()
                .add(PERSONAL_DEPOT.EXAM_ID)
                .end())
                .from(PERSONAL_DEPOT)
                .fetch(PERSONAL_DEPOT.EXAM_ID));
    }

    @Override
    public List<String> getQuestionDepotListByExamId(String examId) {
        return examRegistCommonDao.execute(e -> e.selectDistinct(
                Fields.start()
                .add(PERSONAL_DEPOT.QUESTION_DPORT_ID)
                .end())
                .from(PERSONAL_DEPOT)
                .where(PERSONAL_DEPOT.EXAM_ID.eq(examId))
                .fetch(PERSONAL_DEPOT.QUESTION_DPORT_ID));
    }



    /**
     * 判断超过一次，考试，就不能继续插任务了
     * 这种场景只适合一个人进入考试的场景，不适合指定考试批量生成考试记录
     */
    @Override
    @DataSource
    public boolean moreThanOneTimes(Integer examRegion, String examId, String memberId) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        List<ExamRegist> list = examRegistCommonDao.execute(x -> x.select(
                        Fields.start()
                                .add(examRegistTable.field("f_id", String.class))
                                .add(examRegistTable.field("f_exam_times", Integer.class))
                                .end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                .and(examRegistTable.field("f_member_id", String.class).eq(memberId))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                    examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
                    return examRegist;
                })
        );
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                if (i != 0) {
                    String id = list.get(i).getId();
                    examRegistCommonDao.execute(dslContext ->
                            dslContext.delete(examRegistTable).where(
                                    examRegistTable.field("f_id", String.class).eq(id)
                            ).execute());
                }
            }
            return list.get(0) != null && list.get(0).getExamTimes() != null &&  list.get(0).getExamTimes() >= 1;
        }
        return false;

    }


    /**
     * * 查询需要自动发放证书到了发放证书时间且未发放证书的考试所有学员（注册学员+代维人员）总数用于分页
     */
    @Override
    @DataSource
    public Integer getExamRegisterCount(Integer examRegion, Long currentTime, String stringYear) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(ExamRegist.STRING_EXAM_REGIST+"_"+stringYear);
        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                .from(examRegistTable)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(examRegistTable.field("f_member_id", String.class)))
                .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                .where(EXAM.ISSUE_FLAG.eq(Exam.ISSUE_FLAG_YES),
                        EXAM.ISSUE_TIME.le(currentTime),
                        EXAM.HAS_CERT.eq(1)
                                .and(examRegistTable.field("f_pass_status", Integer.class).eq(ExamRegist.PASS_STATUS_YES))
                                .and(examRegistTable.field("f_certificate_issue", Integer.class).eq(ExamRegist.CERTIFICATE_ISSUE_NO))
                )
                .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }


    /**
     * * 分页查询需要自动发放证书到了发放证书时间且未发放证书的考试所有学员（注册学员+代维人员）
     */
    @Override
    @DataSource
    public List<ExamRegist> findExamRegisterPage(Integer examRegion, int page, Long currentTime, String stringYear) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(ExamRecord.STRING_EXAM_RECORD+"_"+stringYear);

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(ExamRegist.STRING_EXAM_REGIST+"_"+stringYear);

        return examRegistCommonDao.execute(e -> {

            com.zxy.product.exam.jooq.tables.Profession professionTable = PROFESSION.as("profession");
            com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");
            // 专业编码
            Field<String> professionCode = professionTable.CODE.as("profession_code");
            // 专业名称
            Field<String> professionName = professionTable.NAME.as("profession_name");
            // 子专业编码
            Field<String> subProfessionCode = subProfessionTable.CODE.as("sub_profession_code");
            // 子专业名称
            Field<String> subProfessionName = subProfessionTable.NAME.as("sub_profession_name");
            return e.select(Fields.start()
                            .add(EXAM.ID)
                            .add(EXAM.NAME)
                            .add(EXAM.PROFESSION_ID)
                            .add(EXAM.SUB_PROFESSION_ID)
                            .add(EXAM.LEVEL_ID)
                            .add(EXAM.EQUIPMENT_TYPE_ID)
                            .add(EXAM.EVALUTION_RULE)
                            .add(EXAM.CERTIFICATE_ID)
                            .add(EXAM.TYPE)
                            .add(EXAM.ORGANIZATION_ID)
                            .add(PROFESSION_LEVEL.VALID_DATE)
                            .add(PROFESSION_LEVEL.LEVEL)
                            .add(PROFESSION_LEVEL.LEVEL_NAME)
                            .add(professionCode)
                            .add(subProfessionCode)
                            .add(professionName)
                            .add(subProfessionName)
                            .add(EQUIPMENT_TYPE.CODE)
                            .add(EQUIPMENT_TYPE.NAME)
                            .add(examRegistTable.field("f_id", String.class))
                            .add(examRegistTable.field("f_exam_id", String.class))
                            .add(examRegistTable.field("f_member_id", String.class))
                            .add(examRegistTable.field("f_top_score", Integer.class))
                            .add(examRegistTable.field("f_certificate_issue", Integer.class))
                            .add(examRecordTable.field("f_submit_time", Long.class))
                            .add(MEMBER.ORGANIZATION_ID)
                            .add(ORGANIZATION.CODE)
                            .add(CLOUD_PROFESSION.CODE)
                            .add(CLOUD_PROFESSION.NAME)
                            .add(CLOUD_LEVEL.VALID_DATE)
                            .add(CLOUD_LEVEL.LEVEL)
                            .add(CLOUD_LEVEL.LEVEL_NAME)
                            .add(GRID_LEVEL.VALID_DATE)
                            .add(GRID_LEVEL.LEVEL)
                            .add(GRID_LEVEL.LEVEL_NAME)
                            .end())
                    .from(examRegistTable)
                    .leftJoin(MEMBER).on(MEMBER.ID.eq(examRegistTable.field("f_member_id", String.class)))
                    .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(EXAM.ORGANIZATION_ID))
                    .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(EXAM.LEVEL_ID))
                    .leftJoin(professionTable).on(professionTable.ID.eq(EXAM.PROFESSION_ID))
                    .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(EXAM.SUB_PROFESSION_ID))
                    .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(EXAM.EQUIPMENT_TYPE_ID))
                    .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
                    .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
                    .leftJoin(GRID_LEVEL).on(GRID_LEVEL.ID.eq(EXAM.LEVEL_ID))
                    .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                    .where(EXAM.ISSUE_FLAG.eq(Exam.ISSUE_FLAG_YES),
                            EXAM.ISSUE_TIME.le(currentTime),
                            EXAM.HAS_CERT.eq(1)
                                    .and(examRegistTable.field("f_pass_status", Integer.class).eq(ExamRegist.PASS_STATUS_YES))
                                    .and(examRegistTable.field("f_certificate_issue", Integer.class).eq(ExamRegist.CERTIFICATE_ISSUE_NO))
                    )
                    .limit((page - 1) * PAGE_SIZE, PAGE_SIZE)
                    .fetch(r -> {
                        Exam exam = new Exam();
                        exam.setId(r.getValue(EXAM.ID));
                        exam.setName(r.getValue(EXAM.NAME));
                        exam.setProfessionId(r.getValue(EXAM.PROFESSION_ID));
                        exam.setSubProfessionId(r.getValue(EXAM.SUB_PROFESSION_ID));
                        exam.setLevelId(r.getValue(EXAM.LEVEL_ID));
                        exam.setEquipmentTypeId(r.getValue(EXAM.EQUIPMENT_TYPE_ID));
                        exam.setEvalutionRule(r.getValue(EXAM.EVALUTION_RULE));
                        exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                        exam.setType(r.getValue(EXAM.TYPE));
                        exam.setOrganizationId(r.getValue(EXAM.ORGANIZATION_ID));
                        // 考试归属部门
                        Organization organization = new Organization();
                        organization.setCode(r.getValue(ORGANIZATION.CODE));
                        exam.setOrganization(organization);
                        // 级别
                        ProfessionLevel level = r.into(ProfessionLevel.class);
                        exam.setLevel(level);
                        // 专业
                        Profession profession = new Profession();
                        profession.setCode(r.getValue(professionCode));
                        profession.setName(r.getValue(professionName));
                        exam.setProfession(profession);
                        // 子专业
                        Profession subProfession = new Profession();
                        subProfession.setCode(r.getValue(subProfessionCode));
                        subProfession.setName(r.getValue(subProfessionName));
                        exam.setSubProfession(subProfession);
                        // 设备型号
                        EquipmentType equipmentType = r.into(EquipmentType.class);
                        exam.setEquipmentType(equipmentType);
                        // 移动云专业
                        CloudProfession cloudProfession = new CloudProfession();
                        cloudProfession.setCode(r.getValue(CLOUD_PROFESSION.CODE));
                        cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                        exam.setCloudProfession(cloudProfession);
                        // 移动云等级
                        CloudLevel cloudLevel = r.into(CloudLevel.class);
                        exam.setCloudLevel(cloudLevel);
                        // 网格长等级
                        GridLevel gridLevel = r.into(GridLevel.class);
                        exam.setGridLevel(gridLevel);
                        // 用户
                        Member member = new Member();
                        member.setOrganizationId(r.getValue(MEMBER.ORGANIZATION_ID));
                        // 提交试卷时间
                        ExamRecord examRecord = new ExamRecord();
                        examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                        // 考生考试记录
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                        examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                        examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));

                        examRegist.setExam(exam);
                        examRegist.setMember(member);
                        examRegist.setExamRecord(examRecord);
                        return examRegist;
                    });
        });
    }


    /**
     * * 更新证书发放字段
     */
    @Override
    @DataSource
    public void updateExamRegistCertificate(Integer examRegion, List<String> examRegistersIds, String stringYear) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(ExamRegist.STRING_EXAM_REGIST+"_"+stringYear);
        examRegistCommonDao.execute(a -> a.update(examRegistTable)
                .set(examRegistTable.field("f_certificate_issue", Integer.class), ExamRegist.CERTIFICATE_ISSUE_YES)
                .where(examRegistTable.field("f_id", String.class).in(examRegistersIds))
                .execute());
    }


    @Override
    @DataSource
    public Integer getPassCount(Integer examRegion, String examId) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                .from(examRegistTable)
                .where(examRegistTable.field("f_pass_status", Integer.class).eq(ExamRegist.PASS_STATUS_YES)
                        .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }


    @Override
    @DataSource
    public Integer calculateExamJoinNum(Integer examRegion, String examId) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        return examRegistCommonDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                .from(examRegistTable)
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                .fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));
    }





}
