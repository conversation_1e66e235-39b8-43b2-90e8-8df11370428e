package com.zxy.product.examstu.service.support;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.api.ExamNoticeService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.ExamNotice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ExamNoticeServiceSupport implements ExamNoticeService {

	private CommonDao<ExamNotice> examNoticeDao;
	private MessageSender messageSender;

    @Autowired
    public void setExamPublishDao(CommonDao<ExamNotice> examPublishDao) {
        this.examNoticeDao = examPublishDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }


    @Override
    public ExamNotice insert(Integer businessType, String businessId, Integer noticeType,
            String createMemberId, String templetCode,Optional<Integer> noticeUser, Optional<String> noticeUserText,
            Optional<String> noticeUserContent, Optional<String> memberIds) {
        ExamNotice examNotice = new ExamNotice();
        examNotice.forInsert();
        examNotice.setBusinessId(filterNull(businessId));
        examNotice.setBusinessType(businessType);
        examNotice.setNoticeType(noticeType);
        examNotice.setCreateMemberId(createMemberId);
        examNotice.setTempletCode(templetCode);
        examNotice.setNoticeUser(noticeUser.orElse(ExamNotice.NOTICE_NO)); // 是否通知
        examNotice.setNoticeUserText(noticeUserText.orElse(null)); // 通知内容
        examNotice.setNoticeUserContent(noticeUserContent.orElse(null)); // 通知内容

        messageSender.send(MessageTypeContent.EXAM_STU_NOTICE_INSERT,
                MessageHeaderContent.BUSINESS_ID, examNotice.getBusinessId(),
                MessageHeaderContent.BUSINESS_TYPE, businessType+"",
                MessageHeaderContent.NOTICE_TYPE, noticeType+"",
                MessageHeaderContent.MEMBER_ID, createMemberId,
                MessageHeaderContent.TEMPLATE_CODE, templetCode,
                MessageHeaderContent.NOTICE_USER, examNotice.getNoticeUser()+"",
                MessageHeaderContent.NOTICE_USER_TEXT, examNotice.getNoticeUserText() == null ?"":examNotice.getNoticeUserText(),
                MessageHeaderContent.NOTICE_USER_CONTENT, examNotice.getNoticeUserContent() == null ?"":examNotice.getNoticeUserContent(),
                MessageHeaderContent.IDS, memberIds.orElse(""));

        return examNotice;
    }

    @Override
    public ExamNotice insert(Integer businessType, String businessId, Integer noticeType, String organizationId,
            String templetCode, Optional<String[]> contentParams, Optional<String> memberIds) {
        ExamNotice examNotice = new ExamNotice();
        examNotice.forInsert();
        examNotice.setBusinessId(filterNull(businessId)); // 业务id
        examNotice.setBusinessType(businessType); // 业务类型
        examNotice.setNoticeType(noticeType); // 通知类型
        examNotice.setOrganizationId(filterNull(organizationId)); // 组织id
        examNotice.setTempletCode(templetCode); // 模板编码
        examNotice.setNoticeUser(ExamNotice.NOTICE_YES);
        examNotice.setNoticeUserText(null);
        examNotice.setCreateMemberId(null);
        messageSender.send(MessageTypeContent.EXAM_STU_NOTICE_PARAMS_INSERT,
                MessageHeaderContent.BUSINESS_ID, examNotice.getBusinessId(),
                MessageHeaderContent.BUSINESS_TYPE, examNotice.getBusinessType()+"",
                MessageHeaderContent.NOTICE_TYPE, noticeType+"",
                MessageHeaderContent.ORGANIZATION_ID, examNotice.getOrganizationId(),
                MessageHeaderContent.TEMPLATE_CODE, templetCode,
                MessageHeaderContent.NOTICE_USER, examNotice.getNoticeUser()+"",
                MessageHeaderContent.IDS, memberIds.orElse(""),
                MessageHeaderContent.PARAMS, contentParams.map(p -> Arrays.asList(p).stream().collect(Collectors.joining(","))).orElse("")
        );
        return examNotice;
    }

    private String filterNull(String param) {
        return StringUtils.isEmpty(param) ? "" : param;
    }


    @Override
    public List<ExamNotice> batchInsert(List<ExamNotice> examNotices) {
        if(CollectionUtils.isNotEmpty(examNotices)) {
            examNotices.forEach(notice->{
                messageSender.send(MessageTypeContent.EXAM_STU_NOTICE_BATCH_INSERT,
                        MessageHeaderContent.BUSINESS_ID, filterNull(notice.getBusinessId()),
                        MessageHeaderContent.BUSINESS_TYPE, notice.getBusinessType()+"",
                        MessageHeaderContent.NOTICE_USER, notice.getNoticeUser()+"",
                        MessageHeaderContent.NOTICE_TYPE, notice.getNoticeType()+"",
                        MessageHeaderContent.RECEIVERS, notice.getReceivers()==null?"":notice.getReceivers(),
                        MessageHeaderContent.NOTICE_USER_TEXT, notice.getNoticeUserText() == null ?"":notice.getNoticeUserText(),
                        MessageHeaderContent.NOTICE_USER_CONTENT, notice.getNoticeUserContent() == null ?"":notice.getNoticeUserContent(),
                        MessageHeaderContent.TEMPLATE_CODE, filterNull(notice.getTempletCode()),
                        MessageHeaderContent.MEMBER_ID, filterNull(notice.getCreateMemberId()));
            });
        }
        return examNotices;
    }

    @Override
    public ExamNotice get(String id) {
        return examNoticeDao.get(id);
    }
}
