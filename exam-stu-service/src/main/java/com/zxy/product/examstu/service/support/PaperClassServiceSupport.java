package com.zxy.product.examstu.service.support;


import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.PaperClassService;
import com.zxy.product.exam.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.zxy.product.exam.jooq.Tables.*;


/**
 * <AUTHOR>
 *
 */
@Service
public class PaperClassServiceSupport implements PaperClassService {

    private CommonDao<PaperClass> paperClassDao;

    @Autowired
    public void setPaperClassDao(CommonDao<PaperClass> paperClassDao) {
        this.paperClassDao = paperClassDao;
    }



    @DataSource
    private PaperClass getSimplePaperClass(Integer examRegion, String id) {
        return paperClassDao.execute(e -> e.select(
                Fields.start()
                        .add(PAPER_CLASS.ID)
                        .add(PAPER_CLASS.IS_SUBJECTIVE)
                        .add(PAPER_CLASS.NAME)
                        .add(PAPER_CLASS.QUESTION_NUM)
                        .add(PAPER_CLASS.STATUS)
                        .add(PAPER_CLASS.TOTAL_SCORE)
                        .add(PAPER_CLASS.ASSOCIATED_STATE)
                        .add(PAPER_CLASS.TYPE).end())
                .from(PAPER_CLASS)
                .where(PAPER_CLASS.ID.eq(id))
                .fetchOne(r -> {
                    PaperClass paperClass = new PaperClass();
                    paperClass.setId(r.getValue(PAPER_CLASS.ID));
                    paperClass.setIsSubjective(r.getValue(PAPER_CLASS.IS_SUBJECTIVE));
                    paperClass.setName(r.getValue(PAPER_CLASS.NAME));
                    paperClass.setQuestionNum(r.getValue(PAPER_CLASS.QUESTION_NUM));
                    paperClass.setStatus(r.getValue(PAPER_CLASS.STATUS));
                    paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                    paperClass.setAssociatedState(r.getValue(PAPER_CLASS.ASSOCIATED_STATE));
                    paperClass.setType(r.getValue(PAPER_CLASS.TYPE));
                    return paperClass;
                }));
    }

    @Override
    @DataSource
    public PaperClass getSimplePaperClassInfo(Integer examRegion, String paperClassId) {
        return getSimplePaperClass(examRegion, paperClassId);
    }



}
