package com.zxy.product.examstu.service.support;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.api.ExamPaperAttachmentService;
import com.zxy.product.exam.entity.ExamPaperAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.zxy.product.exam.jooq.Tables.EXAM_PAPER_ATTACHMENT;


@Service
public class ExamPaperAttachmentServiceSupport implements ExamPaperAttachmentService {

    private CommonDao<ExamPaperAttachment> examPaperAttachmentDao;


    @Autowired
    public void setExamPaperAttachmentDao(CommonDao<ExamPaperAttachment> examPaperAttachmentDao) {
        this.examPaperAttachmentDao = examPaperAttachmentDao;
    }

    @Override
    public List<ExamPaperAttachment> getPaperListByExamId(String examId) {
        return examPaperAttachmentDao.fetch(EXAM_PAPER_ATTACHMENT.EXAM_ID.eq(examId));
    }

    @Override
    public Optional<ExamPaperAttachment> getByPaperInstanceId(String paperInstanceId) {
        return examPaperAttachmentDao.fetchOne(EXAM_PAPER_ATTACHMENT.PAPER_INSTANCE_ID.eq(paperInstanceId));
    }
}