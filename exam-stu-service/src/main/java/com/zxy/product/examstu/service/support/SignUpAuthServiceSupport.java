package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class SignUpAuthServiceSupport implements SignUpAuthService {
	private CommonDao<PersonalTemplate> personalTemplateDao;
	private CommonDao<SignUpAuth> signUpAuthDao;
    private CommonDao<Exam> examDao;
    private CommonDao<SignupRecord> signupRecordDao;
    private CommonDao<ExamRecord> examRecordDao;
    private CommonDao<SignUp> signUpDao;
    private MessageSender messageSender;


	private CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao;

	private GetTableUtil getTableUtil;

	@Autowired
	public void setGetTableUtil(GetTableUtil getTableUtil) {
		this.getTableUtil = getTableUtil;
	}


    @Autowired
    public void setSignUpDao(CommonDao<SignUp> signUpDao) {
		this.signUpDao = signUpDao;
	}
	@Autowired
    public void setSignupRecord(CommonDao<SignupRecord> signupRecordDao) {
		this.signupRecordDao = signupRecordDao;
	}

	@Autowired
	public void setExamDao(CommonDao<Exam> examDao) {
		this.examDao = examDao;
	}
    @Autowired
	public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
		this.examRecordDao = examRecordDao;
	}

	@Autowired
	public void setSignUpAuthDao(CommonDao<SignUpAuth> signUpAuthDao) {
		this.signUpAuthDao = signUpAuthDao;
	}

	@Autowired
	public void setPersonalTemplateDao(CommonDao<PersonalTemplate> personalTemplateDao) {
		this.personalTemplateDao = personalTemplateDao;
	}
	@Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

	@Autowired
	public void setExamStudyPlanConfigCommonDao(CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao) {
		this.examStudyPlanConfigCommonDao = examStudyPlanConfigCommonDao;
	}

	@Override
	@DataSource
	public SignUpAuth get(Integer examRegion, String id,String memberId) {
		SignUpAuth signUpAuth=new SignUpAuth();
		//先通过用户ID获取是否存在认证模板信息
		Optional<PersonalTemplate> optionalPersonalTemplate=personalTemplateDao.getOptional(memberId);
		//存在的话用模板，不存在查询认证记录表是否存在信息
		if(optionalPersonalTemplate.isPresent()){
			PersonalTemplate personalTemplate = optionalPersonalTemplate.get();
			signUpAuth.setApplyLevel(personalTemplate.getApplyLevel());
			signUpAuth.setApplyProfession(personalTemplate.getApplyProfession());
			signUpAuth.setApplySubProfession(personalTemplate.getApplySubProfession());
			signUpAuth.setApplySupplier(personalTemplate.getApplySupplier());
			signUpAuth.setCrossCondition(personalTemplate.getCrossCondition());
			signUpAuth.setAwardSituation(personalTemplate.getAwardSituation());
			signUpAuth.setEquipmentTypeId(personalTemplate.getEquipmentTypeId());
			signUpAuth.setIsGroupExpert(personalTemplate.getIsGroupExpert());
			signUpAuth.setIsProvinExpert(personalTemplate.getIsProvinExpert());
			signUpAuth.setMemberId(memberId);
			signUpAuth.setOtherExamAppraisal(personalTemplate.getOtherExamAppraisal());
			signUpAuth.setProfessionId(personalTemplate.getProfessionId());
			signUpAuth.setSubProfessionId(personalTemplate.getSubProfessionId());
			signUpAuth.setWorkDepart(personalTemplate.getWorkDepart());
			signUpAuth.setWorkTime(personalTemplate.getWorkTime());
		}else{
			Optional<SignUpAuth> optionalSignUpAuth = signUpAuthDao.getOptional(id);
			signUpAuth= optionalSignUpAuth.orElseGet(SignUpAuth::new);
		}
		return signUpAuth;
	}



	@Override
	@DataSource
	public SignUpAuth findBySignup(Integer examRegion, String signUpId) {
		return signUpAuthDao.execute(e -> {
			Field<String> organizationNameField = ORGANIZATION.NAME.as("organization_name");
			Field<String> organizationIdField = ORGANIZATION.ID.as("organization_id");
			com.zxy.product.exam.jooq.tables.Organization companyOrganization = ORGANIZATION.as("org2");
			Field<String> orgCompanyName = companyOrganization.NAME.as("organizationCompanyName");
			com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");

			// 是否存在个人认证信息，存在取个人认证信息，否则取个人模板信息
			int authCount = signUpAuthDao.count(SIGN_UP_AUTH.SIGN_UP_ID.eq(signUpId));
			Fields fields = Fields.start().add(PROFESSION.ID)
					.add(PROFESSION.NAME)
					.add(subProfessionTable.ID)
					.add(subProfessionTable.NAME)
					.add(EQUIPMENT_TYPE.ID)
					.add(EQUIPMENT_TYPE.NAME)
					.add(MEMBER.NAME)
					.add(MEMBER.FULL_NAME)
					.add(MEMBER.HEAD_PORTRAIT_PATH)
					.add(MEMBER.PHONE_NUMBER)
					.add(MEMBER.IDENTITY_NUMBER)
					.add(MEMBER.EMAIL)
					.add(organizationNameField)
					.add(organizationIdField)
					.add(orgCompanyName);

			SelectOnConditionStep<Record> step;
			if (authCount == 0) {
				step = e.select(fields.add(PERSONAL_TEMPLATE).end()).from(SIGNUP)
						.leftJoin(PERSONAL_TEMPLATE).on(SIGNUP.MEMBER_ID.eq(PERSONAL_TEMPLATE.MEMBER_ID))
						.leftJoin(PROFESSION).on(PROFESSION.ID.eq(PERSONAL_TEMPLATE.PROFESSION_ID))
						.leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(PERSONAL_TEMPLATE.SUB_PROFESSION_ID))
						.leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(PERSONAL_TEMPLATE.EQUIPMENT_TYPE_ID));
			} else {
				step = e.select(fields.add(SIGN_UP_AUTH).end()).from(SIGNUP)
						.leftJoin(SIGN_UP_AUTH).on(SIGNUP.ID.eq(SIGN_UP_AUTH.SIGN_UP_ID))
						.leftJoin(PROFESSION).on(PROFESSION.ID.eq(SIGN_UP_AUTH.PROFESSION_ID))
						.leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(SIGN_UP_AUTH.SUB_PROFESSION_ID))
						.leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(SIGN_UP_AUTH.EQUIPMENT_TYPE_ID));
			}

			List<SignUpAuth> auths = step.leftJoin(MEMBER).on(SIGNUP.MEMBER_ID.eq(MEMBER.ID))
					.leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
					.leftJoin(companyOrganization).on(ORGANIZATION.COMPANY_ID.eq(companyOrganization.ID))
					.where(SIGNUP.ID.eq(signUpId))
					.fetch(r -> {
						SignUpAuth signUpAuth = new SignUpAuth();
						if (authCount == 0) {
							PersonalTemplate personalTemplate = r.into(PersonalTemplate.class);
							signUpAuth.setApplyLevel(personalTemplate.getApplyLevel());
							signUpAuth.setApplyProfession(personalTemplate.getApplyProfession());
							signUpAuth.setApplySubProfession(personalTemplate.getApplySubProfession());
							signUpAuth.setApplySupplier(personalTemplate.getApplySupplier());
							signUpAuth.setCrossCondition(personalTemplate.getCrossCondition());
							signUpAuth.setAwardSituation(personalTemplate.getAwardSituation());
							signUpAuth.setEquipmentTypeId(personalTemplate.getEquipmentTypeId());
							signUpAuth.setIsGroupExpert(personalTemplate.getIsGroupExpert());
							signUpAuth.setIsProvinExpert(personalTemplate.getIsProvinExpert());
							signUpAuth.setMemberId(personalTemplate.getMemberId());
							signUpAuth.setOtherExamAppraisal(personalTemplate.getOtherExamAppraisal());
							signUpAuth.setProfessionId(personalTemplate.getProfessionId());
							signUpAuth.setSubProfessionId(personalTemplate.getSubProfessionId());
							signUpAuth.setWorkDepart(personalTemplate.getWorkDepart());
							signUpAuth.setWorkTime(personalTemplate.getWorkTime());
						} else {
							signUpAuth = r.into(SignUpAuth.class);
						}
						// 人员
						Member member = new Member();
						member.setName(r.getValue(MEMBER.NAME));
						member.setFullName(r.getValue(MEMBER.FULL_NAME));
						member.setHeadPortraitPath(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
						member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
						member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));
						member.setIdentityNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.IDENTITY_NUMBER)));
						signUpAuth.setMember(member);
						// 组织部门
						Organization organization = new Organization();
						organization.setName(r.getValue(organizationNameField));
						organization.setId(r.getValue(organizationIdField));
						organization.setCompanyName(r.getValue(orgCompanyName));
						member.setOrganization(organization);
						// 专业
						Profession professions = r.into(PROFESSION).into(Profession.class);
						signUpAuth.setProfession(professions);
						// 子专业
						Profession subProfessions = r.into(subProfessionTable).into(Profession.class);
						signUpAuth.setSubProfession(subProfessions);
						// 设备
						EquipmentType equipmentTypes = r.into(EquipmentType.class);
						signUpAuth.setEquipmentType(equipmentTypes);
						return signUpAuth;
					});

			if (auths != null && auths.size() > 0) {
				return auths.get(0);
			}
			return null;
		});
	}


	@Override
	@DataSource
	public CloudSignup findCloudBySignup(Integer examRegion, String cloudSignupId) {
		return signUpAuthDao.execute(e -> {
			Field<String> organizationNameField = ORGANIZATION.NAME.as("organization_name");
			Field<String> organizationIdField = ORGANIZATION.ID.as("organization_id");
			com.zxy.product.exam.jooq.tables.Organization companyOrganization = ORGANIZATION.as("org2");
			Field<String> orgCompanyName = companyOrganization.NAME.as("organizationCompanyName");

			return e.select(Fields.start()
							.add(CLOUD_PROFESSION.NAME)
							.add(CLOUD_LEVEL.LEVEL_NAME)
							.add(MEMBER.NAME)
							.add(MEMBER.FULL_NAME)
							.add(MEMBER.HEAD_PORTRAIT_PATH)
							.add(MEMBER.PHONE_NUMBER)
							.add(MEMBER.IDENTITY_NUMBER)
							.add(MEMBER.EMAIL)
							.add(organizationNameField)
							.add(organizationIdField)
							.add(orgCompanyName)
							.add(CLOUD_SIGNUP.ID)
							.add(CLOUD_SIGNUP.CREATE_TIME)
							.add(CLOUD_SIGNUP.EXAM_ID)
							.add(CLOUD_SIGNUP.MEMBER_ID)
							.add(CLOUD_SIGNUP.ORGANIZATION_ID)
							.add(CLOUD_SIGNUP.POSITION)
							.add(CLOUD_SIGNUP.WORK_TIME)
							.add(CLOUD_SIGNUP.STATUS)
							.add(CLOUD_SIGNUP.AUDIT_MEMBER_ID)
							.end()).from(CLOUD_SIGNUP)
					.leftJoin(EXAM).on(EXAM.ID.eq(CLOUD_SIGNUP.EXAM_ID))
					.leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(EXAM.PROFESSION_ID))
					.leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(EXAM.LEVEL_ID))
					.leftJoin(MEMBER).on(CLOUD_SIGNUP.MEMBER_ID.eq(MEMBER.ID))
					.leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
					.leftJoin(companyOrganization).on(ORGANIZATION.COMPANY_ID.eq(companyOrganization.ID))
					.where(CLOUD_SIGNUP.ID.eq(cloudSignupId))
					.fetchOne(r -> {
						CloudSignup cloudSignup = new CloudSignup();
						cloudSignup.setId(r.getValue(CLOUD_SIGNUP.ID));
						cloudSignup.setCreateTime(r.getValue(CLOUD_SIGNUP.CREATE_TIME));
						cloudSignup.setExamId(r.getValue(CLOUD_SIGNUP.EXAM_ID));
						cloudSignup.setMemberId(r.getValue(CLOUD_SIGNUP.MEMBER_ID));
						cloudSignup.setOrganizationId(r.getValue(CLOUD_SIGNUP.ORGANIZATION_ID));
						cloudSignup.setPosition(r.getValue(CLOUD_SIGNUP.POSITION));
						cloudSignup.setWorkTime(r.getValue(CLOUD_SIGNUP.WORK_TIME));
						cloudSignup.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
						cloudSignup.setAuditMemberId(r.getValue(CLOUD_SIGNUP.AUDIT_MEMBER_ID));
						// 人员
						Member member =new Member();
						member.setName(r.getValue(MEMBER.NAME));
						member.setFullName(r.getValue(MEMBER.FULL_NAME));
						member.setHeadPortraitPath(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
						member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
						member.setIdentityNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.IDENTITY_NUMBER)));
						member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));

						// 组织部门
						Organization organization = new Organization();
						organization.setName(r.getValue(organizationNameField));
						organization.setId(r.getValue(organizationIdField));
						organization.setCompanyName(r.getValue(orgCompanyName));
						member.setOrganization(organization);
						// 专业
						CloudProfession cloudProfession = new CloudProfession();
						cloudProfession.setName(r.getValue(CLOUD_PROFESSION.NAME));
						cloudSignup.setCloudProfession(cloudProfession);
						// 等级
						CloudLevel cloudLevel = new CloudLevel();
						cloudLevel.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
						cloudSignup.setCloudLevel(cloudLevel);

						cloudSignup.setMember(member);
						return cloudSignup;
					});
		});
	}


	@Override
	@DataSource
	public PagedResult<Exam> findAuditList(Integer examRegion, String memberId, Integer page,
										   Integer pageSize,Optional<Integer> startTimeOrderBy) {

		return  examDao.execute(e -> {

			SelectSelectStep<Record> selectListField = e.select(
					Fields.start().add(
									EXAM.ID,EXAM.NAME,EXAM.AUDIT_START_TIME,EXAM.AUDIT_END_TIME,EXAM.EXAM_BATCH,EXAM.PRE_APPROVAL,
									EXAM.APPLICANT_START_TIME,EXAM.APPLICANT_END_TIME,
									SIGNUP.ID,SIGNUP.STATUS
							)
							.end()
			);

			SelectSelectStep<Record> selectCountField = e.select(
					Fields.start()
							.add(EXAM.ID.count()).end()
			);

			Stream<Optional<Condition>> conditions = Stream.of(
					Optional.of(memberId).map(SIGNUP.MEMBER_ID::eq),
					Optional.of(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE,Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE)),
					Optional.of(SIGNUP.STATUS.in(SignUp.STATUS_APPROVE,SignUp.STATUS_PASSED,SignUp.STATUS_REFUSE)),
					Optional.of(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 未撤销的考试
			);

			Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
					.reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

				SelectConditionStep<Record> select = a.from(EXAM)
						.leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
						.where(condition)
						.and(EXAM.AUDIT_END_TIME.gt(System.currentTimeMillis()));
				return select;
			};

			// int count = examDao.count(condition);
			int count= stepFunc.apply(selectCountField).fetchOne().getValue(0,Integer.class);

			SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
			if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
				listSetp.orderBy(EXAM.START_TIME.asc());
			} else {
				listSetp.orderBy(EXAM.START_TIME.desc());
			}
			Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

			return  PagedResult.create(count, record.stream().map(r -> {
				Exam exam = new Exam();
				exam.setId(r.getValue(EXAM.ID));
				exam.setName(r.getValue(EXAM.NAME));
				exam.setAuditStartTime(r.getValue(EXAM.AUDIT_START_TIME));
				exam.setAuditEndTime(r.getValue(EXAM.AUDIT_END_TIME));
				exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
				exam.setPreApproval(r.getValue(EXAM.PRE_APPROVAL));
				exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
				exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
				SignUp signUp=new SignUp();
				signUp.setId(r.getValue(SIGNUP.ID));
				signUp.setStatus(r.getValue(SIGNUP.STATUS));
				exam.setSignUp(signUp);
				return exam;
			}).collect(Collectors.toList()));
		});
	}

	@Override
	@DataSource
	public SignUp cancelSign(Integer examRegion, String examId, Integer status, String memberId) {
	    Integer type = examDao.execute(dsl ->
	    dsl.select(Fields.start().add(EXAM.TYPE).end())
                .from(EXAM)
                .where(EXAM.ID.eq(examId))
                .fetchOne(EXAM.TYPE)
        );
	    if (Exam.EXAM_CLOUD_TYPE.equals(type)) {
			return cancelSignForCloud(examRegion, examId,status,memberId);
		}
		if (Exam.EXAM_GRID_TYPE.equals(type)) {
			return cancelSignForGrid(examRegion, examId,status,memberId);
		}
	    return cancelSignup(examRegion, examId,status,memberId);

	}


	private SignUp cancelSignup(Integer examRegion, String examId, Integer status, String memberId) {

	    List<String> signupIds = signUpDao.execute(dsl -> dsl.select(Fields.start().add(SIGNUP.ID).end())
                .from(SIGNUP)
                .where(SIGNUP.EXAM_ID.eq(examId)
                        .and(SIGNUP.MEMBER_ID.eq(memberId)))
                .fetch(SIGNUP.ID)
        );
	    signupIds.stream().forEach(signupId -> {
	        signUpDao.execute(e -> e.update(SIGNUP)
	                .set(SIGNUP.STATUS, status)
	                .set(SIGNUP.CREATE_TIME, System.currentTimeMillis())
	                .set(SIGNUP.MODIFY_DATE, new Timestamp(System.currentTimeMillis()))
	                .where(SIGNUP.ID.eq(signupId))
	                .execute()
	        );
	        //取消考试后更新一条认证考试记录信息
	        updateSignupRecord(signupId,status,memberId);
	    });

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                Fields.start()
                .add(examRecordTable.field("f_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .fetch(examRecordTable.field("f_id", String.class))
            );

        examRecordDao.execute(dslContext ->
            dslContext.delete(examRecordTable).where(
                    examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

        messageSender.send(
            MessageTypeContent.EXAM_SIGNUP_DELETE,
            MessageHeaderContent.EXAM_ID, examId,
            MessageHeaderContent.IDS, memberId,
			MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
        );

		// TODO 学习计划-删除单个
		Exam exam = examDao.get(examId);
		if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
			List<ExamStudyPlanConfig> list = examStudyPlanConfigCommonDao.execute(w -> w
							.select(Fields.start().add(EXAM_STUDY_PLAN_CONFIG.PUSH_LEARNING_PLAN, EXAM_STUDY_PLAN_CONFIG.ID).end())
							.from(EXAM_STUDY_PLAN_CONFIG)
							.where(EXAM_STUDY_PLAN_CONFIG.BUSINESS_ID.eq(examId))
							.and(EXAM_STUDY_PLAN_CONFIG.BUSINESS_TYPE.eq(ExamStudyPlanConfig.BUSINESS_TYPE_EXAM)))
					.fetch().into(ExamStudyPlanConfig.class);
			if (CollectionUtils.isNotEmpty(list)) {
				ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
				if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
					//异步消息
					messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
							MessageHeaderContent.BUSINESS_ID, examId,
							MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
							MessageHeaderContent.EXAM_MEMBER_ID, memberId
					);
				}
			}
		}

        return new SignUp();
    }

	private SignUp cancelSignForCloud(Integer examRegion, String examId, Integer status, String memberId) {

        List<String> signupIds = signUpDao.execute(dsl -> dsl.select(Fields.start().add(CLOUD_SIGNUP.ID).end())
                .from(CLOUD_SIGNUP)
                .where(CLOUD_SIGNUP.EXAM_ID.eq(examId)
                        .and(CLOUD_SIGNUP.MEMBER_ID.eq(memberId)))
                .fetch(CLOUD_SIGNUP.ID)
        );
        signupIds.stream().forEach(signupId -> {
            signUpDao.execute(e -> e.update(CLOUD_SIGNUP)
                    .set(CLOUD_SIGNUP.STATUS, status)
                    .set(CLOUD_SIGNUP.CREATE_TIME, System.currentTimeMillis())
                    .where(CLOUD_SIGNUP.ID.eq(signupId))
                    .execute()
            );
            //取消考试后更新一条认证考试记录信息
            updateSignupRecord(signupId,status,memberId);
        });

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                Fields.start()
                .add(examRecordTable.field("f_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .fetch(examRecordTable.field("f_id", String.class))
            );

        examRecordDao.execute(dslContext ->
            dslContext.delete(examRecordTable).where(
                    examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

        messageSender.send(
            MessageTypeContent.EXAM_SIGNUP_DELETE,
            MessageHeaderContent.EXAM_ID, examId,
            MessageHeaderContent.IDS, memberId,
			MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
        );
        return new SignUp();
    }

	private SignUp cancelSignForGrid(Integer examRegion, String examId, Integer status, String memberId) {

        List<String> signupIds = signUpDao.execute(dsl -> dsl.select(Fields.start().add(GRID_SIGNUP.ID).end())
                .from(GRID_SIGNUP)
                .where(GRID_SIGNUP.EXAM_ID.eq(examId)
                        .and(GRID_SIGNUP.MEMBER_ID.eq(memberId)))
                .fetch(GRID_SIGNUP.ID)
        );
        signupIds.stream().forEach(signupId -> {
            signUpDao.execute(e -> e.update(GRID_SIGNUP)
                    .set(GRID_SIGNUP.STATUS, status)
                    .set(GRID_SIGNUP.CREATE_TIME, System.currentTimeMillis())
                    .where(GRID_SIGNUP.ID.eq(signupId))
                    .execute()
            );
            //取消考试后更新一条认证考试记录信息
            updateSignupRecord(signupId,status,memberId);
        });

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                Fields.start()
                .add(examRecordTable.field("f_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .fetch(examRecordTable.field("f_id", String.class))
            );

        examRecordDao.execute(dslContext ->
            dslContext.delete(examRecordTable).where(
                    examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

        messageSender.send(
            MessageTypeContent.EXAM_SIGNUP_DELETE,
            MessageHeaderContent.EXAM_ID, examId,
            MessageHeaderContent.IDS, memberId,
			MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
        );
        return new SignUp();
    }

    /**
	 *
	 * updateSignupRecord:更新认证报名记录信息 <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @param status
	 * @param memberId
	 * @since JDK 1.8
	 * date: 2017年11月17日 下午1:47:30 <br/>
	 */
	private void updateSignupRecord(String id, Integer status, String memberId){
		signupRecordDao.execute(e -> e.update(SIGNUP_RECORD)
				.set(SIGNUP_RECORD.AUDIT_STATUS, status)
				.where(SIGNUP_RECORD.SIGNUP_ID.eq(id)
						.and(SIGNUP_RECORD.MEMBER_ID.eq(memberId))
						.and(SIGNUP_RECORD.IS_CURRENT.eq(SignupRecord.IS_CURRENT_YES)))
				.execute()
		);
	}

	private void calculateExamApplicantNum(String examId) {
		Exam exam = examDao.get(examId);
		exam.setApplicantNumber(countSignUp(examId));
		examDao.update(exam);
	}

	/**
	 *
	 * countSignUp:统计报名 <br/>
	 *
	 * <AUTHOR>
	 * @param examId
	 * @return
	 * @since JDK 1.8
	 * date: 2017年11月3日 下午4:02:34 <br/>
	 */
	public Integer countSignUp(String examId) {
		return signUpDao.execute(e -> e.fetchCount(SIGNUP, SIGNUP.EXAM_ID.eq(examId).and(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL))));
	}

    @Override
    public SignUpAuth insertSignUpAuth(SignUpAuth signUpAuth) {

        String examId = signUpAuth.getExamId();
        String memberId = signUpAuth.getMemberId();
        signUpAuthDao.delete(SIGN_UP_AUTH.EXAM_ID.eq(examId), SIGN_UP_AUTH.MEMBER_ID.eq(memberId));
        return signUpAuthDao.insert(signUpAuth);
    }

}
