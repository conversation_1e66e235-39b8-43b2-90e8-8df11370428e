package com.zxy.product.examstu.service.support;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 */
@Service
public class ExamStuRecordServiceSupport implements ExamStuRecordService {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamStuRecordServiceSupport.class);

    private final static Integer SUBMIT_DEFAULT = 1;

    private CommonDao<Exam> examDao;

    private CommonDao<Member> memberDao;

    private CommonDao<ExamRecord> examRecordDao;

    private MessageSender messageSender;

    private PaperInstanceService paperInstanceService;

    private CommonDao<AnswerRecord> answerRecordDao;

    private AnswerRecordProcessService answerRecordProcessService;

    private GetTableUtil getTableUtil;

    private Cache submitCache;

    private Cache examRecordCache;

    private CommonDao<ExamRegist> examRegistDao;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.submitCache = cacheService.create("exam", "force#submit");
        this.examRecordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
    }


    @Autowired
    public void setExamRegistDao(CommonDao<ExamRegist> examRegistDao) {
        this.examRegistDao = examRegistDao;
    }

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
        this.answerRecordDao = answerRecordDao;
    }

    @Autowired
    public void setAnswerRecordProcessService(AnswerRecordProcessService answerRecordProcessService) {
        this.answerRecordProcessService = answerRecordProcessService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }


    @Override
    @DataSource
    public ExamRecord getNewestRecord(Integer examRegion, String examId, String memberId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> examRecords = examRecordDao.execute(e -> {
            return e.select(
                            Fields.start()
                                  .add(examRecordTable.field("f_id", String.class))
                                  .add(examRecordTable.field("f_create_time", Long.class))
                                  .add(examRecordTable.field("f_start_time", Long.class))
                                  .add(examRecordTable.field("f_end_time", Long.class))
                                  .add(examRecordTable.field("f_submit_time", Long.class))
                                  .add(examRecordTable.field("f_last_submit_time", Long.class))
                                  .add(examRecordTable.field("f_is_reset", Integer.class))
                                  .add(examRecordTable.field("f_status", Integer.class))
                                  .add(examRecordTable.field("f_order_content", String.class))
                                  .add(examRecordTable.field("f_member_id", String.class))
                                  .add(examRecordTable.field("f_exam_id", String.class))
                                  .add(examRecordTable.field("f_paper_instance_id", String.class))
                                  .add(examRecordTable.field("f_exam_times", Integer.class))
                                  .add(examRecordTable.field("f_personal_code", Integer.class))
                                  .add(examRecordTable.field("f_face_status", Integer.class))
                                  .end()
                    )
                    .from(examRecordTable)
                    .where(
                            examRecordTable.field("f_exam_id", String.class).eq(examId),
                            examRecordTable.field("f_member_id", String.class).eq(memberId),
                            examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                    ).orderBy(examRecordTable.field("f_create_time", Long.class).desc()).limit(0, 1).fetch(r -> {
                        ExamRecord examRecord = new ExamRecord();
                        examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                        examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                        examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                        examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                        examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                        examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                        examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                        examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                        examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                        examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                        examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                        examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                        examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                        examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                        examRecord.setFaceStatus(r.getValue(examRecordTable.field("f_face_status", Integer.class)));
                        return examRecord;
                    });
        });

        if (examRecords.size() > 0) return examRecords.get(0);

        return null;
    }

    @Override
    @DataSource
    public ExamRecord insert(Integer examRegion, String examId, String paperInstanceId,
                             String memberId, Integer status,
                             Optional<Long> startTime, Optional<Long> endTime,
                             Optional<Integer> paperSortRule, Optional<Integer> examedTimes,
                             Optional<Integer> personalCode) {

        // 创建考试记录前需要先把之前的记录IsCurrent设置为0
        updateBeforeRecordBeNoCurrent(examRegion, examId, memberId);
        ExamRecord examRecord = new ExamRecord();
        examRecord.forInsert();
        examRecord.setExamId(examId);
        examRecord.setPaperInstanceId(paperInstanceId);
        examRecord.setMemberId(memberId);
        examRecord.setStatus(status);
        examRecord.setExceptionOrder(null);
        examRecord.setIsCurrent(ExamRecord.CURRENT);
        examRecord.setPersonalCode(personalCode.orElse(null));
        startTime.ifPresent(examRecord::setStartTime);
        endTime.ifPresent(examRecord::setEndTime);
        examRecord.setExamTimes(examedTimes.map(e -> e + 1).orElseGet(() -> {
            Integer count = calculateExamTimes(examRegion, examId, memberId);
            return count == 0 ? 1 : count + 1;
        }));
        insertExamRecord(examRegion, examRecord);

        String newOrderJson = paperInstanceService.createNewQuestionOrder(
                examRegion,
                Optional.empty(), paperInstanceId, paperSortRule.orElseGet(() -> {
                    return examDao.get(examId).getPaperSortRule();
                })
        );
        examRecord.setOrderContent(newOrderJson);
        examRecord.setMember(getMember(examRecord.getMemberId()));
        LOGGER.info("插入考试记录 发送消息 EXAM_EXAM_RECORD_INSERT ：examRegion : {} , examId :{} , memberId : {} , examRecordId : {}",examRegion,examId,memberId,examRecord.getId());
        messageSender.send(
                MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
                MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
                MessageHeaderContent.ID, examRecord.getId(),
                MessageHeaderContent.IDS, examRecord.getMemberId(),
                MessageHeaderContent.EXAM_ID, examId,
                MessageHeaderContent.STATUS, String.valueOf(examRecord.getStatus()),
                MessageHeaderContent.QUESTION_ORDER, Optional.ofNullable(newOrderJson).orElse(""));

        return examRecord;
    }

    @DataSource
    private void updateBeforeRecordBeNoCurrent(Integer examRegion, String examId, String memberId) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        examRecordDao.execute(e ->
                e.update(examRecordTable)
                        .set(examRecordTable.field("f_is_current", Integer.class), ExamRecord.IS_NOT_CURRENT)
                        .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                        .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                        .execute());

    }

    @Override
    @DataSource
    public Integer calculateExamTimes(Integer examRegion, String examId, String memberId) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        return examRecordDao.execute(e ->
                                             e.select(DSL.count(examRecordTable.field("f_id", String.class)))
                                              .from(examRecordTable)
                                              .where(
                                                      examRecordTable.field("f_exam_id", String.class).eq(examId),
                                                      examRecordTable.field("f_member_id", String.class).eq(memberId),
                                                      examRecordTable.field("f_status", Integer.class).gt(ExamRecord.STATUS_TIME_EXCEPTION)
                                              ).fetchOne(DSL.count(examRecordTable.field("f_id", String.class)))
        );
    }

    @DataSource
    private void insertExamRecord(Integer examRegion, ExamRecord examRecord) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examRecord.getExamId()));

        examRecordDao.execute(e ->
                                      e.insertInto(examRecordTable, examRecordTable.field("f_id", String.class),
                                                   examRecordTable.field("f_create_time", Long.class),
                                                   examRecordTable.field("f_exam_id", String.class),
                                                   examRecordTable.field("f_paper_instance_id", String.class),
                                                   examRecordTable.field("f_order_content", String.class),
                                                   examRecordTable.field("f_member_id", String.class),
                                                   examRecordTable.field("f_status", Integer.class),
                                                   examRecordTable.field("f_exception_order", Integer.class),
                                                   examRecordTable.field("f_is_current", Integer.class),
                                                   examRecordTable.field("f_start_time", Long.class),
                                                   examRecordTable.field("f_end_time", Long.class),
                                                   examRecordTable.field("f_exam_times", Integer.class),
                                                   examRecordTable.field("f_personal_code", Integer.class)
                                      ).values(
                                              examRecord.getId(), examRecord.getCreateTime(),
                                              examRecord.getExamId(), examRecord.getPaperInstanceId(),
                                              examRecord.getOrderContent(), examRecord.getMemberId(),
                                              examRecord.getStatus(), examRecord.getExceptionOrder(),
                                              examRecord.getIsCurrent(), examRecord.getStartTime(),
                                              examRecord.getEndTime(), examRecord.getExamTimes(),
                                              examRecord.getPersonalCode()
                                      ).execute()
        );
    }

    @Override
    @DataSource
    public String update(Integer examRegion, String examRecordId,
            Optional<Long> startTime,Optional<Long> endTime,
            Optional<Integer> status,Optional<Long> submitTime,
            Integer isReset,Optional<String> paperInstanceId,
            Integer exceptionOrder,Optional<Long> lastSubmitTime,
            Optional<String> orderContent,Optional<Integer> personalCode,
            String examId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        examRecordDao.execute(e -> {
            UpdateSetMoreStep<?> u = (UpdateSetMoreStep<?>) e.update(examRecordTable);
            if (startTime.isPresent()) {
                u = u.set(examRecordTable.field("f_start_time", Long.class), startTime.get());
            }
            if (endTime.isPresent()) {
                u = u.set(examRecordTable.field("f_end_time", Long.class), endTime.get());
            }
            if (status.isPresent()) {
                u = u.set(examRecordTable.field("f_status", Integer.class), status.get());
            }
            if (submitTime.isPresent()) {
                u = u.set(examRecordTable.field("f_submit_time", Long.class), submitTime.get());
            }
            u = u.set(examRecordTable.field("f_is_reset", Integer.class), isReset);
            if (paperInstanceId.isPresent()) {
                u = u.set(examRecordTable.field("f_paper_instance_id", String.class), paperInstanceId.get());
            }
            u = u.set(examRecordTable.field("f_exception_order", Integer.class),exceptionOrder);
            if (lastSubmitTime.isPresent()) {
                u = u.set(examRecordTable.field("f_last_submit_time", Long.class), lastSubmitTime.get());
            }
            if (orderContent.isPresent()) {
                u = u.set(examRecordTable.field("f_order_content", String.class), orderContent.get());
            }
            if (personalCode.isPresent()) {
                u = u.set(examRecordTable.field("f_personal_code", Integer.class), personalCode.get());
            }
            return u.where(examRecordTable.field("f_id", String.class).eq(examRecordId)).execute();
        });
        return examRecordId;
    }

    @Override
    @DataSource
    public ExamRecord submitPaper(Integer examRegion,String memberId,String examRecordId, Integer clientType,
                                  ExamRecord.SubmitType submitType, Long submitTime,
                                  String userIp,
                                  Integer noAnswerCount, Integer answeredCount,
                                  Integer submitDetailType, String clientVersion,
                                  String examId) {
        LOGGER.info("submitPaper:memberId={},examRecordId={},clientType={},submitType={},submitTime={},noAnswerCount={},answeredCount={},submitDetailType={},clientVersion={},examId={}",
                    memberId,examRecordId,clientType,submitType,submitTime,noAnswerCount,answeredCount,submitDetailType,clientVersion,examId);

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> examRecordList =  getExamRecordList(examRegion,examRecordId, examRecordTable);
        LOGGER.info("查询当前考试记录： examRecordList:{}", examRecordList);

        if (CollectionUtils.isEmpty(examRecordList)){
            return null;
        }

        ExamRecord examRecord = examRecordList.get(0);

        Exam exam = getExam(examRegion, examRecord.getExamId());
        LOGGER.info("查询考试信息： exam:{}", exam);

        //考试记录更新字段：客户端类型，最后保存时间，是否重置，异常排序
        setExamRecordFieldValue(examRecord, clientType,
                                userIp, noAnswerCount,
                                answeredCount, clientVersion);
        LOGGER.info("处理交卷操作 -------- handleSubmiting");
        handleSubmiting(examRegion, memberId, examRecord, examRecord.getExamId(), submitTime);

        if (submitType == ExamRecord.SubmitType.Hand) {
            messageSender.send(MessageTypeContent.EXAM_ANSWER_RECORD_UPDATE_STU,
                               MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion),
                               MessageHeaderContent.MEMBER_ID,memberId,
                               MessageHeaderContent.ID, examRecord.getId(),
                               MessageHeaderContent.EXAM_ID, examRecord.getExamId());
        }
        return null;
    }


    /**
     * 系统自动强制交卷,
     * 此功能是2018-05-26发版，之前的考试未提交的不做处理
     */
    @Override
    @DataSource
    public void doForceSubmitOverTimePaper(Integer examRegion) {

        String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

        for (String examRecordStringTable : allExamRecordStringTable) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

            // 查询 2018-05-26 之后的未提交的考试记录
            List<ExamRecord> examRecordList = examRecordDao.execute(e ->
                    e.select(
                                    Fields.start()
                                            .add(examRecordTable.field("f_id", String.class))
                                            .add(examRecordTable.field("f_exam_id", String.class))
                                            .add(examRecordTable.field("f_member_id", String.class))
                                            .end())
                            .from(examRecordTable)
                            .where(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TIME_EXCEPTION))
                            .and(examRecordTable.field("f_paper_instance_id", String.class).isNotNull())
                            .and(examRecordTable.field("f_create_time", Long.class).ge(1527264000000l))
                            .fetch(r -> {
                                ExamRecord examRecord = new ExamRecord();
                                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                                examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                                return examRecord;
                            }));

            for (ExamRecord examRecord: examRecordList) {
                String id = examRecord.getId();
                if (null == submitCache.get(id, Integer.class)) {
                    String examId = examRecord.getExamId();
                    String userId = examRecord.getMemberId();
                    examRecordCache.clear(ExamRecord.getExamRecordKey(examId, userId));
                    LOGGER.info("强制交卷，临时试题算分：exmId:{}, userId:{}", examId, userId);
                    this.forceSubmitPaperById(examRegion, examId, userId, id);
                    submitCache.set(id, SUBMIT_DEFAULT, 3*60*60);
                }
            }
        }

    }


    @Override
    @DataSource
    public void forceSubmitPaperById(Integer examRegion, String examId, String memberId, String id) {
        ExamRecord examRecord = getExamRecord(examRegion, id, examId);
        if (examRecord != null && examRecord.getId() != null) {
            // 多加1分钟用于区分考试结束后系统自动强制交卷
            Long currentTime = examRecord.getEndTime() != null && System.currentTimeMillis() > (examRecord.getEndTime()+60000)
                    ? (examRecord.getEndTime()+60000)
                    : System.currentTimeMillis();

            update(examRegion, examRecord.getId(), Optional.empty(), Optional.empty(),
                    Optional.empty(), Optional.of(currentTime), null,
                    Optional.empty(),  null, Optional.of(currentTime), Optional.empty(),Optional.empty(),examId);

            // 更新examRegist
            updateExamRegist(examRegion, examId, memberId);

            LOGGER.info("处理交卷操作 -------- handleSubmiting");
            handleSubmiting(examRegion, memberId, examRecord, examId, currentTime);

            messageSender.send(MessageTypeContent.EXAM_ANSWER_RECORD_UPDATE_STU,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion),
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.ID, examRecord.getId(),
                    MessageHeaderContent.EXAM_ID,examId);
        }

    }

    private void updateExamRegist(Integer examRegion, String examId, String memberId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examRegistDao.execute(e -> e.update(examRegistTable)
                .set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_FINISHED)
                .set(examRegistTable.field("f_exam_times", Integer.class), examRegistTable.field("f_exam_times", Integer.class).add(1))
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId),
                        examRegistTable.field("f_member_id", String.class).eq(memberId)).execute()
        );

    }

    /**
     * 前端正常强制交卷
     * @param examId
     * @param memberId
     * @return
     */
    @Override
    @DataSource
    public ExamRecord normalForceSubmitPaper(Integer examRegion,String examId, String memberId) {

        ExamRecord examRecord = getCurrentRecord(examRegion,examId, memberId);
        Long currentTime = System.currentTimeMillis();

        update(examRegion,examRecord.getId(), Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.of(currentTime), null,
                Optional.empty(),  null, Optional.of(currentTime), Optional.empty(),Optional.empty(), examId);

        // 更新examRegist
        updateExamRegist(examRegion,examId, memberId);
        handleSubmiting(examRegion, memberId, examRecord, examId, currentTime);
        messageSender.send(MessageTypeContent.EXAM_ANSWER_RECORD_UPDATE_STU,
                MessageHeaderContent.ID, examRecord.getId(),
                MessageHeaderContent.EXAM_ID,examId,
                MessageHeaderContent.MEMBER_ID,memberId,
                MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion));

        return examRecord;
    }

    @DataSource
    public ExamRecord getCurrentRecord(Integer examRegion, String examId, String memberId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> list = examRecordDao.execute(e ->
                e.selectDistinct(
                                Fields.start()
                                        .add(examRecordTable.fields())
                                        .add(MEMBER.ID)
                                        .add(MEMBER.NAME)
                                        .add(MEMBER.FULL_NAME)
                                        .add(MEMBER.ORGANIZATION_ID)
                                        .add(MEMBER.COMPANY_ID)
                                        .add(MEMBER.FROM)
                                        .add(PAPER_INSTANCE.ID)
                                        .add(PAPER_INSTANCE.PAPER_CLASS_ID)
                                        .add(PAPER_INSTANCE.IS_SUBJECTIVE)
                                        .add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
                                        .add(PAPER_INSTANCE.EXAM_ID)
                                        .end())
                        .from(examRecordTable)
                        .leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
                        .leftJoin(PAPER_INSTANCE).on(PAPER_INSTANCE.ID.eq(examRecordTable.field("f_paper_instance_id", String.class)))
                        .where(
                                examRecordTable.field("f_exam_id", String.class).eq(examId),
                                examRecordTable.field("f_member_id", String.class).eq(memberId),
                                examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                        )
                        .fetch(r -> {
                            ExamRecord examRecord = new ExamRecord();
                            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                            examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                            examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
                            examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                            examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                            examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                            examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
                            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                            examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                            examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                            examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
                            examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                            examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                            examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
                            examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                            examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
                            examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
                            examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
                            examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                            examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                            examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
                            examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                            examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
                            examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
                            examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
                            examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));

                            Member member = new Member();
                            member.setId(r.getValue(MEMBER.ID));
                            member.setName(r.getValue(MEMBER.NAME));
                            member.setFullName(r.getValue(MEMBER.FULL_NAME));
                            member.setOrganizationId(r.getValue(MEMBER.ORGANIZATION_ID));
                            member.setCompanyId(r.getValue(MEMBER.COMPANY_ID));
                            member.setFrom(r.getValue(MEMBER.FROM));
                            examRecord.setMember(member);

                            PaperInstance paperInstance = new PaperInstance();
                            paperInstance.setId(r.getValue(PAPER_INSTANCE.ID));
                            paperInstance.setPaperClassId(r.getValue(PAPER_INSTANCE.PAPER_CLASS_ID));
                            paperInstance.setIsSubjective(r.getValue(PAPER_INSTANCE.IS_SUBJECTIVE));
                            paperInstance.setRemoteOrderContent(r.getValue(PAPER_INSTANCE.REMOTE_ORDER_CONTENT));
                            paperInstance.setExamId(r.getValue(PAPER_INSTANCE.EXAM_ID));
                            examRecord.setPaperInstance(paperInstance);

                            return examRecord;
                        })
        );
        if (list != null && list.size() > 0)
            return list.get(0);
        return new ExamRecord();
    }


    @Override
    @DataSource
    public ExamRecord getExamRecord(Integer examRegion, String examRecordId, String examId) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> examRecordList = examRecordDao.execute(e ->
                                                                        e.select(Fields.start()
                                                                                       .add(examRecordTable.fields())
                                                                                       .end())
                                                                         .from(examRecordTable)
                                                                         .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
        ).fetch(r -> {
            ExamRecord examRecord = new ExamRecord();
            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
            examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
            examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
            examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
            examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
            examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
            examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
            examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
            examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
            examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
            examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
            examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
            examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
            examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
            examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
            examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
            examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
            examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
            examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
            examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
            examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
            examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
            examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
            examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
            examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
            return examRecord;
        });

        if (examRecordList != null && examRecordList.size() > 0)
            return examRecordList.get(0);
        return null;
    }

    @Override
    @DataSource
    public Integer updateExamRecord(Integer examRegion, String examId,
                                String examRecordId, Long lastSubmitTime,
                                Integer noAnswerCount, Integer answeredCount,
                                Integer clientType, String clientVersion, String ipAddr) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

       return examRecordDao.execute(e -> {
            UpdateSetMoreStep<?> u = (UpdateSetMoreStep<?>) e.update(examRecordTable);

            u = u.set(examRecordTable.field("f_last_submit_time", Long.class), lastSubmitTime);

            u = u.set(examRecordTable.field("f_no_answer_count", Integer.class), noAnswerCount);

            u = u.set(examRecordTable.field("f_answered_count", Integer.class), answeredCount);

            u = u.set(examRecordTable.field("f_client_type", Integer.class), clientType);

            u = u.set(examRecordTable.field("f_client_version", String.class), clientVersion);

            u = u.set(examRecordTable.field("f_user_ip", String.class), ipAddr);

            return u.where(examRecordTable.field("f_id", String.class).eq(examRecordId)).execute();
        });
    }

    @Override
    @DataSource
    public Integer updateExamRecordInConfirm(Integer examRegion, String id,
                                         Integer status, Integer isFinished,
                                         Integer score, String examId,
                                         Integer answeredCount, Integer noAnswerCount,
                                         Integer rightCount) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        return examRecordDao.execute(e ->
                                             e.update(examRecordTable)
                                              .set(examRecordTable.field("f_status", Integer.class), status)
                                              .set(examRecordTable.field("f_is_finished", Integer.class), isFinished)
                                              .set(examRecordTable.field("f_score", Integer.class), score)
                                              .set(examRecordTable.field("f_answered_count", Integer.class), answeredCount)
                                              .set(examRecordTable.field("f_no_answer_count", Integer.class), noAnswerCount)
                                              .set(examRecordTable.field("f_right_count", Integer.class), rightCount)
                                              .where(examRecordTable.field("f_id", String.class).eq(id))
                                              .execute());
    }

    @Override
    @DataSource
    public Integer updateExamRecordToBeOver(Integer examRegion, ExamRecord examRecord) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examRecord.getExamId()));

        return examRecordDao.execute(e -> {
            return e.update(examRecordTable)
                    .set(examRecordTable.field("f_status", Integer.class), ExamRecord.STATUS_TO_BE_OVER)
                    .set(examRecordTable.field("f_answered_count", Integer.class), examRecord.getAnsweredCount())
                    .set(examRecordTable.field("f_no_answer_count", Integer.class), examRecord.getNoAnswerCount())
                    .set(examRecordTable.field("f_right_count", Integer.class), examRecord.getRightCount())
                    .where(examRecordTable.field("f_id", String.class).eq(examRecord.getId()))
                    .execute();
        });
    }

    @Override
    @DataSource
    public Integer updateExamRecord(Integer examRegion, String id,
                                    Integer status, Integer isFinished,
                                    Integer score, Integer rightCount, String examId) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        return examRecordDao.execute(dslContext ->
                                             dslContext.update(examRecordTable)
                                                       .set(examRecordTable.field("f_status", Integer.class), status)
                                                       .set(examRecordTable.field("f_is_finished", Integer.class), isFinished)
                                                       .set(examRecordTable.field("f_score", Integer.class), score)
                                                       .set(examRecordTable.field("f_right_count", Integer.class), rightCount)
                                                       .where(examRecordTable.field("f_id", String.class).eq(id))
                                                       .execute());

    }

    @Override
    @DataSource
    public Integer batchInsert(Integer examRegion,String examId, List<ExamRecord> inserts) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        Field<?>[] fileds = {
                examRecordTable.field("f_id", String.class),
                examRecordTable.field("f_member_id", String.class),
                examRecordTable.field("f_organization_id", String.class),
                examRecordTable.field("f_start_time", Long.class),
                examRecordTable.field("f_end_time", Long.class),
                examRecordTable.field("f_last_submit_time", Long.class),
                examRecordTable.field("f_score", Integer.class),
                examRecordTable.field("f_client_type", Integer.class),
                examRecordTable.field("f_status", Integer.class),
                examRecordTable.field("f_exam_id", String.class),
                examRecordTable.field("f_paper_instance_id", String.class),
                examRecordTable.field("f_exam_number", Integer.class),
                examRecordTable.field("f_create_time", Long.class),
                examRecordTable.field("f_submit_time", Long.class),
                examRecordTable.field("f_duration", Long.class),
                examRecordTable.field("f_is_reset", Integer.class),
                examRecordTable.field("f_is_current", Integer.class),
                examRecordTable.field("f_is_finished", Integer.class),
                examRecordTable.field("f_exception_order", Integer.class),
                examRecordTable.field("f_order_content", String.class),
                examRecordTable.field("f_exam_times", Integer.class),
                examRecordTable.field("f_switch_times", Integer.class),
                examRecordTable.field("f_personal_code", Integer.class),
                examRecordTable.field("f_user_ip", String.class),
                examRecordTable.field("f_no_answer_count", Integer.class),
                examRecordTable.field("f_answered_count", Integer.class),
                examRecordTable.field("f_client_version", String.class)
        };
        examRecordDao.execute(e -> {
            InsertValuesStepN<?> step = e.insertInto(examRecordTable, fileds);
            inserts.forEach(examRecord -> {
                step.values(
                        examRecord.getId(),
                        examRecord.getMemberId(),
                        examRecord.getOrganizationId(),
                        examRecord.getStartTime(),
                        examRecord.getEndTime(),
                        examRecord.getLastSubmitTime(),
                        examRecord.getScore(),
                        examRecord.getClientType(),
                        examRecord.getStatus(),
                        examRecord.getExamId(),
                        examRecord.getPaperInstanceId(),
                        examRecord.getExamNumber(),
                        examRecord.getCreateTime(),
                        examRecord.getSubmitTime(),
                        examRecord.getDuration(),
                        examRecord.getIsReset(),
                        examRecord.getIsCurrent(),
                        examRecord.getIsFinished(),
                        examRecord.getExceptionOrder(),
                        examRecord.getOrderContent(),
                        examRecord.getExamTimes(),
                        examRecord.getSwitchTimes(),
                        examRecord.getPersonalCode(),
                        examRecord.getUserIp(),
                        examRecord.getNoAnswerCount(),
                        examRecord.getAnsweredCount(),
                        examRecord.getClientVersion()
                );
            });
            return step.execute();
        });
        for (ExamRecord examRecord : inserts) {
            messageSender.send(
                    com.zxy.product.exam.content.MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
                    MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
                    com.zxy.product.exam.content.MessageHeaderContent.IDS, examRecord.getMemberId(),
                    com.zxy.product.exam.content.MessageHeaderContent.EXAM_ID, examId,
                    com.zxy.product.exam.content.MessageHeaderContent.STATUS, String.valueOf(ExamRecord.STATUS_TO_BE_STARTED));
        }
        return 0;
    }


    @DataSource
    public List<ExamRecord> getExamRecordList(Integer examRegion,String examRecordId, TableImpl<?> examRecordTable) {
        return examRecordDao.execute(e ->
                                             e.select(Fields.start()
                                                            .add(examRecordTable.fields())
                                                            .end())
                                              .from(examRecordTable)
                                              .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
        ).fetch(r -> {
            ExamRecord examRecord = new ExamRecord();
            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
            examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
            examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
            examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
            examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
            examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
            examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
            examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
            examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
            examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
            examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
            examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
            examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
            examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
            examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
            examRecord.setIsFinished(r.getValue(examRecordTable.field("f_is_finished", Integer.class)));
            examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
            examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
            examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
            examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
            examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
            examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
            examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
            examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
            examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
            return examRecord;
        });
    }

    @DataSource
    public Exam getExam(Integer examRegion,String examId) {
        return examDao.execute(e ->
                                       e.select(
                                                Fields.start()
                                                      .add(EXAM.ID)
                                                      .add(EXAM.SOURCE_TYPE)
                                                      .end()
                                        )
                                        .from(EXAM)
                                        .where(EXAM.ID.eq(examId)).fetchOneInto(Exam.class)
        );
    }

    private void setExamRecordFieldValue(ExamRecord examRecord,
                                         Integer clientType,String userIp,
                                         Integer noAnswerCount, Integer answeredCount,
                                         String clientVersion) {
        examRecord.setClientType(clientType);
        examRecord.setIsReset(null);
        examRecord.setExceptionOrder(null);
        examRecord.setUserIp(userIp);
        examRecord.setNoAnswerCount(noAnswerCount);
        examRecord.setAnsweredCount(answeredCount);
        examRecord.setClientVersion(clientVersion);
    }

    /**
     * 处理交卷操作
     * @param examRecord 考试记录
     * @param examId 考试Id
     * @param submitTime 交卷时间
     */
    private void handleSubmiting(Integer examRegion,String memberId,ExamRecord examRecord, String examId,
                                 Long submitTime) {

        LOGGER.info("开始交卷操作 ， 解析answerRecords ");
        Map<String, List<AnswerRecord>> answerRecordMap = parseAnswerRecords(examRegion, memberId, examRecord);

        LOGGER.info(" 解析answerRecords 完成 ：{}",answerRecordMap);
        doingQuerysOperations(examRegion,examRecord, examId, answerRecordMap, submitTime);
    }

    /**
     * 一个事务执行交卷操作的sql
     * 	a.新增答题记录
     * 	b.更新答题记录
     * 	c.更新以往考试记录为非当前状态
     * 	d.实时更新考试记录字段
     * 	e.统计考试人数，考试人次
     * 	f.统计交卷类型人数 人次
     * @param examRecord
     * @param examId
     * @param answerRecordMap
     * @param submitTime
     */
    @DataSource
    public void doingQuerysOperations(Integer examRegion,ExamRecord examRecord,
                                       String examId, Map<String, List<AnswerRecord>> answerRecordMap,
                                       Long submitTime) {

        List<AnswerRecord> insertAnswerRecords = answerRecordMap.get("insert");
        List<AnswerRecord> updateAnswerRecords = answerRecordMap.get("update");

        examRecordDao.execute(dsl -> dsl.batch(
                submitPaperUpdateQuerys(examRegion,dsl,
                                        examRecord, examId,
                                        insertAnswerRecords,updateAnswerRecords,
                                        submitTime)).execute());
    }


    private Member getMember(String memberId) {
        return memberDao.execute(e -> e.select(
                                               Fields.start()
                                                     .add(MEMBER.ID)
                                                     .add(MEMBER.NAME)
                                                     .add(MEMBER.FULL_NAME)
                                                     .end()
                                       )
                                       .from(MEMBER)
                                       .where(MEMBER.ID.eq(memberId)).fetchOneInto(Member.class)
        );
    }

    @DataSource
    public List<Query> submitPaperUpdateQuerys(Integer examRegion,DSLContext context, ExamRecord examRecord,
                                                String examId, List<AnswerRecord> inserts, List<AnswerRecord> updates, Long submitTime) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<Query> temp = new ArrayList<>();

        if (hasCurrentRecordBefore(examRegion,examRecord)) {
            temp.add(
                    context.update(examRecordTable)
                           .set(examRecordTable.field("f_is_current", Integer.class), ExamRecord.IS_NOT_CURRENT)
                           .where(examRecordTable.field("f_exam_id", String.class).eq(examId),
                                  examRecordTable.field("f_member_id", String.class).eq(examRecord.getMemberId()),
                                  examRecordTable.field("f_create_time", Long.class).lt(examRecord.getCreateTime()),
                                  examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
            );
        }

        List<Query> insertAnswerRecordQuerys = inserts.stream().map(t -> {
            return context.insertInto(
                    table, table.field("f_id", String.class),
                    table.field("f_create_time", Long.class),
                    table.field("f_answer", String.class),
                    table.field("f_exam_record_id", String.class),
                    table.field("f_question_id", String.class),
                    table.field("f_is_right", Integer.class)
            ).values(
                    t.getId(),
                    t.getCreateTime(),
                    t.getAnswer(),
                    t.getExamRecordId(),
                    t.getQuestionId(),
                    t.getIsRight()
            );
        }).collect(Collectors.toList());

        List<Query> updateAnswerRecordQuerys = updates.stream().map(t -> {
            return context.update(table)
                          .set(table.field("f_answer", String.class), t.getAnswer())
                          .where(table.field("f_id", String.class).eq(t.getId()));
        }).collect(Collectors.toList());

        temp.addAll(insertAnswerRecordQuerys);
        temp.addAll(updateAnswerRecordQuerys);

        temp.add(context.update(examRecordTable)
                        .set(examRecordTable.field("f_last_submit_time", Long.class), submitTime)
                        .set(examRecordTable.field("f_is_reset", Integer.class), examRecord.getIsReset())
                        .set(examRecordTable.field("f_exception_order", Integer.class), examRecord.getExceptionOrder())
                        .set(examRecordTable.field("f_client_type", Integer.class), examRecord.getClientType())
                        .set(examRecordTable.field("f_user_ip", String.class), examRecord.getUserIp())
                        .set(examRecordTable.field("f_no_answer_count", Integer.class), examRecord.getNoAnswerCount())
                        .set(examRecordTable.field("f_answered_count", Integer.class), examRecord.getAnsweredCount())
                        .set(examRecordTable.field("f_client_version", String.class), examRecord.getClientVersion())
                        .where(examRecordTable.field("f_id", String.class).eq(examRecord.getId()))
        );

        return temp;
    }

    public Map<String, List<AnswerRecord>> parseAnswerRecords(Integer examRegion,String memberId,ExamRecord examRecord) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examRecord.getExamId()));

        List<AnswerRecord> existedRecords = getExistsAnswerRecord(examRegion,examRecord, table);

        LOGGER.info("getExistsAnswerRecord:{}", existedRecords);

        //将已经存在的答题记录和当前流水表中做比较后做新增修改操作。
        List<AnswerRecordProcess> answerRecordProcessList = answerRecordProcessService.getListByExamRecordId(examRegion, memberId, examRecord.getId(), null);
        LOGGER.info("流水表中获取当前答题信息 ： {}", answerRecordProcessList);

        Map<String, AnswerRecord> questionIdToAnswerRecordMap = existedRecords.stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, Function.identity(), (k1, k2) -> k1));
        LOGGER.info("questionIdToAnswerRecordMap:{}", questionIdToAnswerRecordMap);

        List<AnswerRecord> answerRecordToInsert = filterToInsert(answerRecordProcessList, questionIdToAnswerRecordMap,examRecord.getId());
        LOGGER.info("answerRecordToInsert:{}", answerRecordToInsert);

        List<AnswerRecord>  answerRecordToUpdate = filterToUpdate(answerRecordProcessList, questionIdToAnswerRecordMap);
        LOGGER.info("answerRecordToUpdate:{}", answerRecordToUpdate);

        Map<String, List<AnswerRecord>> result = new HashMap<>();
        result.put("insert", answerRecordToInsert);
        result.put("update", answerRecordToUpdate);

        return result;
    }

    private List<AnswerRecord> filterToInsert(List<AnswerRecordProcess> answerRecordProcessList, Map<String, AnswerRecord> questionIdToAnswerRecordMap,String examRecordId) {
        return answerRecordProcessList.stream().filter(answerRecordProcess ->
                                                               !questionIdToAnswerRecordMap.containsKey(answerRecordProcess.getQuestionId()))
                                      .map(answerRecordProcess -> buildAnswerRecord(answerRecordProcess,examRecordId))
                                      .collect(Collectors.toList());
    }

    private List<AnswerRecord>  filterToUpdate(List<AnswerRecordProcess> answerRecordProcessList, Map<String, AnswerRecord> questionIdToAnswerRecordMap) {
        return answerRecordProcessList.stream().filter(answerRecordProcess -> {
            AnswerRecord existingAnswerRecord = questionIdToAnswerRecordMap.get(answerRecordProcess.getQuestionId());
            return existingAnswerRecord != null && !Objects.equals(answerRecordProcess.getAnswer(), existingAnswerRecord.getAnswer());
        }).map(answerRecordProcess -> buildUpdateAnswerRecord(questionIdToAnswerRecordMap.get(answerRecordProcess.getQuestionId()), answerRecordProcess)).collect(Collectors.toList());
    }

    private AnswerRecord buildUpdateAnswerRecord(AnswerRecord existingAnswerRecord,AnswerRecordProcess answerRecordProcess) {
        existingAnswerRecord.setIsRight(null);
        existingAnswerRecord.setScore(null);
        existingAnswerRecord.setAnswer(answerRecordProcess.getAnswer());

        return existingAnswerRecord;
    }

    private AnswerRecord buildAnswerRecord(AnswerRecordProcess answerRecordProcess,String examRecordId) {
        AnswerRecord answerRecord = new AnswerRecord();
        answerRecord.forInsert();
        answerRecord.setIsRight(null);
        answerRecord.setScore(null);
        answerRecord.setQuestionId(answerRecordProcess.getQuestionId());
        answerRecord.setExamRecordId(examRecordId);
        answerRecord.setAnswer(answerRecordProcess.getAnswer());
        return  answerRecord;
    };

    @DataSource
    public List<AnswerRecord> getExistsAnswerRecord(Integer examRegion,ExamRecord examRecord, TableImpl<?> table) {
        return answerRecordDao.execute(e -> {
            return e.select(
                            Fields.start()
                                  .add(table.fields())
                                  .add(QUESTION_COPY.ID)
                                  .add(QUESTION_COPY.TYPE)
                                  .end()
                    )
                    .from(table)
                    .leftJoin(QUESTION_COPY).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
                    .where(table.field("f_exam_record_id", String.class).eq(examRecord.getId()))
                    .fetch(r -> {
                        AnswerRecord answerRecord = new AnswerRecord();
                        answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                        answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                        answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                        answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                        answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                        answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                        answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                        QuestionCopy questionCopy = new QuestionCopy();
                        questionCopy.setId(r.getValue(QUESTION_COPY.ID));
                        questionCopy.setType(r.getValue(QUESTION_COPY.TYPE));
                        answerRecord.setQuestionCopy(questionCopy);
                        return answerRecord;
                    });
        });
    }

    /**
     * 判断之前记录是否有isCurrent=0的数据
     * 如果有，
     * @param examRecord
     * @return
     */
    @DataSource
    public boolean hasCurrentRecordBefore(Integer examRegion, ExamRecord examRecord) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examRecord.getExamId()));

        Integer count = examRecordDao.execute(e ->
                          e.select(DSL.count(examRecordTable.field("f_id", String.class)))
                           .from(examRecordTable)
                           .where(
                                   examRecordTable.field("f_exam_id", String.class).eq(examRecord.getExamId()),
                                   examRecordTable.field("f_member_id", String.class).eq(examRecord.getMemberId()),
                                   examRecordTable.field("f_create_time", Long.class).lt(examRecord.getCreateTime()),
                                   examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                           ).fetchOne(DSL.count(examRecordTable.field("f_id", String.class))));

        return count > 0;
    }

    /**
     * 统计交卷类型人次人数sql
     * @param context
     * @param submitDetailType
     * @return
     */
    private Query getCountSubmitTypeSql(ExamRecord examRecord, DSLContext context, Integer submitDetailType) {
        Map<Integer, TableField<com.zxy.product.exam.jooq.tables.records.ExamRecord, Integer>> fieldMap = new HashMap<>();
        fieldMap.put(submitDetailType, getCountSubmitTypeTimeField(submitDetailType));
        return context.update(EXAM)
                      .set(fieldMap.get(submitDetailType), fieldMap.get(submitDetailType).add(1))
                      .where(EXAM.ID.eq(examRecord.getExamId()));
    }

    private void putExistedIdInNewRecord(AnswerRecord answerRecord,List<AnswerRecord> olds) {
        if (olds != null) {
            olds.forEach(e -> {
                if (e.getExamRecordId().equals(answerRecord.getExamRecordId()) && e.getQuestionId().equals(answerRecord.getQuestionId())) {
                    answerRecord.setId(e.getId());
                    answerRecord.setCreateTime(e.getCreateTime());
                }
            });
        }
    }

    /**
     * @param submitDetailType
     * @return
     */
    private TableField getCountSubmitTypeTimeField(Integer submitDetailType) {
        if (submitDetailType.equals(ExamRecord.SUBMIT_TYPE_HAND)) return EXAM.NORMAL_SUBMIT_TIME;
        if (submitDetailType.equals(ExamRecord.SUBMIT_TYPE_FULL_SWITCH)) return EXAM.SWITCH_SUBMIT_PERSON_TIME;
        if (submitDetailType.equals(ExamRecord.SUBMIT_TYPE_TIMEOUT)) return EXAM.OVER_TIME_SUBMIT_TIME;
        if (submitDetailType.equals(ExamRecord.SUBMIT_TYPE_FORCE)) return EXAM.FORCE_SUBMIT_TIME;
        return EXAM.NORMAL_SUBMIT_TIME;
    }


}
