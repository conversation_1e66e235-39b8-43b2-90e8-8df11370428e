package com.zxy.product.examstu.service.rule.preaudit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ProfessionLevel;

/**
 * Created by ouyang on 2017/11/1.
 * 预审核规则(默认规则)：如果当前考试等级是L1级，则直接通过审核
 */
@Component("defaultPreAuditRule")
public class DefaultPreAuditRule implements PreAuditRule {

    private CommonDao<ProfessionLevel> professionLevelDao;

    @Autowired
    public void setProfessionLevelDao(CommonDao<ProfessionLevel> professionLevelDao) {
        this.professionLevelDao = professionLevelDao;
    }

    @Override
    public boolean audit(Exam exam, String memberId) {
        //如果当前考试的等级是L1，可通过
        if (exam != null && exam.getLevelId() != null && ProfessionLevel.LEVEL_ID_1.equals(exam.getLevelId())) {
            return true;
        }
        return false;
    }
}
