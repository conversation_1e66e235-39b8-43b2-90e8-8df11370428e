package com.zxy.product.examstu.service.component.sequence;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.product.examstu.api.sequence.SequenceGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * redis实现序列号生成器
 * <AUTHOR>
 * @date 2017年11月11日
 */
@Service
public class RedisSequenceGenerator implements SequenceGenerator {

    @SuppressWarnings("unused")
    private CacheService cacheService;

    private Cache sequenceCache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cacheService = cacheService;
        this.sequenceCache = cacheService.create("exam-service", RedisSequenceGenerator.class.getName());
    }

    @Override
    public Long getSequence(String type) {
        return sequenceCache.increment(type, 1L);
    }

    @Override
    public void setSequence(String type, Long sequence) {
        // 注意这里不能使用get和set方法来设置 会出现类型异常
        Long seq = getSequence(type);
        sequenceCache.increment(type, sequence - seq);
    }
}
