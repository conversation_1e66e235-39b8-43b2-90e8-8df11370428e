package com.zxy.product.examstu.service.support;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.examstu.content.MessageConstant;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.rule.preaudit.PreAuditRule;
import com.zxy.product.examstu.service.rule.preaudit.PreAuditRuleFactoryBean;
import com.zxy.product.examstu.service.util.GetTableUtil;
import com.zxy.product.system.util.DateUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class SignUpServiceSupport implements SignUpService{


    // 预审核状态，1：待审核，2：通过，3：拒绝
    private static final int PRE_STATUS_WAIT_APPROVAL = 1;
    private static final int PRE_STATUS_PASS = 2;
    private static final int PRE_STATUS_REFUSE = 3;

    private static final Integer PER_SIZE = 1000;

    private CommonDao<PersonalTemplate> personalTemplateDao;

    private CommonDao<SignUpAuth> signUpAuthDao;

    private CommonDao<SignupRecord> signUpRecordDao;

    private CommonDao<SignUp> signUpDao;

    private CommonDao<Member> memberDao;

    private MessageSender messageSender;

    private CommonDao<Exam> examDao;

    private CommonDao<CertificateRecord> certificateRecordDao;

    private CommonDao<ExamRecord> examRecordDao;

    private CommonDao<CloudSignup> cloudSignupDao;

    private CommonDao<GridSignup> gridSignupDao;

    private PaperInstanceService paperInstanceService;

    private ExamRecordService examRecordService;

    private ExamNoticeService examNoticeService;

    private CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setGridSignupDao(CommonDao<GridSignup> gridSignupDao) {
        this.gridSignupDao = gridSignupDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    private PreAuditRuleFactoryBean factoryBean;

    @Value("${network.organizationId}")
    private String orgId;

    @Value("${jituan.organizationId}")
    private String jituanPath;


    @Autowired
    public void setCloudSignupDao(CommonDao<CloudSignup> cloudSignupDao) {
        this.cloudSignupDao = cloudSignupDao;
    }

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }

    @Autowired
    public void setPersonalTemplateDao(CommonDao<PersonalTemplate> personalTemplateDao) {
        this.personalTemplateDao = personalTemplateDao;
    }

    @Autowired
    public void setSignUpAuthDao(CommonDao<SignUpAuth> signUpAuthDao) {
        this.signUpAuthDao = signUpAuthDao;
    }

    @Autowired
    public void setSignUpRecordDao(CommonDao<SignupRecord> signUpRecordDao) {
        this.signUpRecordDao = signUpRecordDao;
    }

    @Autowired
    public void setExamNoticeService(ExamNoticeService examNoticeService) {
        this.examNoticeService = examNoticeService;
    }

    @Autowired
    public void setSignUpDao(CommonDao<SignUp> signUpDao) {
        this.signUpDao = signUpDao;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService) {
        this.examRecordService = examRecordService;
    }

    @Autowired
    public void setExamStudyPlanConfigCommonDao(CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao) {
        this.examStudyPlanConfigCommonDao = examStudyPlanConfigCommonDao;
    }


    private Optional<List<String>> findOrgIdsByOrgDetail(Optional<String> organizationId) {
        return organizationId.map(orgId -> {
            return examRecordDao.execute(e ->
                    e.select(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL)
                            .where(ORGANIZATION_DETAIL.ROOT.eq(orgId)).fetch(ORGANIZATION_DETAIL.SUB));
        });
    }

    /**
     * 单个修改
     * @param id
     * @param status
     * @return
     */
    @Override
    @DataSource
    public SignUp update(
            Integer examRegion,
            String id,
            Integer status,
            String memberId) {

        SignUp signUp = signUpDao.get(id);
        signUp.setStatus(status);
        signUp.setCreateTime(System.currentTimeMillis());
        signUp.setModifyDate(null);
        signUp = signUpDao.update(signUp);

        String examId = signUp.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    signUp.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());

            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }

        // 统计报名人数
        if (status == SignUp.STATUS_PASSED || status == SignUp.STATUS_APPROVE) {
            //calculateExamApplicantNum(Arrays.asList(examId));
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, signUp.getExamId(),
                    MessageHeaderContent.IDS, signUp.getMemberId()
            );
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }
        return signUp;
    }

    public CloudSignup updateForCloudExam(
            Integer examRegion,
            String id,
            Integer status,
            String memberId) {

        CloudSignup cloudSignup = cloudSignupDao.get(id);
        cloudSignup.setStatus(status);
        cloudSignup.setCreateTime(System.currentTimeMillis());
        cloudSignup = cloudSignupDao.update(cloudSignup);

        String examId = cloudSignup.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    cloudSignup.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }

        // 统计报名人数
        if (status == SignUp.STATUS_PASSED || status == SignUp.STATUS_APPROVE) {
            //calculateExamApplicantNum(Arrays.asList(examId));
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, cloudSignup.getExamId(),
                    MessageHeaderContent.IDS, cloudSignup.getMemberId()
            );
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }
        return cloudSignup;
    }
    public GridSignup updateForGridExam(
            Integer examRegion,
            String id,
            Integer status,
            String memberId) {

        GridSignup gridSignup = gridSignupDao.get(id);
        gridSignup.setStatus(status);
        gridSignup.setCreateTime(System.currentTimeMillis());
        gridSignup = gridSignupDao.update(gridSignup);

        String examId = gridSignup.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    gridSignup.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }

        // 统计报名人数
        if (status == SignUp.STATUS_PASSED || status == SignUp.STATUS_APPROVE) {
            //calculateExamApplicantNum(Arrays.asList(examId));
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, gridSignup.getExamId(),
                    MessageHeaderContent.IDS, gridSignup.getMemberId()
            );
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }

        }
        return gridSignup;
    }

    private SignUp preUpdate(
            Integer examRegion,
            String id,
            Integer status,
            String memberId, String preAuditMemberId) {

        SignUp signUp = signUpDao.get(id);
        signUp.setStatus(status);
        signUp.setCreateTime(System.currentTimeMillis());
        signUp.setAuditMemberId(preAuditMemberId);
        signUp.setModifyDate(null);
        signUp = signUpDao.update(signUp);

        String examId = signUp.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    signUp.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());

            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }
        return signUp;
    }
    private CloudSignup preUpdateForCloudExam(
            Integer examRegion,
            String id,
            Integer status,
            String memberId, String preAuditMemberId) {

        CloudSignup cloudSignup = cloudSignupDao.get(id);
        cloudSignup.setStatus(status);
        cloudSignup.setCreateTime(System.currentTimeMillis());
        cloudSignup.setAuditMemberId(preAuditMemberId);
        cloudSignup = cloudSignupDao.update(cloudSignup);

        String examId = cloudSignup.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    cloudSignup.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }
        return cloudSignup;
    }
    private GridSignup preUpdateForGridExam(
            Integer examRegion,
            String id,
            Integer status,
            String memberId, String preAuditMemberId) {

        GridSignup gridSignup = gridSignupDao.get(id);
        gridSignup.setStatus(status);
        gridSignup.setCreateTime(System.currentTimeMillis());
        gridSignup.setAuditMemberId(preAuditMemberId);
        gridSignup = gridSignupDao.update(gridSignup);

        String examId = gridSignup.getExamId();
        //报名通过  添加考试记录
        if (status == SignUp.STATUS_PASSED) {
            PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
            examRecordService.insert(
                    examRegion,
                    examId,
                    paperInstance.getId(),
                    gridSignup.getMemberId(),
                    ExamRecord.STATUS_TO_BE_STARTED,
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            // TODO 学习计划-新增单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }

        //取消报名
        if (status == SignUp.STATUS_CANCEL) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

            List<String> examRecordIds = examRecordDao.execute(x -> x.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class)).end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                    .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .fetch(examRecordTable.field("f_id", String.class))
            );

            examRecordDao.execute(dslContext ->
                    dslContext.delete(examRecordTable).where(
                            examRecordTable.field("f_id", String.class).in(examRecordIds)
                    ).execute());

            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_DELETE,
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.IDS, memberId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
            );

            // TODO 学习计划-删除单个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        //异步消息
                        messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                MessageHeaderContent.BUSINESS_ID, examId,
                                MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                MessageHeaderContent.EXAM_MEMBER_ID, memberId
                        );
                    }
                }
            }
        }
        return gridSignup;
    }

    /**
     * 批量修改-审核
     */
    @Override
    @DataSource
    public List<SignUp> update(
            Integer examRegion,
            List<String> signUpIds,
            Integer status,
            String currentUserId,
            Optional<Integer> noticeUser,
            Optional<String> noticeUserText,
            Optional<String> noticeUserContent, String uri) {
        // 消息通知集（考试为维度）
        List<ExamNotice> examNotices = new ArrayList<>();
        if (signUpIds.size() > 0) {
            List<SignUp> signUps = new ArrayList<SignUp>();
            if (Exam.CLOUD_URI.equals(uri)) {
                // 移动云报名记录集合
                signUps = getCloudSignup(signUpIds);
            } else if (Exam.GRID_URI.equals(uri)) {
                // 网格长报名记录
                signUps = getGridSignup(signUpIds);
            } else {
                // 报名记录集合
                signUps = getSignup(signUpIds);
            }

            if (org.springframework.util.CollectionUtils.isEmpty(signUps)){
                return signUps;
            }

            // 以考试为维度处理审批数据
            Map<String, List<SignUp>> examMap = signUps.stream().filter(e -> e.getExamId() != null).collect(Collectors.groupingBy(SignUp::getExamId));
            Long time = System.currentTimeMillis();


            examMap.forEach((examId, examSignUps)-> {
                if ( examSignUps != null && examSignUps.size() > 0 && examSignUps.get(0) != null) {
                    SignUp signUp = examSignUps.get(0);
                    Exam exam = signUp.getExam();

                    Integer noticeType = getNoticeType(status);
                    String templateCode = getTemplateCodeV2(noticeType, exam.getFaceMonitor(), exam.getFaceEnter());

                    // 不在允许的审核时间内
                    boolean notAuditTime = (exam.getAuditStartTime() != null && exam.getAuditEndTime() != null)
                            && (exam.getAuditStartTime() > time || exam.getAuditEndTime() < time);
                    if (notAuditTime) {
                        throw new UnprocessableException(ErrorCode.ExamNotAuditTime);
                    }
                    // 单场考试审批的人员
                    List<String> examMemberIds = new ArrayList();
                    examSignUps.forEach(sign -> {
                        if (sign.getStatus() == SignUp.STATUS_CANCEL) {
                            throw new UnprocessableException(ErrorCode.HasSignUpBeCancel);
                        }
                        examMemberIds.add(sign.getMemberId());
                    });
                    // 拼装消息通知
                    appendExamNotice(currentUserId, noticeUser, noticeUserText, noticeUserContent,
                            noticeType, templateCode, examId, examMemberIds, examNotices);
                    // 审核通过生成考试record记录，审核拒绝要删除未开始的record记录
                    if (status == SignUp.STATUS_PASSED) {
                        examRecordService.insert(examRegion, examId, examMemberIds);
                    }
                    // 单场考试审批异步消息
                    sendMessage(examRegion, examId, examSignUps, status);
                }
            });

            if (Exam.CLOUD_URI.equals(uri)) {
                // 移动云考试更新这些报名记录审核状态和审核人
                cloudSignupDao.execute(e ->
                        e.update(CLOUD_SIGNUP)
                                .set(CLOUD_SIGNUP.STATUS, status)
                                .set(CLOUD_SIGNUP.AUDIT_MEMBER_ID, currentUserId)
                                .where(CLOUD_SIGNUP.ID.in(signUpIds)).execute());
            } else if (Exam.GRID_URI.equals(uri)) {
                // 网格长考试更新这些报名记录审核状态和审核人
                gridSignupDao.execute(e ->
                        e.update(GRID_SIGNUP)
                                .set(GRID_SIGNUP.STATUS, status)
                                .set(GRID_SIGNUP.AUDIT_MEMBER_ID, currentUserId)
                                .where(GRID_SIGNUP.ID.in(signUpIds)).execute());
            } else {
                // 更新这些报名记录审核状态和审核人
                signUpDao.execute(e ->
                        e.update(SIGNUP)
                                .set(SIGNUP.STATUS, status)
                                .set(SIGNUP.AUDIT_MEMBER_ID, currentUserId)
                                .set(SIGNUP.MODIFY_DATE, new Timestamp(System.currentTimeMillis()))
                                .where(SIGNUP.ID.in(signUpIds)).execute());
            }

            // 更新报名流水记录审核状态和审核人
            signUpRecordDao.execute(e -> e.update(SIGNUP_RECORD)
                    .set(SIGNUP_RECORD.AUDIT_STATUS, status)
                    .set(SIGNUP_RECORD.AUDIT_MEMBER_ID, currentUserId)
                    .where(SIGNUP_RECORD.SIGNUP_ID.in(signUpIds)
                            .and(SIGNUP_RECORD.IS_CURRENT.eq(SignupRecord.IS_CURRENT_YES)))
                    .execute()
            );

            // 计算这些考试的报名人数
//            calculateExamApplicantNum(new ArrayList<>(examMap.keySet()));

            // 报名审核发送通知
            examNoticeService.batchInsert(examNotices);

            return signUps;
        }
        return null;
    }

    /** 根据审批状态得到消息类型 */
    private Integer getNoticeType(Integer status){
        Map<Integer, Integer> noticeTypeMap = ImmutableMap.of(
                SignUp.STATUS_PASSED, ExamNotice.TYPE_SIGN_UP_PASS,
                SignUp.STATUS_REFUSE, ExamNotice.TYPE_SIGN_UP_REFUSE);
        return noticeTypeMap.get(status);
    }

    private List<SignUp> getSignup(List<String> signUpIds) {
        return signUpDao.execute(e -> e.select(
                                Fields.start()
                                        .add(SIGNUP)
                                        .add(EXAM.ID)
                                        .add(EXAM.AUDIT_START_TIME)
                                        .add(EXAM.AUDIT_END_TIME)
                                        .add(EXAM.FACE_MONITOR)
                                        .add(EXAM.FACE_ENTER)
                                        .end()
                        ).from(SIGNUP)
                        .leftJoin(EXAM).on(EXAM.ID.eq(SIGNUP.EXAM_ID))
                        .where(SIGNUP.ID.in(signUpIds))
                        .fetch(r -> {
                            SignUp signUp = r.into(SignUp.class);
                            Exam exam = new Exam();
                            exam.setId(r.getValue(EXAM.ID));
                            exam.setAuditStartTime(r.getValue(EXAM.AUDIT_START_TIME));
                            exam.setAuditEndTime(r.getValue(EXAM.AUDIT_END_TIME));
                            exam.setFaceMonitor(r.getValue(EXAM.FACE_MONITOR));
                            signUp.setExam(exam);
                            return signUp;
                        })
        );
    }

    private List<SignUp> getCloudSignup(List<String> signUpIds) {
        return cloudSignupDao.execute(e -> e.select(
                                Fields.start()
                                        .add(CLOUD_SIGNUP.ID)
                                        .add(CLOUD_SIGNUP.CREATE_TIME)
                                        .add(CLOUD_SIGNUP.EXAM_ID)
                                        .add(CLOUD_SIGNUP.MEMBER_ID)
                                        .add(CLOUD_SIGNUP.ORGANIZATION_ID)
                                        .add(CLOUD_SIGNUP.STATUS)
                                        .add(CLOUD_SIGNUP.AUDIT_MEMBER_ID)
                                        .add(EXAM.ID)
                                        .add(EXAM.AUDIT_START_TIME)
                                        .add(EXAM.AUDIT_END_TIME)
                                        .add(EXAM.FACE_MONITOR)
                                        .end()
                        ).from(CLOUD_SIGNUP)
                        .leftJoin(EXAM).on(EXAM.ID.eq(CLOUD_SIGNUP.EXAM_ID))
                        .where(CLOUD_SIGNUP.ID.in(signUpIds))
                        .fetch(r -> {
                            SignUp signUp = new SignUp();
                            signUp.setId(r.getValue(CLOUD_SIGNUP.ID));
                            signUp.setCreateTime(r.getValue(CLOUD_SIGNUP.CREATE_TIME));
                            signUp.setExamId(r.getValue(CLOUD_SIGNUP.EXAM_ID));
                            signUp.setMemberId(r.getValue(CLOUD_SIGNUP.MEMBER_ID));
                            signUp.setOrganizationId(r.getValue(CLOUD_SIGNUP.ORGANIZATION_ID));
                            signUp.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
                            signUp.setAuditMemberId(r.getValue(CLOUD_SIGNUP.AUDIT_MEMBER_ID));
                            Exam exam = new Exam();
                            exam.setId(r.getValue(EXAM.ID));
                            exam.setAuditStartTime(r.getValue(EXAM.AUDIT_START_TIME));
                            exam.setAuditEndTime(r.getValue(EXAM.AUDIT_END_TIME));
                            exam.setFaceMonitor(r.getValue(EXAM.FACE_MONITOR));
                            signUp.setExam(exam);
                            return signUp;
                        })
        );
    }

    private List<SignUp> getGridSignup(List<String> signUpIds) {
        return gridSignupDao.execute(e -> e.select(
                                Fields.start()
                                        .add(GRID_SIGNUP.ID)
                                        .add(GRID_SIGNUP.CREATE_TIME)
                                        .add(GRID_SIGNUP.EXAM_ID)
                                        .add(GRID_SIGNUP.MEMBER_ID)
                                        .add(GRID_SIGNUP.STATUS)
                                        .add(GRID_SIGNUP.AUDIT_MEMBER_ID)
                                        .add(EXAM.ID)
                                        .add(EXAM.AUDIT_START_TIME)
                                        .add(EXAM.AUDIT_END_TIME)
                                        .add(EXAM.FACE_MONITOR)
                                        .add(EXAM.FACE_ENTER)
                                        .end()
                        ).from(GRID_SIGNUP)
                        .leftJoin(EXAM).on(EXAM.ID.eq(GRID_SIGNUP.EXAM_ID))
                        .where(GRID_SIGNUP.ID.in(signUpIds))
                        .fetch(r -> {
                            SignUp signUp = new SignUp();
                            signUp.setId(r.getValue(GRID_SIGNUP.ID));
                            signUp.setCreateTime(r.getValue(GRID_SIGNUP.CREATE_TIME));
                            signUp.setExamId(r.getValue(GRID_SIGNUP.EXAM_ID));
                            signUp.setMemberId(r.getValue(GRID_SIGNUP.MEMBER_ID));
                            signUp.setStatus(r.getValue(GRID_SIGNUP.STATUS));
                            signUp.setAuditMemberId(r.getValue(GRID_SIGNUP.AUDIT_MEMBER_ID));
                            Exam exam = new Exam();
                            exam.setId(r.getValue(EXAM.ID));
                            exam.setAuditStartTime(r.getValue(EXAM.AUDIT_START_TIME));
                            exam.setAuditEndTime(r.getValue(EXAM.AUDIT_END_TIME));
                            exam.setFaceMonitor(r.getValue(EXAM.FACE_MONITOR));
                            signUp.setExam(exam);
                            return signUp;
                        })
        );
    }

    /** 拼装消息通知 */
    private void appendExamNotice(String currentUserId, Optional<Integer> noticeUser, Optional<String> noticeUserText,
                                  Optional<String> noticeUserContent, Integer noticeType,  String templateCode,
                                  String examId, List<String> examMemberIds, List<ExamNotice> examNotices) {
        ExamNotice notice = new ExamNotice();
        notice.forInsert();
        notice.setBusinessType(ExamNotice.BUSINESS_TYPE_EXAM);
        notice.setBusinessId(examId);
        notice.setNoticeUser(noticeUser.orElse(ExamNotice.NOTICE_NO));
        notice.setNoticeType(noticeType);
        notice.setReceivers(examMemberIds.stream().collect(Collectors.joining(",")));
        notice.setNoticeUserText(noticeUserText.orElse(null));
        notice.setNoticeUserContent(noticeUserContent.orElse(null));
        notice.setCreateMemberId(currentUserId);
        notice.setTempletCode(templateCode);
        examNotices.add(notice);
    }


    /** 根据消息类型得到消息模板编码 */
    private String getTemplateCode(Integer noticeType){
        Map<Integer, String> map = ImmutableMap.of(
                ExamNotice.TYPE_SIGN_UP, MessageConstant.EXAM_SIGN_UP,
                ExamNotice.TYPE_SIGN_UP_PASS, MessageConstant.EXAM_AUDIT_PASS,
                ExamNotice.TYPE_SIGN_UP_REFUSE, MessageConstant.EXAM_AUDIT_REFUSE
        );
        return map.get(noticeType);
    }

    /**
     * 根据消息类型得到消息模板编码
     * 由于考试新增了人脸监考，因此这里进行了修改
     * */
    private String getTemplateCodeV2(Integer noticeType, Integer faceMonitor, Integer faceEnter){
        String code = getTemplateCode(noticeType);

        //  如果审批通过并且是人脸监考 则使用人脸监考的模板
        boolean isFaceExam = Exam.FACE_ENTER_YES.equals(faceEnter) || Exam.FACE_MONITOR_YES.equals(faceMonitor);
        if (isFaceExam && MessageConstant.EXAM_AUDIT_PASS.equals(code)) {
            return MessageConstant.EXAM_FACE_MONITOR_AUDIT_PASS;
        }
        return code;
    }

    private void sendMessage(Integer examRegion, String examId, List<SignUp> signUpList, Integer status) {
        if (new Integer(SignUp.STATUS_PASSED).equals(status)) {
            messageSender.send(MessageTypeContent.EXAM_SIGNUP_PASS,
                    MessageHeaderContent.IDS, signUpList.stream().map(SignUp::getMemberId).collect(Collectors.joining(",")),
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
            // TODO 学习计划-新增多个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        for (int i = 0; i < signUpList.size(); i++) {
                            //异步消息
                            messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                    MessageHeaderContent.BUSINESS_ID, examId,
                                    MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                    MessageHeaderContent.EXAM_MEMBER_ID, signUpList.get(i).getMemberId()
                            );
                        }
                    }
                }
            }

        } else if (new Integer(SignUp.STATUS_REFUSE).equals(status)) {
            messageSender.send(MessageTypeContent.EXAM_SIGNUP_REFUSE,
                    MessageHeaderContent.IDS, signUpList.stream().map(SignUp::getMemberId).collect(Collectors.joining(",")),
                    MessageHeaderContent.EXAM_ID, examId,
                    MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
            // TODO 学习计划-删除多个
            Exam exam = examDao.get(examId);
            if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                if (CollectionUtils.isNotEmpty(list)) {
                    ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                    if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                        for (int i = 0; i < signUpList.size(); i++) {
                            //异步消息
                            messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE,
                                    MessageHeaderContent.BUSINESS_ID, examId,
                                    MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                    MessageHeaderContent.EXAM_MEMBER_ID, signUpList.get(i).getMemberId()
                            );
                        }
                    }
                }
            }
        }

    }

    private void insertExamNotice(String examId, ExamNotice examNotice) {

        Map<Integer, String> map = ImmutableMap.of(
                ExamNotice.TYPE_SIGN_UP, MessageConstant.EXAM_SIGN_UP,
                ExamNotice.TYPE_SIGN_UP_PASS, MessageConstant.EXAM_AUDIT_PASS,
                ExamNotice.TYPE_SIGN_UP_REFUSE, MessageConstant.EXAM_AUDIT_REFUSE
        );

        examNoticeService.insert(
                ExamNotice.BUSINESS_TYPE_EXAM,
                examId,
                examNotice.getNoticeType(),
                examNotice.getCreateMemberId(),
                map.get(examNotice.getNoticeType()),
                Optional.ofNullable(examNotice.getNoticeUser()),
                Optional.ofNullable(examNotice.getNoticeUserText()),
                Optional.ofNullable(examNotice.getNoticeUserContent()),
                Optional.ofNullable(examNotice.getReceiverIds())
        );
    }

    @Override
    @DataSource
    public SignUp getByExamIdAndMemberId(Integer examRegion, String examId, String memberId) {
        return signUpDao.fetchOne(SIGNUP.EXAM_ID.eq(examId).and(SIGNUP.MEMBER_ID.eq(memberId))).orElse(null);
    }

    public CloudSignup getByExamIdAndMemberIdForCloudExam(String examId, String memberId) {
        List<CloudSignup> cloudSignupList = cloudSignupDao.fetch(CLOUD_SIGNUP.EXAM_ID.eq(examId).and(CLOUD_SIGNUP.MEMBER_ID.eq(memberId)));
        if(cloudSignupList != null && cloudSignupList.size() > 0)
            return cloudSignupList.get(0);
        return null;
    }

    public GridSignup getByExamIdAndMemberIdForGridExam(String examId, String memberId) {
        List<GridSignup> gridSignupList = gridSignupDao.fetch(GRID_SIGNUP.EXAM_ID.eq(examId).and(GRID_SIGNUP.MEMBER_ID.eq(memberId)));
        return CollectionUtils.isNotEmpty(gridSignupList) ? gridSignupList.get(0) : null;
    }

    @Override
    @DataSource
    public CloudSignup getByExamIdAndMemberIdForCloudExamInterface(Integer examRegion, String examId, String memberId) {
        List<CloudSignup> cloudSignupList = cloudSignupDao.fetch(CLOUD_SIGNUP.EXAM_ID.eq(examId).and(CLOUD_SIGNUP.MEMBER_ID.eq(memberId)));
        if(cloudSignupList != null && cloudSignupList.size() > 0)
            return cloudSignupList.get(0);
        return null;
    }

    @Override
    @DataSource
    public GridSignup getByExamIdAndMemberIdForGridExamInterface(Integer examRegion, String examId, String memberId) {
        List<GridSignup> gridSignupList = gridSignupDao.fetch(GRID_SIGNUP.EXAM_ID.eq(examId).and(GRID_SIGNUP.MEMBER_ID.eq(memberId)));
        if(gridSignupList != null && gridSignupList.size() > 0)
            return gridSignupList.get(0);
        return null;
    }

    /**
     * @param exam
     * @param memberId
     * @param organizationId
     * @param status
     * @param disableToSendAsyncMessage 为true ，禁止发异步消息，因为后面如果审核通过，会发生成考试记录的异步消息，防止examRegist重复生成
     * @return
     */
    @DataSource
    public SignUp insertSu(Integer examRegion, Exam exam, String memberId, String organizationId, int status, boolean disableToSendAsyncMessage) {
        SignUp signUp = new SignUp();
        signUp.setExamId(exam.getId());
        signUp.setMemberId(memberId);
        signUp.setOrganizationId(organizationId);
        signUp.setStatus(status);
        signUp.forInsert();
        signUpDao.insert(signUp);

        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, signUp.getExamId(),
                    MessageHeaderContent.IDS, signUp.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, signUp.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return signUp;
    }

    public CloudSignup insertForCloudExam(Exam exam, String memberId, String organizationId, int status, boolean disableToSendAsyncMessage, CloudSignup cloudSignup) {
        cloudSignup.setOrganizationId(organizationId);
        cloudSignup.setStatus(status);
        cloudSignupDao.insert(cloudSignup);

        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, cloudSignup.getExamId(),
                    MessageHeaderContent.IDS, cloudSignup.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, cloudSignup.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return cloudSignup;
    }

    public GridSignup insertForGridExam(Exam exam, String memberId, int status, boolean disableToSendAsyncMessage, GridSignup gridSignup) {
        gridSignup.setStatus(status);
        gridSignupDao.insert(gridSignup);

        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, gridSignup.getExamId(),
                    MessageHeaderContent.IDS, gridSignup.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, gridSignup.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return gridSignup;
    }

    private SignUp preInsert(Exam exam, String memberId, String organizationId, int status,
                             String preAuditMemberId, boolean disableToSendAsyncMessage) {

        SignUp signUp = new SignUp();
        signUp.setExamId(exam.getId());
        signUp.setMemberId(memberId);
        signUp.setOrganizationId(organizationId);
        signUp.setStatus(status);
        signUp.setAuditMemberId(preAuditMemberId);
        signUp.forInsert();
        signUpDao.insert(signUp);
// TODO
        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, signUp.getExamId(),
                    MessageHeaderContent.IDS, signUp.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, signUp.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return signUp;
    }
    private CloudSignup preInsertForCloudExam(Exam exam, String memberId, String organizationId, int status,
                                              String preAuditMemberId, boolean disableToSendAsyncMessage, CloudSignup cloudSignup) {

        cloudSignup.setOrganizationId(organizationId);
        cloudSignup.setStatus(status);
        cloudSignup.setAuditMemberId(preAuditMemberId);
        cloudSignupDao.insert(cloudSignup);
// TODO
        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, cloudSignup.getExamId(),
                    MessageHeaderContent.IDS, cloudSignup.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, cloudSignup.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return cloudSignup;
    }
    private GridSignup preInsertForGridExam(Exam exam, String memberId, int status,
                                            String preAuditMemberId, boolean disableToSendAsyncMessage, GridSignup gridSignup) {

        gridSignup.setStatus(status);
        gridSignup.setAuditMemberId(preAuditMemberId);
        gridSignupDao.insert(gridSignup);
// TODO
        if (!disableToSendAsyncMessage) {
            messageSender.send(
                    MessageTypeContent.EXAM_SIGNUP_INSERT,
                    MessageHeaderContent.EXAM_ID, gridSignup.getExamId(),
                    MessageHeaderContent.IDS, gridSignup.getMemberId(),
                    MessageHeaderContent.MEMBER_ID, gridSignup.getMemberId());
        }

        insertRemoteExamNotice(exam, memberId);

        return gridSignup;
    }

    private void insertRemoteExamNotice(Exam exam, String memberId) {
        // 消息通知
        if (exam.getPublisherId() != null) {
            examNoticeService.insert(
                    ExamNotice.BUSINESS_TYPE_EXAM,
                    exam.getId(),
                    ExamNotice.TYPE_SIGN_UP,
                    exam.getOrganizationId(),
                    MessageConstant.EXAM_SIGN_UP,
                    Optional.of(new String[]{exam.getName()}),
                    Optional.ofNullable(exam.getPublisherId())
            );
        }

    }

    /**
     * 考生报名
     */
    private SignUp insertAll(Integer examRegion, SignUpAuth signUpAuth, String examId, String memberId, int preApprovalStatus, Exam exam) {

        //判断考试已经撤销删除
//        Exam exam = examDao.getOptional(examId).orElse(null);

        if (exam == null) throw new UnprocessableException(ErrorCode.ExamNullError);

        //考试在发布后才可以报名
        if (canSignUpStatus(exam)) {
// TODO
            SignUp signUp = getByExamIdAndMemberId(examRegion, examId, memberId);

            if (signUp == null) {

                signUp = insertSignUp(examRegion, exam, memberId, preApprovalStatus);
                exam.setSignUp(signUp);
//                exam.setApplicantNumber(countSignUp(examId));
//                examDao.update(exam);

                //如果报名审核 通过，生成考试记录
                if (isSignUpPassed(signUp.getStatus())) {
                    createExamRecord(examRegion, examId, signUp);
                    // TODO 学习计划-新增单个
                    if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                        List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                        if (CollectionUtils.isNotEmpty(list)) {
                            ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                            if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                                //异步消息
                                messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                        MessageHeaderContent.BUSINESS_ID, examId,
                                        MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                        MessageHeaderContent.EXAM_MEMBER_ID, memberId
                                );
                            }
                        }
                    }
                }

                //添加报名认证信息
                insertSignUpAuthInfo(signUpAuth, signUp);

            } else if (canSignUpAgain(signUp)) {
                signUp = updateSignUp(examRegion, preApprovalStatus, signUp, memberId);
                updateSignUpAuthInfo(signUp, signUpAuth);
            }

            // 更新报名流水记录表状态
            updateSignUpRecordCurrentStatus(exam, memberId);

            createOneSignUpRecord(preApprovalStatus, signUp.getId(), exam, memberId);

            // 更新个人模板信息（报名信息和模板信息双向同步）
            updatePersonalTemplate(signUpAuth,memberId);

            // 审核消息通知
            insertRemoteExamNoticePreApproval(exam, memberId, preApprovalStatus);

            // 添加考试信息
            signUp.setExamLevelId(exam.getLevelId());

            return signUp;
        }
        throw new UnprocessableException(ErrorCode.EXAM_STATUS_ERROR);
    }

    /**
     * 考生报名-移动云
     */
    private CloudSignup insertAllForCloud(Integer examRegion, CloudSignup cloudSignup, String examId, String memberId, int preApprovalStatus, Exam exam) {

        //判断考试已经撤销删除
//        Exam exam = examDao.getOptional(examId).orElse(null);

        if (exam == null) throw new UnprocessableException(ErrorCode.ExamNullError);

        //考试在发布后才可以报名
        if (canSignUpStatus(exam)) {
// TODO
            CloudSignup cloudSignupData = getByExamIdAndMemberIdForCloudExam(examId, memberId);

            if (cloudSignupData == null) {

                cloudSignupData = insertCloudSignup(exam, memberId, preApprovalStatus, cloudSignup);
                exam.setCloudSignup(cloudSignupData);
                //如果报名审核 通过，生成考试记录
                if (isSignUpPassed(cloudSignupData.getStatus())) {
                    createExamRecordForCloudExam(examRegion, examId, cloudSignupData);
                    // TODO 学习计划-新增单个
                    if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                        List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                        if (CollectionUtils.isNotEmpty(list)) {
                            ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                            if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                                //异步消息
                                messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                        MessageHeaderContent.BUSINESS_ID, examId,
                                        MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                        MessageHeaderContent.EXAM_MEMBER_ID, memberId
                                );
                            }
                        }
                    }
                }

            } else if (canSignUpAgainForCloudExam(cloudSignupData)) {
                cloudSignupData = updateSignUpForExam(examRegion, preApprovalStatus, cloudSignupData, memberId);
            }

            // 更新报名流水记录表状态
            updateSignUpRecordCurrentStatus(exam, memberId);

            createOneSignUpRecord(preApprovalStatus, cloudSignupData.getId(), exam, memberId);

            // 审核消息通知
            insertRemoteExamNoticePreApproval(exam, memberId, preApprovalStatus);

            return cloudSignupData;
        }
        throw new UnprocessableException(ErrorCode.EXAM_STATUS_ERROR);
    }

    /**
     * 考生报名-网格长
     */
    private GridSignup insertAllForGrid(Integer examRegion, GridSignup gridSignup, String examId, String memberId, int preApprovalStatus, Exam exam) {

        //判断考试已经撤销删除
//        Exam exam = examDao.getOptional(examId).orElse(null);

        if (exam == null) throw new UnprocessableException(ErrorCode.ExamNullError);

        //考试在发布后才可以报名
        if (canSignUpStatus(exam)) {
// TODO
            GridSignup gridSignupData = getByExamIdAndMemberIdForGridExam(examId, memberId);

            if (gridSignupData == null) {

                gridSignupData = insertGridSignup(exam, memberId, preApprovalStatus, gridSignup);
                exam.setGridSignup(gridSignupData);
                //如果报名审核 通过，生成考试记录
                if (isSignUpPassed(gridSignupData.getStatus())){
                    createExamRecordForGridExam(examRegion, examId, gridSignupData);
                    // TODO 学习计划-新增单个
                    if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                        List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                        if (CollectionUtils.isNotEmpty(list)) {
                            ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
                            if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                                //异步消息
                                messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                        MessageHeaderContent.BUSINESS_ID, examId,
                                        MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                        MessageHeaderContent.EXAM_MEMBER_ID, memberId
                                );
                            }
                        }
                    }

                }

            } else if (canSignUpAgainForGridExam(gridSignupData)) {
                gridSignupData = updateSignUpForGridExam(examRegion, preApprovalStatus, gridSignupData, memberId);
            }

            // 更新报名流水记录表状态
            updateSignUpRecordCurrentStatus(exam, memberId);

            createOneSignUpRecord(preApprovalStatus, gridSignupData.getId(), exam, memberId);

            // 审核消息通知
            insertRemoteExamNoticePreApproval(exam, memberId, preApprovalStatus);

            return gridSignupData;
        }
        throw new UnprocessableException(ErrorCode.EXAM_STATUS_ERROR);
    }

    /**
     * 审核消息通知
     * @param exam
     * @param memberId
     * @param preApprovalStatus
     */
    private void insertRemoteExamNoticePreApproval(Exam exam, String memberId, Integer preApprovalStatus) {

        Date day=new Date();

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String time = df.format(day);

        if (preApprovalStatus != PRE_STATUS_WAIT_APPROVAL) {
            // 消息通知
            examNoticeService.insert(
                    ExamNotice.BUSINESS_TYPE_EXAM,
                    exam.getId(),
                    preApprovalStatus == PRE_STATUS_PASS
                            ? ExamNotice.TYPE_SIGN_UP_PASS
                            : ExamNotice.TYPE_SIGN_UP_REFUSE,
                    exam.getOrganizationId(),
                    preApprovalStatus == PRE_STATUS_PASS
                            ? MessageConstant.EXAM_AUDIT_PASS
                            : MessageConstant.EXAM_AUDIT_REFUSE,
                    preApprovalStatus == PRE_STATUS_PASS
                            ? Optional.of(new String[]{time, exam.getName(), DateUtil.dateLongToString(exam.getStartTime(),"yyyy-MM-dd,HH:mm:ss")})
                            : Optional.of(new String[]{time, exam.getName()}),
                    Optional.of(memberId)
            );

        }

    }

    /**
     *  更新个人模板信息
     * @param signUpAuth
     * @param memberId
     */
    private void updatePersonalTemplate(SignUpAuth signUpAuth, String memberId) {
        personalTemplateDao.delete(PERSONAL_TEMPLATE.MEMBER_ID.eq(memberId));
        PersonalTemplate personalTemplate=new PersonalTemplate();
        personalTemplate.forInsert();
        personalTemplate.setMemberId(memberId);
        personalTemplate.setProfessionId(signUpAuth.getProfessionId());
        personalTemplate.setSubProfessionId(signUpAuth.getSubProfessionId());
        personalTemplate.setEquipmentTypeId(signUpAuth.getEquipmentTypeId());
        personalTemplate.setWorkDepart(signUpAuth.getWorkDepart());
        personalTemplate.setWorkTime(signUpAuth.getWorkTime());
        personalTemplate.setIsGroupExpert(signUpAuth.getIsGroupExpert());
        personalTemplate.setIsProvinExpert(signUpAuth.getIsProvinExpert());
        personalTemplate.setOtherExamAppraisal(signUpAuth.getOtherExamAppraisal());
        personalTemplate.setAwardSituation(signUpAuth.getAwardSituation());
        personalTemplate.setCrossCondition(signUpAuth.getCrossCondition());
        personalTemplate.setApplyLevel(signUpAuth.getApplyLevel());
        personalTemplate.setApplyProfession(signUpAuth.getApplyProfession());
        personalTemplate.setApplySubProfession(signUpAuth.getApplySubProfession());
        personalTemplate.setApplySupplier(signUpAuth.getApplySupplier());
        personalTemplate.setProvince(signUpAuth.getProvince());
        personalTemplate.setCity(signUpAuth.getCity());
        personalTemplateDao.insert(personalTemplate);
    }

    private void createOneSignUpRecord(int preApprovalStatus, String signupId, Exam exam, String memberId) {
        // 添加一个报名记录表数据
        SignupRecord signupRecord = new SignupRecord();
        // 没有开启预审核，没有审核人
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            signupRecord.setSignupId(signupId);
            signupRecord.setIsCurrent(SignupRecord.IS_CURRENT_YES);
            signupRecord.setExamId(exam.getId());
            signupRecord.setMemberId(memberId);
            signupRecord.setAuditStatus(SignUp.STATUS_APPROVE);
//                signupRecord.setAuditMemberId(SignupRecord.PRE_AUDIT_MEMBER_ID);
            signupRecord.setAuditTime(System.currentTimeMillis());
            signupRecord.forInsert();
            signUpRecordDao.insert(signupRecord);
        } else {
            // 开启了预审核，审核人为系统自动审核
            signupRecord.setSignupId(signupId);
            signupRecord.setIsCurrent(SignupRecord.IS_CURRENT_YES);
            signupRecord.setExamId(exam.getId());
            signupRecord.setMemberId(memberId);
            signupRecord.setAuditStatus(1 == preApprovalStatus
                    ? SignUp.STATUS_APPROVE :
                    (2 == preApprovalStatus
                            ? SignUp.STATUS_PASSED : SignUp.STATUS_REFUSE));
            signupRecord.setAuditMemberId(SignupRecord.PRE_AUDIT_MEMBER_ID);
            signupRecord.setAuditTime(System.currentTimeMillis());
            signupRecord.forInsert();
            signUpRecordDao.insert(signupRecord);
        }
    }

    private void updateSignUpRecordCurrentStatus(Exam exam, String memberId) {
        signUpRecordDao.execute(e ->
                e.update(SIGNUP_RECORD).set(SIGNUP_RECORD.IS_CURRENT, SignupRecord.IS_CURRENT_NO)
                        .where(SIGNUP_RECORD.EXAM_ID.eq(exam.getId())
                                .and(SIGNUP_RECORD.MEMBER_ID.eq(memberId))).execute());
    }

    private void updateSignUpAuthInfo(SignUp signUp, SignUpAuth signUpAuth) {
        //判断认证报名信息表ID是否为空，更新操作
        signUpAuth.setSignUpId(signUp.getId());
        signUpAuthDao.update(signUpAuth);
    }


    /**
     * @param preApprovalStatus
     * @param signUp
     * @param memberId
     */
    private SignUp updateSignUp(Integer examRegion, int preApprovalStatus, SignUp signUp, String memberId) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return update(examRegion, signUp.getId(), SignUp.STATUS_APPROVE, memberId);
        }
        // 开启预审核，审核人为系统自动审核
        return preUpdate(examRegion, signUp.getId(), getPreApprovalStatus(preApprovalStatus), memberId, SignupRecord.PRE_AUDIT_MEMBER_ID);
    }

    /**
     * @param preApprovalStatus
     * @param cloudSignup
     * @param memberId
     */
    private CloudSignup updateSignUpForExam(Integer examRegion, int preApprovalStatus, CloudSignup cloudSignup, String memberId) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return updateForCloudExam(examRegion, cloudSignup.getId(), SignUp.STATUS_APPROVE, memberId);
        }
        // 开启预审核，审核人为系统自动审核
        return preUpdateForCloudExam(examRegion, cloudSignup.getId(), getPreApprovalStatus(preApprovalStatus), memberId, SignupRecord.PRE_AUDIT_MEMBER_ID);
    }

    /**
     * @param preApprovalStatus
     * @param gridSignup
     * @param memberId
     */
    private GridSignup updateSignUpForGridExam(Integer examRegion, int preApprovalStatus, GridSignup gridSignup, String memberId) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return updateForGridExam(examRegion, gridSignup.getId(), SignUp.STATUS_APPROVE, memberId);
        }
        // 开启预审核，审核人为系统自动审核
        return preUpdateForGridExam(examRegion, gridSignup.getId(), getPreApprovalStatus(preApprovalStatus), memberId, SignupRecord.PRE_AUDIT_MEMBER_ID);
    }

    /**
     * 可以再报名
     * @param signUp
     * @return
     */
    private boolean canSignUpAgain(SignUp signUp) {
        if (signUp != null) {
            return (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_CANCEL)
                    ||
                    (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_REFUSE);
        }
        return false;
    }
    /**
     * 可以再报名
     * @param cloudSignup
     * @return
     */
    private boolean canSignUpAgainForCloudExam(CloudSignup cloudSignup) {
        if (cloudSignup != null) {
            return (cloudSignup.getStatus() != null && cloudSignup.getStatus().intValue() == SignUp.STATUS_CANCEL)
                    ||
                    (cloudSignup.getStatus() != null && cloudSignup.getStatus().intValue() == SignUp.STATUS_REFUSE);
        }
        return false;
    }
    /**
     * 可以再报名-网格长
     * @param gridSignup
     * @return
     */
    private boolean canSignUpAgainForGridExam(GridSignup gridSignup) {
        if (gridSignup != null) {
            return (gridSignup.getStatus() != null && gridSignup.getStatus().intValue() == SignUp.STATUS_CANCEL)
                    ||
                    (gridSignup.getStatus() != null && gridSignup.getStatus().intValue() == SignUp.STATUS_REFUSE);
        }
        return false;
    }

    private void insertSignUpAuthInfo(SignUpAuth signUpAuth, SignUp signUp) {
        signUpAuth.forInsert();
        signUpAuth.setSignUpId(signUp.getId());
        signUpAuthDao.insert(signUpAuth);
    }

    /**
     * 创建考试记录
     */
    private void createExamRecord(Integer examRegion, String examId, SignUp signUp) {
        PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
        examRecordService.insert(examRegion, examId, paperInstance.getId(), signUp.getMemberId(), ExamRecord.STATUS_TO_BE_STARTED,
                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty());
    }

    /**
     * 创建考试记录-移动云
     */
    private void createExamRecordForCloudExam(Integer examRegion, String examId, CloudSignup cloudSignup) {
        PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
        examRecordService.insert(examRegion, examId, paperInstance.getId(), cloudSignup.getMemberId(), ExamRecord.STATUS_TO_BE_STARTED,
                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty());
    }

    /**
     * 创建考试记录-网格长
     * @param examId
     * @param gridSignup
     */
    private void createExamRecordForGridExam(Integer examRegion, String examId, GridSignup gridSignup) {
        PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
        examRecordService.insert(examRegion, examId, paperInstance.getId(), gridSignup.getMemberId(), ExamRecord.STATUS_TO_BE_STARTED,
                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty());
    }

    /**
     * 判断是否报名通过
     * @param status
     * @return
     */
    private boolean isSignUpPassed(Integer status) {
        return status != null && status.intValue() == SignUp.STATUS_PASSED;
    }

    /**
     * 报名插入记录
     * @param exam
     * @param memberId
     * @param preApprovalStatus
     * @return
     */
    private SignUp insertSignUp(Integer examRegion, Exam exam, String memberId, int preApprovalStatus) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return insertSu(examRegion, exam, memberId, exam.getOrganizationId(),
                    SignUp.STATUS_APPROVE, getDisabledSendMessageStatus(SignUp.STATUS_APPROVE));
        }
        // 开启了预审核，审核人为系统自动审核
        //添加报名记录
        return preInsert(exam, memberId, exam.getOrganizationId(),
                getPreApprovalStatus(preApprovalStatus), SignupRecord.PRE_AUDIT_MEMBER_ID,
                getDisabledSendMessageStatus(getPreApprovalStatus(preApprovalStatus)));
    }

    /**
     * 报名插入记录-移动云
     * @param exam
     * @param memberId
     * @param preApprovalStatus
     * @param cloudSignup
     * @return
     */
    private CloudSignup insertCloudSignup(Exam exam, String memberId, int preApprovalStatus, CloudSignup cloudSignup) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return insertForCloudExam(exam, memberId, exam.getOrganizationId(),
                    SignUp.STATUS_APPROVE, getDisabledSendMessageStatus(SignUp.STATUS_APPROVE), cloudSignup);
        }
        // 开启了预审核，审核人为系统自动审核
        //添加报名记录
        return preInsertForCloudExam(exam, memberId, exam.getOrganizationId(),
                getPreApprovalStatus(preApprovalStatus), SignupRecord.PRE_AUDIT_MEMBER_ID,
                getDisabledSendMessageStatus(getPreApprovalStatus(preApprovalStatus)), cloudSignup);
    }

    /**
     * 报名插入记录-网格长
     * @param exam
     * @param memberId
     * @param preApprovalStatus
     * @param gridSignup
     * @return
     */
    private GridSignup insertGridSignup(Exam exam, String memberId, int preApprovalStatus, GridSignup gridSignup) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            return insertForGridExam(exam, memberId,
                    SignUp.STATUS_APPROVE, getDisabledSendMessageStatus(SignUp.STATUS_APPROVE), gridSignup);
        }
        // 开启了预审核，审核人为系统自动审核
        //添加报名记录
        return preInsertForGridExam(exam, memberId,
                getPreApprovalStatus(preApprovalStatus), SignupRecord.PRE_AUDIT_MEMBER_ID,
                getDisabledSendMessageStatus(getPreApprovalStatus(preApprovalStatus)), gridSignup);
    }

    /**
     * 获取报名状态
     * @param preApprovalStatus
     * @return
     */
    private int getPreApprovalStatus(int preApprovalStatus) {
        if (preApprovalStatus == PRE_STATUS_WAIT_APPROVAL) return SignUp.STATUS_APPROVE;
        if (preApprovalStatus == PRE_STATUS_PASS) return SignUp.STATUS_PASSED;
        return SignUp.STATUS_REFUSE;
    }

    /**
     * 判断是否需要发送报名插入记录之后的异步消息
     * @param statusApprove
     * @return
     */
    private boolean getDisabledSendMessageStatus(int statusApprove) {
        return statusApprove == SignUp.STATUS_PASSED;
    }

    /**
     * 判断是否能报名的状态
     * @param exam
     * @return
     */
    private boolean canSignUpStatus(Exam exam) {
        return Exam.STATUS_NOT_START == exam.getStatus()
                || Exam.STATUS_SIGNUPING == exam.getStatus()
                || Exam.STATUS_STARTING == exam.getStatus();
    }

    @Override
    public List<SignUp> findSignUpingRecord() {
        return signUpDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(SIGNUP)
                                    .add(EXAM).end()
                    )
                    .from(SIGNUP)
                    .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                    .where(
                            SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE),
                            EXAM.STATUS.ne(Exam.STATUS_NOT_PUBLISH),
                            EXAM.END_TIME.ge(System.currentTimeMillis()))
                    .fetch(r -> {
                        SignUp signUp = r.into(SIGNUP).into(SignUp.class);
                        Exam exam = r.into(EXAM).into(Exam.class);
                        signUp.setExam(exam);
                        return signUp;
                    });
        });
    }

    @Override
    @DataSource
    public Optional<SignUp> getOptional(Integer examRegion, String examId, String memberId) {
        return signUpDao.fetchOne(SIGNUP.EXAM_ID.eq(examId), SIGNUP.MEMBER_ID.eq(memberId));
    }

    /* (non-Javadoc)
     * 统计报名人数
     * 取消报名不算
     * @see com.zxy.product.examstu.api.SignUpService#countSignUp(java.lang.String)
     */
    @Override
    public Integer countSignUp(String examId) {
        return signUpDao.execute(e -> e.fetchCount(SIGNUP, SIGNUP.EXAM_ID.eq(examId).and(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL))));
    }

    /*
     * 根据考试类型，统计报名成功人数
     */
    @Override
    @DataSource
    public Integer countSignUpByExamType(Integer examRegion, Integer type, String examId) {
        // 查询该考试的报名人数
        if (Exam.EXAM_CLOUD_TYPE.equals(type)) {
            return cloudSignupDao.execute(e -> e.fetchCount(CLOUD_SIGNUP, CLOUD_SIGNUP.EXAM_ID.eq(examId).and(CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))));
        }else if (Exam.EXAM_GRID_TYPE.equals(type)) {
            return gridSignupDao.execute(e -> e.fetchCount(GRID_SIGNUP, GRID_SIGNUP.EXAM_ID.eq(examId).and(GRID_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))));
        }else {
            return signUpDao.execute(e -> e.fetchCount(SIGNUP, SIGNUP.EXAM_ID.eq(examId).and(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))));
        }
    }




    private SelectConditionStep<Record> getOrderListStep(SelectConditionStep<Record> listSetp) {
        listSetp.orderBy(SIGNUP.CREATE_TIME.desc());
        return listSetp;
    }

    private Function<SelectSelectStep<Record>, SelectConditionStep<Record>> getFindSignUpStepFun(
            List<Condition> param) {
        Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a ->
                a.from(SIGNUP)
                        .leftJoin(MEMBER).on(SIGNUP.MEMBER_ID.eq(MEMBER.ID))
                        .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                        .where(param);
        return stepFunc;
    }

    @Override
    @DataSource
    public List<SignUp> findByMemberId(Integer examRegion, String memberId) {
        return this.signUpDao.fetch(SIGNUP.MEMBER_ID.eq(memberId));
    }

    /**
     * 报名
     * a.普通考试报名
     * b.认证考试报名
     */
    @Override
    @DataSource
    public Map<String, Object> signUpAuth(Integer examRegion, SignUpAuth signUpAuth, Optional<String> organizationId, String companyId,int preApprovalStatus) {

        String examId = signUpAuth.getExamId();

        String currentUserId = signUpAuth.getMemberId();

        HashMap<String, Object> map = new HashMap<>();

        Exam exam = examDao.get(examId);

        List<Exam> list = new ArrayList<>();


        //我已报名的考试（审核状态 3:被拒绝）
        if (hasRefuseRecord(examId, currentUserId)) {
            if (!isOrdinaryExam(exam.getType()) || !Exam.PRE_APPROVAL_YES.equals(exam.getPreApproval())) {
                throw new UnprocessableException(ErrorCode.RegistrationHasBeenRejected);
            }
        }

        // 认证考试
        if (!isOrdinaryExam(exam.getType())) {
            // 我已报名的认证考试（审核状态 1：待审核，2：已通过）
            // 认证考试判断交叉时间
            list = findCurrentMemberAuthExamSignUpRecords(currentUserId);
        } else{
            list = findCurrentMemberExamSignUpRecords(currentUserId);
        }

        //判断是否存在交叉时间
        if (haveOverlap(list, exam)) {
            throw new UnprocessableException(ErrorCode.ExamTimeConflicts);
        }

        int signUpCountPreApprovalStatus = PRE_STATUS_WAIT_APPROVAL;
        // 报名次数限制 2019-5 (仅限于集团网络部组织的认证考试(每月的日常考试))
        if (orgId.equals(exam.getOrganizationId()) && Exam.EXAM_AUTHENTICATION_TYPE == exam.getType()) {
            // 获取当前年份
            Calendar date = Calendar.getInstance();
            String year = String.valueOf(date.get(Calendar.YEAR));
            Map<String,String> examBatchMap = new HashMap<String,String>();
            examBatchMap.put(year+"01", ""); examBatchMap.put(year+"02", "");
            examBatchMap.put(year+"03", ""); examBatchMap.put(year+"04", "");
            examBatchMap.put(year+"05", ""); examBatchMap.put(year+"06", "");
            examBatchMap.put(year+"07", ""); examBatchMap.put(year+"08", "");
            examBatchMap.put(year+"09", ""); examBatchMap.put(year+"10", "");
            examBatchMap.put(year+"11", ""); examBatchMap.put(year+"12", "");
            // 如果是日常的考试则进入报名次数逻辑判断
            if (examBatchMap.containsKey(exam.getExamBatch())) {
                signUpCountPreApprovalStatus = signUpCountRule(currentUserId, companyId, signUpAuth, year);
            }
        }

        // int preApprovalStatus = PRE_STATUS_WAIT_APPROVAL;

        // 满足报名次数限制进入预审核判断
        if (signUpCountPreApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            if (!isOrdinaryExam(exam.getType())){// 预审核初始状态为1，待审核
                preApprovalStatus = getPreApprovalStatus(exam, currentUserId);
            }
        }else {
            // 不满足报名次数限制，预审核直接拒绝
            preApprovalStatus = signUpCountPreApprovalStatus;
        }

        SignUp signUp = this.insertAll(examRegion, signUpAuth, examId, currentUserId, preApprovalStatus, exam);

        map.put("signUp", signUp);
        map.put("overSignupRule", signUpAuth.getOverSignupRule());
        map.put("mySignupCount", signUpAuth.getMyCount());

        return map;
    }

    @Override
    @DataSource
    public Integer getSignUpCount(Integer examRegion, String lastOrgId, String memberId) {
        // 获取当前年份
        Calendar date = Calendar.getInstance();
        String year = String.valueOf(date.get(Calendar.YEAR));
        int mySignUpCount = getMySignUpCount(memberId, year);//当年已报名
        // 我的组织机构id
        String companyId = getMyCompanyId(memberId);
        // 31家省公司+两个网络事业部的考生+集团公司，每年每人成功报名考试次数不可超过15次；
        Map<String,String> companyMap = new HashMap<>();
        companyMap.put(Organization.COMPANY_BJ, ""); companyMap.put(Organization.COMPANY_TJ, "");
        companyMap.put(Organization.COMPANY_HB, ""); companyMap.put(Organization.COMPANY_SX, "");
        companyMap.put(Organization.COMPANY_NMG, ""); companyMap.put(Organization.COMPANY_LN, "");
        companyMap.put(Organization.COMPANY_JL, ""); companyMap.put(Organization.COMPANY_HLJ, "");
        companyMap.put(Organization.COMPANY_SH, ""); companyMap.put(Organization.COMPANY_SZ, "");
        companyMap.put(Organization.COMPANY_ZJ, ""); companyMap.put(Organization.COMPANY_FJ, "");
        companyMap.put(Organization.COMPANY_AH, ""); companyMap.put(Organization.COMPANY_JX, "");
        companyMap.put(Organization.COMPANY_SD, ""); companyMap.put(Organization.COMPANY_HN, "");
        companyMap.put(Organization.COMPANY_HB_2, ""); companyMap.put(Organization.COMPANY_HN_2, "");
        companyMap.put(Organization.COMPANY_GD, ""); companyMap.put(Organization.COMPANY_GX, "");
        companyMap.put(Organization.COMPANY_HN_3, ""); companyMap.put(Organization.COMPANY_CQ, "");
        companyMap.put(Organization.COMPANY_SC, ""); companyMap.put(Organization.COMPANY_GZ, "");
        companyMap.put(Organization.COMPANY_YN, ""); companyMap.put(Organization.COMPANY_XZ, "");
        companyMap.put(Organization.COMPANY_SX_2, ""); companyMap.put(Organization.COMPANY_GS, "");
        companyMap.put(Organization.COMPANY_QH, ""); companyMap.put(Organization.COMPANY_NX, "");
        companyMap.put(Organization.COMPANY_XJ, ""); companyMap.put(Organization.COMPANY_WLB_1, "");
        companyMap.put(Organization.COMPANY_WLB_2, "");companyMap.put(Organization.COMPANY_JT, "");
        if (companyMap.containsKey(companyId)) {
            return mySignUpCount >= 15 ? 0 : 15 - mySignUpCount;
        }
        String path = getMyOrgPath(memberId);
        if (path != null && path.startsWith(jituanPath)) {//集团单独校验一下
            return mySignUpCount >= 15 ? 0 : 15 - mySignUpCount;
        }
        //其余不超过10次，但是需要判断一种情况，如果上一次任职的是省公司，还是保留15次
        if (companyMap.containsKey(lastOrgId)) {
            return mySignUpCount >= 15 ? 0 : 15 - mySignUpCount;
        }
        return mySignUpCount >= 10 ? 0 : 10 - mySignUpCount;
    }

    @Override
    @DataSource
    public Boolean isSignUpPass(Integer examRegion, String memberId, String examId) {
        return signUpDao.fetchOne(SIGNUP.MEMBER_ID.eq(memberId), SIGNUP.EXAM_ID.eq(examId)).map(signUp -> {
            if (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_PASSED) return true;
            return false;
        }).orElse(false);
    }


    @Override
    @DataSource
    public List<String> approveMemberIds(Integer examRegion, String examId, Integer page) {
         return signUpDao.execute(e -> e.select(SIGNUP.MEMBER_ID)
                    .from(SIGNUP)
                    .where(SIGNUP.EXAM_ID.eq(examId))
                    .and(SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE))
                    .limit((page - 1) * PER_SIZE, PER_SIZE)
                    .fetch(SIGNUP.MEMBER_ID));

    }

    @Override
    @DataSource
    public Integer approveMemberCount(Integer examRegion, String examId) {
        Integer count = signUpDao.execute(e -> e.select(DSL.count(SIGNUP.MEMBER_ID))
                        .from(SIGNUP)
                .where(SIGNUP.EXAM_ID.eq(examId))
                .and(SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE))
                .fetchOne(DSL.count(SIGNUP.MEMBER_ID)));
        return count;


    }



    /**
     * 移动云考试-报名
     */
    @Override
    @DataSource
    public Map<String, Object> insertCloudSignup(Integer examRegion, CloudSignup cloudSignup) {

        String examId = cloudSignup.getExamId();

        String currentUserId = cloudSignup.getMemberId();

        HashMap<String, Object> map = new HashMap<>();

        Exam exam = examDao.get(examId);

        //我已报名的考试（审核状态 3:被拒绝）
//        if (cloudExamHasRefuseRecord(examId, currentUserId)) {
//            throw new UnprocessableException(ErrorCode.RegistrationHasBeenRejected);
//        }

        int signUpCountPreApprovalStatus = PRE_STATUS_WAIT_APPROVAL;
        // 报名次数限制 移动云每年6次
        if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
            // 获取当前年份
            Calendar date = Calendar.getInstance();
            String year = String.valueOf(date.get(Calendar.YEAR));
            Map<String,String> examBatchMap = new HashMap<String,String>();
            examBatchMap.put(year+"01", ""); examBatchMap.put(year+"02", "");
            examBatchMap.put(year+"03", ""); examBatchMap.put(year+"04", "");
            examBatchMap.put(year+"05", ""); examBatchMap.put(year+"06", "");
            examBatchMap.put(year+"07", ""); examBatchMap.put(year+"08", "");
            examBatchMap.put(year+"09", ""); examBatchMap.put(year+"10", "");
            examBatchMap.put(year+"11", ""); examBatchMap.put(year+"12", "");
            // 如果是日常的考试则进入报名次数逻辑判断
            if (examBatchMap.containsKey(exam.getExamBatch())) {
                signUpCountPreApprovalStatus = cloudExamSignUpCountRule(currentUserId, year, cloudSignup);
            }
        }

        int preApprovalStatus = PRE_STATUS_WAIT_APPROVAL;

        // 满足报名次数限制进入预审核判断
        if (signUpCountPreApprovalStatus == PRE_STATUS_WAIT_APPROVAL) {
            // 预审核初始状态为1，待审核
            preApprovalStatus = getPreApprovalStatus(exam, currentUserId);
        }else {
            // 不满足报名次数限制，预审核直接拒绝
            preApprovalStatus = signUpCountPreApprovalStatus;
        }

        CloudSignup signUp = this.insertAllForCloud(examRegion, cloudSignup, examId, currentUserId, preApprovalStatus, exam);

        map.put("signUp", signUp);
        map.put("overSignupRule", cloudSignup.getOverSignupRule());
        map.put("mySignupCount", cloudSignup.getMyCount());

        return map;
    }

    /**
     * 网格长考试-报名
     */
    @Override
    @DataSource
    public Map<String, Object> insertGridSignup(Integer examRegion, GridSignup gridSignup, Boolean finishAllCourse) {
        String examId = gridSignup.getExamId();
        String currentUserId = gridSignup.getMemberId();
        HashMap<String, Object> map = new HashMap<>();
        Exam exam = examDao.get(examId);
        exam.setFinishAllCourse(finishAllCourse);
        // 预审核初始状态为1，待审核
        int preApprovalStatus = getPreApprovalStatus(exam, currentUserId);

        GridSignup signUp = this.insertAllForGrid(examRegion, gridSignup, examId, currentUserId, preApprovalStatus, exam);

        map.put("signUp", signUp);

        return map;
    }

    /**
     * 报名次数规则限制 2019-5月
     * 1.  31家省公司每年每人成功报名考试次数不可超过15次；
     * 2.  集团总部每年每人成功报名考试次数不可超过15次，但除辛姆巴科公司、卓望控股有限公司、中国移动香港有限公司、香港机构、中移信息技术有限公司、中移智行网络科技有限公司，此6家公司每年每人成功报名考试次数不可超过6次；
     * 3.  除集团总部、31家省公司外的其余公司每年每人成功报名考试次数不可超过6次；
     * 4.  报名成功后未在报名截止前取消报名则计参加一次考试，报名成功后取消不计入次数，认证考试的模拟考试不计入次数；
     *
     * @param currentUserId
     * @param companyId
     * @param signUpAuth
     * @param year
     * @return
     */
    private int signUpCountRule(String currentUserId, String companyId, SignUpAuth signUpAuth, String year) {

        // 我的组织机构id
        if (companyId == null || "".equals(companyId)){
            companyId = getMyCompanyId(currentUserId);
        }

        // 我是31家省公司+网络事业部的考生，每年每人成功报名考试次数不可超过15次；
        Map<String,String> companyMap = new HashMap<String,String>();
        companyMap.put(Organization.COMPANY_BJ, ""); companyMap.put(Organization.COMPANY_TJ, "");
        companyMap.put(Organization.COMPANY_HB, ""); companyMap.put(Organization.COMPANY_SX, "");
        companyMap.put(Organization.COMPANY_NMG, ""); companyMap.put(Organization.COMPANY_LN, "");
        companyMap.put(Organization.COMPANY_JL, ""); companyMap.put(Organization.COMPANY_HLJ, "");
        companyMap.put(Organization.COMPANY_SH, ""); companyMap.put(Organization.COMPANY_SZ, "");
        companyMap.put(Organization.COMPANY_ZJ, ""); companyMap.put(Organization.COMPANY_FJ, "");
        companyMap.put(Organization.COMPANY_AH, ""); companyMap.put(Organization.COMPANY_JX, "");
        companyMap.put(Organization.COMPANY_SD, ""); companyMap.put(Organization.COMPANY_HN, "");
        companyMap.put(Organization.COMPANY_HB_2, ""); companyMap.put(Organization.COMPANY_HN_2, "");
        companyMap.put(Organization.COMPANY_GD, ""); companyMap.put(Organization.COMPANY_GX, "");
        companyMap.put(Organization.COMPANY_HN_3, ""); companyMap.put(Organization.COMPANY_CQ, "");
        companyMap.put(Organization.COMPANY_SC, ""); companyMap.put(Organization.COMPANY_GZ, "");
        companyMap.put(Organization.COMPANY_YN, ""); companyMap.put(Organization.COMPANY_XZ, "");
        companyMap.put(Organization.COMPANY_SX_2, ""); companyMap.put(Organization.COMPANY_GS, "");
        companyMap.put(Organization.COMPANY_QH, ""); companyMap.put(Organization.COMPANY_NX, "");
        companyMap.put(Organization.COMPANY_XJ, ""); companyMap.put(Organization.COMPANY_WLB_1, "");
        companyMap.put(Organization.COMPANY_WLB_2, "");companyMap.put(Organization.COMPANY_JT, "");

        if (companyMap.containsKey(companyId)) {
            // 我今年已成功报名的次数
            int mySignUpCount = getMySignUpCount(currentUserId, year);
            if (mySignUpCount >= 15) {
                signUpAuth.setMyCount(mySignUpCount);
                signUpAuth.setOverSignupRule(SignUpAuth.IS_OVER);
                return PRE_STATUS_REFUSE;
            }
            signUpAuth.setMyCount(mySignUpCount);
            signUpAuth.setOverSignupRule(SignUpAuth.NOT_OVER);
            return PRE_STATUS_WAIT_APPROVAL;
        }

        // 集团总部每年每人成功报名考试次数不可超过15次，但除辛姆巴科公司、卓望控股有限公司、中国移动香港有限公司、香港机构、中移信息技术有限公司、中移智行网络科技有限公司，此6家公司每年每人成功报名考试次数不可超过6次；
        // 我是内部-集团总部的人，并且不属于辛姆巴科公司、卓望控股有限公司、中国移动香港有限公司、香港机构、中移信息技术有限公司、中移智行网络科技有限公司，这6家公司
/*        if (isJTnot6(currentUserId)){
            // 我今年已成功报名的次数
            int mySignUpCount = getMySignUpCount(currentUserId, year);
            if (mySignUpCount >= 15) {
                signUpAuth.setMyCount(mySignUpCount);
                signUpAuth.setOverSignupRule(SignUpAuth.IS_OVER);
                return PRE_STATUS_REFUSE;
            }
            signUpAuth.setMyCount(mySignUpCount);
            signUpAuth.setOverSignupRule(SignUpAuth.NOT_OVER);
            return PRE_STATUS_WAIT_APPROVAL;
        }*/
        // 其余公司每年每人成功报名考试次数不可超过10次；
        // 我今年已成功报名的次数
        int mySignUpCount = getMySignUpCount(currentUserId, year);
        if (mySignUpCount >= 10) {
            signUpAuth.setMyCount(mySignUpCount);
            signUpAuth.setOverSignupRule(SignUpAuth.IS_OVER);
            return PRE_STATUS_REFUSE;
        }
        signUpAuth.setMyCount(mySignUpCount);
        signUpAuth.setOverSignupRule(SignUpAuth.NOT_OVER);
        return PRE_STATUS_WAIT_APPROVAL;
    }

    /**
     * 移动云报名次数规则限制
     * 每年6次
     * @param cloudSignup
     *
     */
    private int cloudExamSignUpCountRule(String currentUserId, String year, CloudSignup cloudSignup) {
        // 移动云考试每年每人成功报名考试次数不可超过6次；
        // 我今年已成功报名的次数
        int mySignUpCount = getMySignUpCountForCloudExam(currentUserId, year);
        if (mySignUpCount >= 6) {
            cloudSignup.setMyCount(mySignUpCount);
            cloudSignup.setOverSignupRule(SignUpAuth.IS_OVER);
            return PRE_STATUS_REFUSE;
        }
        cloudSignup.setMyCount(mySignUpCount);
        cloudSignup.setOverSignupRule(SignUpAuth.NOT_OVER);
        return PRE_STATUS_WAIT_APPROVAL;
    }

    /**
     * 判断是否为内部-集团并且不是那6家公司 的人
     * @param currentUserId
     * @return
     */
    private Boolean isJTnot6(String currentUserId) {
        Integer count = signUpDao.execute(e -> e.select(DSL.count(MEMBER.ID))
                .from(MEMBER)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                .where(MEMBER.ID.eq(currentUserId))
                .and(MEMBER.FROM.eq(Member.FROM_INNER))
                .and(ORGANIZATION.PATH.like(Member.JT_PATH))
                .and(ORGANIZATION.COMPANY_ID.ne(Organization.COMPANY_XMBK))
                .and(ORGANIZATION.COMPANY_ID.ne(Organization.COMPANY_ZWKG))
                .and(ORGANIZATION.COMPANY_ID.ne(Organization.COMPANY_ZGYDXG))
                .and(ORGANIZATION.COMPANY_ID.ne(Organization.COMPANY_XGJG))
                .and(ORGANIZATION.COMPANY_ID.ne(Organization.COMPANY_ZYZX))
                .and(ORGANIZATION.ID.ne(Organization.ORGANIZATION_ZYXX))
                .fetchOne(DSL.count(MEMBER.ID)));

        return count > 0;
    }

    /**
     * 2019年3月山东省参加过考试的考生不论参加几场考试，考试次数只记一次。
     * @param currentUserId
     * @param year
     * @return
     */
    private int getSDSignUpCount(String currentUserId, String year) {
        List<String> examBatchs = new ArrayList<String>();
        examBatchs.add(year+"01");
        examBatchs.add(year+"02");
//        examBatchs.add(year+"03");
        examBatchs.add(year+"04");
        examBatchs.add(year+"05");
        examBatchs.add(year+"06");
        examBatchs.add(year+"07");
        examBatchs.add(year+"08");
        examBatchs.add(year+"09");
        examBatchs.add(year+"10");
        examBatchs.add(year+"11");
        examBatchs.add(year+"12");

        String batch03 = year+"03";
        int myCount = 0;

        // 我201903考试批次报名次数
        Integer batch03count = signUpDao.execute(e -> e.select(DSL.count(SIGNUP.ID))
                .from(SIGNUP)
                .leftJoin(EXAM).on(EXAM.ID.eq(SIGNUP.EXAM_ID))
                .where(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                .and(EXAM.ORGANIZATION_ID.eq(orgId))
                .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE))
                .and(EXAM.EXAM_BATCH.eq(batch03))
                .fetchOne(DSL.count(SIGNUP.ID)));

        if (batch03count > 0) {
            myCount = 1;
        }

        // 我2019除了03月其他月的批次，考试报名次数
        Integer otherCount = signUpDao.execute(e -> e.select(DSL.count(SIGNUP.ID))
                .from(SIGNUP)
                .leftJoin(EXAM).on(EXAM.ID.eq(SIGNUP.EXAM_ID))
                .where(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                .and(EXAM.ORGANIZATION_ID.eq(orgId))
                .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE))
                .and(EXAM.EXAM_BATCH.in(examBatchs))
                .fetchOne(DSL.count(SIGNUP.ID)));

        return myCount + otherCount;
    }

    /**
     * 获取当前人的组织机构id
     * @param currentUserId
     * @return
     */
    private String getMyCompanyId(String currentUserId) {
        List<String> companyIds = signUpDao.execute(dsl -> dsl.select(Fields.start().add(ORGANIZATION.COMPANY_ID).end())
                .from(MEMBER)
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(MEMBER.ID.eq(currentUserId))
                .fetch(ORGANIZATION.COMPANY_ID)
        );
        return (companyIds != null && companyIds.size() > 0) ? companyIds.get(0) : "";
    }

    /**
     * 获取当前人的组织path
     * @param currentUserId
     * @return
     */
    private String getMyOrgPath(String currentUserId) {
        List<String> path = signUpDao.execute(dsl -> dsl.select(Fields.start().add(ORGANIZATION.PATH).end())
                .from(MEMBER)
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(MEMBER.ID.eq(currentUserId))
                .fetch(ORGANIZATION.PATH)
        );
        return (path != null && path.size() > 0) ? path.get(0) : "";
    }

    /**
     * 我今年已成功报名的次数(总部网络部的集团认证考试)
     * @param currentUserId
     * @param year
     * @return
     */
    private int getMySignUpCount(String currentUserId, String year) {


        List<String> examBatchs = new ArrayList<String>();
        examBatchs.add(year+"01");
        examBatchs.add(year+"02");
        examBatchs.add(year+"03");
        examBatchs.add(year+"04");
        examBatchs.add(year+"05");
        examBatchs.add(year+"06");
        examBatchs.add(year+"07");
        examBatchs.add(year+"08");
        examBatchs.add(year+"09");
        examBatchs.add(year+"10");
        examBatchs.add(year+"11");
        examBatchs.add(year+"12");

        return signUpDao.execute(e -> e.select(DSL.count(SIGNUP.ID))
                .from(SIGNUP)
                .leftJoin(EXAM).on(EXAM.ID.eq(SIGNUP.EXAM_ID))
                .where(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                .and(EXAM.ORGANIZATION_ID.eq(orgId))
                .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE))
                .and(EXAM.EXAM_BATCH.in(examBatchs))
                .fetchOne(DSL.count(SIGNUP.ID)));
    }

    /**
     * 我今年已成功报名的次数(移动云考试)
     * @param currentUserId
     * @param year
     * @return
     */
    private int getMySignUpCountForCloudExam(String currentUserId, String year) {
        List<String> examBatchs = new ArrayList<String>();
        examBatchs.add(year+"01");
        examBatchs.add(year+"02");
        examBatchs.add(year+"03");
        examBatchs.add(year+"04");
        examBatchs.add(year+"05");
        examBatchs.add(year+"06");
        examBatchs.add(year+"07");
        examBatchs.add(year+"08");
        examBatchs.add(year+"09");
        examBatchs.add(year+"10");
        examBatchs.add(year+"11");
        examBatchs.add(year+"12");

        return signUpDao.execute(e -> e.select(DSL.count(CLOUD_SIGNUP.ID))
                .from(CLOUD_SIGNUP)
                .leftJoin(EXAM).on(EXAM.ID.eq(CLOUD_SIGNUP.EXAM_ID))
                .where(CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                .and(CLOUD_SIGNUP.MEMBER_ID.eq(currentUserId))
                .and(EXAM.TYPE.eq(Exam.EXAM_CLOUD_TYPE))
                .and(EXAM.EXAM_BATCH.in(examBatchs))
                .fetchOne(DSL.count(CLOUD_SIGNUP.ID)));
    }



    private int getPreApprovalStatus(Exam exam, String currentUserId) {
        // 如果考试为普通考试，无论有没有勾选预审核，预审核状态都为待审核
//        if (isOrdinaryExam(exam.getType())) {
//            return PRE_STATUS_WAIT_APPROVAL;
//        }
        // 判断是否启用预审核
        if (Exam.PRE_APPROVAL_YES.equals(exam.getPreApproval())) {
            // 如果是移动云考试，走自己的逻辑
            if (isCloudExam(exam.getType())) {
                return this.cloudExamPreApprovalStatus(exam, currentUserId);
            }
            if (isGridExam(exam.getType())) {
                return this.gridExamPreApprovalStatus(exam);
            }
            // 判断预审核的状态
            return this.preApprovalStatus(exam, currentUserId);
        }
        return PRE_STATUS_WAIT_APPROVAL;
    }

    /**
     * 判断是否有被拒绝记录
     * @param examId
     * @param currentUserId
     * @return
     */
    private boolean hasRefuseRecord(String examId, String currentUserId) {
        Integer count = signUpDao.execute(e ->
                e.select(DSL.count(SIGNUP.ID)).from(SIGNUP)
                        .where(SIGNUP.MEMBER_ID.eq(currentUserId), SIGNUP.STATUS.eq(SignUp.STATUS_REFUSE),
                                SIGNUP.EXAM_ID.eq(examId))
                        .fetchOne(DSL.count(SIGNUP.ID)));
        return count > 0;
    }

    /**
     * 判断是否有被拒绝记录
     * @param examId
     * @param currentUserId
     * @return
     */
    private boolean cloudExamHasRefuseRecord(String examId, String currentUserId) {
        Integer count = signUpDao.execute(e ->
                e.select(DSL.count(CLOUD_SIGNUP.ID)).from(CLOUD_SIGNUP)
                        .where(CLOUD_SIGNUP.MEMBER_ID.eq(currentUserId),
                                CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_REFUSE),
                                CLOUD_SIGNUP.EXAM_ID.eq(examId))
                        .fetchOne(DSL.count(CLOUD_SIGNUP.ID)));
        return count > 0;
    }

    /**
     * 判断交叉时间
     * @param examList
     * @param exam
     * @return
     */
    private boolean haveOverlap(List<Exam> examList, Exam exam) {
        boolean haveOverlap = false;

        if (examList != null && examList.size() > 0) {
            for (int i = 0; i < examList.size(); i++) {
                Exam signUpExam = examList.get(i);
                if (signUpExam != null) {
                    Long signUpStartTime = signUpExam.getStartTime();
                    Long signUpEndTime = signUpExam.getEndTime();
                    Long startTime = exam.getStartTime();
                    Long endTime = exam.getEndTime();
                    haveOverlap = this.isOverlap(startTime, endTime, signUpStartTime, signUpEndTime);
                    if (haveOverlap) {
                        break;
                    }
                }
            }
        }
        return haveOverlap;
    }

    /**
     * 查询当前用户已报名的认证考试记录，用以判断交叉时间
     * @param currentUserId
     * @return
     */
    private List<Exam> findCurrentMemberAuthExamSignUpRecords(String currentUserId) {
        return signUpDao.execute(e ->
                e.select(Fields.start().add(EXAM.START_TIME).add(EXAM.END_TIME).end())
                        .from(SIGNUP)
                        .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                        .where(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE, Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 考试未撤销
                        .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                        .and((SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE).or(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))))
                        .fetchInto(Exam.class));
    }

    /**
     * 查询当前用户已报名的普通考试记录，用以判断交叉时间
     * @param currentUserId
     * @return
     */
    private List<Exam> findCurrentMemberExamSignUpRecords(String currentUserId) {
        return signUpDao.execute(e ->
                e.select(Fields.start().add(EXAM.START_TIME).add(EXAM.END_TIME).end())
                        .from(SIGNUP)
                        .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                        .where(EXAM.TYPE.in(Exam.EXAM_OFFICIAL_TYPE, Exam.EXAM_UN_OFFICIAL_TYPE))
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 考试未撤销
                        .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                        .and((SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE).or(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))))
                        .fetchInto(Exam.class));
    }

    /**
     * 是否普通考试
     * @param type
     * @return
     */
    private  boolean isOrdinaryExam(Integer type) {
        return Exam.EXAM_OFFICIAL_TYPE.equals(type) || Exam.EXAM_UN_OFFICIAL_TYPE.equals(type);
    }
    /**
     * 是否移动云考试
     * @param type
     * @return
     */
    private  boolean isCloudExam(Integer type) {
        return Exam.EXAM_CLOUD_TYPE.equals(type);
    }

    /**
     * 是否网格考试
     * @param type
     * @return
     */
    private  boolean isGridExam(Integer type) {
        return Exam.EXAM_GRID_TYPE.equals(type);
    }

    /**
     * 判断两个时间段是否有交集
     * @param startdate1
     * @param enddate1
     * @param startdate2
     * @param enddate2
     * @return
     */
    private boolean isOverlap(long startdate1, long enddate1,long startdate2, long enddate2) {

        return
                ((startdate1 >= startdate2)
                        && startdate1 < enddate2)
                        ||
                        ((startdate1 > startdate2)
                                && startdate1 < enddate2)
                        ||
                        ((startdate2 >= startdate1)
                                && startdate2 < enddate1)
                        ||
                        ((startdate2 > startdate1)
                                && startdate2 < enddate1);

    }



    /**
     * 判断预审核状态
     */
    private int preApprovalStatus(Exam exam, String memberId) {

        // 预审核状态，1：待审核，2：通过，3：拒绝
        int status = PRE_STATUS_WAIT_APPROVAL;

        // 获取当前考试的预审规则
        String preApprovalRule = exam.getPreApprovalRule();
        if (preApprovalRule != null) {
            // 解析预审规则
            JSONObject jsonObject = new JSONObject(preApprovalRule);

            // 因为默认规则没有在数据库中，先判断默认规则，通过即为通过，不通过继续
            PreAuditRule defaultRule = factoryBean.getDefaultRule();
            if (defaultRule.audit(exam, memberId)) {
                status = PRE_STATUS_PASS;
            }else {
                // 默认规则不通过，判断其他规则
                // 取出按范围大小排序得规则
                String[] seq = factoryBean.configRuleSeq();

                // 按范围大小循环判断是否满足预审核条件
                for(String ruleName: seq) {

                    // 获得这条预审核规则
                    PreAuditRule rule = factoryBean.getRule(ruleName);

                    if (rule != null) {

                        // 取出这个规则的勾选状态
                        int useRule = new Integer(jsonObject.get(ruleName).toString());

                        // 判断这个规则是否勾选
                        if(Exam.PRE_APPROVAL_RULE_YES.intValue() == useRule) {
                            // 如果勾选了规则，并且一个都没满足，为3拒绝，
                            status = PRE_STATUS_REFUSE;

                            // 如果勾选了此规则，只要有一个满足，就通过
                            if (rule.audit(exam, memberId)) {
                                // 如果满足此规则，预审核通过
                                status = PRE_STATUS_PASS;
                                return status;
                            }
                        }

                    }
                }

            }

        }

        return status;
    }

    /**
     * 判断移动云预审核状态
     */
    private int cloudExamPreApprovalStatus(Exam exam, String memberId) {
        // 申报中级考试的学员需具备有效的初级证书或,中级证书
        Integer count = certificateRecordDao.execute(cr ->
                cr.select(
                                DSL.count(CERTIFICATE_RECORD.ID)
                        )
                        .from(CERTIFICATE_RECORD)
                        .where(
                                CERTIFICATE_RECORD.MEMBER_ID.eq(memberId),
                                CERTIFICATE_RECORD.CLOUD.eq(CertificateRecord.IS_CLOUD_YES)
                        )
                        .and(
                                ((CERTIFICATE_RECORD.PROFESSION_LEVEL_ID.eq(CloudLevel.LEVEL_ID_1))
                                        .and(CERTIFICATE_RECORD.VALID_DATE.ge(System.currentTimeMillis())))
                                        .or(
                                                CERTIFICATE_RECORD.PROFESSION_LEVEL_ID.eq(CloudLevel.LEVEL_ID_2)
                                        )
                        )
                        .fetchOne(DSL.count(CERTIFICATE_RECORD.ID))
        );
        if (count > 0) {
            return PRE_STATUS_PASS;
        }
        return PRE_STATUS_REFUSE;
    }


    /**
     * 判断网格长预审核状态
     */
    private int gridExamPreApprovalStatus(Exam exam) {
        // 获取当前考试的预审规则
        String preApprovalRule = exam.getPreApprovalRule();
        if (preApprovalRule != null) {
            // 解析预审规则JSONObject
            JSONObject jsonObject = new JSONObject(preApprovalRule);
            // 网格长预审核如果勾选了r2，直接通过
            int useRule2 = new Integer(jsonObject.get("r2").toString());
            if (Exam.PRE_APPROVAL_RULE_YES.intValue() == useRule2) {
                return PRE_STATUS_PASS;
            }
            int useRule1 = new Integer(jsonObject.get("r1").toString());
            if (Exam.PRE_APPROVAL_RULE_YES.intValue() == useRule1) {
                // 验证是否满足r1条件，全部完成学习
                if (exam.getFinishAllCourse()) {
                    return PRE_STATUS_PASS;
                }else {
                    return PRE_STATUS_REFUSE;
                }
            }
        }
        return PRE_STATUS_WAIT_APPROVAL;
    }

    @Override
    @DataSource
    public Exam overlapTime(Integer examRegion, String examId, String currentUserId, String organizationId) {

        Exam exam = examDao.get(examId);

        List<Exam> list = new ArrayList<>();

        // 认证考试
        if (!isOrdinaryExam(exam.getType())) {
            // 我已报名的认证考试（审核状态 1：待审核，2：已通过）
            // 认证考试判断交叉时间
            list = findCurrentMemberAuthExamSignUpRecords(currentUserId);
        }

        //判断是否存在交叉时间
        if (haveOverlap(list, exam)) {
            throw new UnprocessableException(ErrorCode.ExamTimeConflicts);
        }

        return exam;
    }

    /**
     * 普通考试考生报名
     */
    @Override
    @DataSource
    public SignUp ordinaryInsert(Integer examRegion, String examId, String memberId,int preApprovalStatus) {

        //我已报名的考试（审核状态 1：待审核，2：已通过）(普通考试)
        List<Exam> list = signUpDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(EXAM.START_TIME)
                                    .add(EXAM.END_TIME)
                                    .end()
                    )
                    .from(SIGNUP)
                    .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                    .where(
                            SIGNUP.MEMBER_ID.eq(memberId),
                            (SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE).or(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))),
                            (EXAM.TYPE.eq(Exam.EXAM_OFFICIAL_TYPE).or(EXAM.TYPE.eq(Exam.EXAM_UN_OFFICIAL_TYPE))))
                    .fetch(r -> {
                        Exam exam = r.into(EXAM).into(Exam.class);
                        return exam;
                    });
        });

        //当前考试
        Exam exam = examDao.get(examId);

        //判断是否存在交叉时间
        boolean b = false;
        for (int i = 0; i < list.size(); i++) {
            Exam signUpExam = list.get(i);
            if (signUpExam != null) {
                Long signUpStartTime = signUpExam.getStartTime();
                Long signUpEndTime = signUpExam.getEndTime();
                Long startTime = exam.getStartTime();
                Long endTime = exam.getEndTime();
                b = this.isOverlap(startTime, endTime, signUpStartTime, signUpEndTime);
                if (b) {
                    break;
                }
            }
        }

        if (b) {
            throw new UnprocessableException(ErrorCode.ExamTimeConflicts);
        }

        //我已报名的考试（审核状态 3:被拒绝）
        List<Exam> signUpRefuselist = signUpDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(SIGNUP.ID)
                                    .end()
                    )
                    .from(SIGNUP)
                    .where(
                            SIGNUP.MEMBER_ID.eq(memberId),
                            SIGNUP.STATUS.eq(SignUp.STATUS_REFUSE),
                            SIGNUP.EXAM_ID.eq(examId))
                    .fetch(r -> {
                        return r.into(Exam.class);
                    });
        });

        if (signUpRefuselist != null && !signUpRefuselist.isEmpty()) {
            if (!isOrdinaryExam(exam.getType()) || !Exam.PRE_APPROVAL_YES.equals(exam.getPreApproval())) {
                throw new UnprocessableException(ErrorCode.RegistrationHasBeenRejected);
            }
        }

        // 预审核
        Integer preApproval = exam.getPreApproval();

        // 如果没有启用预审核，状态为1，待审核
        // preApprovalStatus = 1;
        // 判断是否启用预审核
        if (!Exam.PRE_APPROVAL_YES.equals(preApproval)) {
            // 判断预审核的状态
            preApprovalStatus = 1;
        }

        //判断考试已经撤销删除
        Exam thisExam = examDao.getOptional(examId).orElse(null);
        if (thisExam == null) {
            throw new UnprocessableException(ErrorCode.ExamNullError);
        }

        //考试在发布后才可以报名
        if ((Exam.STATUS_NOT_START == thisExam.getStatus()
                || Exam.STATUS_SIGNUPING == thisExam.getStatus()
                || Exam.STATUS_STARTING == thisExam.getStatus())) {

            SignUp signUp = getByExamIdAndMemberId(examRegion, examId, memberId);

            if (signUp == null) {

                //添加报名记录
                signUp = insertSu(
                        examRegion,
                        exam,
                        memberId,
                        exam.getOrganizationId(),
                        1 == preApprovalStatus
                                ? SignUp.STATUS_APPROVE :
                                (2 == preApprovalStatus
                                        ? SignUp.STATUS_PASSED : SignUp.STATUS_REFUSE),
                        false
                );

                exam.setSignUp(signUp);
                exam.setApplicantNumber(countSignUp(examId));
                exam.setModifyDate(null);
                examDao.update(exam);

                //如果报名直接不用审核 通过，生成考试记录
                if (signUp.getStatus() == SignUp.STATUS_PASSED) {
                    PaperInstance paperInstance = paperInstanceService.getWithRandomByExamId(examRegion, examId);
                    examRecordService.insert(
                            examRegion,
                            examId,
                            paperInstance.getId(),
                            signUp.getMemberId(),
                            ExamRecord.STATUS_TO_BE_STARTED,
                            Optional.empty(),
                            Optional.empty(),
                            Optional.empty(),
                            Optional.empty(),
                            Optional.empty()
                    );

                    // TODO 学习计划-新增单个
                    if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
                        List<ExamStudyPlanConfig> configList = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
                        if (CollectionUtils.isNotEmpty(configList)) {
                            ExamStudyPlanConfig examStudyPlanConfig = configList.get(0);
                            if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
                                //异步消息
                                messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
                                        MessageHeaderContent.BUSINESS_ID, examId,
                                        MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
                                        MessageHeaderContent.EXAM_MEMBER_ID, memberId
                                );
                            }
                        }
                    }

                }

            } else if (signUp != null && (signUp.getStatus() == SignUp.STATUS_CANCEL || signUp.getStatus() == SignUp.STATUS_REFUSE)) {
                //  再次报名
                update(examRegion,
                        signUp.getId(),
                        1 == preApprovalStatus
                                ? SignUp.STATUS_APPROVE :
                                (2 == preApprovalStatus
                                        ? SignUp.STATUS_PASSED : SignUp.STATUS_REFUSE),
                        memberId
                );
            }


            return signUp;
        }
        throw new UnprocessableException(ErrorCode.EXAM_STATUS_ERROR);
    }

    @Override
    @DataSource
    public SignUp get(Integer examRegion, String id) {

        return signUpDao.execute(e -> {
            Collection<Field<?>> fields = Fields.start()
                    .add(SIGNUP.ID)
                    .add(SIGNUP.EXAM_ID)
                    .add(SIGNUP.MEMBER_ID)
                    .add(SIGNUP.AUDIT_MEMBER_ID)
                    .add(SIGNUP.STATUS)
                    .add(SIGNUP.CREATE_TIME)
                    .add(SIGNUP.ORGANIZATION_ID)
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(MEMBER.ID)
                    .add(MEMBER.NAME)
                    .add(MEMBER.FULL_NAME)
                    .end();
            Record record = e.select(fields).from(SIGNUP)
                    .leftJoin(EXAM).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                    .leftJoin(MEMBER).on(SIGNUP.MEMBER_ID.eq(MEMBER.ID))
                    .where(SIGNUP.ID.eq(id)).fetchOne();
            SignUp signUp = record.into(SignUp.class);
            Exam exam = record.into(Exam.class);
            signUp.setExam(exam);
            Member member = record.into(Member.class);
            signUp.setMember(member);
            return signUp;
        });
    }

    @Override
    @DataSource
    public Map<String, Object> getCloudStatus(Integer examRegion, String currentUserId, List<String> examIds) {
        Map<String,Object> map = new HashMap<String, Object>();
        List<CloudSignup> list = signUpDao.execute(e -> e.select(
                        Fields.start()
                                .add(CLOUD_SIGNUP.ID)
                                .add(CLOUD_SIGNUP.EXAM_ID)
                                .add(CLOUD_SIGNUP.STATUS)
                                .end())
                .from(CLOUD_SIGNUP)
                .where(CLOUD_SIGNUP.MEMBER_ID.eq(currentUserId)
                        .and(CLOUD_SIGNUP.EXAM_ID.in(examIds)))
                .fetch(r -> {
                    CloudSignup cloudSignup = new CloudSignup();
                    cloudSignup.setId(r.getValue(CLOUD_SIGNUP.ID));
                    cloudSignup.setExamId(r.getValue(CLOUD_SIGNUP.EXAM_ID));
                    cloudSignup.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
                    return cloudSignup;
                }));

        List<Member> memberList = memberDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(MEMBER.EMAIL)
                                    .add(MEMBER.PHONE_NUMBER)
                                    .end()
                    )
                    .from(MEMBER)
                    .where(
                            MEMBER.ID.eq(currentUserId))
                    .fetch(r -> {
                        Member member = new Member();
                        member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));
                        member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                        return member;
                    });
        });

        map.put("cloudSignup", list);
        map.put("member", memberList != null && memberList.size() > 0 ? memberList.get(0) : null);
        return map;

    }

    @Override
    @DataSource
    public List<SignUp> getSignUpsByMemberIdsAndExamId(Integer examRegion, List<String> memberIds, String examId) {
        return signUpDao.execute(e -> e.select(Fields.start().add(SIGNUP.CREATE_TIME).add(SIGNUP.MEMBER_ID).end())
                .from(SIGNUP).where(SIGNUP.MEMBER_ID.in(memberIds), SIGNUP.EXAM_ID.eq(examId)).fetchInto(SignUp.class));
    }



    @Override
    @DataSource
    public Member getMember(Integer examRegion, String memberId) {
        List<Member> memberList = memberDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(MEMBER.EMAIL)
                                    .add(MEMBER.PHONE_NUMBER)
                                    .end()
                    )
                    .from(MEMBER)
                    .where(
                            MEMBER.ID.eq(memberId))
                    .fetch(r -> {
                        Member member = new Member();
                        member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));
                        member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                        return member;
                    });
        });
        return memberList != null && memberList.size() > 0 ? memberList.get(0) : new Member();
    }

    @Override
    @DataSource
    public void changeApplicantExam(Integer examRegion, String examId) {

        Exam exam = examDao.execute(e ->  e.select(EXAM.TYPE).from(EXAM).where(EXAM.ID.eq(examId)).fetchOneInto(Exam.class));
        if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
            // 更新全部报名记录已通过（个人中心场景）
            this.update(examRegion,
                    cloudSignupDao.fetch(CLOUD_SIGNUP.EXAM_ID.eq(examId),
                                    CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_PASSED)).stream()
                            .map(CloudSignup::getId).collect(Collectors.toList()),
                    SignUp.STATUS_PASSED, null, Optional.empty(), Optional.empty(),Optional.empty(),  Exam.CLOUD_URI);
        }else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
            // 更新全部报名记录已通过（个人中心场景）
            this.update(examRegion,
                    gridSignupDao.fetch(GRID_SIGNUP.EXAM_ID.eq(examId),
                                    GRID_SIGNUP.STATUS.ne(SignUp.STATUS_PASSED)).stream()
                            .map(GridSignup::getId).collect(Collectors.toList()),
                    SignUp.STATUS_PASSED, null, Optional.empty(), Optional.empty(),Optional.empty(),  Exam.GRID_URI);
        }else {
            // 更新全部报名记录已通过（个人中心场景）
            this.update(examRegion,
                    signUpDao.fetch(SIGNUP.EXAM_ID.eq(examId),
                                    SIGNUP.STATUS.ne(SignUp.STATUS_PASSED)).stream()
                            .map(SignUp::getId).collect(Collectors.toList()),
                    SignUp.STATUS_PASSED, null, Optional.empty(), Optional.empty(),Optional.empty(),  Exam.URI);
        }

        messageSender.send(
                MessageTypeContent.EXAM_CHANGE_TO_ORDINARY,
                MessageHeaderContent.EXAM_ID, examId,
                MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));

    }


    @Override
    @DataSource
    public String getMemberIdsAboutExam(Integer examRegion, String examId) {
        List<String> signUpMemberIds = signUpDao.fetch(
                        SIGNUP.EXAM_ID.eq(examId),SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL))
                .stream().map(SignUp::getMemberId).collect(Collectors.toList());
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        List<String> examRecordMemberIds = examRecordDao.execute(x -> x.select(
                        Fields.start()
                                .add(examRecordTable.field("f_member_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .fetch(examRecordTable.field("f_member_id", String.class))
        );
        signUpMemberIds.addAll(examRecordMemberIds);
        return signUpMemberIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
    }






    public List<ExamStudyPlanConfig> findStudyPlanConfig(String businessId, Integer type) {
        return examStudyPlanConfigCommonDao.execute(w -> w.select(Fields.start().add(EXAM_STUDY_PLAN_CONFIG).end())
                .from(EXAM_STUDY_PLAN_CONFIG)
                .where(EXAM_STUDY_PLAN_CONFIG.BUSINESS_ID.eq(businessId))
                .and(EXAM_STUDY_PLAN_CONFIG.BUSINESS_TYPE.eq(type))
                .fetch()).into(ExamStudyPlanConfig.class);
    }

}



