package com.zxy.product.examstu.service.rule.preaudit;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Created by ouyang on 2017/11/1.
 */
@Component
public class PreAuditRuleFactoryBean {
    @Autowired
    @Qualifier("defaultPreAuditRule")
    private PreAuditRule defaultPreAuditRule;

    @Autowired
    @Qualifier("preAuditRule1")
    private PreAuditRule preAuditRule1;

    @Autowired
    @Qualifier("preAuditRule2")
    private PreAuditRule preAuditRule2;

    @Autowired
    @Qualifier("preAuditRule3")
    private PreAuditRule preAuditRule3;

    @Autowired
    @Qualifier("preAuditRule4")
    private PreAuditRule preAuditRule4;

    @Autowired
    @Qualifier("preAuditRule5")
    private PreAuditRule preAuditRule5;


    private Map<String, PreAuditRule> mapRule = new HashMap<>();

    //数据库中规则为{r1:0, r2:1, r3:0, r4:0, r5:0}
    private String[] ruleSeq = new String[]{"r5", "r3", "r2", "r4", "r1"};

    public String[] configRuleSeq() {
        return ruleSeq;
    }

    public PreAuditRule getRule(String ruleName) {
        initMapRule();
        return mapRule.get(ruleName);
    }

    public void initMapRule() {
        if(null == mapRule || mapRule.size() <= 0) {
            mapRule.put("r1", preAuditRule1);
            mapRule.put("r2", preAuditRule2);
            mapRule.put("r3", preAuditRule3);
            mapRule.put("r4", preAuditRule4);
            mapRule.put("r5", preAuditRule5);
        }
    }

    // 获取默认规则
    public PreAuditRule getDefaultRule() {
        return defaultPreAuditRule;
    }


}
