package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.QuestionCopyService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.Select;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class  QuestionCopyServiceSupport implements QuestionCopyService{

    private CommonDao<QuestionCopy> dao;

    private Cipher cipher = null;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    public QuestionCopyServiceSupport() throws NoSuchAlgorithmException, NoSuchPaddingException {
    	cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
	}

    /**
     * ase加密需要向量
     */
    private final static String IV = "1234567890123456";

    @Autowired
    public void setDao(CommonDao<QuestionCopy> dao) {
        this.dao = dao;
    }

    private List<QuestionCopy> getQuestionsByMap2(Integer examRegion,String paperInstanceId, Result<Record> records, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

    	Map<String, QuestionAttrCopy> questionAttrCopyMap = records.stream().map(t -> {
    		QuestionAttrCopy attr = new QuestionAttrCopy();
    		attr.setId(t.getValue(QUESTION_ATTR_COPY.ID.as("attrId")));
    		attr.setName(t.getValue(QUESTION_ATTR_COPY.NAME));
    		attr.setValue(t.getValue(QUESTION_ATTR_COPY.VALUE));
    		attr.setType(t.getValue(QUESTION_ATTR_COPY.TYPE));
    		attr.setQuestionCopyId(t.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
    		return attr;
    	}).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

//    	Map<String, QuestionAttrCopy> questionAttrCopyMap = records.into(QUESTION_ATTR_COPY).into(QuestionAttrCopy.class)
//                .stream().collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

    	Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

    	TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        Map<String, AnswerRecord> answerRecordMap = records.into(AnswerRecord.class)
                .stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = records.stream().map(r -> {
            PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
            paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
            paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
            return paperInstanceQuestionCopy;
        }).collect(Collectors.toList());

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

    	Map<String, QuestionCopy> questionCopyMap = records.into(QuestionCopy.class)
    			.stream().map(t -> {
    				t.setQuestionAttrCopys(questionAttrCopyListMap.get(t.getId()));
    				t.setAnswerRecord(answerRecordMap.get(t.getId()));
    				t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
    				return t;
    			}).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

    	Map<String, List<QuestionCopy>> subsMap = getSubsMap(examRegion,paperInstanceId, questionCopyMap, examRecordId, searchScoreNull, examId);

    	return questionCopyMap.values().stream().map(t -> {
    		if (t.getType() == Question.READING_COMPREHENSION) {
    			t.setSubs(subsMap.getOrDefault(t.getId(), new ArrayList<>()));
    		}
    		return t;
    	}).collect(Collectors.toList());
    }

    /**
     * @param questionCopyMap
     * @param examId
     * @return
     * 阅读理解 子题目
     * searchScoreNull： 兼容评卷API 过滤分数没有或者未答题目
     */
    private Map<String, List<QuestionCopy>> getSubsMap(Integer examRegion,String paperInstanceId, Map<String, QuestionCopy> questionCopyMap, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<QuestionCopy> readings = questionCopyMap.values().stream().filter(t -> {
            return t.getType() == Question.READING_COMPREHENSION;
        }).collect(Collectors.toList());

        if (readings.size() > 0) {

            List<Exam> list = dao.execute(e ->
                    e.select(
                                    Fields.start()
                                            .add(QUESTION_COPY.ID)
                                            .add(QUESTION_COPY.TYPE)
                                            .add(QUESTION_COPY.CONTENT)
                                            .add(QUESTION_COPY.PARENT_ID)
                                            .add(QUESTION_COPY.SCORE)
                                            .add(QUESTION_COPY.QUESTION_ID)
                                            .add(QUESTION.ID)
                                            .add(QUESTION.ERROR_RATE)
                                            .add(QUESTION.PARSING_TEXT)
                                            .add(QUESTION.PARSING)
                                            .add(table.fields())
                                            .add(QUESTION_ATTR_COPY.ID)
                                            .add(QUESTION_ATTR_COPY.NAME)
                                            .add(QUESTION_ATTR_COPY.VALUE)
                                            .add(QUESTION_ATTR_COPY.TYPE)
                                            .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                                            .add(paperInstanceQuestionCopyTable.fields())
                                            .end()
                            )
                            .from(QUESTION_COPY)
                            .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
                            .leftJoin(table).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID)).and(table.field("f_exam_record_id", String.class).eq(examRecordId))
                            .leftJoin(paperInstanceQuestionCopyTable).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID)).and(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId))
                            .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
                            .where(QUESTION_COPY.PARENT_ID.in(readings.stream().map(QuestionCopy::getId).collect(Collectors.toList())),
                                    searchScoreNull.map(t -> table.field("f_score", Integer.class).isNull()).orElse(DSL.trueCondition()))
                            .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())).fetch(r -> {
                Exam exam = new Exam();
                QuestionCopy questionCopy = new QuestionCopy();
                questionCopy.setId(r.getValue(QUESTION_COPY.ID));
                questionCopy.setType(r.getValue(QUESTION_COPY.TYPE));
                questionCopy.setContent(r.getValue(QUESTION_COPY.CONTENT));
                questionCopy.setParentId(r.getValue(QUESTION_COPY.PARENT_ID));
                questionCopy.setScore(r.getValue(QUESTION_COPY.SCORE));
                questionCopy.setQuestionId(r.getValue(QUESTION_COPY.QUESTION_ID));
                exam.setQuestionCopy(questionCopy);

                Question question = new Question();
                question.setId(r.getValue(QUESTION.ID));
                question.setErrorRate(r.getValue(QUESTION.ERROR_RATE));
                question.setParsingText(r.getValue(QUESTION.PARSING_TEXT));
                question.setParsing(r.getValue(QUESTION.PARSING));
                exam.setQuestion(question);

                AnswerRecord answerRecord = new AnswerRecord();
                answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                exam.setAnswerRecord(answerRecord);

                QuestionAttrCopy questionAttrCopy = new QuestionAttrCopy();
                questionAttrCopy.setId(r.getValue(QUESTION_ATTR_COPY.ID));
                questionAttrCopy.setName(r.getValue(QUESTION_ATTR_COPY.NAME));
                questionAttrCopy.setValue(r.getValue(QUESTION_ATTR_COPY.VALUE));
                questionAttrCopy.setType(r.getValue(QUESTION_ATTR_COPY.TYPE));
                questionAttrCopy.setQuestionCopyId(r.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
                exam.setQuestionAttrCopy(questionAttrCopy);

                PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
                paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
                paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
                paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
                paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
                paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
                paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
                exam.setPaperInstanceQuestionCopy(paperInstanceQuestionCopy);

                return exam;
            });

            Map<String, QuestionAttrCopy> questionAttrMap = list.stream().map(Exam::getQuestionAttrCopy).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (k, v) -> v));

            Map<String, List<QuestionAttrCopy>> questionAttrListMap = reduceQuestionAttrCopyList(questionAttrMap.values());

            Map<String, AnswerRecord> answerRecordMap =  list.stream().map(Exam::getAnswerRecord).collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

            Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys =  list.stream().map(Exam::getPaperInstanceQuestionCopy).collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

            Map<String, Question> questions = list.stream().map(Exam::getQuestion).collect(Collectors.toMap(Question::getId, e -> e, (k, v) -> v));

            List<QuestionCopy> questionCopyList= list.stream().map(Exam::getQuestionCopy).collect(Collectors.toList());

            Map<String, QuestionCopy> questionMap = questionCopyList.stream().map(t -> {
            	if (questions.get(t.getQuestionId()) != null) {
            		t.setErrorRate(questions.get(t.getQuestionId()).getErrorRate());
            		t.setParsing(questions.get(t.getQuestionId()).getParsing());
                    t.setParsingText(questions.get(t.getQuestionId()).getParsingText());
            	}
            	t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
            	return t;
            }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

            Map<String, List<QuestionCopy>> subsMap = subQuestionsKeyByParentId(answerRecordMap, questionMap.values(), questionAttrListMap);

            return subsMap;
        }
        return new HashMap<>();
    }

    private Map<String, List<QuestionAttrCopy>> reduceQuestionAttrCopyList(Collection<QuestionAttrCopy> questionAttrCopies) {

        Map<String, List<QuestionAttrCopy>> map = new HashMap<>();
        questionAttrCopies.stream().forEach(questionAttr -> {
            if (map.get(questionAttr.getQuestionCopyId()) == null) {
            	map.put(questionAttr.getQuestionCopyId(), new ArrayList<>());
            }
            map.get(questionAttr.getQuestionCopyId()).add(questionAttr);
        });
        return map;
    }

    private Map<String, List<QuestionCopy>> subQuestionsKeyByParentId(Map<String, AnswerRecord> answerRecordMap, Collection<QuestionCopy> questionCopies,
            Map<String, List<QuestionAttrCopy>> questionAttrListMap) {

        Map<String, List<QuestionCopy>> map = new HashMap<>();
        questionCopies.stream().forEach(questionCopy -> {
            questionCopy.setQuestionAttrCopys(questionAttrListMap.get(questionCopy.getId()));
            if (map.get(questionCopy.getParentId()) == null) {
            	map.put(questionCopy.getParentId(), new ArrayList<>());
            }
            questionCopy.setAnswerRecord(answerRecordMap.get(questionCopy.getId()));
            map.get(questionCopy.getParentId()).add(questionCopy);
        });
        return map;
    }

	@Override
	public List<QuestionCopy> findQuestionsByPaperId(Integer examRegion,String paperInstanceId, String examRecordId, String examId) {

	    TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

		 Result<Record> records = dao.execute(e ->
	         e.selectDistinct(
	             Fields.start()
	             .add(QUESTION_COPY.ID)
	             .add(QUESTION_COPY.CONTENT)
	             .add(QUESTION_COPY.PARENT_ID)
	             .add(QUESTION_COPY.SCORE)
	             .add(QUESTION_COPY.TYPE)
	             .add(QUESTION_COPY.DIFFICULTY)
	             .add(QUESTION_ATTR_COPY.ID.as("attrId"))
	             .add(QUESTION_ATTR_COPY.NAME)
	             .add(QUESTION_ATTR_COPY.VALUE)
	             .add(QUESTION_ATTR_COPY.TYPE)
	             .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
	             .add(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class))
	             .add(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class))
	             .end()
	         )
	         .from(QUESTION_COPY)
	         .leftJoin(paperInstanceQuestionCopyTable).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID))
	         .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
	         .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId),QUESTION_COPY.PARENT_ID.isNull())
	         .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
	         .fetch()
     );

     List<QuestionCopy> questions = getQuestionsByMap2(examRegion,paperInstanceId, records, examRecordId, Optional.empty(), examId);
     return questions;
	}

	@Override
	public List<QuestionCopy> findQuestionCopysByExamId(String examId) {
		Result<Record> records = dao.execute(e -> {
			return e.select(
				Fields.start()
				.add(QUESTION_COPY)
				.add(QUESTION_ATTR_COPY)
				.end()
			)
			.from(QUESTION_COPY)
			.leftJoin(QUESTION_ATTR_COPY).on(QUESTION_COPY.ID.eq(QUESTION_ATTR_COPY.QUESTION_COPY_ID))
			.where(QUESTION_COPY.EXAM_ID.eq(examId))
			.fetch();
		});

		Map<String, QuestionCopy> questionCopys = records.into(QuestionCopy.class).stream().collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
		List<QuestionAttrCopy> questionAttrCopys = records.into(QuestionAttrCopy.class).stream().filter(f -> f.getId() != null).collect(Collectors.toList());

		Map<String, List<QuestionAttrCopy>> questionAttrCopyMap = questionAttrCopys.stream().collect(Collectors.groupingBy(QuestionAttrCopy::getQuestionCopyId));


		return questionCopys.values().stream().map(q -> {
			q.setQuestionAttrCopys(questionAttrCopyMap.get(q.getId()));
			return q;
		}).collect(Collectors.toList());
	}



    @Override
    @DataSource
    public List<QuestionCopy> findQuestionsByPaperAndExamRecord(Integer examRegion, String paperInstanceId, String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<Exam> list = dao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(QUESTION_COPY.ID)
                                        .add(QUESTION_COPY.TYPE)
                                        .add(QUESTION_COPY.CONTENT)
                                        .add(QUESTION_COPY.PARENT_ID)
                                        .add(QUESTION_COPY.SCORE)
                                        .add(QUESTION_COPY.QUESTION_ID)
                                        .add(QUESTION.ID)
                                        .add(QUESTION.ERROR_RATE)
                                        .add(QUESTION.PARSING_TEXT)
                                        .add(QUESTION.PARSING)
                                        .add(table.fields())
                                        .add(QUESTION_ATTR_COPY.ID)
                                        .add(QUESTION_ATTR_COPY.NAME)
                                        .add(QUESTION_ATTR_COPY.VALUE)
                                        .add(QUESTION_ATTR_COPY.TYPE)
                                        .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                                        .add(paperInstanceQuestionCopyTable.fields())
                                        .end()
                        )
                        .from(paperInstanceQuestionCopyTable)
                        .leftJoin(QUESTION_COPY).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID))
                        .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
                        .leftJoin(table).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)).and(table.field("f_exam_record_id", String.class).eq(examRecordId)))
                        .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(table.field("f_exam_record_id", String.class))).and(examRecordTable.field("f_id", String.class).eq(examRecordId))
                        .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
                        .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId), QUESTION_COPY.PARENT_ID.isNull())
                        .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
        ).fetch(r -> {
            Exam exam = new Exam();
            QuestionCopy questionCopy = new QuestionCopy();
            questionCopy.setId(r.getValue(QUESTION_COPY.ID));
            questionCopy.setType(r.getValue(QUESTION_COPY.TYPE));
            questionCopy.setContent(r.getValue(QUESTION_COPY.CONTENT));
            questionCopy.setParentId(r.getValue(QUESTION_COPY.PARENT_ID));
            questionCopy.setScore(r.getValue(QUESTION_COPY.SCORE));
            questionCopy.setQuestionId(r.getValue(QUESTION_COPY.QUESTION_ID));
            exam.setQuestionCopy(questionCopy);

            Question question = new Question();
            question.setId(r.getValue(QUESTION.ID));
            question.setErrorRate(r.getValue(QUESTION.ERROR_RATE));
            question.setParsingText(r.getValue(QUESTION.PARSING_TEXT));
            question.setParsing(r.getValue(QUESTION.PARSING));
            exam.setQuestion(question);

            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            exam.setAnswerRecord(answerRecord);

            QuestionAttrCopy questionAttrCopy = new QuestionAttrCopy();
            questionAttrCopy.setId(r.getValue(QUESTION_ATTR_COPY.ID));
            questionAttrCopy.setName(r.getValue(QUESTION_ATTR_COPY.NAME));
            questionAttrCopy.setValue(r.getValue(QUESTION_ATTR_COPY.VALUE));
            questionAttrCopy.setType(r.getValue(QUESTION_ATTR_COPY.TYPE));
            questionAttrCopy.setQuestionCopyId(r.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
            exam.setQuestionAttrCopy(questionAttrCopy);

            PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
            paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
            paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
            paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
            paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
            paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
            paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
            exam.setPaperInstanceQuestionCopy(paperInstanceQuestionCopy);

            return exam;
        });

        List<QuestionCopy> questions = getQuestionsByMap(examRegion, paperInstanceId, list, examRecordId, Optional.empty(), examId);
        Collections.sort(questions);
        return questions;
    }

    @Override
    @DataSource
    public List<Question> findQuestionsTopError(Integer examRegion, List<String> questionDepotList) {
        List<Question> questionList = dao.execute(e -> e.select(
                        Fields.start()
                                .add(QUESTION.ID)
                                .add(QUESTION.ERROR_RATE)
                                .add(QUESTION.TYPE)
                                .add(QUESTION.CONTENT)
                                .add(QUESTION.DIFFICULTY)
                                .add(QUESTION.SCORE)
                                .end())
                .from(QUESTION)
                .where(QUESTION.QUESTION_DEPOT_ID.in(questionDepotList))
                .and(QUESTION.ERROR_RATE.isNotNull())
                .and(QUESTION.ERROR_RATE.ne(0))
                .and(QUESTION.ERROR_RATE.ne(1000000))
                .orderBy(QUESTION.ERROR_RATE.desc())
                .limit(0,10)
                .fetch(r -> {
                    Question question = new Question();
                    question.setId(r.getValue(QUESTION.ID));
                    question.setErrorRate(r.getValue(QUESTION.ERROR_RATE));
                    question.setType(r.getValue(QUESTION.TYPE));
                    question.setContent(r.getValue(QUESTION.CONTENT));
                    question.setDifficulty(r.getValue(QUESTION.DIFFICULTY));
                    question.setScore(r.getValue(QUESTION.SCORE));
                    return question;
                }));

        List<String> questionIds = questionList.stream().map(Question::getId).collect(Collectors.toList());

        Map<String, QuestionAttr> questionAttrMap = dao.execute(e -> e.select(
                                Fields.start()
                                        .add(QUESTION_ATTR)
                                        .end())
                        .from(QUESTION_ATTR)
                        .where(QUESTION_ATTR.QUESTION_ID.in(questionIds))
                        .fetchInto(QuestionAttr.class))
                .stream().collect(Collectors.toMap(QuestionAttr::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttr>> questionAttrListMap = reduceQuestionAttrList(questionAttrMap.values());

        List<Question> list = questionList.stream().map(t -> {
            t.setQuestionAttrs(questionAttrListMap.get(t.getId()));
            return t;
        }).collect(Collectors.toList());

        return list;
    }


    @Override
    @DataSource
    public List<Question> findAnswerRecord(Integer examRegion, String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));


        List<Question> questionList = dao.execute(e -> e.select(
                        Fields.start()
                                .add(table.fields())
                                .add(QUESTION.ID)
                                .end())
                .from(table)
                .leftJoin(QUESTION_COPY).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
                .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
                .where(table.field("f_exam_record_id", String.class).eq(examRecordId))
                .fetch(r -> {
                    AnswerRecord answerRecord = new AnswerRecord();
                    answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                    answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                    answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                    answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                    answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                    answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                    answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                    Question question = new Question();
                    question.setId(r.getValue(QUESTION.ID));
                    question.setAnswerReocrd(answerRecord);
                    return question;
                }));

        return questionList;
    }


    private Map<String, List<QuestionAttr>> reduceQuestionAttrList(Collection<QuestionAttr> questionAttrs) {
        Map<String, List<QuestionAttr>> map = new HashMap<>();
        questionAttrs.stream().forEach(questionAttr -> {
            if (map.get(questionAttr.getQuestionId()) == null) {
                map.put(questionAttr.getQuestionId(), new ArrayList<>());
            }
            map.get(questionAttr.getQuestionId()).add(questionAttr);
        });
        return map;
    }


    private List<QuestionCopy> getQuestionsByMap(Integer examRegion, String paperInstanceId, List<Exam> list, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

        Map<String, QuestionAttrCopy> questionAttrCopyMap = list.stream().map(Exam::getQuestionAttrCopy).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

        List<AnswerRecord> answerRecordList = list.stream().map(Exam::getAnswerRecord).collect(Collectors.toList());

        Map<String, AnswerRecord> answerRecordMap = answerRecordList
                .stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = list.stream().map(Exam::getPaperInstanceQuestionCopy).collect(Collectors.toList());

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

        Map<String, Question> questions = list.stream().map(Exam::getQuestion).collect(Collectors.toMap(Question::getId, e -> e, (k, v) -> v));

        List<QuestionCopy> questionCopyList= list.stream().map(Exam::getQuestionCopy).collect(Collectors.toList());

        Map<String, QuestionCopy> questionCopyMap = questionCopyList.stream().map(t -> {
                    if (questions.get(t.getQuestionId()) != null) {
                        t.setErrorRate(questions.get(t.getQuestionId()).getErrorRate());
                        t.setParsing(questions.get(t.getQuestionId()).getParsing());
                        t.setParsingText(questions.get(t.getQuestionId()).getParsingText());
                    }
                    t.setQuestionAttrCopys(questionAttrCopyListMap.get(t.getId()));
                    t.setAnswerRecord(answerRecordMap.get(t.getId()));
                    t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
                    return t;
                }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

        Map<String, List<QuestionCopy>> subsMap = getSubsMap(examRegion, paperInstanceId, questionCopyMap, examRecordId, searchScoreNull, examId);

        return questionCopyMap.values().stream().map(t -> {
            if (t.getType() == Question.READING_COMPREHENSION) {
                t.setSubs(subsMap.getOrDefault(t.getId(), new ArrayList<>()));
            }
            return t;
        }).collect(Collectors.toList());
    }


}
