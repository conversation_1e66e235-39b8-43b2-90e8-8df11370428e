package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.product.examstu.api.MemberService;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.Organization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.exam.jooq.Tables.MEMBER;
import static com.zxy.product.exam.jooq.Tables.ORGANIZATION;

@Service
public class MemberServiceSupport implements MemberService{

    private CommonDao<Member> memberDao;

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Override
    public List<Member> findGrantByMemberNames(List<String> memberNames, List<String> organizationIds) {
        return memberDao.fetch(MEMBER.NAME.in(memberNames).and(MEMBER.ORGANIZATION_ID.in(organizationIds)));
    }


    @Override
    public List<Member> findByMemberNames(List<String> memberNames) {
        return memberDao.fetch(MEMBER.NAME.in(memberNames));
    }

    @Override
    public List<Member> findByNames(List<String> memberNames) {
        List<Member> members = memberDao.execute(e ->
                e.select(Fields.start().add(MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME,
                                ORGANIZATION.ID, ORGANIZATION.PATH).end())
                        .from(MEMBER)
                        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(MEMBER.NAME.in(memberNames))
                        .fetch(r -> {
                            Member member = new Member();
                            member.setId(r.getValue(MEMBER.ID));
                            member.setName(r.getValue(MEMBER.NAME));
                            member.setFullName(r.getValue(MEMBER.FULL_NAME));

                            Organization organization = new Organization();
                            organization.setId(r.getValue(ORGANIZATION.ID));
                            organization.setPath(r.getValue(ORGANIZATION.PATH));

                            member.setOrganization(organization);
                            return member;
                        }));
        return members;
    }

    @Override
    public Member getMember(String id) {
       List<Member> members = memberDao.execute(e ->
        e.select(Fields.start().add(MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME,
            MEMBER.IDENTITY_NUMBER, MEMBER.PHONE_NUMBER,
            MEMBER.EMAIL, ORGANIZATION.ID, ORGANIZATION.NAME).end())
            .from(MEMBER)
            .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
            .where(MEMBER.ID.eq(id))
            .fetch(r -> {
                Member member = new Member();
                member.setId(r.getValue(MEMBER.ID));
                member.setName(r.getValue(MEMBER.NAME));
                member.setFullName(r.getValue(MEMBER.FULL_NAME));
                member.setIdentityNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.IDENTITY_NUMBER)));
                member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));

                Organization organization = new Organization();
                organization.setId(r.getValue(ORGANIZATION.ID));
                organization.setName(r.getValue(ORGANIZATION.NAME));

                member.setOrganization(organization);
                return member;
            }));
        return CollectionUtils.isNotEmpty(members) ? members.get(0) : new Member();
    }


}
