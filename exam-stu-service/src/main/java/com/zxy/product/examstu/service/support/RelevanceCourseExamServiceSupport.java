package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.RelevanceCourseExamService;
import com.zxy.product.exam.entity.RelevanceCourseExam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.zxy.product.exam.jooq.Tables.RELEVANCE_COURSE_EXAM;

@Service
public class RelevanceCourseExamServiceSupport implements RelevanceCourseExamService {
    @Resource
    private CommonDao<RelevanceCourseExam> relevanceCourseExamDao;


    @Override
    @DataSource
    public List<RelevanceCourseExam> findRelevanceCourseExamList(Integer examRegion, String examId) {
        return relevanceCourseExamDao.execute(dsl -> dsl.select(RELEVANCE_COURSE_EXAM.COURSE_ID).from(RELEVANCE_COURSE_EXAM).where(RELEVANCE_COURSE_EXAM.EXAM_ID.eq(examId)).fetch(r->r.into(RelevanceCourseExam.class)));

    }


}
