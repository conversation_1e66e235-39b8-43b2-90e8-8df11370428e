package com.zxy.product.examstu.service.support;


import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamNoticeService;
import com.zxy.product.examstu.api.MarkConfigService;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class MarkConfigServiceSupport implements MarkConfigService {

    private CommonDao<MarkConfig> markConfigDao;
    private ExamNoticeService examNoticeService;
    private CommonDao<ExamRecord> examRecordDao;
    private CommonDao<ToDo> toDoDao;

	private GetTableUtil getTableUtil;

	@Autowired
	public void setGetTableUtil(GetTableUtil getTableUtil) {
		this.getTableUtil = getTableUtil;
	}


    @Autowired
    public void setToDoDao(CommonDao<ToDo> toDoDao) {
		this.toDoDao = toDoDao;
	}

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
		this.examRecordDao = examRecordDao;
	}

    @Autowired
    public void setExamNoticeService(ExamNoticeService examNoticeService) {
		this.examNoticeService = examNoticeService;
	}

    @Autowired
    public void setMarkConfigDao(CommonDao<MarkConfig> markConfigDao) {
        this.markConfigDao = markConfigDao;
    }

//	@Override
//    public List<MarkConfig> insert(String examId, List<MarkConfig> markConfigs,
//    		Integer anonymityMark, List<ExamNotice> examNotices) {
//
//		Exam exam = examDao.get(examId);
//
//		List<MarkConfig> olds = find(examId);
//		String oldMembers = olds.stream().map(MarkConfig::getMemberId).collect(Collectors.joining(","));
//		List<MarkConfig> needCreateToDos = markConfigs.stream().filter(t -> {
//			if (t.getMemberId().equals("1")) {
//				return loopCheckingId(oldMembers, t.getMemberId());
//			}
//			return oldMembers.indexOf(t.getMemberId()) < 0;
//		}).collect(Collectors.toList());
//
//		deleteToDoByUpdateMarkConfig(olds, markConfigs, examId);
//		List<String> markConfigIds = markConfigDao.execute(e -> e.select(MARK_CONFIG.ID)
//                .from(MARK_CONFIG)
//                .where(MARK_CONFIG.EXAM_ID.eq(examId))
//                .fetch(MARK_CONFIG.ID));
//        for (String markConfigId : markConfigIds) {
//            markConfigDao.delete(MARK_CONFIG.ID.eq(markConfigId));
//        }
//		markConfigDao.insert(markConfigs);
//
//		exam.setAnonymityMark(anonymityMark);
//		exam.setModifyDate(null);
//		examDao.update(exam);
//
//        //生成待办
//        List<ExamRecord> examRecords = createToDo(needCreateToDos, exam);
//        //发送通知
//        sendMarkNotice(needCreateToDos, examNotices, exam, examRecords);
//        return markConfigs;
//    }

	/**
	 * 循环检查是否包含memberId
	 * @param oldMembers
	 * @param memberId
	 * @return
	 */
//	private boolean loopCheckingId(String oldMembers, String memberId) {
//		return Arrays.stream(oldMembers.split(",")).filter(oldId -> {
//			return !oldId.equals(memberId);
//		}).collect(Collectors.toList()).size() > 0;
//	}
//
//	private void deleteToDoByUpdateMarkConfig(List<MarkConfig> oldMarkConfigs, List<MarkConfig> newMarkConfigs, String examId) {
//    	//用新的评卷老师来比对下 旧的评卷老师，判断哪些是已经没有存在的老师
//    	String memberIds = newMarkConfigs.stream().map(MarkConfig::getMemberId).collect(Collectors.joining(","));
//    	Map<String, String> map = oldMarkConfigs.stream().filter(o -> memberIds.indexOf(o.getMemberId()) < 0)
//    			.collect(Collectors.toMap(MarkConfig::getId, MarkConfig::getMemberId));
//
//    	TableImpl<?> examRecordTable = getExamRecordTable(answerRecordService.getExamRecordStringTable(examId));
//
//    	List<String> todoIds = markConfigDao.execute(e -> e.select(TO_DO.ID).from(TO_DO)
//    			.leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class))).and(examRecordTable.field("f_exam_id", String.class).eq(examId))
//    			.leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
//    			.where(EXAM.ID.eq(examId), TO_DO.MEMBER_ID.in(map.values())).fetch(TO_DO.ID));
//
//    	messageSender.send(MessageTypeContent.UPDATE_MARK_CONFIG, MessageHeaderContent.EXAM_ID, examId);
//
//    	toDoDao.delete(todoIds);
//	}

    /**
     * 1.查询考试共有的考试记录
     * 2.查询已存在的待办
     * 3.生成不存在的待办
     * @param updates
     * @param exam
     * @return
     */
//    private List<ExamRecord> createToDo(List<MarkConfig> updates, Exam exam) {
//
//        TableImpl<?> examRecordTable = getExamRecordTable(answerRecordService.getExamRecordStringTable(exam.getId()));
//
//        TableImpl<?> answerRecordTable = getAnswerRecordTable(answerRecordService.getAnswerRecordStringTable(exam.getId()));
//
//    	List<ExamRecord> examRecords = examRecordDao.execute(e ->
//        e.select(Fields.start()
//            .add(examRecordTable.fields())
//            .end())
//            .from(examRecordTable)
//            .where(examRecordTable.field("f_exam_id", String.class).eq(exam.getId()))
//            .and(examRecordTable.field("f_status", Integer.class).ge(ExamRecord.STATUS_TO_BE_OVER))
//            ).fetch(r -> {
//                ExamRecord examRecord = new ExamRecord();
//                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
//                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
//                examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
//                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
//                examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
//                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
//                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
//                examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
//                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
//                examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
//                examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
//                examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
//                examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
//                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
//                examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
//                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
//                examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
//                examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
//                examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
//                examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
//                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
//                examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
//                examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
//                examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
//                examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
//                examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
//                examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
//                return examRecord;
//            });
//
//    	for (ExamRecord examRecord : examRecords) {
//    	    List<Integer> types = examRecordDao.execute(e ->
//            e.selectDistinct(Fields.start()
//                .add(QUESTION_COPY.TYPE)
//                .end())
//                .from(answerRecordTable)
//                .leftJoin(QUESTION_COPY).on(answerRecordTable.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
//                .where(answerRecordTable.field("f_exam_record_id", String.class).eq(examRecord.getId()))
//                .and(answerRecordTable.field("f_answer", String.class).ne(""))
//                .and(answerRecordTable.field("f_answer", String.class).isNotNull())
//                .and(QUESTION_COPY.TYPE.eq(Question.SENTENCE_COMPLETION).or(QUESTION_COPY.TYPE.eq(Question.QUESTION_ANWSER)))
//                ).fetch(QUESTION_COPY.TYPE);
//    	    if (types.contains(Question.SENTENCE_COMPLETION) && !types.contains(Question.QUESTION_ANWSER)) {
//    	        examRecord.setType(ToDo.INCLUDE_TYPE_1);
//    	    } else if (types.contains(Question.SENTENCE_COMPLETION) && types.contains(Question.QUESTION_ANWSER)) {
//    	        examRecord.setType(ToDo.INCLUDE_TYPE_2);
//    	    } else {
//    	        examRecord.setType(ToDo.INCLUDE_TYPE_0);
//    	    }
//        }
//
//    	List<ToDo> oldToDos = toDoDao.execute(e ->
//			e.select(
//				Fields.start()
//				.add(TO_DO)
//				.end()
//			)
//			.from(TO_DO)
//			.leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(TO_DO.TARGET_ID))
//			.where(examRecordTable.field("f_exam_id", String.class).eq(exam.getId())).fetchInto(ToDo.class)
//		);
//
//    	if (!examRecords.isEmpty()) {
//
//			List<ToDo> toDos = new ArrayList<>();
//			updates.stream().map(MarkConfig::getMemberId).collect(Collectors.toSet()).stream().forEach(t -> {
//				toDos.addAll(examRecords.stream().map(e -> {
//
//					if (ToDo.INCLUDE_TYPE_1 == e.getType()) {
//					    int size = oldToDos.stream().filter(o ->
//            	                    ToDo.INCLUDE_TYPE_1 == o.getIncludeType() && o.getTargetId().equals(e.getId())
//            	                    && ToDo.AUDITED_1 == o.getAudited())
//					            .collect(Collectors.toList()).size();
//
//					    ToDo temp = new ToDo();
//	                    temp.forInsert();
//	                    temp.setMemberId(t);
//	                    temp.setTargetId(e.getId());
//	                    temp.setType(ToDo.MARK_PAPER_TYPE);
//	                    temp.setSubmited(ToDo.SUBMITED_0);
//	                    temp.setAudited(ToDo.AUDITED_0);
//	                    temp.setIncludeType(e.getType());
//
//					    if (size > 0) {
//					        temp.setAudited(ToDo.AUDITED_1);
//		                    return temp;
//					    }
//					    return temp;
//
//					}
//	                   if (ToDo.INCLUDE_TYPE_0 == e.getType()) {
//	                        int size = oldToDos.stream().filter(o ->
//	                                    ToDo.INCLUDE_TYPE_0 == o.getIncludeType() && o.getTargetId().equals(e.getId())
//	                                    && ToDo.SUBMITED_0 == o.getSubmited())
//	                                .collect(Collectors.toList()).size();
//
//	                        if (size > 0) {
//	                            ToDo temp = new ToDo();
//	                            temp.forInsert();
//	                            temp.setMemberId(t);
//	                            temp.setTargetId(e.getId());
//	                            temp.setType(ToDo.MARK_PAPER_TYPE);
//	                            temp.setSubmited(ToDo.SUBMITED_0);
//	                            temp.setAudited(ToDo.AUDITED_0);
//	                            temp.setIncludeType(e.getType());
//	                            return temp;
//	                        }
//
//	                    }
//                       if (ToDo.INCLUDE_TYPE_2 == e.getType()) {
//                           int size = oldToDos.stream().filter(o ->
//                                       ToDo.INCLUDE_TYPE_2 == o.getIncludeType() && o.getTargetId().equals(e.getId())
//                                       && ToDo.SUBMITED_1 == o.getSubmited())
//                                   .collect(Collectors.toList()).size();
//                           ToDo temp = new ToDo();
//                           temp.forInsert();
//                           temp.setMemberId(t);
//                           temp.setTargetId(e.getId());
//                           temp.setType(ToDo.MARK_PAPER_TYPE);
//                           temp.setSubmited(ToDo.SUBMITED_0);
//                           temp.setAudited(ToDo.AUDITED_0);
//                           temp.setIncludeType(e.getType());
//
//                           if (size > 0) {
//                               temp.setSubmited(ToDo.SUBMITED_1);
//                           }
//                           int size2 = oldToDos.stream().filter(o ->
//                                       ToDo.INCLUDE_TYPE_2 == o.getIncludeType() && o.getTargetId().equals(e.getId())
//                                       && ToDo.AUDITED_1 == o.getAudited())
//                                   .collect(Collectors.toList()).size();
//                           if (size2 > 0) {
//                               temp.setAudited(ToDo.AUDITED_1);
//                           }
//
//                           return temp;
//
//                       }
//                    return new ToDo();
//
//				}).filter(todo -> todo!=null&&todo.getId() != null).collect(Collectors.toList()));
//			});
//
//			List<ToDo> temp = new ArrayList<>();
//			temp.addAll(toDos);
//			//过滤已存在的待办
//            temp = temp.stream().filter(t -> {
//                return oldToDos.stream().filter(o ->
//                    t!=null&&t.getMemberId()!=null&&t.getTargetId()!=null&&o!=null&&o.getMemberId()!=null&&o.getTargetId()!=null&&o.getMemberId().equals(t.getMemberId()) && o.getTargetId().equals(t.getTargetId()))
//                        .collect(Collectors.toList()).size() == 0;
//            }).collect(Collectors.toList());
//
//			List<List<ToDo>> bigList = Lists.partition(temp, 500);
//			for (List<ToDo> list : bigList) {
//				toDoDao.insert(list);
//			}
//		}
//		return examRecords;
//	}

	/**
	 * 1 阅卷通知
	 * 2 如果有考试记录 发评卷通知
	 * @param examNotices
	 * @param exam
	 * @param examRecords
	 */
	private void sendMarkNotice(List<MarkConfig> needCreateToDos,
			List<ExamNotice> examNotices, Exam exam, List<ExamRecord> examRecords) {
		examNotices.forEach(examNotice -> {
			// 阅卷安排通知此处不需发送，只需发送待办的评卷通知，阅卷安排在点击'通知'的时候才发送
//			if (examNotice.getNoticeType().equals(ExamNotice.TYPE_MARKING_ARRANGE)) {
//				examNoticeService.insert(
//					examNotice.getBusinessType(),
//					examNotice.getBusinessId(),
//					examNotice.getNoticeType(),
//					exam.getOrganizationId(),
//					examNotice.getTempletCode(),
//					getMarkingArrangeParams(exam),
//					Optional.ofNullable(needCreateToDos.stream()
//							.map(MarkConfig::getMemberId)
//							.collect(Collectors.toSet()).stream().collect(Collectors.joining(",")))
//				);
//			}
			if (!examRecords.isEmpty() && examNotice.getNoticeType().equals(ExamNotice.TYPE_MARK_PAPER)) {
				examRecords.forEach(e -> {
					examNoticeService.insert(
						examNotice.getBusinessType(),
						e.getId(),
						examNotice.getNoticeType(),
						exam.getOrganizationId(),
						examNotice.getTempletCode(),
						Optional.of(new String[]{exam.getName()}),
						Optional.ofNullable(needCreateToDos.stream()
								.map(MarkConfig::getMemberId)
								.collect(Collectors.toSet()).stream().collect(Collectors.joining(",")))
					);
				});
			}
		});
	}

//	private Optional<String[]> getMarkingArrangeParams(Exam exam) {
//		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//		String startTime = df.format(new Date(exam.getStartTime()));
//		String endTime = df.format(new Date(exam.getEndTime()));
//		return Optional.of(new String[]{ exam.getName(),
//					startTime + "~" + endTime, exam.getDuration() + "分钟"});
//	}
//
	@Override
	@DataSource
    public List<MarkConfig> find(Integer examRegion, String examId, String memberId) {
    	List<MarkConfig> markConfigs = markConfigDao.fetch(MARK_CONFIG.EXAM_ID.eq(examId),
    			                                           MARK_CONFIG.MEMBER_ID.eq(memberId));
    	return !markConfigs.isEmpty()
    			? markConfigs : markConfigDao.fetch(MARK_CONFIG.EXAM_ID.eq(examId));
    }

	@Override
	@DataSource
	public List<MarkConfig> find(Integer examRegion, String examId) {
		return markConfigDao.fetch(MARK_CONFIG.EXAM_ID.eq(examId));
	}


	@Override
	@DataSource
	public boolean validateMarkPaper(Integer examRegion, String examRecordId, String memberId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

		if (invigilatorBeDeleted(examRecordId, memberId, examId)) {
			throw new UnprocessableException(ErrorCode.NoMarkPermit);
		}

		if (toDobeDeleted(examRecordId, memberId)) {
		    throw new UnprocessableException(ErrorCode.MarkPaperError);
		}

		if (hadMarked(examRecordId, memberId)) {
    		throw new UnprocessableException(ErrorCode.HadMarkedError);
    	}

		//  考生的答题记录
		List<QuestionCopy> questionCopies = examRecordDao.execute(e -> {
		    return e.selectDistinct(Fields.start().add(table.fields()).add(QUESTION_COPY).end())
		            .from(table)
		            .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)))
		            .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
		            .where(
		                    table.field("f_exam_record_id", String.class).eq(examRecordId),
		                    QUESTION_COPY.TYPE.in(
		                            Arrays.asList(Question.SENTENCE_COMPLETION, Question.QUESTION_ANWSER, Question.READING_COMPREHENSION)))
		            .fetch(r -> {
		                QuestionCopy questionCopy = r.into(QuestionCopy.class);
		                AnswerRecord answerRecord = new AnswerRecord();
	                    answerRecord.setId(r.getValue(table.field("f_id", String.class)));
	                    answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
	                    answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
	                    answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
	                    answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
	                    answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
	                    answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
		                questionCopy.setAnswerRecord(answerRecord);
		                Question question = r.into(Question.class);
		                questionCopy.setQuestion(question);
		                return questionCopy;
		            });
		});

		//组装阅读题
		 Map<String, List<QuestionCopy>> subsMap = questionCopies.stream()
				 .filter(q -> q.getParentId() != null)
				 	.collect(Collectors.groupingBy(QuestionCopy::getParentId));

		 questionCopies.forEach(q -> {
			 if (subsMap.get(q.getId()) != null) {
				 q.setSubs(subsMap.get(q.getId()));
			 }
		 });

		 TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		 List<ExamRecord> examRecordList = examRecordDao.execute(e ->
	        e.select(Fields.start()
	            .add(examRecordTable.fields())
	            .end())
	            .from(examRecordTable)
	            .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
	            ).fetch(r -> {
	                ExamRecord examRecord = new ExamRecord();
	                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
	                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
	                examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
	                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
	                examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
	                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
	                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
	                examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
	                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
	                examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
	                examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
	                examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
	                examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
	                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
	                examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
	                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
	                examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
	                examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
	                examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
	                examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
	                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
	                examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
	                examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
	                examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
	                examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
	                examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
	                examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
	                return examRecord;
	            });
	        if (examRecordList == null || examRecordList.size() == 0)
	            return false;
	        ExamRecord examRecord = examRecordList.get(0);

		 if (examRecord == null) return false;

		 //评卷规则
		 List<MarkConfig> markConfigs = find(examRegion, examRecord.getExamId(), memberId);

		 questionCopies = questionCopies.stream().filter(t -> validateMark(t, markConfigs)).collect(Collectors.toList());

		 return questionCopies.size() > 0;
	}

	@Override
	@DataSource
	public boolean validateAuditPaper(Integer examRegion, String examRecordId, String memberId, String examId) {

	    TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

	    if (invigilatorBeDeleted(examRecordId, memberId, examId)) {
	        throw new UnprocessableException(ErrorCode.NoMarkPermit);
	    }

	    if (toDobeDeleted(examRecordId, memberId)) {
	        throw new UnprocessableException(ErrorCode.MarkPaperError);
	    }

	    if (hadAudited(examRecordId, memberId)) {
	        throw new UnprocessableException(ErrorCode.HadMarkedError);
	    }

	    //  考生的答题记录
	    List<QuestionCopy> questionCopies = examRecordDao.execute(e -> {
	        return e.selectDistinct(Fields.start().add(table.fields()).add(QUESTION_COPY).end())
	                .from(table)
	                .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)))
	                .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
	                .where(
	                        table.field("f_exam_record_id", String.class).eq(examRecordId),
	                        QUESTION_COPY.TYPE.eq(Question.SENTENCE_COMPLETION))
	                .fetch(r -> {
	                    QuestionCopy questionCopy = r.into(QuestionCopy.class);
	                    AnswerRecord answerRecord = new AnswerRecord();
	                    answerRecord.setId(r.getValue(table.field("f_id", String.class)));
	                    answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
	                    answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
	                    answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
	                    answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
	                    answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
	                    answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
	                    questionCopy.setAnswerRecord(answerRecord);
	                    Question question = r.into(Question.class);
	                    questionCopy.setQuestion(question);
	                    return questionCopy;
	                });
	    });

	    TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

	    List<ExamRecord> examRecordList = examRecordDao.execute(e ->
	    e.select(Fields.start()
	            .add(examRecordTable.fields())
	            .end())
	    .from(examRecordTable)
	    .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
	            ).fetch(r -> {
	                ExamRecord examRecord = new ExamRecord();
	                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
	                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
	                examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
	                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
	                examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
	                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
	                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
	                examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
	                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
	                examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
	                examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
	                examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
	                examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
	                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
	                examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
	                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
	                examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
	                examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
	                examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
	                examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
	                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
	                examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
	                examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
	                examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
	                examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
	                examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
	                examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
	                return examRecord;
	            });
	    if (examRecordList == null || examRecordList.size() == 0)
	        return false;
	    ExamRecord examRecord = examRecordList.get(0);

	    if (examRecord == null) return false;

	    //评卷规则
	    List<MarkConfig> markConfigs = find(examRegion, examRecord.getExamId(), memberId);

	    questionCopies = questionCopies.stream().filter(t -> validateAudit(t, markConfigs)).collect(Collectors.toList());

	    return questionCopies.size() > 0;
	}

	private boolean toDobeDeleted(String examRecordId, String memberId) {
	    Integer count = toDoDao.count(TO_DO.TARGET_ID.eq(examRecordId), TO_DO.MEMBER_ID.eq(memberId));
        return count == 0;
    }

    private boolean invigilatorBeDeleted(String examRecordId, String memberId, String examId) {
        Integer count = markConfigDao.count(MARK_CONFIG.EXAM_ID.eq(examId), MARK_CONFIG.MEMBER_ID.eq(memberId));
        return count == 0;
	}

	private boolean hadMarked(String examRecordId, String memberId) {
		return toDoDao.fetchOne(TO_DO.MEMBER_ID.eq(memberId), TO_DO.TARGET_ID.eq(examRecordId)).map(t -> {
			return t.getSubmitTime() != null || t.getSubmited() == ToDo.SUBMITED_1;
		}).orElseGet(() -> {
			return false;
		});
	}

	private boolean hadAudited(String examRecordId, String memberId) {
	    return toDoDao.fetchOne(TO_DO.MEMBER_ID.eq(memberId), TO_DO.TARGET_ID.eq(examRecordId)).map(t -> {
	        return t.getAuditTime() != null || t.getAudited() == ToDo.AUDITED_1;
	    }).orElseGet(() -> {
	        return false;
	    });
	}


	/**
	 * 分析每道题是否符合评卷规则的题目，
	 * 看是否已评
	 * 有分数就是已评
	 * 答案不能为空
	 * @param questionCopy
	 * @param markConfigs
	 * @return
	 */
	private boolean validateMark(QuestionCopy questionCopy, List<MarkConfig> markConfigs) {
		boolean flag = false;
		if (markConfigs != null && markConfigs.size() > 0) {
			Integer type = markConfigs.get(0).getType();

			if (type == MarkConfig.PAPER) {
				flag =  questionCopy.getAnswerRecord() != null
						&& questionCopy.getAnswerRecord().getScore() == null
						&& questionCopy.getAnswerRecord().getAnswer() != null
						&& !questionCopy.getAnswerRecord().getAnswer().equals("");
			}

			if (type == MarkConfig.QUESTION) {
				flag =  validateMarkQuestion(questionCopy, markConfigs);
			}

			if (type == MarkConfig.QUESTION_TYPE) {
				List<MarkConfig> ms = markConfigs.stream().filter(t -> {
					if (questionCopy.getParentId() != null) {
						return t.getTypeId().equals(String.valueOf(Question.READING_COMPREHENSION));
					}
					return t.getTypeId().equals(String.valueOf(questionCopy.getType()));
				}).collect(Collectors.toList());
				flag = ms.size() > 0
						&& questionCopy.getAnswerRecord() != null
						&& questionCopy.getAnswerRecord().getScore() == null
						&& questionCopy.getAnswerRecord().getAnswer() != null
						&& !questionCopy.getAnswerRecord().getAnswer().equals("");
			}
		}
		return flag;
	}
	/**
	 * 分析每道题是否符合审核规则的题目，
	 * 答案不能为空
	 * @param questionCopy
	 * @param markConfigs
	 * @return
	 */
	private boolean validateAudit(QuestionCopy questionCopy, List<MarkConfig> markConfigs) {
	    boolean flag = false;
	    if (markConfigs != null && markConfigs.size() > 0) {
	        Integer type = markConfigs.get(0).getType();

	        if (type == MarkConfig.PAPER) {
	            flag =  questionCopy.getAnswerRecord() != null
	                    && questionCopy.getAnswerRecord().getAnswer() != null
	                    && !questionCopy.getAnswerRecord().getAnswer().equals("");
	        }

	        if (type == MarkConfig.QUESTION) {
	            flag =  validateAuditQuestion(questionCopy, markConfigs);
	        }

	        if (type == MarkConfig.QUESTION_TYPE) {
	            List<MarkConfig> ms = markConfigs.stream().filter(t -> {
	                return t.getTypeId().equals(String.valueOf(questionCopy.getType()));
	            }).collect(Collectors.toList());
	            flag = ms.size() > 0
	                    && questionCopy.getAnswerRecord() != null
	                    && questionCopy.getAnswerRecord().getAnswer() != null
	                    && !questionCopy.getAnswerRecord().getAnswer().equals("");
	        }
	    }
	    return flag;
	}

	private boolean validateMarkQuestion(QuestionCopy questionCopy, List<MarkConfig> markConfigs) {
		List<MarkConfig> questionCopyMarkConfigs = markConfigs.stream().filter(t -> {
			if (questionCopy.getParentId() != null) {
				return questionCopy.getQuestion().getParentId() != null
						&& t.getTypeId().equals(questionCopy.getQuestion().getParentId());
			}
			return t.getTypeId().equals(questionCopy.getQuestionId());
		}).collect(Collectors.toList());

		if (questionCopyMarkConfigs.size() > 0) {
			if (questionCopy.getType() == Question.READING_COMPREHENSION) {
				return questionCopy.getSubs().stream().filter(s -> {
					return s.getType() == Question.QUESTION_ANWSER
							&& s.getAnswerRecord() != null
							&& s.getAnswerRecord().getScore() == null
							&& s.getAnswerRecord().getAnswer() != null
							&& !s.getAnswerRecord().getAnswer().equals("");
				}).collect(Collectors.toList()).size() > 0;
			}

			return questionCopy.getAnswerRecord() != null
					&& questionCopy.getAnswerRecord().getScore() == null
					&& questionCopy.getAnswerRecord().getAnswer() != null
					&& !questionCopy.getAnswerRecord().getAnswer().equals("");
		}

		return false;
	}
	private boolean validateAuditQuestion(QuestionCopy questionCopy, List<MarkConfig> markConfigs) {
	    List<MarkConfig> questionCopyMarkConfigs = markConfigs.stream().filter(t -> {
	        return t.getTypeId().equals(questionCopy.getQuestionId());
	    }).collect(Collectors.toList());

	    if (questionCopyMarkConfigs.size() > 0) {
	        return questionCopy.getAnswerRecord() != null
	                && questionCopy.getAnswerRecord().getAnswer() != null
	                && !questionCopy.getAnswerRecord().getAnswer().equals("");
	    }

	    return false;
	}

	 public TableImpl<?> getAnswerRecordTable(String answerRecordStringTable) {
	        if (AnswerRecord.STRING_ANSWER_RECORD_2019.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2019;
	        if (AnswerRecord.STRING_ANSWER_RECORD_2020.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2020;
	        if (AnswerRecord.STRING_ANSWER_RECORD_2021.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2021;
	        if (AnswerRecord.STRING_ANSWER_RECORD_2022.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2022;
	        if (AnswerRecord.STRING_ANSWER_RECORD_2023.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2023;
	        if (AnswerRecord.STRING_ANSWER_RECORD_2024.equals(answerRecordStringTable))
	            return ANSWER_RECORD_2024;
	        return ANSWER_RECORD;
	 }

	    public TableImpl<?> getExamRecordTable(String examRecordStringTable) {
	        if (ExamRecord.STRING_EXAM_RECORD.equals(examRecordStringTable))
	            return EXAM_RECORD;
	        if (ExamRecord.STRING_EXAM_RECORD_2017.equals(examRecordStringTable))
	            return EXAM_RECORD_2017;
	        if (ExamRecord.STRING_EXAM_RECORD_2018.equals(examRecordStringTable))
	            return EXAM_RECORD_2018;
	        if (ExamRecord.STRING_EXAM_RECORD_2019.equals(examRecordStringTable))
	            return EXAM_RECORD_2019;
	        if (ExamRecord.STRING_EXAM_RECORD_2020.equals(examRecordStringTable))
	            return EXAM_RECORD_2020;
	        if (ExamRecord.STRING_EXAM_RECORD_2021.equals(examRecordStringTable))
	            return EXAM_RECORD_2021;
	        if (ExamRecord.STRING_EXAM_RECORD_2022.equals(examRecordStringTable))
	            return EXAM_RECORD_2022;
	        if (ExamRecord.STRING_EXAM_RECORD_2023.equals(examRecordStringTable))
	            return EXAM_RECORD_2023;
	        if (ExamRecord.STRING_EXAM_RECORD_2024.equals(examRecordStringTable))
	            return EXAM_RECORD_2024;
	        return EXAM_RECORD;
	    }
}
