package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.GridCourseService;
import com.zxy.product.exam.entity.GridCourse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.exam.jooq.Tables.GRID_COURSE;

@Service
public class GridCourseServiceSupport implements GridCourseService {

    private CommonDao<GridCourse> gridCourseDao;

    @Autowired
    public void setGridCourseDao(CommonDao<GridCourse> gridCourseDao) {
        this.gridCourseDao = gridCourseDao;
    }

    @Override
    @DataSource
    public List<GridCourse> findGridCourseByExamId(Integer examRegion, String examId) {
        return gridCourseDao.fetch(GRID_COURSE.EXAM_ID.eq(examId));
    }
}
