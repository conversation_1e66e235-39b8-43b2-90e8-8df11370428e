package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class AnswerRecordServiceSupport implements AnswerRecordService {

    private CommonDao<AnswerRecord> answerRecordDao;
    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
        this.answerRecordDao = answerRecordDao;
    }


    /**
     * 更新答题分数
     * 填空题自动判分 2020-08
     */
    @Override
    @DataSource
    public AnswerRecord updateScore(Integer examRegion, AnswerRecord answerRecord) {
        QuestionCopy q = answerRecord.getQuestionCopy();
        boolean isRight = false;
        switch (q.getType()) {
            case 1:
                isRight = dealSingleChoose(answerRecord);
                break;
            case 2:
                isRight = dealMutipleChoose(answerRecord);
                break;
            case 3:
                isRight = dealJdugement(answerRecord);
                break;
            case 4:
                isRight = dealSentenceCompletion(answerRecord);
                break;
            case 8:
                isRight = dealSorting(answerRecord);
                break;
            default:
                break;
        }
        if (isRight) {
            answerRecord.setIsRight(AnswerRecord.RIGHT);
            answerRecord.setScore(q.getScore());
        } else {
            answerRecord.setIsRight(AnswerRecord.WRONG);
            answerRecord.setScore(AnswerRecord.ZERO_SCORE);
        }
        return answerRecord;
    }

    /**
     * 查询考生的答卷记录
     */
    @Override
    @DataSource
    public List<AnswerRecord> findIncludeQuestionByExamRecordId(Integer examRegion, String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        Result<Record> records = answerRecordDao.execute(e ->
                                 e.select(
                                          Fields.start()
                                                .add(table.fields())
                                                .add(QUESTION_COPY.ID.as("questionCopyId"))
                                                .add(QUESTION_COPY.TYPE)
                                                .add(QUESTION_COPY.SCORE)
                                                .add(QUESTION_COPY.PARENT_ID)
                                                .add(QUESTION.ID.as("questionId"))
                                                .add(QUESTION_ATTR_COPY.ID.as("attrId"))
                                                .add(QUESTION_ATTR_COPY.NAME)
                                                .add(QUESTION_ATTR_COPY.VALUE)
                                                .add(QUESTION_ATTR_COPY.TYPE)
                                                .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                                                .end())
                                  .from(table)
                                  .leftJoin(QUESTION_COPY).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
                                  .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_COPY.ID.eq(QUESTION_ATTR_COPY.QUESTION_COPY_ID))
                                  .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
                                  .where(table.field("f_exam_record_id", String.class).eq(examRecordId))
                                  .fetch()
        );

        Map<String, QuestionAttrCopy> questionAttrCopyMap = records.stream().map(t -> {
            QuestionAttrCopy attr = new QuestionAttrCopy();
            attr.setId(t.getValue(QUESTION_ATTR_COPY.ID.as("attrId")));
            attr.setName(t.getValue(QUESTION_ATTR_COPY.NAME));
            attr.setValue(t.getValue(QUESTION_ATTR_COPY.VALUE));
            attr.setType(t.getValue(QUESTION_ATTR_COPY.TYPE));
            attr.setQuestionCopyId(t.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
            return attr;
        }).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

        Map<String, QuestionCopy> questionCopyMap = records
                .stream().map(t -> {
                    QuestionCopy questionCopy = new QuestionCopy();
                    questionCopy.setId(t.getValue(QUESTION_COPY.ID.as("questionCopyId")));
                    questionCopy.setType(t.getValue(QUESTION_COPY.TYPE));
                    questionCopy.setScore(t.getValue(QUESTION_COPY.SCORE));
                    questionCopy.setParentId(t.getValue(QUESTION_COPY.PARENT_ID));
                    questionCopy.setQuestionAttrCopys(questionAttrCopyListMap.get(questionCopy.getId()));
                    Question question = new Question();
                    question.setId(t.getValue(QUESTION.ID.as("questionId")));
                    questionCopy.setQuestion(question);
                    return questionCopy;
                }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

        List<AnswerRecord> answerRecordList = records.stream().map(r -> {
            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            return answerRecord;
        }).collect(Collectors.toList());

        List<AnswerRecord> answerRecords = answerRecordList
                .stream().collect(Collectors.toMap(AnswerRecord::getId, e -> e, (k, v) -> v)).values()
                .stream().map(t -> {
                    t.setQuestionCopy(questionCopyMap.get(t.getQuestionId()));
                    return t;
                }).collect(Collectors.toList());
        return answerRecords;
    }

    @Override
    @DataSource
    public void batchUpdate(Integer examRegion,String examId, List<AnswerRecord> answerRecords) {
        String table = getTableUtil.getAnswerRecordStringTable(examId);

        TableImpl<?> answerRecordTable = AnswerRecord.getAnswerRecordTable(table);
        answerRecordDao.execute(e -> {
            return e.batch(
                    answerRecords.stream().map(answerRecord -> {
                        return e.update(answerRecordTable)
                                .set(answerRecordTable.field("f_create_time", Long.class), answerRecord.getCreateTime())
                                .set(answerRecordTable.field("f_exam_record_id", String.class), answerRecord.getExamRecordId())
                                .set(answerRecordTable.field("f_question_id", String.class), answerRecord.getQuestionId())
                                .set(answerRecordTable.field("f_answer", String.class), answerRecord.getAnswer())
                                .set(answerRecordTable.field("f_is_right", Integer.class), answerRecord.getIsRight())
                                .set(answerRecordTable.field("f_score", Integer.class), answerRecord.getScore())
                                .where(answerRecordTable.field("f_id", String.class).eq(answerRecord.getId()));
                    }).collect(Collectors.toList())
            ).execute();
        });
    }

    private Map<String, List<QuestionAttrCopy>> reduceQuestionAttrCopyList(Collection<QuestionAttrCopy> questionAttrCopies) {

        Map<String, List<QuestionAttrCopy>> map = new HashMap<>();
        questionAttrCopies.stream().forEach(questionAttr -> {
            map.compute(questionAttr.getQuestionCopyId(), (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                v.add(questionAttr);
                return v;
            });
        });
        return map;
    }

    private boolean dealSentenceCompletion(AnswerRecord answerRecord) {

        QuestionCopy q = answerRecord.getQuestionCopy();
        List<QuestionAttrCopy> questionAttrCopys = q.getQuestionAttrCopys();

        String correctAnswer = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue();
        String[] correctAnswerArr = correctAnswer.split("\\|");

        String answer = answerRecord.getAnswer();
        String[] answerArr = answer.split("@answer@");

        if(correctAnswerArr.length != answerArr.length)
            return false;

        for (int i = 0; i < correctAnswerArr.length; i++) {
            String[] tureAnswer = correctAnswerArr[i].trim().split("#");
            List<String> list = Arrays.asList(tureAnswer).stream().map(String::trim).collect(Collectors.toList());
            if (list != null && !list.contains(answerArr[i].trim())) {
                return false;
            }

        }
        return true;
    }

    /**
     * 排序
     * @param answerRecord
     * @return
     */
    private boolean dealSorting(AnswerRecord answerRecord) {
        QuestionCopy q = answerRecord.getQuestionCopy();
        List<QuestionAttrCopy> questionAttrCopys = q.getQuestionAttrCopys();
        String correctAnswer = questionAttrCopys.stream()
                .filter(t -> t.getType().equals(String.valueOf(QuestionAttr.ANSWER_TYPE)))
                .collect(Collectors.toList()).get(0).getValue();

        String answer = answerRecord.getAnswer();
        return correctAnswer.equals(answer);
    }

    /**
     * 判断
     * @param answerRecord
     * @return
     */
    private boolean dealJdugement(AnswerRecord answerRecord) {
        QuestionCopy q = answerRecord.getQuestionCopy();
        List<QuestionAttrCopy> questionAttrCopys = q.getQuestionAttrCopys();
        String correctAnswer = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue();
        String answer = answerRecord.getAnswer();
        return correctAnswer.equals(answer);
    }

    /**
     * 多选
     * @param answerRecord
     * @return
     */
    private boolean dealMutipleChoose(AnswerRecord answerRecord) {
        QuestionCopy q = answerRecord.getQuestionCopy();
        List<QuestionAttrCopy> questionAttrCopys = q.getQuestionAttrCopys();

        if (answerRecord.getAnswer() == null) {
            return false;
        }

        String[] answers = answerRecord.getAnswer().split(",");
        Map<String, Object> map = new HashMap<>();
        for(String str : answers){
            map.put(str, str);
        }
        List<QuestionAttrCopy> correctAnswers = questionAttrCopys.stream()
                .filter(t -> String.valueOf(QuestionAttr.ANSWER_TYPE).equals(t.getType()))
                .collect(Collectors.toList());

        if (map.keySet().size() == correctAnswers.size()) {
            for (int i = 0; i < correctAnswers.size(); i++) {
                QuestionAttrCopy attr = correctAnswers.get(i);
                if (answerRecord.getAnswer().indexOf(attr.getName()) < 0) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 单选
     * @param answerRecord
     * @return
     */
    private boolean dealSingleChoose(AnswerRecord answerRecord) {
        QuestionCopy q = answerRecord.getQuestionCopy();
        List<QuestionAttrCopy> questionAttrCopys = q.getQuestionAttrCopys();
        for (int i = 0; i < questionAttrCopys.size(); i++) {
            QuestionAttrCopy attr = questionAttrCopys.get(i);
            if((String.valueOf(QuestionAttr.ANSWER_TYPE).equals(attr.getType())) && (!attr.getName().equals(answerRecord.getAnswer()))){
                return false;
            }
        }
        return true;
    }


    @Override
    @DataSource
    public List<AnswerRecord> findByExamRecordId(Integer examRegion, String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        return answerRecordDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(table.fields())
                                    .add(QUESTION_COPY.TYPE)
                                    .add(QUESTION_COPY.PARENT_ID)
                                    .end()
                    )
                    .from(table)
                    .leftJoin(QUESTION_COPY).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
                    .where(table.field("f_exam_record_id", String.class).eq(examRecordId)).fetch(r -> {
                        AnswerRecord answerRecord = new AnswerRecord();
                        answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                        answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                        answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                        answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                        answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                        answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                        answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                        answerRecord.setQuestionCopyParentId(r.getValue(QUESTION_COPY.PARENT_ID));
                        QuestionCopy questionCopy = new QuestionCopy();
                        questionCopy.setType(r.getValue(QUESTION_COPY.TYPE));
                        answerRecord.setQuestionCopy(questionCopy);
                        return answerRecord;
                    });
        });
    }

}
