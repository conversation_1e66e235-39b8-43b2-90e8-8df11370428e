package com.zxy.product.examstu.service.config;

import com.zxy.common.encrypt.SM4.SM4Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;


/**
 * 配置文件读取
 */
@Configuration
public class PropertiesConfig implements EnvironmentAware, InitializingBean {

	private Logger LOGGER = LoggerFactory.getLogger(PropertiesConfig.class);
	private static String secretkeySM4;

	@Override
	public void setEnvironment(Environment env) {
		secretkeySM4 = env.getProperty("app.secretKey.sm4", String.class, "");
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		SM4Utils.SECRET_KEY = secretkeySM4;

		LOGGER.info("[secretKeySm4=" + secretkeySM4 + "]");
	}

}

