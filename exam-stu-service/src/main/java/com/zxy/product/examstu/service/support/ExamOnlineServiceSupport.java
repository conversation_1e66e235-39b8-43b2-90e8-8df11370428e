package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.ExamOnlineLog;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamOnlineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.zxy.product.exam.jooq.Tables.EXAM_ONLINE_LOG;

/**
 * 限流限频：考试相关Service实现类
 * <AUTHOR>
 * @date 2025年2月13日 12:15
 */
@Service
public class ExamOnlineServiceSupport implements ExamOnlineService {
    private static final Logger logger = LoggerFactory.getLogger( ExamOnlineServiceSupport.class );
    private CommonDao<ExamOnlineLog> examOnlineLogDao;

    @Autowired
    public void setExamOnlineLogDao(CommonDao<ExamOnlineLog> examOnlineLogDao){this.examOnlineLogDao=examOnlineLogDao;}

    /**
     * 查询考试限流流水OPTIONAL对象
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     * @return 根据条件查询的考试限流流水OPTIONAL对象
     */
    @Override
    @DataSource
    public Optional<ExamOnlineLog> doSingleOnlineLogOpt(Integer examRegion, String examId, String memberId) {
        return examOnlineLogDao.execute(ew1 ->
                ew1.select(EXAM_ONLINE_LOG.EXPIRE_TIME, EXAM_ONLINE_LOG.ID, EXAM_ONLINE_LOG.BUSINESS_ID, EXAM_ONLINE_LOG.MEMBER_ID)
                        .from(EXAM_ONLINE_LOG)
                        .where(EXAM_ONLINE_LOG.MEMBER_ID.eq(memberId))
                        .fetchOptional(ew2 -> {
                            ExamOnlineLog examOnlineLog = new ExamOnlineLog();
                            examOnlineLog.setId(ew2.getValue(EXAM_ONLINE_LOG.ID));
                            examOnlineLog.setBusinessId(ew2.getValue(EXAM_ONLINE_LOG.BUSINESS_ID));
                            examOnlineLog.setMemberId(ew2.getValue(EXAM_ONLINE_LOG.MEMBER_ID));
                            examOnlineLog.setExpireTime(ew2.getValue(EXAM_ONLINE_LOG.EXPIRE_TIME));
                            return examOnlineLog;
                        }));
    }

    /**
     * 添加考试限流前置流水
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     * @param expireTime 过期时间
     * @return 添加的对象
     */
    @Override
    @DataSource
    public ExamOnlineLog insertOnlineLog(Integer examRegion, String examId, String memberId, Long expireTime) {
        ExamOnlineLog examOnlineLog = new ExamOnlineLog();
        examOnlineLog.setBusinessId(examId);
        examOnlineLog.setMemberId(memberId);
        examOnlineLog.setExpireTime(expireTime);
        examOnlineLog.setCreateTime(System.currentTimeMillis());
        return examOnlineLogDao.insert(examOnlineLog);
    }

    /**
     * 释放考试流水
     * @param examRegion 考试所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     */
    @Override
    @DataSource
    public void releaseOnlineLog(Integer examRegion, String examId, String memberId) {
        Optional<ExamOnlineLog> examOnlineLogOpt = this.doSingleOnlineLogOpt(examRegion, examId, memberId);
        examOnlineLogOpt.ifPresent(ew1->{
            ew1.setExpireTime(System.currentTimeMillis());
            examOnlineLogDao.update(ew1);
        });
    }

    /**
     * 查询考试未过期流水数据
     * @param examRegion 查询考试
     * @return 查询考试未过期流水数据
     */
    @Override
    @DataSource
    public Integer doSelectCount(Integer examRegion) {
        return examOnlineLogDao.execute(ew1 ->
                ew1.selectCount()
                        .from(EXAM_ONLINE_LOG)
                        .where(EXAM_ONLINE_LOG.EXPIRE_TIME.ge(System.currentTimeMillis()))
                        .fetchOne()
                        .value1());
    }

    /**
     * 清理考试无效数据
     * @param examRegion 考试所属数据库
     */
    @Override
    @DataSource
    public void cleanExamOnline( Integer examRegion ) {
        List<Long> keyCollect = examOnlineLogDao.execute(ew1 ->
                ew1.select( EXAM_ONLINE_LOG.ID )
                        .from( EXAM_ONLINE_LOG )
                        .where( EXAM_ONLINE_LOG.EXPIRE_TIME.le( System.currentTimeMillis() ))
                        .fetch( EXAM_ONLINE_LOG.ID ));
        Optional.of( keyCollect )
                .filter( CollectionUtils::isNotEmpty )
                .ifPresent( ew1->{
                    List<List<Long>> partCollect = Lists.partition( ew1, 100 );
                    partCollect.forEach(ew2->examOnlineLogDao.delete(EXAM_ONLINE_LOG.ID.in( ew2 )));
                });
        logger.info( "当前考试数据库{},总过期数据条数{}", examRegion, keyCollect.size() );
    }
}
