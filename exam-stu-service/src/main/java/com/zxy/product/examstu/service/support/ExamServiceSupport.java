package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.examstu.api.ExamRecordService;
import com.zxy.product.examstu.api.ExamService;
import com.zxy.product.examstu.api.StrongBaseService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;


import java.text.SimpleDateFormat;
import java.util.*;

import java.util.Comparator;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;
import static com.zxy.product.examstu.util.DatasetProcessing.*;
import static org.jooq.impl.DSL.trueCondition;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zxy.product.examstu.annotation.DataSource;



@Service
public class ExamServiceSupport implements ExamService {
    private static final Logger logger= LoggerFactory.getLogger(ExamServiceSupport.class);

    private DSLContext context;

    private CommonDao<Exam> examDao;

    private CommonDao<ExamRecord> examRecordDao;

    private CommonDao<SignUp> signUpDao;

    private CommonDao<Organization> organizationDao;

    private CommonDao<Member> memberDao;

    private CommonDao<Profession> professionDao;

    private CommonDao<EquipmentType> equipmentTypeDao;

    private CommonDao<ProfessionLevel> professionLevelDao;

    private CommonDao<SignUpAuth> signUpAuthDao;

    private CommonDao<ExamRegist> examRegistDao;

    private StrongBaseService strongBaseService;

    private ExamRecordService examRecordService;

    private static final Integer START_PAGE = 1; // 起始页

    private static final Integer REPETITION_FACTOR = 2; // 重复因子

    private GetTableUtil getTableUtil;

    @Autowired
    public void setContext(DSLContext context){this.context=context; }

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setExamRegistDao(CommonDao<ExamRegist> examRegistDao) {
        this.examRegistDao = examRegistDao;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }

    @Autowired
    public void setProfessionLevelDao(CommonDao<ProfessionLevel> professionLevelDao) {
        this.professionLevelDao = professionLevelDao;
    }

    @Autowired
    public void setEquipmentTypeDao(CommonDao<EquipmentType> equipmentTypeDao) {
        this.equipmentTypeDao = equipmentTypeDao;
    }

    @Autowired
    public void setProfessionDao(CommonDao<Profession> professionDao) {
        this.professionDao = professionDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }



    @Autowired
    public void setSignUpAuthDao(CommonDao<SignUpAuth> signUpAuthDao) {
        this.signUpAuthDao = signUpAuthDao;
    }

    @Autowired
    public void setSignUpDao(CommonDao<SignUp> signUpDao) {
        this.signUpDao = signUpDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService) {
        this.examRecordService = examRecordService;
    }


    @Autowired
    public void setStrongBaseService(StrongBaseService strongBaseService) {
        this.strongBaseService = strongBaseService;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }


    //我的认证考试
   @Override
   @DataSource
    public Map<String, Object> findNewPersonCenterListByRegist(
       Integer examRegion,
       int page,
       int pageSize,
       Optional<String> name,
       Optional<Integer> type,
       String memberId,
       Optional<Integer> searchStatus,
       Optional<Integer> startTimeOrderBy,
       List<String> userIds,
       Optional<String> organizationId,
       Optional<String> year,
       boolean isAuthExam,
       boolean pageSwitch, Optional<String> all) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(year.map(value -> (ExamRegist.STRING_EXAM_REGIST + "_" + value)).orElse(ExamRegist.STRING_EXAM_REGIST));

         TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(year.map(value -> (ExamRecord.STRING_EXAM_RECORD + "_" + value)).orElse(ExamRecord.STRING_EXAM_RECORD));

         Stream<Optional<Condition>> conditions = Stream.of(
                 name.map(EXAM.NAME::contains),//考试名
                 type.map(EXAM.TYPE::eq),//考试类型
                 Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                 Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(e -> {
                     if (all.isPresent()) {
                         return DSL.trueCondition();
                     }
                     return EXAM.SOURCE_TYPE.eq(e);
                 }),//考试活动类型
                 Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                 getSearchStatus(searchStatus, examRecordTable)//查询状态
                 );

         Stream<Optional<Condition>> conditionsForExamCancel = Stream.of(
                 name.map(EXAM.NAME::contains),//考试名
                 type.map(EXAM.TYPE::eq),//考试类型
                 Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                 Optional.of(ExamRegist.STATUS_FINISHED).map(examRegistTable.field("f_status", Integer.class)::eq),//查询状态
                 Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(e -> {
                     if (all.isPresent()) {
                         return DSL.trueCondition();
                     }
                     return EXAM.SOURCE_TYPE.eq(e);
                 }),//考试活动类型
                 Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::eq)//非撤销
                 );

         Stream<Optional<Condition>> conditionsForExamEnd = Stream.of(
                 name.map(EXAM.NAME::contains),//考试名
                 type.map(EXAM.TYPE::eq),//考试类型
                 Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                 Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(e -> {
                     if (all.isPresent()) {
                         return DSL.trueCondition();
                     }
                     return EXAM.SOURCE_TYPE.eq(e);
                 }),//考试活动类型
                 Optional.of(System.currentTimeMillis()).map(EXAM.END_TIME::lt)//结束
                 );

         Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                 .reduce(Condition::and).orElse(DSL.trueCondition())
                 .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                 .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                 .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                 .and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).gt(System.currentTimeMillis()).or(examRecordTable.field("f_status", Integer.class).ne(ExamRecord.STATUS_TO_BE_STARTED)));

         Condition conditionForExamCancel = conditionsForExamCancel.filter(Optional::isPresent).map(Optional::get)
                 .reduce(Condition::and).orElse(DSL.trueCondition())
                 .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                 .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                 .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

	   Condition conditionForExamEnd = conditionsForExamEnd.filter(Optional::isPresent).map(Optional::get)
			   .reduce(Condition::and).orElse(DSL.trueCondition())
			   .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED)
					   .or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED)
							   .and(
									   SIGNUP.STATUS.eq(SignUp.STATUS_PASSED)
											   .or(CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
											   .or(GRID_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
							   )
					   )
			   )
			   .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
			   .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
			   .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

         Condition mergeCondition;

         if (!isAuthExam && type.isPresent()) {
             mergeCondition = searchStatus.map(s -> {
                 if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                     return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                 }
                 return condition;
             }).orElseGet(() -> {
                 return condition.or(conditionForExamCancel).or(conditionForExamEnd);
             });
         }else if (!isAuthExam) {
             mergeCondition = searchStatus.map(s -> {
                 if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                     return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                 }
                 return condition;
             }).orElseGet(() -> {
                 return condition.or(conditionForExamCancel).or(conditionForExamEnd);
             }).and(
                     EXAM.TYPE.eq(Exam.EXAM_OFFICIAL_TYPE)
                     .or(EXAM.TYPE.eq(Exam.EXAM_UN_OFFICIAL_TYPE)));
         }else {
             mergeCondition = searchStatus.map(s -> {
                 if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                     return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                 }
                 return condition;
             }).orElseGet(() -> {
                 return condition.or(conditionForExamCancel).or(conditionForExamEnd);
             }).and(organizationId.map(o -> {
             	 if (Exam.GRID.equals(o)) {
					 return EXAM.TYPE.eq(Exam.EXAM_GRID_TYPE);
				 }
                 // 点击网络部，市场部tab，只查询考试类型为网络部，市场部的集团认证考试
                 return EXAM.ORGANIZATION_ID.eq(o).and(EXAM.TYPE.in(Exam.EXAM_AUTH_CLOUD_TYPE));
             }).orElse(EXAM.TYPE.in(Exam.EXAM_AUTH_GRID_TYPE)));
         }


	   SelectConditionStep<Record> examStep = examDao.execute(e ->
			   e.select(Fields.start().add(
					   examRegistTable.field("f_top_score", Integer.class),
					   examRegistTable.field("f_top_score_record_id", String.class),
					   examRegistTable.field("f_exam_times", Integer.class),
					   examRegistTable.field("f_member_id", String.class),
					   examRegistTable.field("f_pass_status", Integer.class),
					   EXAM.ID,
					   EXAM.EXAM_BATCH,
					   EXAM.NAME,
					   EXAM.TYPE,
					   EXAM.STATUS,
					   EXAM.START_TIME,
					   EXAM.END_TIME,
					   EXAM.PASS_SCORE,
					   EXAM.ALLOW_EXAM_TIMES,
					   EXAM.IS_SHOW_ANSWER_IMMED,
					   EXAM.NEED_APPLICANT,
					   EXAM.APPLICANT_NEED_AUDIT,
					   EXAM.APPLICANT_START_TIME,
					   EXAM.APPLICANT_END_TIME,
					   EXAM.HAS_CERT,
					   EXAM.CERTIFICATE_ID,
					   EXAM.SHOW_ANSWER_RULE,
					   EXAM.IS_SET_PASSWORD,
					   EXAM.IS_SET_PERSONAL_CODE,
					   EXAM.ADMISSION_TICKET,
					   EXAM.SHOW_SCORE_TIME,
					   EXAM.DURATION,
					   examRecordTable.field("f_id", String.class),
					   examRecordTable.field("f_member_id", String.class),
					   examRecordTable.field("f_status", Integer.class),
					   examRecordTable.field("f_score", Integer.class),
					   examRecordTable.field("f_submit_time", Long.class),
					   examRecordTable.field("f_is_reset", Integer.class),
					   examRecordTable.field("f_start_time", Long.class),
					   examRecordTable.field("f_exam_times", Integer.class),
					   examRecordTable.field("f_face_status", Integer.class),
                       examRecordTable.field("f_start_time", Long.class),
                       examRecordTable.field("f_last_submit_time", Long.class),
                       SIGNUP.ID.as("signUpId"),
					   SIGNUP.STATUS,
					   CLOUD_SIGNUP.ID.as("cloudsignUpId"),
					   CLOUD_SIGNUP.STATUS,
					   GRID_SIGNUP.ID.as("gridsignUpId"),
					   GRID_SIGNUP.STATUS,
					   PAPER_CLASS.TOTAL_SCORE,
					   CERTIFICATE_RECORD.ID,
					   EXAM.FACE_ENTER,
					   EXAM.FACE_MONITOR,
					   EXAM.STRONG_BASE_FLAG
			   )
					   .end())
					   .from(examRegistTable)
					   .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
					   .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class)))
					   .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))).and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
					   .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
					   .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CLOUD_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
					   .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(GRID_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
					   .leftJoin(CERTIFICATE_RECORD).on(CERTIFICATE_RECORD.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CERTIFICATE_RECORD.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
					   .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
					   .where(mergeCondition)
					   .and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE)
							   .or(examRegistTable.field("f_top_score_record_id", String.class).isNotNull())
							   .or(examRecordTable.field("f_status", Integer.class)
									   .in(ExamRecord.STATUS_TO_BE_OVER, ExamRecord.STATUS_PASS, ExamRecord.STATUS_NOT_PASS, ExamRecord.STATUS_FINISHED, ExamRecord.STATUS_NULLIFY))
					   ));


         final SelectOrderByStep<Record> stepFinal = examStep;
         long count = 0L;
         if(pageSwitch){
			 count =  stepFinal.fetch().stream().count();
		 }
         int firstResult = (page - 1) * pageSize;

         //排序字段
         SortField<?> sf = startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1 ? EXAM.START_TIME.asc() : EXAM.START_TIME.desc();

         Result<Record> result =stepFinal.orderBy(sf).limit(firstResult, pageSize + 1).fetch();

         List<Exam> exams = result.map(r -> {
             Exam exam = new Exam();
             exam.setId(r.getValue(EXAM.ID));
             exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
             exam.setName(r.getValue(EXAM.NAME));
             exam.setType(r.getValue(EXAM.TYPE));
             exam.setStatus(r.getValue(EXAM.STATUS));
             exam.setStartTime(r.getValue(EXAM.START_TIME));
             exam.setEndTime(r.getValue(EXAM.END_TIME));
             exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
             exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
             exam.setIsShowAnswerImmed(r.getValue(EXAM.IS_SHOW_ANSWER_IMMED));
             exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
             exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
             exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
             exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
             exam.setHasCert(r.getValue(EXAM.HAS_CERT));
             exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
             exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
             exam.setIsSetPassword(r.getValue(EXAM.IS_SET_PASSWORD));
             exam.setIsSetPersonalCode(r.getValue(EXAM.IS_SET_PERSONAL_CODE));
             exam.setAdmissionTicket(r.getValue(EXAM.ADMISSION_TICKET));
             exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
             exam.setDuration(r.getValue(EXAM.DURATION));
             exam.setFaceEnter(r.getValue(EXAM.FACE_ENTER));
             exam.setFaceMonitor(r.getValue(EXAM.FACE_MONITOR));
             exam.setStrongBaseFlag(r.getValue(EXAM.STRONG_BASE_FLAG));
			 // 如果该考试为强基的考试并且已发布，需要查询是否还有考试机会
			 if (!isAuthExam && exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1 == exam.getStrongBaseFlag()) {
				 if (strongBaseService.authType(examRegion, exam.getId())) {
					 exam.setExamAgain(strongBaseService.examAgain(examRegion, exam.getId(),memberId));
				 } else {
					 // 如果为模拟考试，构造前端正常次数逻辑
					 exam.setStrongBaseFlag(Exam.STRONG_BASE_FLAG_0);
					 exam.setExamAgain(null);
				 }
			 }
             ExamRecord examRecord = new ExamRecord();

             examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
             examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
             examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
             examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
             examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
             examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
             examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
             examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
             examRecord.setFaceStatus(r.getValue(examRecordTable.field("f_face_status", Integer.class)));

             examRecord.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
             examRecord.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
             examRecord.setCurrentTime(System.currentTimeMillis());
             examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
             examRecord.setParticipateTime(computeParticipateTime(examRecord.getStartTime(), examRecord.getLastSubmitTime(), exam.getDuration()));

             // 考试记录为空的时候取考试注册表的MemberID
             if (examRecord.getMemberId() == null) {
                 examRecord.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
             }

             if (r.getValue(PAPER_CLASS.TOTAL_SCORE) != null) {
                 PaperClass paperClass = new PaperClass();
                 paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                 exam.setPaperClass(paperClass);
             };

             SignUp signUp = new SignUp();
             if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
                 signUp.setId(r.getValue(CLOUD_SIGNUP.ID.as("cloudsignUpId")));
                 signUp.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
             }else if(Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
				 signUp.setId(r.getValue(GRID_SIGNUP.ID.as("gridsignUpId")));
				 signUp.setStatus(r.getValue(GRID_SIGNUP.STATUS));
			 }else {
                 signUp.setId(r.getValue(SIGNUP.ID.as("signUpId")));
                 signUp.setStatus(r.getValue(SIGNUP.STATUS));
             }
			 exam.setSignUp(signUp);
             if (r.getValue(examRegistTable.field("f_top_score", Integer.class)) != null) {
                 examRecord.setScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)) );
             }
             exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion,exam.getId(), memberId));
             exam.setExamRecord(examRecord);

             CertificateRecord certificateRecord = new CertificateRecord();
             certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
             exam.setCertificateRecord(certificateRecord);
			 ExamRegist examRegist = new ExamRegist();
			 examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
			 exam.setExamRegist(examRegist);
			 return exam;
         });

         Integer more = 0;
         if(Objects.nonNull(exams)) {
             if(pageSize < exams.size()) {
             	more = 1;
             	exams.remove(pageSize);
             }
         }

         Map<String, Object> map = new HashMap<>();

         map.put("more",more);
         map.put("items", exams);
         if (pageSwitch){
			 map.put("recordCount", count);
		 }
         return map;
    }

    @Override
    @DataSource
    public Exam getSimpleData(Integer examRegion, String id) {
        Exam exam = examDao.execute(e ->
                                            e.select(Fields.start()
                                                           .add(EXAM.ID).add(EXAM.NAME)
                                                           .add(EXAM.START_TIME).add(EXAM.END_TIME)
                                                           .add(EXAM.EXAM_NOTES).add(EXAM.DURATION)
                                                           .add(EXAM.PASS_SCORE).add(EXAM.TYPE)
                                                           .add(EXAM.NEED_APPLICANT).add(EXAM.APPLICANT_NEED_AUDIT)
                                                           .add(EXAM.IS_OVER_BY_PASS_EXAM).add(EXAM.APPLICANT_START_TIME)
                                                           .add(EXAM.APPLICANT_END_TIME).add(EXAM.STATUS)
                                                           .add(EXAM.ALLOW_EXAM_TIMES).add(EXAM.ALLOW_SWITCH_TIMES)
                                                           .add(EXAM.IS_SHOW_ANSWER_IMMED).add(EXAM.SOURCE_TYPE)
                                                           .add(EXAM.PAPER_SHOW_RULE).add(EXAM.PAPER_SORT_RULE)
                                                           .add(EXAM.SHOW_ANSWER_RULE).add(EXAM.SUPPORT_APP)
                                                           .add(EXAM.IS_ALLOW_SWITCH).add(EXAM.SEND_TO_CENTER)
                                                           .add(EXAM.JOIN_NUMBER).add(EXAM.PUBLISHER_ID)
                                                           .add(EXAM.APPLICANT_NUMBER).add(EXAM.PAPER_CLASS_ID)
                                                           .add(EXAM.CLIENT_TYPE).add(EXAM.IS_SET_PASSWORD)
                                                           .add(EXAM.IS_SET_PERSONAL_CODE).add(EXAM.IS_PERMIT_VIEW_CODE)
                                                           .add(EXAM.PASSWORD).add(EXAM.EXAM_BATCH)
                                                           .add(EXAM.ORGANIZATION_ID).add(EXAM.NEED_FILL_OUT_INFO)
                                                           .add(EXAM.SHOW_SCORE_TIME)
                                                           .add(EXAM.RANDOM_TYPE)
                                                           .add(EXAM.CREATE_TIME)
                                                           .add(EXAM.PROFESSION_ID)
                                                           .add(EXAM.LEVEL_ID)
                                                           .add(EXAM.FACE_ENTER)
                                                           .add(EXAM.FACE_MONITOR)
                                                           .add(EXAM.FACE2_ENTER)
                                                           .add(EXAM.FACE2_MONITOR)
                                                           .add(EXAM.INDEFINITE)
                                                           .add(EXAM.STRONG_BASE_FLAG)
                                                           .add(EXAM.PRE_APPROVAL_RULE)
                                                           .end()
                                             )
                                             .from(EXAM)
                                             .where(EXAM.ID.eq(id)).fetchOneInto(Exam.class)
        );
        return exam;
    }

    @Override
    @DataSource
    public Integer findArchiveExamRegistCount(Integer examRegion,
                                              String currentUserId, Optional<Long> startTime, Optional<Long> endTime,
                                              String examRegistStringTable, String examRecordStringTable) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);
        return examDao.execute(e -> {
            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(EXAM.ID).end());
            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                    startTime.map(EXAM.START_TIME::ge),
                    endTime.map(EXAM.START_TIME::le)
            );
            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce(Condition::and).orElse(DSL.trueCondition());
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFuncCount = a -> a.from(examRegistTable)
                    .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                    .where(condition.and(examRegistTable.field("f_member_id", String.class).eq(currentUserId)));
            return e.select(DSL.count()).from(stepFuncCount.apply(countField)).fetchOne(DSL.count());
        });
    }


    @Override
    @DataSource
    public Exam getCloudSignUpAndExamRecord(Integer examRegion, String examId, String memberId) {
        // 无需查询DB
        Exam exam = new Exam();
        exam.setId(examId);
        exam.setSignUp(new SignUp());
        exam.setExamRecord(new ExamRecord());
        exam.setExamedTimes(0);

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        Optional<Exam> exams = examDao.execute(e -> e.select(
                        Fields.start()
                                .add(examRegistTable.field("f_top_score", Integer.class))
                                .add(examRegistTable.field("f_top_score_record_id", String.class))
                                .add(EXAM.ID)
                                .add(CLOUD_SIGNUP.ID)
                                .add(CLOUD_SIGNUP.STATUS)
                                .add(examRecordTable.field("f_id", String.class))
                                .add(examRecordTable.field("f_status", Integer.class))
                                .add(examRecordTable.field("f_start_time", Long.class))
                                .add(examRecordTable.field("f_end_time", Long.class))
                                .add(examRecordTable.field("f_submit_time", Long.class))
                                .add(examRecordTable.field("f_is_finished", Integer.class))
                                .add(examRecordTable.field("f_is_reset", Integer.class))
                                .add(examRecordTable.field("f_exam_times", Integer.class))
                                .add(examRecordTable.field("f_face_status", Integer.class))
                                .end()
                )
                .from(EXAM)
                .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(EXAM.ID).and(CLOUD_SIGNUP.MEMBER_ID.eq(memberId)))
                .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID)
                        .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                                .and(examRecordTable.field("f_member_id", String.class).eq(memberId))))
                .leftJoin(examRegistTable).on(examRegistTable.field("f_exam_id", String.class).eq(examId)).and(examRegistTable.field("f_member_id", String.class).eq(memberId))
                .where(EXAM.ID.eq(examId))
                .orderBy(examRecordTable.field("f_create_time", Long.class).desc())
                .limit(1)
                .fetchOptional(t -> {
                    SignUp signUp = new SignUp();
                    signUp.setId(t.getValue(CLOUD_SIGNUP.ID));
                    signUp.setStatus(t.getValue(CLOUD_SIGNUP.STATUS));
                    exam.setSignUp(signUp);
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setStartTime(t.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setEndTime(t.getValue(examRecordTable.field("f_end_time", Long.class)));
                    examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setIsFinished(t.getValue(examRecordTable.field("f_is_finished", Integer.class)));
                    examRecord.setIsReset(t.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                    examRecord.setExamTimes(t.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setFaceStatus(t.getValue(examRecordTable.field("f_face_status", Integer.class)));
                    exam.setExamRecord(examRecord);
                    examRecord.setTotalScore(t.getValue(examRegistTable.field("f_top_score", Integer.class)));
                    examRecord.setTopScoreRecordId(t.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                    exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion,examId, memberId));
                    return exam;
                }));

        return exams.orElse(exam);
    }



    @Override
    @DataSource
    public Exam getGridSignUpAndExamRecord(Integer examRegion, String examId, String memberId) {
        // 无需查询DB
        Exam exam = new Exam();
        exam.setId(examId);
        exam.setSignUp(new SignUp());
        exam.setExamRecord(new ExamRecord());
        exam.setExamedTimes(0);

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        Optional<Exam> exams = examDao.execute(e -> e.select(
                        Fields.start()
                                .add(examRegistTable.field("f_top_score", Integer.class))
                                .add(examRegistTable.field("f_top_score_record_id", String.class))
                                .add(EXAM.ID)
                                .add(GRID_SIGNUP.ID)
                                .add(GRID_SIGNUP.STATUS)
                                .add(examRecordTable.field("f_id", String.class))
                                .add(examRecordTable.field("f_status", Integer.class))
                                .add(examRecordTable.field("f_start_time", Long.class))
                                .add(examRecordTable.field("f_end_time", Long.class))
                                .add(examRecordTable.field("f_submit_time", Long.class))
                                .add(examRecordTable.field("f_is_finished", Integer.class))
                                .add(examRecordTable.field("f_is_reset", Integer.class))
                                .add(examRecordTable.field("f_exam_times", Integer.class))
                                .add(examRecordTable.field("f_face_status", Integer.class))
                                .end()
                )
                .from(EXAM)
                .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(EXAM.ID).and(GRID_SIGNUP.MEMBER_ID.eq(memberId)))
                .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID)
                        .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                                .and(examRecordTable.field("f_member_id", String.class).eq(memberId))))
                .leftJoin(examRegistTable).on(examRegistTable.field("f_exam_id", String.class).eq(examId)).and(examRegistTable.field("f_member_id", String.class).eq(memberId))
                .where(EXAM.ID.eq(examId))
                .orderBy(examRecordTable.field("f_create_time", Long.class).desc())
                .limit(1)
                .fetchOptional(t -> {
                    SignUp signUp = new SignUp();
                    signUp.setId(t.getValue(GRID_SIGNUP.ID));
                    signUp.setStatus(t.getValue(GRID_SIGNUP.STATUS));
                    exam.setSignUp(signUp);
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setStartTime(t.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setEndTime(t.getValue(examRecordTable.field("f_end_time", Long.class)));
                    examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setIsFinished(t.getValue(examRecordTable.field("f_is_finished", Integer.class)));
                    examRecord.setIsReset(t.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                    examRecord.setExamTimes(t.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setFaceStatus(t.getValue(examRecordTable.field("f_face_status", Integer.class)));
                    exam.setExamRecord(examRecord);
                    examRecord.setTotalScore(t.getValue(examRegistTable.field("f_top_score", Integer.class)));
                    examRecord.setTopScoreRecordId(t.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                    exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion,examId, memberId));
                    return exam;
                }));

        return exams.orElse(exam);
    }


    @Override
    @DataSource
    public Exam getSignUpAndExamRecord(Integer examRegion, String examId, String memberId) {
        Exam exam = new Exam();
        exam.setId(examId);
        exam.setSignUp(new SignUp());
        exam.setExamRecord(new ExamRecord());
        exam.setExamedTimes(0);
        doSelectSignUp(memberId,examId).ifPresent(exam::setSignUp);
        doSelectRecord(memberId,examId).ifPresent(ew1->{
            exam.setExamRecord(ew1);
            exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion,examId, memberId));
        });
        return exam;
    }

    /**
     * 根据用户Id和考试Id查询考试注册记录
     * @param memberId 用户Id
     * @param examId 考试Id
     * @return 考试注册记录
     */
    private Optional<ExamRegist> doSelectRegist(String memberId, String examId){
        TableImpl<?> registTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        Collection<SelectField<?>> select = doSelect(registTable.field("f_top_score", Integer.class), registTable.field("f_top_score_record_id", String.class));
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(registTable,selectStep)
                .where(registTable.field("f_exam_id", String.class).eq(examId))
                .and(registTable.field("f_member_id", String.class).eq(memberId))
                .fetchOptional(ew1->{
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setTopScore(ew1.getValue(registTable.field("f_top_score", Integer.class)));
                    examRegist.setTopScoreRecordId(ew1.getValue(registTable.field("f_top_score_record_id", String.class)));
                    return examRegist;
                });
    }

    /**
     * 根据用户Id和考试Id获取某场考试当前用户最新的考试记录
     * @param memberId 用户Id
     * @param examId 考试Id
     * @return 获取某场考试当前用户最新的考试记录
     */
    private Optional<ExamRecord> doSelectRecord(String memberId, String examId){
        TableImpl<?> recordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        Collection<SelectField<?>> select = doSelect(recordTable.field("f_id", String.class),
                recordTable.field("f_start_time", Long.class), recordTable.field("f_end_time", Long.class),
                recordTable.field("f_submit_time", Long.class), recordTable.field("f_is_finished", Integer.class),
                recordTable.field("f_is_reset", Integer.class), recordTable.field("f_exam_times", Integer.class),
                recordTable.field("f_face_status", Integer.class), recordTable.field("f_exam_times", Integer.class),
                recordTable.field("f_status", Integer.class), recordTable.field("f_create_time", Long.class));
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        List<ExamRecord> examRecordCollect = doSingleRecord(recordTable, selectStep)
                .where(recordTable.field("f_member_id", String.class).eq(memberId))
                .and(recordTable.field("f_exam_id", String.class).eq(examId))
                .and(recordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .fetch(ew1 -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(ew1.getValue(recordTable.field("f_id", String.class)));
                    examRecord.setStatus(ew1.getValue(recordTable.field("f_status", Integer.class)));
                    examRecord.setStartTime(ew1.getValue(recordTable.field("f_start_time", Long.class)));
                    examRecord.setEndTime(ew1.getValue(recordTable.field("f_end_time", Long.class)));
                    examRecord.setSubmitTime(ew1.getValue(recordTable.field("f_submit_time", Long.class)));
                    examRecord.setIsFinished(ew1.getValue(recordTable.field("f_is_finished", Integer.class)));
                    examRecord.setIsReset(ew1.getValue(recordTable.field("f_is_reset", Integer.class)));
                    examRecord.setExamTimes(ew1.getValue(recordTable.field("f_exam_times", Integer.class)));
                    examRecord.setFaceStatus(ew1.getValue(recordTable.field("f_face_status", Integer.class)));
                    examRecord.setCreateTime(ew1.getValue(recordTable.field("f_create_time", Long.class)));
                    return examRecord;
                });
        if(CollectionUtils.isNotEmpty(examRecordCollect) && examRecordCollect.size()>1){
            logger.error("查询出现异常情况，当前用户{}当前考试{}最新状态{}出现多条记录,记录条数{}",memberId,examId,ExamRecord.CURRENT,examRecordCollect.size());
            examRecordCollect.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
            return Optional.of(populateData(memberId,examId,examRecordCollect.get(0)));
        }
        if(CollectionUtils.isNotEmpty(examRecordCollect) && examRecordCollect.size()==1){
            return  Optional.of(populateData(memberId,examId,examRecordCollect.get(0)));
        }
        return Optional.empty();
    }

    /**
     * 根据用户Id和考试Id获取用户报名记录
     * @param memberId 用户Id
     * @param examId 考试Id
     * @return 用户报名记录
     */
    private Optional<SignUp> doSelectSignUp(String memberId, String examId){
        Collection<SelectField<?>> select = doSelect(SIGNUP.ID, SIGNUP.STATUS);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(SIGNUP,selectStep)
                .where(SIGNUP.MEMBER_ID.eq(memberId))
                .and(SIGNUP.EXAM_ID.eq(examId))
                .fetchOptional(ew1->{
                    SignUp signUp = new SignUp();
                    signUp.setId(ew1.getValue(SIGNUP.ID));
                    signUp.setStatus(ew1.getValue(SIGNUP.STATUS));
                    return signUp;
                });
    }

    /**
     * 填充考试记录数据
     * @param memberId 用户Id
     * @param examId 考试Id
     * @param examRecord 某场考试当前用户最新的考试记录
     * @return 填充考试记录数据
     */
    private ExamRecord populateData(String memberId, String examId, ExamRecord examRecord){
        Optional<ExamRegist> examRegistOpt = doSelectRegist(memberId, examId);
        examRegistOpt.ifPresent(ew1 -> {
            examRecord.setTotalScore(ew1.getTopScore());
            examRecord.setTopScoreRecordId(ew1.getTopScoreRecordId());
        });
        return examRecord;
    }


    @Override
    @DataSource
    public boolean isOtherModuleExam(Integer examRegion, String examId) {
        Exam exam = examDao.execute(e ->  e.select(EXAM.SOURCE_TYPE).from(EXAM).where(EXAM.ID.eq(examId)).fetchOneInto(Exam.class));
        return exam.getSourceType() != Exam.EXAM_ACTIVITY_SOURCE_TYPE;
    }

    /**
     * 个人中心-我的考试（班级/专题/课程）
     */
    @Override
    @DataSource
    public Map<String, Object> otherExamRecords(Integer examRegion, int page, int pageSize,
                                                Optional<String> name,
                                                Optional<Integer> sourceType,
                                                List<String> userIds,
                                                String memberId,
                                                Optional<String> year,
                                                Optional<Integer> startTimeOrderBy,
                                                boolean pageSwitch) {
        //1、构建考试报名与考试记录真实数据源
        TableImpl<?> examRegisterTable = getTableUtil.getExamRegistTable(this.constructDataSource(year,ExamRegist.STRING_EXAM_REGIST));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(this.constructDataSource(year,ExamRecord.STRING_EXAM_RECORD));

        //2、条件构造器
        Stream<Optional<Condition>> conditions = Stream.of(
                name.map(EXAM.NAME::contains),//考试名
                sourceType.map(EXAM.SOURCE_TYPE::eq),//考试类型
                Optional.of(userIds).map(examRegisterTable.field("f_member_id", String.class)::in),
                Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne)
        );

        //3、拼接条件构造器
        Condition condition = conditions.filter(Optional::isPresent)
                .map(Optional::get)
                .reduce(Condition::and).orElse(DSL.trueCondition())
                .and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000))
                        .gt(System.currentTimeMillis())
                        .or(examRecordTable.field("f_status", Integer.class).ne(ExamRecord.STATUS_TO_BE_STARTED))
                );

        SelectConditionStep<Record> progressStep = examDao.execute(ew -> ew.select(this.examRecordsSql(examRegisterTable, examRecordTable))
                .from(examRegisterTable)
                .leftJoin(EXAM)
                .on(EXAM.ID.eq(examRegisterTable.field("f_exam_id", String.class)))
                .leftJoin(examRecordTable)
                .on(examRecordTable.field("f_exam_id", String.class).eq(examRegisterTable.field("f_exam_id", String.class)))
                .and(examRecordTable.field("f_member_id", String.class).eq(examRegisterTable.field("f_member_id", String.class)))
                .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
                .where(condition)
                .and(examRegisterTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE)
                        .or(examRegisterTable.field("f_top_score_record_id", String.class).isNotNull())
                        .or(examRecordTable.field("f_status", Integer.class)
                                .in(ExamRecord.STATUS_TO_BE_OVER, ExamRecord.STATUS_PASS, ExamRecord.STATUS_NOT_PASS, ExamRecord.STATUS_FINISHED, ExamRecord.STATUS_NULLIFY))));

        Long count = 0L;
        if (pageSwitch){
            count = progressStep.fetch().stream().count();
        }
        //5、组装出参
        List<Exam> examCollect = progressStep.stream().map(ew -> {
            Exam exam = new Exam();
            BeanUtils.copyProperties(ew.into(EXAM),exam);
            exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion, exam.getId(), memberId));
            ExamRecord examRecord = new ExamRecord();
            examRecord.setId(ew.getValue(examRecordTable.field("f_id", String.class)));
            examRecord.setMemberId(ew.getValue(examRecordTable.field("f_member_id", String.class)));
            examRecord.setStatus(ew.getValue(examRecordTable.field("f_status", Integer.class)));
            examRecord.setScore(ew.getValue(examRegisterTable.field("f_top_score", Integer.class)));
            examRecord.setSubmitTime(ew.getValue(examRecordTable.field("f_submit_time", Long.class)));
            examRecord.setIsReset(ew.getValue(examRecordTable.field("f_is_reset", Integer.class)));
            examRecord.setTotalScore(ew.getValue(PAPER_CLASS.TOTAL_SCORE));
            examRecord.setStartTime(ew.getValue(examRecordTable.field("f_start_time", Long.class)));
            examRecord.setExamTimes(ew.getValue(examRecordTable.field("f_exam_times", Integer.class)));
            examRecord.setFaceStatus(ew.getValue(examRecordTable.field("f_face_status", Integer.class)));
            examRecord.setTopScoreRecordId(ew.getValue(examRegisterTable.field("f_top_score_record_id", String.class)));
            examRecord.setCurrentTime(System.currentTimeMillis());
            exam.setExamRecord(examRecord);
            PaperClass paperClass = new PaperClass();
            paperClass.setTotalScore(ew.getValue(PAPER_CLASS.TOTAL_SCORE));
            ExamRegist examRegist = new ExamRegist();
            examRegist.setPassStatus(ew.getValue(examRegisterTable.field("f_pass_status", Integer.class)));
            exam.setExamRegist(examRegist);
            exam.setPaperClass(paperClass);
            return exam;
        }).collect(Collectors.toList());

        Integer more = 0;
        if(pageSize < examCollect.size()) {
            examCollect.remove(pageSize);
            more = 1;
        }

        //6、构造返回值
        Map<String, Object> map = new HashMap<>(2);
        map.put("items", examCollect);
        map.put("more", more);
        if (pageSwitch) {
            map.put("recordCount", count);
        }
        return map;
    }

    /**
     * 个人中心-我的考试（专题/课程/班级）SQL片段
     *
     * @param examRegisterTable   考试注册表
     * @param examRecordTable  考试记录表
     * @return 查询的Sql片段
     */
    private Collection<Field<?>>  examRecordsSql(TableImpl<?> examRegisterTable,
                                                 TableImpl<?> examRecordTable){
        return Fields.start().add(
                EXAM.ID,
                EXAM.EXAM_BATCH,
                EXAM.NAME,
                EXAM.TYPE,
                EXAM.STATUS,
                EXAM.START_TIME,
                EXAM.END_TIME,
                EXAM.PASS_SCORE,
                EXAM.ALLOW_EXAM_TIMES,
                EXAM.IS_SHOW_ANSWER_IMMED,
                EXAM.NEED_APPLICANT,
                EXAM.APPLICANT_NEED_AUDIT,
                EXAM.APPLICANT_START_TIME,
                EXAM.APPLICANT_END_TIME,
                EXAM.HAS_CERT,
                EXAM.CERTIFICATE_ID,
                EXAM.SHOW_ANSWER_RULE,
                EXAM.IS_SET_PASSWORD,
                EXAM.IS_SET_PERSONAL_CODE,
                EXAM.ADMISSION_TICKET,
                EXAM.SHOW_SCORE_TIME,
                EXAM.DURATION,
                EXAM.FACE_ENTER,
                EXAM.FACE_MONITOR,
                EXAM.STRONG_BASE_FLAG,
                PAPER_CLASS.TOTAL_SCORE,
                examRecordTable.field("f_id", String.class),
                examRecordTable.field("f_member_id", String.class),
                examRecordTable.field("f_status", Integer.class),
                examRecordTable.field("f_score", Integer.class),
                examRecordTable.field("f_submit_time", Long.class),
                examRecordTable.field("f_is_reset", Integer.class),
                examRecordTable.field("f_start_time", Long.class),
                examRecordTable.field("f_exam_times", Integer.class),
                examRecordTable.field("f_face_status", Integer.class),
                examRegisterTable.field("f_top_score", Integer.class),
                examRegisterTable.field("f_top_score_record_id", String.class),
                examRegisterTable.field("f_exam_times", Integer.class),
                examRegisterTable.field("f_member_id", String.class),
                examRegisterTable.field("f_pass_status", Integer.class)
        ).end();
    }


    @Override
    @DataSource
    public PagedResult<Exam> findAllPersonCenterListByRegist(Integer examRegion, int page, int pageSize, Optional<String> name,
                                                             Optional<Integer> type, String memberId, Optional<Integer> searchStatus, Optional<Integer> startTimeOrderBy,
                                                             List<String> userIds, Optional<String> organizationId, Optional<String> year) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(year.isPresent() ? (ExamRegist.STRING_EXAM_REGIST+"_"+year.get()) : ExamRegist.STRING_EXAM_REGIST);

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(year.isPresent() ? (ExamRecord.STRING_EXAM_RECORD+"_"+year.get()) : ExamRecord.STRING_EXAM_RECORD);

        return examDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start().add(
                                    examRegistTable.field("f_top_score", Integer.class),
                                    examRegistTable.field("f_top_score_record_id", String.class),
                                    examRegistTable.field("f_exam_times", Integer.class),
                                    examRegistTable.field("f_member_id", String.class),
                                    examRegistTable.field("f_pass_status", Integer.class),
                                    EXAM.ID,
                                    EXAM.EXAM_BATCH,
                                    EXAM.NAME,
                                    EXAM.TYPE,
                                    EXAM.STATUS,
                                    EXAM.START_TIME,
                                    EXAM.END_TIME,
                                    EXAM.PASS_SCORE,
                                    EXAM.ALLOW_EXAM_TIMES,
                                    EXAM.IS_SHOW_ANSWER_IMMED,
                                    EXAM.NEED_APPLICANT,
                                    EXAM.APPLICANT_NEED_AUDIT,
                                    EXAM.APPLICANT_START_TIME,
                                    EXAM.APPLICANT_END_TIME,
                                    EXAM.HAS_CERT,
                                    EXAM.CERTIFICATE_ID,
                                    EXAM.SHOW_ANSWER_RULE,
                                    EXAM.IS_SET_PASSWORD,
                                    EXAM.IS_SET_PERSONAL_CODE,
                                    EXAM.ADMISSION_TICKET,
                                    EXAM.SHOW_SCORE_TIME,
                                    EXAM.DURATION,
                                    examRecordTable.field("f_id", String.class),
                                    examRecordTable.field("f_member_id", String.class),
                                    examRecordTable.field("f_status", Integer.class),
                                    examRecordTable.field("f_score", Integer.class),
                                    examRecordTable.field("f_submit_time", Long.class),
                                    examRecordTable.field("f_is_reset", Integer.class),
                                    examRecordTable.field("f_start_time", Long.class),
                                    examRecordTable.field("f_exam_times", Integer.class),
                                    examRecordTable.field("f_start_time", Long.class),
                                    examRecordTable.field("f_last_submit_time", Long.class),
                                    SIGNUP.ID.as("signUpId"),
                                    SIGNUP.STATUS,
                                    CLOUD_SIGNUP.ID.as("cloudsignUpId"),
                                    CLOUD_SIGNUP.STATUS,
                                    GRID_SIGNUP.ID.as("gridsignUpId"),
                                    GRID_SIGNUP.STATUS,
                                    PAPER_CLASS.TOTAL_SCORE,
                                    CERTIFICATE_RECORD.ID
                            )
                            .end()
            );


            Stream<Optional<Condition>> conditions = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    type.map(EXAM.TYPE::eq),//考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),//考试活动类型
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                    getSearchStatus(searchStatus, examRecordTable)//查询状态
            );

            Stream<Optional<Condition>> conditionsForExamCancel = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    type.map(EXAM.TYPE::eq),//考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(ExamRegist.STATUS_FINISHED).map(examRegistTable.field("f_status", Integer.class)::eq),//查询状态
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),//考试活动类型
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::eq)//非撤销
            );

            Stream<Optional<Condition>> conditionsForExamEnd = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    type.map(EXAM.TYPE::eq),//考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq), //考试活动类型
                    Optional.of(System.currentTimeMillis()).map(EXAM.END_TIME::lt)//结束
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(trueCondition())
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).gt(System.currentTimeMillis()).or(examRecordTable.field("f_status", Integer.class).ne(ExamRecord.STATUS_TO_BE_STARTED)));

            Condition conditionForExamCancel = conditionsForExamCancel.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(trueCondition())
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

            Condition conditionForExamEnd = conditionsForExamEnd.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(trueCondition())
                    .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED)
                            .or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED)
                                    .and(
                                            SIGNUP.STATUS.eq(SignUp.STATUS_PASSED)
                                                    .or(CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                                                    .or(GRID_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                                    )
                            )
                    )
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

            Condition mergeCondition;

            mergeCondition = searchStatus.map(s -> {
                if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                    return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                }
                return condition;
            }).orElseGet(() -> {
                return condition.or(conditionForExamCancel).or(conditionForExamEnd);
            }).and(organizationId.map(o -> {
                if (Exam.GRID.equals(o)) {
                    return EXAM.TYPE.eq(Exam.EXAM_GRID_TYPE);
                }
                // 点击网络部，市场部tab，只查询考试类型为网络部，市场部的集团认证考试
                return EXAM.ORGANIZATION_ID.eq(o).and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE));
            }).orElse(EXAM.TYPE.in(Exam.EXAM_ALL_TYPE)));

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                        .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class)))
                        .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))).and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                        .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CLOUD_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(GRID_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(CERTIFICATE_RECORD).on(CERTIFICATE_RECORD.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CERTIFICATE_RECORD.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
                        .where(mergeCondition).and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE).or(examRegistTable.field("f_top_score_record_id", String.class).isNotNull()).or(examRecordTable.field("f_status", Integer.class).in(ExamRecord.STATUS_TO_BE_OVER,ExamRecord.STATUS_PASS,ExamRecord.STATUS_NOT_PASS,ExamRecord.STATUS_FINISHED,ExamRecord.STATUS_NULLIFY)));

                return select;
            };

            int count = e.select(DSL.count(examRegistTable.field("f_id", String.class))).from(examRegistTable)
                    .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                    .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class)))
                    .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))).and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CLOUD_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(GRID_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .where(mergeCondition).and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE).or(examRegistTable.field("f_top_score_record_id", String.class).isNotNull()).or(examRecordTable.field("f_status", Integer.class).in(ExamRecord.STATUS_TO_BE_OVER,ExamRecord.STATUS_PASS,ExamRecord.STATUS_NOT_PASS,ExamRecord.STATUS_FINISHED,ExamRecord.STATUS_NULLIFY))).fetchOne(DSL.count(examRegistTable.field("f_id", String.class)));

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
                exam.setIsShowAnswerImmed(r.getValue(EXAM.IS_SHOW_ANSWER_IMMED));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setHasCert(r.getValue(EXAM.HAS_CERT));
                exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                exam.setIsSetPassword(r.getValue(EXAM.IS_SET_PASSWORD));
                exam.setIsSetPersonalCode(r.getValue(EXAM.IS_SET_PERSONAL_CODE));
                exam.setAdmissionTicket(r.getValue(EXAM.ADMISSION_TICKET));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
                exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                exam.setDuration(r.getValue(EXAM.DURATION));

                ExamRecord examRecord = new ExamRecord();
                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                examRecord.setParticipateTime(computeParticipateTime(examRecord.getStartTime(), examRecord.getLastSubmitTime(), exam.getDuration()));

                examRecord.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                examRecord.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                examRecord.setCurrentTime(System.currentTimeMillis());

                // 考试记录为空的时候取考试注册表的MemberID
                if (examRecord.getMemberId() == null) {
                    examRecord.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                }

                if (r.getValue(PAPER_CLASS.TOTAL_SCORE) != null) {
                    PaperClass paperClass = new PaperClass();
                    paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                    exam.setPaperClass(paperClass);
                };

                SignUp signUp = new SignUp();
                if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
                    signUp.setId(r.getValue(CLOUD_SIGNUP.ID.as("cloudsignUpId")));
                    signUp.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
                }else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
                    signUp.setId(r.getValue(GRID_SIGNUP.ID.as("gridsignUpId")));
                    signUp.setStatus(r.getValue(GRID_SIGNUP.STATUS));
                }else {
                    signUp.setId(r.getValue(SIGNUP.ID.as("signUpId")));
                    signUp.setStatus(r.getValue(SIGNUP.STATUS));
                }
                exam.setSignUp(signUp);
                if (r.getValue(examRegistTable.field("f_top_score", Integer.class)) != null) {
                    examRecord.setScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)) );
                }
                exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion, exam.getId(), memberId));
                exam.setExamRecord(examRecord);

                CertificateRecord certificateRecord = new CertificateRecord();
                certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                exam.setCertificateRecord(certificateRecord);

                ExamRegist examRegist = new ExamRegist();
                examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                exam.setExamRegist(examRegist);

                return exam;
            }).collect(Collectors.toList()));
        });
    }

    /**
     * 统计学员参与考试时长，为交卷时间和进入考试时间相减，但不能大于这场考试规定的时长。
     */
    private Integer computeParticipateTime(Long startTime, Long lastSubmitTime, Integer duration) {
        if (startTime == null || lastSubmitTime == null) {
            return 0;
        }
        Long time = lastSubmitTime - startTime;
        if (time > duration * 60 * 1000) {
            return duration * 60 * 1000;
        }
        return time.intValue();
    }

    @Override
    @DataSource
    public PagedResult<Exam> findActivityList(
            Integer examRegion,
            int page,
            int pageSize,
            Optional<String> name,
            Optional<Integer> type,
            String memberId,
            Optional<Integer> searchStatus,
            String clientType,
            Optional<String> topicId) {

        return examDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                            .add(EXAM.ID)
                            .add(EXAM.NAME)
                            .add(EXAM.START_TIME)
                            .add(EXAM.END_TIME)
                            .add(EXAM.DURATION)
                            .add(EXAM.EXAM_NOTES)
                            .add(EXAM.CREATE_TIME)
                            .add(EXAM.JOIN_NUMBER)
                            .add(EXAM.TYPE)
                            .add(EXAM.SHOW_SCORE_TIME)
                            .end()
            );

            SelectSelectStep<Record> selectIdFiedld = e.selectDistinct(
                    Fields.start()
                            .add(EXAM.ID)
                            .end()
            );

            Stream<Optional<Condition>> conditions = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(EXAM.NAME, " ", "").contains(str);
                    }),
                    type.map(EXAM.TYPE::eq),
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),
                    Optional.of(Exam.STATUS_PUBLISHING).map(EXAM.STATUS::gt),
                    Optional.of(new Integer[]{Integer.valueOf(clientType), Integer.valueOf(Exam.CLIENT_ALL)}).map(EXAM.CLIENT_TYPE::in),
                    getSearchStatusCondition(searchStatus),
                    topicId.map(BUSINESS_TOPIC.TOPIC_ID::eq),
                    Optional.of(AudienceObject.TYPE_EXAM).map(AUDIENCE_OBJECT.TYPE::eq),
                    Optional.of(Exam.EXAM_CLOUD_TYPE).map(EXAM.TYPE::ne)
            );

            Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(EXAM)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID)).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                        .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(EXAM.ID)).and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.TYPE_EXAM))
                        .where(c).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId));

                return select;
            };

            SelectConditionStep<Record> idsListSetp = stepFunc.apply(selectIdFiedld);

            Integer pageSizeFinal = pageSize;
            if (START_PAGE.equals(new Integer(page))) { // 第一页不去重
                pageSizeFinal = pageSize * REPETITION_FACTOR;
                idsListSetp = stepFunc.apply(e.select(Fields.start().add(EXAM.ID).end()));
            }
            idsListSetp.orderBy(EXAM.CREATE_TIME.desc());
            List<String> ids = idsListSetp.limit((page - 1) * pageSizeFinal, pageSizeFinal).fetch(EXAM.ID);
            if (ids != null && ids.size() > pageSize) {
                // 加了重复因子的查询，去重处理，使用LinkedHashSet保证顺序
                List<String> idsUnique = new ArrayList(new LinkedHashSet(ids));
                ids = idsUnique.subList(0, idsUnique.size() > pageSize ? pageSize : idsUnique.size());
            }

            Result<Record> record = selectListField.from(EXAM).where(EXAM.ID.in(ids)).orderBy(EXAM.CREATE_TIME.desc()).fetch();
            return PagedResult.create(record.size(), record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setExamNotes(r.getValue(EXAM.EXAM_NOTES));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setDuration(r.getValue(EXAM.DURATION));
                exam.setJoinNumber(r.getValue(EXAM.JOIN_NUMBER));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
                exam.setExamEndTime(exam.getEndTime() + exam.getDuration().intValue() * 60 * 1000);
                return exam;
            }).collect(Collectors.toList()));

        });

    }



    @Override
    @DataSource
    public Map<String,Object> findArchiveListByExamRegist(Integer examRegion, Integer page, Integer pageSize,
                                                          String currentUserId, Optional<Long> startTime, Optional<Long> endTime,List<String> userIds,
                                                          String examRegistStringTable, String examRecordStringTable,boolean pageSwitch) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);

        return examDao.execute(e -> {

            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(EXAM.ID).end());

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(examRegistTable.field("f_id", String.class))
                    .add(examRegistTable.field("f_member_id", String.class))
                    .add(examRegistTable.field("f_exam_times", Integer.class))
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.TYPE)
                    .add(EXAM.EXAM_NOTES)
                    .add(EXAM.STATUS)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.CREATE_TIME)
                    .add(EXAM.PASS_SCORE)
                    .add(EXAM.ALLOW_EXAM_TIMES)
                    .add(EXAM.IS_SHOW_ANSWER_IMMED)
                    .add(EXAM.NEED_APPLICANT)
                    .add(EXAM.APPLICANT_NEED_AUDIT)
                    .add(EXAM.APPLICANT_START_TIME)
                    .add(EXAM.APPLICANT_END_TIME)
                    .add(EXAM.SOURCE_TYPE)
                    .add(EXAM.HAS_CERT)
                    .add(EXAM.CERTIFICATE_ID)
                    .add(EXAM.SHOW_ANSWER_RULE)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .add(PAPER_CLASS.TOTAL_SCORE)
                    .end());

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                    startTime.map(EXAM.START_TIME::ge),
                    endTime.map(EXAM.START_TIME::le)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
                        .where(condition.and(examRegistTable.field("f_member_id", String.class).in(userIds)));

                return select;
            };

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFuncCount = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                        .where(condition.and(examRegistTable.field("f_member_id", String.class).in(userIds)));

                return select;
            };

            int count = e.select(DSL.count()).from(stepFuncCount.apply(countField)).fetchOne(DSL.count());

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(EXAM.START_TIME.desc(),EXAM.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize + 1).fetch();

            List<String> examIds = record.into(Exam.class).stream().map(Exam::getId).collect(Collectors.toList());
            List<ExamRecord> examRecords = findExamRecords(examIds, userIds, examRecordStringTable);
            List<SignUp> signUps = findSignUps(examIds, userIds);
            List<CertificateRecord> certificateRecords = findCertificateRecords(examIds, userIds);

            Map<String, ExamRecord> examRecordMap = examRecords.stream().collect(Collectors.toMap((t) -> {
                return ExamRecord.getExamRecordKey(t.getExamId(), t.getMemberId());
            }, t -> t, (o1, o2) -> o2));

            Map<String, SignUp> signUpMap = signUps.stream().collect(Collectors.toMap((t) -> {
                return ExamRecord.getExamRecordKey(t.getExamId(), t.getMemberId());
            }, t -> t, (o1, o2) -> o2));

            Map<String, CertificateRecord> certificateRecordMap = certificateRecords.stream().collect(Collectors.toMap((t) -> {
                return ExamRecord.getExamRecordKey(t.getExamId(), t.getMemberId());
            }, t -> t, (o1, o2) -> o2));

            List<Exam> resultList = record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setExamNotes(r.getValue(EXAM.EXAM_NOTES));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setCreateTime(r.getValue(EXAM.CREATE_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
                exam.setIsShowAnswerImmed(r.getValue(EXAM.IS_SHOW_ANSWER_IMMED));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setSourceType(r.getValue(EXAM.SOURCE_TYPE));
                exam.setHasCert(r.getValue(EXAM.HAS_CERT));
                exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));

                ExamRegist examRegist = new ExamRegist();
                examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
                String key = ExamRecord.getExamRecordKey(exam.getId(), examRegist.getMemberId());

                if (examRegist != null && !currentUserId.equals(examRegist.getMemberId())) {
                    examRegist.setIsOperation(ExamRegist.OPERATION_NO);
                }

                ExamRecord examRecord = getExamRecordByMap(key, examRecordMap);
                examRecord.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                exam.setExamRecord(examRecord);

                SignUp signUp = getSignUpByMap(key, signUpMap);
                exam.setSignUp(signUp);

                CertificateRecord certificateRecord = getCertificateRecordByMap(key, certificateRecordMap);
                examRegist.setCertificateRecord(certificateRecord);
                exam.setExamRegist(examRegist);

                return exam;
            }).collect(Collectors.toList());
            Integer more = 0;

            if (CollectionUtils.isNotEmpty(resultList) && resultList.size() > pageSize){
                resultList.remove(pageSize);
                more =  1;
            }
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("items",resultList);
            resultMap.put("more",more);
            if(pageSwitch){
                resultMap.put("recordCount",count);
            }
            return resultMap;
        });
    }


    /**
     * 判断是否将会超出考试限制最大次数
     */
    @Override
    @DataSource
    public boolean isOverExamTime(Integer examRegion, String examId, String currentUserId) {
        Integer allowExamTimes = examDao.execute(e ->
                e.select(EXAM.ALLOW_EXAM_TIMES).from(EXAM)
                        .where(EXAM.ID.eq(examId)).fetchOne(EXAM.ALLOW_EXAM_TIMES));

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<String> examRecordList = examRecordDao.execute(x -> x.select(
                        Fields.start()
                                .add(examRecordTable.field("f_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .and(examRecordTable.field("f_member_id", String.class).eq(currentUserId))
                .fetch(examRecordTable.field("f_id", String.class))
        );
        Integer examedTimes = examRecordList != null ? examRecordList.size() : 0;

        ExamRecord examRecord = examRecordService.getNewestRecord(examRegion, examId, currentUserId);

        // 未开考，考试不限次数
        if (examedTimes == 0 || allowExamTimes == 0 || examRecord == null) return false;

        return (examedTimes.intValue() + 1)  > allowExamTimes.intValue()
                && (examRecord.getStatus() != null &&
                ExamRecord.STATUS_TIME_EXCEPTION.intValue() < examRecord.getStatus().intValue());
    }



    private List<CertificateRecord> findCertificateRecords(List<String> examIds, List<String> userIds) {
        return examRecordDao.execute(e -> e.select(
                        Fields.start()
                                .add(CERTIFICATE_RECORD.ID)
                                .add(CERTIFICATE_RECORD.MEMBER_ID)
                                .add(CERTIFICATE_RECORD.EXAM_ID).end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.EXAM_ID.in(examIds), CERTIFICATE_RECORD.MEMBER_ID.in(userIds)).fetchInto(CertificateRecord.class));
    }

    private List<SignUp> findSignUps(List<String> examIds, List<String> userIds) {
        return examRecordDao.execute(e -> e.select(
                        Fields.start()
                                .add(SIGNUP.ID)
                                .add(SIGNUP.MEMBER_ID)
                                .add(SIGNUP.EXAM_ID)
                                .add(SIGNUP.STATUS).end())
                .from(SIGNUP)
                .where(SIGNUP.EXAM_ID.in(examIds), SIGNUP.MEMBER_ID.in(userIds)).fetchInto(SignUp.class));
    }

    private ExamRecord getExamRecordByMap(String key, Map<String, ExamRecord> examRecordMap) {
        return examRecordMap.getOrDefault(key, new ExamRecord());
    }

    private SignUp getSignUpByMap(String key, Map<String, SignUp> signUpMap) {
        return signUpMap.getOrDefault(key, new SignUp());
    }

    private CertificateRecord getCertificateRecordByMap(String key, Map<String, CertificateRecord> certificateRecordMap) {
        return certificateRecordMap.getOrDefault(key, new CertificateRecord());
    }

    private List<ExamRecord> findExamRecords(List<String> examIds, List<String> userIds, String examRecordStringTable) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);
        return examRecordDao.execute(e -> e.select(
                        Fields.start()
                                .add(examRecordTable.field("f_id", String.class))
                                .add(examRecordTable.field("f_status", Integer.class))
                                .add(examRecordTable.field("f_score", Integer.class))
                                .add(examRecordTable.field("f_member_id", String.class))
                                .add(examRecordTable.field("f_exam_id", String.class))
                                .add(examRecordTable.field("f_submit_time", Long.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_exam_id", String.class).in(examIds),
                        examRecordTable.field("f_member_id", String.class).in(userIds),
                        examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)).fetch(r -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                    examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    return examRecord;
                }));
    }


    /**
     * 获取活动首页查询状态
     * @param searchStatus
     * @return
     */
    private Optional<Condition> getSearchStatusCondition(Optional<Integer> searchStatus) {
        return searchStatus.map(t -> {
            Long currentTime = System.currentTimeMillis();
            if (t == Activity.ACTIVITY_HOME_STATUS_RUNNING){
                return Optional.of(EXAM.START_TIME.lt(currentTime).and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).gt(currentTime)));

            } else if (t == Activity.ACTIVITY_HOME_STATUS_NOT_START){
                return Optional.of(EXAM.START_TIME.gt(currentTime));

            } else if (t == Activity.ACTIVITY_HOME_STATUS_FINISH){
                return Optional.of(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).lt(currentTime));
            }

            return Optional.of(DSL.trueCondition());
        }).orElse(Optional.of(DSL.trueCondition()));
    }




    /**
     * 个人中心-我的考试 （App）
     */
    @Override
    @DataSource
    public PagedResult<Exam> findPersonCenterListByRegist(
            Integer examRegion,
            int page,
            int pageSize,
            Optional<String> name,
            Optional<Integer> type,
            String memberId,
            Optional<Integer> searchStatus,
            Optional<Integer> startTimeOrderBy,
            List<String> userIds,
            Optional<String> organizationId,
            Optional<String> year,
            boolean isAuthExam) {

        //1、入参year定位考试注册
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(year.isPresent() ? (ExamRegist.STRING_EXAM_REGIST+"_"+year.get()) : ExamRegist.STRING_EXAM_REGIST);

        //2、入参year定位考试记录
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(year.isPresent() ? (ExamRecord.STRING_EXAM_RECORD+"_"+year.get()) : ExamRecord.STRING_EXAM_RECORD);

        //3、查询SQL片段
        return examDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start().add(
                                    examRegistTable.field("f_top_score", Integer.class),
                                    examRegistTable.field("f_top_score_record_id", String.class),
                                    examRegistTable.field("f_exam_times", Integer.class),
                                    examRegistTable.field("f_member_id", String.class),
                                    examRegistTable.field("f_pass_status", Integer.class),
                                    EXAM.ID,
                                    EXAM.EXAM_BATCH,
                                    EXAM.NAME,
                                    EXAM.TYPE,
                                    EXAM.STATUS,
                                    EXAM.START_TIME,
                                    EXAM.END_TIME,
                                    EXAM.PASS_SCORE,
                                    EXAM.ALLOW_EXAM_TIMES,
                                    EXAM.IS_SHOW_ANSWER_IMMED,
                                    EXAM.NEED_APPLICANT,
                                    EXAM.APPLICANT_NEED_AUDIT,
                                    EXAM.APPLICANT_START_TIME,
                                    EXAM.APPLICANT_END_TIME,
                                    EXAM.HAS_CERT,
                                    EXAM.CERTIFICATE_ID,
                                    EXAM.SHOW_ANSWER_RULE,
                                    EXAM.IS_SET_PASSWORD,
                                    EXAM.IS_SET_PERSONAL_CODE,
                                    EXAM.ADMISSION_TICKET,
                                    EXAM.SHOW_SCORE_TIME,
                                    EXAM.DURATION,
                                    examRecordTable.field("f_id", String.class),
                                    examRecordTable.field("f_member_id", String.class),
                                    examRecordTable.field("f_status", Integer.class),
                                    examRecordTable.field("f_score", Integer.class),
                                    examRecordTable.field("f_submit_time", Long.class),
                                    examRecordTable.field("f_is_reset", Integer.class),
                                    examRecordTable.field("f_start_time", Long.class),
                                    examRecordTable.field("f_exam_times", Integer.class),
                                    SIGNUP.ID.as("signUpId"),
                                    SIGNUP.STATUS,
                                    CLOUD_SIGNUP.ID.as("cloudsignUpId"),
                                    CLOUD_SIGNUP.STATUS,
                                    GRID_SIGNUP.ID.as("gridsignUpId"),
                                    GRID_SIGNUP.STATUS,
                                    PAPER_CLASS.TOTAL_SCORE,
                                    CERTIFICATE_RECORD.ID
                            )
                            .end()
            );

            //4、考试状态查询条件构造
            Stream<Optional<Condition>> conditions = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    /*type.map(EXAM.TYPE::in),*///考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),//考试活动类型
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                    getSearchStatus(searchStatus, examRecordTable)//查询状态
            );

            //5、未发布查询条件构造
            Stream<Optional<Condition>> conditionsForExamCancel = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    /*type.map(EXAM.TYPE::in),*///考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(ExamRegist.STATUS_FINISHED).map(examRegistTable.field("f_status", Integer.class)::eq),//查询状态
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),//考试活动类型
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::eq)//非撤销
            );

            //6、已结束查询条件构造
            Stream<Optional<Condition>> conditionsForExamEnd = Stream.of(
                    name.map(EXAM.NAME::contains),//考试名
                    /*type.map(EXAM.TYPE::in),*///考试类型
                    Optional.of(userIds).map(examRegistTable.field("f_member_id", String.class)::in),//用户ID
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq), //考试活动类型
                    Optional.of(System.currentTimeMillis()).map(EXAM.END_TIME::lt)//结束
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition())
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).gt(System.currentTimeMillis()).or(examRecordTable.field("f_status", Integer.class).ne(ExamRecord.STATUS_TO_BE_STARTED)));

            Condition conditionForExamCancel = conditionsForExamCancel.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition())
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

            Condition conditionForExamEnd = conditionsForExamEnd.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
                    .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_FINISHED)
                            .or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED)
                                    .and(
                                            SIGNUP.STATUS.eq(SignUp.STATUS_PASSED)
                                                    .or(CLOUD_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                                                    .or(GRID_SIGNUP.STATUS.eq(SignUp.STATUS_PASSED))
                                    )
                            )
                    )
                    .and(SIGNUP.STATUS.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(CLOUD_SIGNUP.STATUS.isNull().or(CLOUD_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)))
                    .and(GRID_SIGNUP.STATUS.isNull().or(GRID_SIGNUP.STATUS.ne(SignUp.STATUS_CANCEL)));

            Condition mergeCondition;

            if (!isAuthExam && type.isPresent()) {
                mergeCondition = searchStatus.map(s -> {
                    if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                        return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                    }
                    return condition;
                }).orElseGet(() -> {
                    return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                });
            }else if (!isAuthExam && !type.isPresent()) {
                mergeCondition = searchStatus.map(s -> {
                    if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                        return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                    }
                    return condition;
                }).orElseGet(() -> {
                    return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                }).and(
                        EXAM.TYPE.eq(Exam.EXAM_OFFICIAL_TYPE)
                                .or(EXAM.TYPE.eq(Exam.EXAM_UN_OFFICIAL_TYPE)));
            }else {
                mergeCondition = searchStatus.map(s -> {
                    if (s == Exam.FINISHED_PERSON_CENTER_STATUS) {
                        return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                    }
                    return condition;
                }).orElseGet(() -> {
                    return condition.or(conditionForExamCancel).or(conditionForExamEnd);
                }).and(organizationId.map(o -> {
                    if (Exam.GRID.equals(o)) {
                        return EXAM.TYPE.eq(Exam.EXAM_GRID_TYPE);
                    }
                    // 点击网络部，市场部tab，只查询考试类型为网络部，市场部的集团认证考试
                    return EXAM.ORGANIZATION_ID.eq(o).and(EXAM.TYPE.in(Exam.EXAM_AUTH_CLOUD_TYPE));
                }).orElse(EXAM.TYPE.in(Exam.EXAM_AUTH_GRID_TYPE)));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                        .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class)))
                        .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))).and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                        .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CLOUD_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(GRID_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(CERTIFICATE_RECORD).on(CERTIFICATE_RECORD.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CERTIFICATE_RECORD.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                        .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
                        .where(mergeCondition)
                        .and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE)
                                .or(examRegistTable.field("f_top_score_record_id", String.class).isNotNull())
                                .or(examRecordTable.field("f_status", Integer.class)
                                        .in(ExamRecord.STATUS_TO_BE_OVER,ExamRecord.STATUS_PASS,ExamRecord.STATUS_NOT_PASS,ExamRecord.STATUS_FINISHED, ExamRecord.STATUS_NULLIFY)))
                        .and(mergingOrdinaryAndTest(type));
                return select;
            };

            int count = e.select(DSL.count(examRegistTable.field("f_id", String.class))).from(examRegistTable)
                    .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))
                    .leftJoin(examRecordTable).on(examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class)))
                    .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))).and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                    .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .leftJoin(CLOUD_SIGNUP).on(CLOUD_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(CLOUD_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .leftJoin(GRID_SIGNUP).on(GRID_SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)).and(GRID_SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))))
                    .where(mergeCondition).and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE).or(examRegistTable.field("f_top_score_record_id", String.class).isNotNull()).or(examRecordTable.field("f_status", Integer.class).in(ExamRecord.STATUS_TO_BE_OVER,ExamRecord.STATUS_PASS,ExamRecord.STATUS_NOT_PASS,ExamRecord.STATUS_FINISHED,ExamRecord.STATUS_NULLIFY))).fetchOne(DSL.count(examRegistTable.field("f_id", String.class)));

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
                exam.setIsShowAnswerImmed(r.getValue(EXAM.IS_SHOW_ANSWER_IMMED));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setHasCert(r.getValue(EXAM.HAS_CERT));
                exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                exam.setIsSetPassword(r.getValue(EXAM.IS_SET_PASSWORD));
                exam.setIsSetPersonalCode(r.getValue(EXAM.IS_SET_PERSONAL_CODE));
                exam.setAdmissionTicket(r.getValue(EXAM.ADMISSION_TICKET));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));
                exam.setDuration(r.getValue(EXAM.DURATION));

                ExamRecord examRecord = new ExamRecord();

                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));

                examRecord.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                examRecord.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                examRecord.setCurrentTime(System.currentTimeMillis());

                // 考试记录为空的时候取考试注册表的MemberID
                if (examRecord.getMemberId() == null) {
                    examRecord.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                }

                if (r.getValue(PAPER_CLASS.TOTAL_SCORE) != null) {
                    PaperClass paperClass = new PaperClass();
                    paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                    exam.setPaperClass(paperClass);
                };

                SignUp signUp = new SignUp();
                if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
                    signUp.setId(r.getValue(CLOUD_SIGNUP.ID.as("cloudsignUpId")));
                    signUp.setStatus(r.getValue(CLOUD_SIGNUP.STATUS));
                }else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
                    signUp.setId(r.getValue(GRID_SIGNUP.ID.as("gridsignUpId")));
                    signUp.setStatus(r.getValue(GRID_SIGNUP.STATUS));
                }else {
                    signUp.setId(r.getValue(SIGNUP.ID.as("signUpId")));
                    signUp.setStatus(r.getValue(SIGNUP.STATUS));
                }
                exam.setSignUp(signUp);
                if (r.getValue(examRegistTable.field("f_top_score", Integer.class)) != null) {
                    examRecord.setScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)) );
                }
                exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion, exam.getId(), memberId));
                exam.setExamRecord(examRecord);

                CertificateRecord certificateRecord = new CertificateRecord();
                certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                exam.setCertificateRecord(certificateRecord);

                ExamRegist examRegist = new ExamRegist();
                examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                exam.setExamRegist(examRegist);

                return exam;
            }).collect(Collectors.toList()));
        });
    }



    @Override
    @DataSource
    public List<Exam> findExamByAudient(Integer examRegion, String[] examIds, String memberId) {
        return examDao.execute(e ->
                e.selectDistinct(Fields.start().add(EXAM).end())
                        .from(EXAM)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .where(EXAM.ID.in(examIds), AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)).fetchInto(Exam.class)
        );
    }


    @Override
    @DataSource
    public List<Exam> findAllArchivrList(Integer examRegion, Optional<Long> startTime, Optional<Long> endTime, String memberId, List<String> userIds, String examRegistStringTable, String examRecordStringTable) {
        return examDao.execute(e -> {

            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(examRegistStringTable);
            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.TYPE)
                    .add(EXAM.EXAM_NOTES)
                    .add(EXAM.STATUS)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.CREATE_TIME)
                    .add(EXAM.PASS_SCORE)
                    .add(EXAM.ALLOW_EXAM_TIMES)
                    .add(EXAM.IS_SHOW_ANSWER_IMMED)
                    .add(EXAM.NEED_APPLICANT)
                    .add(EXAM.APPLICANT_NEED_AUDIT)
                    .add(EXAM.APPLICANT_START_TIME)
                    .add(EXAM.APPLICANT_END_TIME)
                    .add(EXAM.SOURCE_TYPE)
                    .add(EXAM.HAS_CERT)
                    .add(EXAM.CERTIFICATE_ID)
                    .add(EXAM.SHOW_ANSWER_RULE)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .add(examRecordTable.field("f_id", String.class))
                    .add(examRecordTable.field("f_status", Integer.class))
                    .add(examRecordTable.field("f_score", Integer.class))
                    .add(examRecordTable.field("f_submit_time", Long.class))
                    .add(SIGNUP.ID)
                    .add(SIGNUP.STATUS)
                    .add(PAPER_CLASS.TOTAL_SCORE)
                    .end());

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(Exam.EXAM_ACTIVITY_SOURCE_TYPE).map(EXAM.SOURCE_TYPE::eq),
                    Optional.of(Exam.STATUS_NOT_PUBLISH).map(EXAM.STATUS::ne),
                    startTime.map(EXAM.START_TIME::ge),
                    endTime.map(EXAM.START_TIME::le)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .leftJoin(EXAM).on(EXAM.ID.eq(examRegistTable.field("f_exam_id", String.class)))

                        .leftJoin(examRecordTable).on(examRegistTable.field("f_exam_id", String.class).eq(examRecordTable.field("f_exam_id", String.class)))
                        .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                                .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class))))

                        .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(examRegistTable.field("f_exam_id", String.class)))
                        .and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class)))

                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))

                        .where(condition.and(examRegistTable.field("f_member_id", String.class).in(userIds)));

                return select;
            };

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(EXAM.START_TIME.desc(),EXAM.CREATE_TIME.desc());
            Result<Record> record = listSetp.fetch();

            return record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setExamNotes(r.getValue(EXAM.EXAM_NOTES));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setCreateTime(r.getValue(EXAM.CREATE_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
                exam.setIsShowAnswerImmed(r.getValue(EXAM.IS_SHOW_ANSWER_IMMED));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setSourceType(r.getValue(EXAM.SOURCE_TYPE));
                exam.setHasCert(r.getValue(EXAM.HAS_CERT));
                exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));

                ExamRecord examRecord = new ExamRecord();
                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                examRecord.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                exam.setExamRecord(examRecord);

                SignUp signUp = new SignUp();
                signUp.setId(r.getValue(SIGNUP.ID));
                signUp.setStatus(r.getValue(SIGNUP.STATUS));
                exam.setSignUp(signUp);

                return exam;
            }).collect(Collectors.toList());
        });
    }


    /**
     * 合并普通与测试的考试类型 （App接口）
     *
     * @param type 条件构造查询 type
     * @return 条件构造器
     */
    private Condition mergingOrdinaryAndTest(Optional<Integer> type){
        Condition condition = trueCondition();
        if(type.isPresent() && Exam.EXAM_OFFICIAL_TYPE.equals(type.get())
                || type.isPresent() && Exam.EXAM_UN_OFFICIAL_TYPE.equals(type.get())){
            condition=EXAM.TYPE.in(Arrays.asList(Exam.EXAM_OFFICIAL_TYPE,Exam.EXAM_UN_OFFICIAL_TYPE));
        }

        return condition;
    }




    @Override
    @DataSource
    public List<Exam> findBasicByIdsByMemberId(Integer examRegion, String[] ids, String memberId) {
        if (ids == null || ids.length == 0) {
            return new ArrayList<>();
        }
        List<Exam> examList = new ArrayList<Exam>();
        for (String id : ids) {
            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(id));
            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(id));
            List<Exam> elist = examDao.execute(e -> {
                return e.select(
                                Fields.start()
                                        .add(EXAM)
                                        .add(examRegistTable.field("f_exam_times", Integer.class))
                                        .add(PAPER_CLASS.ID)
                                        .add(PAPER_CLASS.TYPE)
                                        .add(examRecordTable.field("f_id", String.class))
                                        .add(examRecordTable.field("f_status", Integer.class))
                                        .add(examRecordTable.field("f_score", Integer.class))
                                        .end())
                        .from(EXAM)
                        .leftJoin(examRegistTable).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID), examRegistTable.field("f_member_id", String.class).eq(memberId))
                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
                        .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                        .where(EXAM.ID.eq(id)).fetch(r -> {
                            Exam exam = r.into(Exam.class);
                            PaperClass paperClass = new PaperClass();
                            paperClass.setId(r.getValue(PAPER_CLASS.ID));
                            paperClass.setType(r.getValue(PAPER_CLASS.TYPE));
                            exam.setPaperClass(paperClass);
                            ExamRecord examRecord = new ExamRecord();
                            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                            exam.setExamRecord(examRecord);
                            exam.setExamedTimes(examRecordService.calculateExamTimes(examRegion, exam.getId(), memberId));
                            return exam;
                        });
            });
            examList.addAll(elist);
        }
        return examList;
    }

    @Override
    @DataSource
    public Optional<Exam> getOptionalById(Integer examRegion, String id) {
       return examDao.getOptional(id);
    }


    @Override
    @DataSource
    public PagedResult<Exam> findAuthExamList(
            Integer examRegion,
            String organizationId,
            Integer page,
            Integer pageSize,
            Optional<String> proId,
            Optional<String> subProId,
            Optional<String> levelId,
            Optional<String> equipmentId,
            Optional<Integer> startTimeOrderBy,
            String memberId) {

        return examDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.selectDistinct(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.EXAM_BATCH)
                    .add(EXAM.NAME)
                    .add(EXAM.TYPE)
                    .add(EXAM.NEED_APPLICANT)
                    .add(EXAM.APPLICANT_NEED_AUDIT)
                    .add(EXAM.NEED_FILL_OUT_INFO)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.APPLICANT_START_TIME)
                    .add(EXAM.APPLICANT_END_TIME)
                    .add(EXAM.STATUS)
                    .add(EXAM.APPLICANT_NUMBER)
                    .add(PAPER_CLASS.ID)
                    .end());

            SelectSelectStep<Record> selectIdFiedld = e.selectDistinct(
                    Fields.start()
                            .add(EXAM.ID)
                            .end()
            );

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(organizationId).map(EXAM.ORGANIZATION_ID::eq),
                    Optional.of(Exam.EXAM_YES).map(EXAM.NEED_APPLICANT::eq), // 考试需要报名
                    proId.map(EXAM.PROFESSION_ID::eq),
                    subProId.map(EXAM.SUB_PROFESSION_ID::eq),
                    levelId.map(EXAM.LEVEL_ID::eq),
                    equipmentId.map(EXAM.EQUIPMENT_TYPE_ID::eq)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(EXAM)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
                        .where(condition)
                        .and(EXAM.APPLICANT_END_TIME.ge(System.currentTimeMillis())) // 当前时间可以报名
                        .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE).or(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))) // 集团级、省级认证考试
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 考试未撤销的
                        .and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId));
                return select;
            };

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            int count = stepFunc.apply(selectIdFiedld).fetch().size();

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setNeedFillOutInfo(r.getValue(EXAM.NEED_FILL_OUT_INFO));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setApplicantNumber(r.getValue(EXAM.APPLICANT_NUMBER));

                SignUp signUp = signUpDao.execute(x -> {
                    return x.select(Fields.start()
                                    .add(SIGNUP.ID)
                                    .add(SIGNUP.STATUS)
                                    .add(SIGNUP.MEMBER_ID)
                                    .end())
                            .from(SIGNUP)
                            .where(SIGNUP.EXAM_ID.eq(exam.getId())).and(SIGNUP.MEMBER_ID.eq(memberId)).fetchOneInto(SignUp.class);
                });

                TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(exam.getId()));

                List<String> examRecordList = examRecordDao.execute(x -> x.select(
                                Fields.start()
                                        .add(examRecordTable.field("f_id", String.class)).end())
                        .from(examRecordTable)
                        .where(examRecordTable.field("f_exam_id", String.class).eq(exam.getId()))
                        .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
                        .fetch(examRecordTable.field("f_id", String.class))
                );

                if (examRecordList != null && examRecordList.size() > 0) {
                    if (signUp == null) {
                        exam.setHaveExamTimes(Exam.EXAM_TIMES_YES);
                    }else{
                        exam.setHaveExamTimes(Exam.EXAM_TIMES_NO);
                    }
                }else{
                    exam.setHaveExamTimes(Exam.EXAM_TIMES_NO);
                }
                exam.setSignUp(signUp);
                String paperClassId = r.getValue(PAPER_CLASS.ID);
                if (paperClassId != null) {
                    PaperClass paperClass = new PaperClass();
                    paperClass.setId(paperClassId);
                    exam.setPaperClass(paperClass);
                }
                ExamRecord examRecord = new ExamRecord();
                examRecord.setCurrentTime(System.currentTimeMillis());
                exam.setExamRecord(examRecord);
                return exam;
            }).collect(Collectors.toList()));

        });
    }



    @Override
    @DataSource
    public PagedResult<Exam> findTicketExamList(Integer examRegion, String organizationId, Integer page, Integer pageSize,
                                                Optional<Integer> startTimeOrderBy, String memberId, Optional<String> year) {

        TableImpl<?> table = getTableUtil.getExamRegistTable(year.isPresent() ? (ExamRegist.STRING_EXAM_REGIST+"_"+year.get()) : ExamRegist.STRING_EXAM_REGIST);

        return examDao.execute(e -> {

            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(EXAM.ID).end());

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.EXAM_BATCH)
                    .add(EXAM.STATUS)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.PASS_SCORE)
                    .add(PAPER_CLASS.TOTAL_SCORE)
                    .end());

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(organizationId).map(EXAM.ORGANIZATION_ID::eq)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

//            1  需要报名的认证考试，并且.考生报名成功；或者不需要报名，但是推送到个人中心的考试
//            2：考试需要准考证
//            3：考试未开始
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(table)
                        .leftJoin(EXAM).on(table.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(EXAM.ID).and(SIGNUP.MEMBER_ID.eq(memberId)))
                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
                        .where(condition)
                        .and(table.field("f_member_id", String.class).eq(memberId))
                        .and(EXAM.ADMISSION_TICKET.eq(Exam.ADMISSION_TICKET_YES))
                        .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE).or(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE)))
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 考试未撤销
                        .and(EXAM.START_TIME.gt(System.currentTimeMillis())) // 考试未开始
                        .and(
                                (EXAM.NEED_APPLICANT.eq(Exam.EXAM_NEED_APPLICANT_YES).and(SIGNUP.STATUS.eq(SignUp.STATUS_PASSED)))
                                        .or(EXAM.NEED_APPLICANT.eq(Exam.EXAM_NEED_APPLICANT_NO).and(EXAM.SEND_TO_CENTER.eq(Exam.EXAM_YES)))
                        );
                return select;
            };

            int count = e.select(DSL.count()).from(stepFunc.apply(countField)).fetchOne(DSL.count());

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                PaperClass paperClass = new PaperClass();
                paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                exam.setPaperClass(paperClass);
                return exam;
            }).collect(Collectors.toList()));
        });
    }




    @Override
    @DataSource
    public Exam findTicketDetail(Integer examRegion, String examId, String memberId) {

        Exam exam = examDao.execute(e -> {
            return e.select(Fields.start()
                            .add(EXAM)
                            .end()
                    ).from(EXAM)
                    .where(EXAM.ID.eq(examId))
                    .fetchOneInto(Exam.class);
        });

        // 报名id
        List<SignUp> signUpList = signUpDao.execute(s -> {
            return s.select(Fields.start()
                            .add(SIGNUP.ID)
                            .end()
                    ).from(SIGNUP)
                    .where(SIGNUP.EXAM_ID.eq(examId))
                    .and(SIGNUP.MEMBER_ID.eq(memberId))
                    .fetchInto(SignUp.class);
        });

        SignUpAuth signUpAuth = new SignUpAuth();
        if (signUpList != null && signUpList.size() > 0) {
            // 报名认证信息
            signUpAuth = signUpAuthDao.execute(e -> {
                return e.select(Fields.start()
                                .add(SIGN_UP_AUTH)
                                .end()
                        )
                        .from(SIGN_UP_AUTH)
                        .where(SIGN_UP_AUTH.MEMBER_ID.eq(memberId))
                        .and(SIGN_UP_AUTH.EXAM_ID.eq(examId))
                        .and(SIGN_UP_AUTH.SIGN_UP_ID.eq(signUpList.get(0).getId()))
                        .fetchOneInto(SignUpAuth.class);
            });
        }
        //基本信息
        Member member = memberDao.execute(e -> {
            return  e.select(Fields.start()
                            .add(MEMBER)
                            .end()
                    ).from(MEMBER)
                    .where(MEMBER.ID.eq(memberId))
                    .fetchOneInto(Member.class);
        });
        //专业
        Optional<Profession> profession = professionDao.getOptional(exam.getProfessionId());
        //子专业
        Optional<Profession> subProfession = professionDao.getOptional(exam.getSubProfessionId());
        //设备型号
        Optional<EquipmentType> equipmentType = equipmentTypeDao.getOptional(exam.getEquipmentTypeId());
        //等级
        Optional<ProfessionLevel> level = professionLevelDao.getOptional(exam.getLevelId());
        //监考老师
//        List<Member> invigilatorList = invigilatorDao.execute(e -> {
//            SelectSelectStep<Record> selectListField = e.select(Fields.start()
//                    .add(MEMBER)
//                    .end());
//            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
//                SelectConditionStep<Record> select = a.from(INVIGILATOR)
//                        .leftJoin(MEMBER).on(MEMBER.ID.eq(INVIGILATOR.MEMBER_ID))
//                        .where(INVIGILATOR.EXAM_ID.eq(examId));
//                return select;
//            };
//            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
//              return listSetp.fetch().stream().map(r -> {
//                 Member member2 = r.into(Member.class);
//                  return member2;
//              }).collect(Collectors.toList());
//        });
        // 部门
        Optional<Organization> organization = null;
        if (member != null) {
            organization = organizationDao.getOptional(member.getOrganizationId());
        }
        Optional<Organization> company = null;
        if (organization.isPresent()) {
            // 公司
            company = organizationDao.getOptional(organization.get().getCompanyId());
        }

        // 准考证
        ExamRegist examRegist = new ExamRegist();

        String startTimeCode = getExamStartTimeFormmatForYYYYMMDD(exam.getStartTime());

        // 员工编号
        String name = member.getName();

        // 普通考试
        if (isOrdinaryExam(exam.getType())) {

            // 普通考试无专业、子专业、等级，则准考证编码规则为：
            // 进入考试开始时间日期（具体到年月日，eg：20171106）+员工编号
            examRegist.setTicket(startTimeCode + name);

        } else {

            // 认证考试
            // 准考证编码规则：专业编号+子专业编号+等级+进入考试开始时间日期（具体到年月日，eg：20171106）+员工编号
            String professionCode = "";
            String subProfessionCode = "";
            String levelCode = "";

            // 专业编号
            if (profession.isPresent()){
                professionCode = profession.get().getCode();
            }

            // 子专业编号
            if (subProfession.isPresent()){
                subProfessionCode = subProfession.get().getCode();
            }

            // 等级
            if (level.isPresent()){
                levelCode = level.get().getLevel()+"";
            }

            examRegist.setTicket(professionCode + subProfessionCode + levelCode + startTimeCode + name);
        }

        exam.setSignUpAuth(signUpAuth);
        exam.setMember(member);
        profession.ifPresent(x->{
            exam.setProfession(x);
        });
        subProfession.ifPresent(x->{
            exam.setSubProfession(x);
        });
        level.ifPresent(x->{
            exam.setLevel(x);
        });
        equipmentType.ifPresent(x->{
            exam.setEquipmentType(x);
        });
//        exam.setInvigilatorList(invigilatorList);
        organization.ifPresent(x ->{
            exam.setOrganization(x);
        });
        company.ifPresent(x ->{
            exam.setCompany(x);
        });
        exam.setExamRegist(examRegist);
        return exam;
    }

    private String getExamStartTimeFormmatForYYYYMMDD(Long startTime) {
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        String start = formateDate(startTime);
        return p.matcher(start).replaceAll("").trim();
    }

    private String formateDate(Long date) {
        SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd");
        String d = format.format(date);
        return d;
    }

    private boolean isOrdinaryExam(Integer type) {
        if (type == null) return true;
        return Exam.EXAM_OFFICIAL_TYPE.intValue() == type || Exam.EXAM_UN_OFFICIAL_TYPE.intValue() == type;
    }




    @Override
    @DataSource
    public PagedResult<Exam> findMyExamList(Integer examRegion, String organizationId, Integer page, Integer pageSize,
                                            Optional<Integer> startTimeOrderBy, String currentUserId, Optional<String> year) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(year.isPresent() ? (ExamRegist.STRING_EXAM_REGIST+"_"+year.get()) : ExamRegist.STRING_EXAM_REGIST);

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(year.isPresent() ? (ExamRecord.STRING_EXAM_RECORD+"_"+year.get()) : ExamRecord.STRING_EXAM_RECORD);

        return examRegistDao.execute(e -> {

            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(examRegistTable.field("f_id", String.class)).end());

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.EXAM_BATCH)
                    .add(EXAM.STATUS)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.PASS_SCORE)
                    .add(PAPER_CLASS.ID)
                    .add(PAPER_CLASS.TOTAL_SCORE)
                    .add(examRecordTable.field("f_status", Integer.class))
                    .add(SIGNUP.STATUS)
                    .end());

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(System.currentTimeMillis()).map(EXAM.START_TIME::le),
                    Optional.of(System.currentTimeMillis()).map((EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)))::ge)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .innerJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                        .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
                        .leftJoin(examRecordTable).on(
                                examRecordTable.field("f_exam_id", String.class).eq(examRegistTable.field("f_exam_id", String.class))
                                        .and(examRecordTable.field("f_member_id", String.class).eq(examRegistTable.field("f_member_id", String.class)))
                                        .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)))
                        .leftJoin(SIGNUP).on(
                                SIGNUP.EXAM_ID.eq(EXAM.ID).
                                        and(SIGNUP.MEMBER_ID.eq(examRegistTable.field("f_member_id", String.class))).and(SIGNUP.STATUS.notEqual(SignUp.STATUS_REFUSE)))
                        .where(condition)
                        .and(examRegistTable.field("f_member_id", String.class).eq(currentUserId))
                        .and(examRegistTable.field("f_status", Integer.class).notEqual(ExamRegist.STATUS_BE_REFUSE).or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_OVER)))
                        .and(examRecordTable.field("f_status", Integer.class).in(ExamRecord.STATUS_TO_BE_OVER,ExamRecord.STATUS_TO_BE_STARTED,ExamRecord.STATUS_DOING,ExamRecord.STATUS_TIME_EXCEPTION))
                        .and(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE,Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)) // 考试未撤销
                        .and(EXAM.ORGANIZATION_ID.eq(organizationId));

                return select;
            };


            int count = e.select(DSL.count()).from(stepFunc.apply(countField)).fetchOne(DSL.count());

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));

                ExamRecord examRecord = new ExamRecord();
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                exam.setExamRecord(examRecord);

                PaperClass paperClass = new PaperClass();
                paperClass.setId(r.getValue(PAPER_CLASS.ID));
                paperClass.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
                exam.setPaperClass(paperClass);

                SignUp signUp=new SignUp();
                signUp.setStatus(r.getValue(SIGNUP.STATUS));
                exam.setSignUp(signUp);
                return exam;
            }).collect(Collectors.toList()));
        });

    }



    @Override
    @DataSource
    public PagedResult<Exam> findAuditList(Integer examRegion, String organizationId, Integer page, Integer pageSize,
                                           Optional<Integer> startTimeOrderBy, String currentUserId) {

        return examDao.execute(e -> {

            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(EXAM.ID).end());

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.EXAM_BATCH)
                    .add(EXAM.STATUS)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.AUDIT_START_TIME)
                    .add(EXAM.AUDIT_END_TIME)
                    .add(EXAM.PRE_APPROVAL)
                    .add(SIGNUP.ID)
                    .add(SIGNUP.STATUS)
                    .end());

            Stream<Optional<Condition>> conditions = Stream.of(
                    Optional.of(organizationId).map(EXAM.ORGANIZATION_ID::eq)
            );

            Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(EXAM)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .leftJoin(SIGNUP).on(SIGNUP.EXAM_ID.eq(EXAM.ID))
                        .where(condition).and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                        .and(SIGNUP.MEMBER_ID.eq(currentUserId));
                return select;
            };

            int count = e.select(DSL.count()).from(stepFunc.apply(countField)).fetchOne(DSL.count());

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                Exam exam = r.into(Exam.class);
                SignUp signUp = r.into(SignUp.class);
                exam.setSignUp(signUp);
                return exam;
            }).collect(Collectors.toList()));
        });
    }



    @Override
    @DataSource
    public List<Exam> findExamDay(Integer examRegion, String organizationId, Long dayStartTime, Long dayEndTime, String currentUserId) {

        return  examDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.selectDistinct(Fields.start()
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.TYPE)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.DURATION)
                    .add(EXAM.NEED_APPLICANT)
                    .add(EXAM.APPLICANT_NEED_AUDIT)
                    .add(EXAM.APPLICANT_START_TIME)
                    .add(EXAM.APPLICANT_END_TIME)
                    .add(EXAM.NEED_FILL_OUT_INFO)
                    .add(PAPER_CLASS.ID)
                    .end());

            Stream<Condition> conditions = Stream.of(
                    EXAM.ORGANIZATION_ID.eq(organizationId)
            );

            Condition condition = conditions.reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(EXAM)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.TARGET_ID.eq(EXAM.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
                        .where(condition)
                        .and(EXAM.STATUS.in(Exam.STATUS_NOT_START,Exam.STATUS_SIGNUPING,Exam.STATUS_STARTING,Exam.STATUS_END)
                        )
                        .and(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE).or(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))) // 集团级、省级认证考试
                        .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                        .and(
                                // 判断今天和考试的时间有交叉的考试
                                (EXAM.START_TIME.le(dayStartTime).and(EXAM.END_TIME.gt(dayStartTime)))
                                        .or(EXAM.START_TIME.lt(dayStartTime).and(EXAM.END_TIME.ge(dayStartTime)))
                                        .or(EXAM.START_TIME.ge(dayStartTime).and(EXAM.START_TIME.lt(dayEndTime)))
                                        .or(EXAM.START_TIME.gt(dayStartTime).and(EXAM.START_TIME.le(dayEndTime)))
                        );
                return select;
            };

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            Result<Record> record = listSetp.orderBy(EXAM.START_TIME.asc()).fetch();

            return record.stream().map(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setDuration(r.getValue(EXAM.DURATION));
                exam.setNeedApplicant(r.getValue(EXAM.NEED_APPLICANT));
                exam.setApplicantNeedAudit(r.getValue(EXAM.APPLICANT_NEED_AUDIT));
                exam.setApplicantStartTime(r.getValue(EXAM.APPLICANT_START_TIME));
                exam.setApplicantEndTime(r.getValue(EXAM.APPLICANT_END_TIME));
                exam.setNeedFillOutInfo(r.getValue(EXAM.NEED_FILL_OUT_INFO));

                SignUp signUp = signUpDao.execute(x -> {
                    return x.select(Fields.start()
                                    .add(SIGNUP.ID)
                                    .add(SIGNUP.STATUS)
                                    .add(SIGNUP.MEMBER_ID)
                                    .end())
                            .from(SIGNUP)
                            .where(SIGNUP.EXAM_ID.eq(exam.getId())).and(SIGNUP.MEMBER_ID.eq(currentUserId)).fetchOneInto(SignUp.class);
                });

                TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(exam.getId()));

                List<String> examRecordList = examRecordDao.execute(x -> x.select(
                                Fields.start()
                                        .add(examRecordTable.field("f_id", String.class)).end())
                        .from(examRecordTable)
                        .where(examRecordTable.field("f_exam_id", String.class).eq(exam.getId()))
                        .and(examRecordTable.field("f_member_id", String.class).eq(currentUserId))
                        .fetch(examRecordTable.field("f_id", String.class))
                );
                if (examRecordList != null && examRecordList.size() > 0) {
                    exam.setHaveExamTimes(Exam.EXAM_TIMES_YES);
                }else{
                    exam.setHaveExamTimes(Exam.EXAM_TIMES_NO);
                }
                exam.setSignUp(signUp);
                String paperClassId = r.getValue(PAPER_CLASS.ID);
                if (paperClassId != null) {
                    PaperClass paperClass = new PaperClass();
                    paperClass.setId(paperClassId);
                    exam.setPaperClass(paperClass);
                }
                ExamRecord examRecord = new ExamRecord();
                examRecord.setCurrentTime(System.currentTimeMillis());
                exam.setExamRecord(examRecord);
                return exam;
            }).collect(Collectors.toList());

        });

    }



    @Override
    @DataSource
    public PagedResult<Exam> findMyScoreList(Integer examRegion, String organizationId, Integer page, Integer pageSize,
                                             Optional<Integer> startTimeOrderBy, String currentUserId, Optional<String> year) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(year.isPresent() ? (ExamRegist.STRING_EXAM_REGIST+"_"+year.get()) : ExamRegist.STRING_EXAM_REGIST);

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(year.isPresent() ? (ExamRecord.STRING_EXAM_RECORD+"_"+year.get()) : ExamRecord.STRING_EXAM_RECORD);

        return examRecordDao.execute(e -> {

            SelectSelectStep<Record> countField = e.select(Fields.start()
                    .add(examRegistTable.field("f_id", String.class)).end());

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(examRecordTable.field("f_id", String.class))
                    .add(examRecordTable.field("f_start_time", Long.class))
                    .add(examRecordTable.field("f_end_time", Long.class))
                    .add(examRecordTable.field("f_last_submit_time", Long.class))
                    .add(examRecordTable.field("f_score", Integer.class))
                    .add(examRecordTable.field("f_status", Integer.class))
                    .add(examRegistTable.field("f_pass_status", Integer.class))
                    .add(EXAM.ID)
                    .add(EXAM.ALLOW_EXAM_TIMES)
                    .add(EXAM.NAME)
                    .add(EXAM.EXAM_BATCH)
                    .add(EXAM.START_TIME)
                    .add(EXAM.END_TIME)
                    .add(EXAM.DURATION)
                    .add(EXAM.STATUS)
                    .add(EXAM.PASS_SCORE)
                    .add(EXAM.SHOW_ANSWER_RULE)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .add(PAPER_CLASS.ID)
                    .add(PAPER_CLASS.TOTAL_SCORE)
                    .end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(examRegistTable)
                        .innerJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                        .innerJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                        .innerJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
                        .where(EXAM.ORGANIZATION_ID.eq(organizationId))
                        .and(examRecordTable.field("f_member_id", String.class).eq(currentUserId))
                        .and(examRegistTable.field("f_member_id", String.class).eq(currentUserId));
                return select;
            };

            int count = e.select(DSL.count()).from(stepFunc.apply(countField)).fetchOne(DSL.count());

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);

            if (startTimeOrderBy.isPresent() && startTimeOrderBy.get() == 1) {
                listSetp.orderBy(EXAM.START_TIME.asc());
            } else {
                listSetp.orderBy(EXAM.START_TIME.desc());
            }

            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {

                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setAllowExamTimes(r.getValue(EXAM.ALLOW_EXAM_TIMES));
                exam.setName(r.getValue(EXAM.NAME));
                exam.setExamBatch(r.getValue(EXAM.EXAM_BATCH));
                exam.setStartTime(r.getValue(EXAM.START_TIME));
                exam.setEndTime(r.getValue(EXAM.END_TIME));
                exam.setDuration(r.getValue(EXAM.DURATION));
                exam.setStatus(r.getValue(EXAM.STATUS));
                exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
                exam.setShowAnswerRule(r.getValue(EXAM.SHOW_ANSWER_RULE));
                exam.setShowScoreTime(r.getValue(EXAM.SHOW_SCORE_TIME));

                ExamRecord examRecord = new ExamRecord();
                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                Integer score = r.getValue(examRecordTable.field("f_score", Integer.class));
                if (score == null) {
                    score = 0;
                }
                Integer totalScore = r.getValue(PAPER_CLASS.TOTAL_SCORE);
                if (totalScore == null) {
                    totalScore = 0;
                }
                examRecord.setScore(score);
                PaperClass paperClass = new PaperClass();
                paperClass.setId(r.getValue(PAPER_CLASS.ID));
                paperClass.setTotalScore(totalScore);

                exam.setExamRecord(examRecord);
                exam.setPaperClass(paperClass);

                ExamRegist examRegist = new ExamRegist();
                examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                exam.setExamRegist(examRegist);
                return exam;
            }).collect(Collectors.toList()));
        });
    }

    /**
     * 构造个人中心-我的考试（课程/专题/班级）的查询数据源
     *
     * @param year 条件构造查询入参(year)
     * @return 数据源Map集合
     */
    private String constructDataSource(Optional<String> year, String sourceName){
        return year.map(ew1->sourceName+ "_" + ew1)
                .orElse(sourceName);
    }


    /**
     * 个人中心 -考试- 查询状态
     *
     * @param searchStatus 查询状态
     * @param examRecordTable 考试记录表
     * @return 条件构造
     */
    private Optional<Condition> getSearchStatus(Optional<Integer> searchStatus, TableImpl<?> examRecordTable) {
        return searchStatus.map(t -> {
            // 设置当前时间
            Long currentTime = System.currentTimeMillis();

            //进行中：进入考试开始时间<当前时间<（进入考试截止时间+考试时长）并且考试状态为未开始（定时器未及时触发）或进行中
            if (t == Exam.WAIT_EXAM_PERSON_CENTER_STATUS) {
                return Optional.of(EXAM.START_TIME.lt(currentTime)
                        .and(EXAM.END_TIME.plus(EXAM.DURATION.multiply(60000)).gt(currentTime))
                        .and(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED)
                                .or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_DOING))
                        )
                );

                //未开始 考试开始时间小于当前时间 且 （报名表 数据为空 or  报名表状态 不等于 待审核）
            } else if (t == Exam.WAIT_START_PERSON_CENTER_STATUS){
                return Optional.of(EXAM.START_TIME.gt(currentTime)
                        .and(SIGNUP.ID.isNull().or(SIGNUP.STATUS.ne(SignUp.STATUS_APPROVE))));

                // 审核中 报名表状态=审核中
            } else if (t == Exam.WAIT_APPROVE_PERSON_CENTER_STATUS){
                return Optional.of(SIGNUP.STATUS.eq(SignUp.STATUS_APPROVE));

                //已完成 考试记录表中状态非异常
            } else if (t == Exam.FINISHED_PERSON_CENTER_STATUS){
                return Optional.of(examRecordTable.field("f_status", Integer.class).ge(ExamRecord.STATUS_TIME_EXCEPTION));

            }
            return Optional.of(DSL.trueCondition());
        }).orElse(Optional.of(DSL.trueCondition()));
    }




}

