package com.zxy.product.examstu.service.component.sequence;

/**
 * <p>数字与字母互转，类似于Excel列号格式：A-Z,AA-ZZ,AAA-ZZZ...</p>
 * <ul>
 *    <li>1-26对应为A-Z</li>
 *    <li>27对应为AA，53对应为BA，依此类推</li>
 * </ul>
 * <AUTHOR>
 * @date 2017年11月15日
 */
public class AlphaNumUtil {

    /**
     * 字母转换为数字
     * <ul>
     *    <li>A-Z对应为1-26</li>
     *    <li>AA对应为27，BA对应为53，依此类推</li>
     * </ul>
     * @param alpha 要转换的字母
     */
    public static int alphaToNum(String alpha, int length) {
        int num = 0;
        int result = 0;
        for(int i = 0; i < length; i++) {
            char ch = alpha.charAt(length - i - 1);
            num = (int)(ch - 'A' + 1) ;
            num *= Math.pow(26, i);
            result += num;
        }
        return result;
    }

    /**
     * 数字转换为字母：A-Z,AA-ZZ,AAA-ZZZ...
     * <ul>
     *    <li>1-26对应为A-Z</li>
     *    <li>27对应为AA，53为BA，依此类推</li>
     * </ul>
     * @param num 要转换的数字
     * @return
     */
    public static String numToAlpha(int num) {
        if (num <= 0) {
            return null;
        }
        String alpha = "";
        num--;
        do {
            if (alpha.length() > 0) {
                num--;
            }
            alpha = ((char) (num % 26 + (int) 'A')) + alpha;
            num = (int) ((num - num % 26) / 26);
        } while (num > 0);
        return alpha;
    }


   /* public static void main(String[] args) {
        String codeStr = "AA";
        int sequence = alphaToNum(codeStr, codeStr.length());
        System.out.println("'" + codeStr + "' ：" + sequence);

        sequence = 703;
        codeStr = numToAlpha(sequence);
        System.out.println(sequence + " ：" + codeStr);

        codeStr = "ZZ";
        sequence = alphaToNum(codeStr, codeStr.length());
        System.out.println("'" + codeStr + "' ：" + sequence);

        sequence = 466948;
        codeStr = numToAlpha(sequence);
        System.out.println(sequence + " ：" + codeStr);
    }*/

}

