package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordProcessService;
import com.zxy.product.exam.entity.AnswerRecordProcess;

import com.zxy.product.exam.entity.QuestionCopy;
import org.jooq.*;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class AnswerRecordProcessSupport implements AnswerRecordProcessService {
    private final static Logger LOGGER = LoggerFactory.getLogger(AnswerRecordProcessSupport.class);

    private CommonDao<AnswerRecordProcess> answerRecordProcessDao;

    static TableImpl<?>[] tables;

    static {
        tables = new TableImpl<?>[]{
                ANSWER_RECORD_PROCESS_0,
                ANSWER_RECORD_PROCESS_1,
                ANSWER_RECORD_PROCESS_2,
                ANSWER_RECORD_PROCESS_3,
                ANSWER_RECORD_PROCESS_4,
                ANSWER_RECORD_PROCESS_5,
                ANSWER_RECORD_PROCESS_6,
                ANSWER_RECORD_PROCESS_7,
                ANSWER_RECORD_PROCESS_8,
                ANSWER_RECORD_PROCESS_9
        };
    }

    @Autowired
    public void setAnswerRecordProcessDao(CommonDao<AnswerRecordProcess> answerRecordProcessDao) {
        this.answerRecordProcessDao = answerRecordProcessDao;
    }

    @Override
    @DataSource
    public List<String> answerSubmit(Integer examRegion, String memberId, List<AnswerRecordProcess> answerRecordList) {
        TableImpl<?> targetTable = getTargetTable(memberId);

        List<AnswerRecordProcess> insertList = new ArrayList<>();
        List<AnswerRecordProcess> updateList = new ArrayList<>();
        List<AnswerRecordProcess> deleteList = new ArrayList<>();

        List<String> successQuestionId = new ArrayList<>();

        dataClassification(answerRecordList, deleteList, insertList, updateList);

        deleteEmptyAnswer(deleteList, targetTable, successQuestionId);

        insert(insertList, targetTable,successQuestionId);

        update(updateList, targetTable,successQuestionId);

        return successQuestionId;
    }

    @Override
    @DataSource
    public Integer answerRecordCount(Integer examRegion, String memberId, String examRecordId) {
        TableImpl<?> targetTable = getTargetTable(memberId);
        return answerRecordProcessDao.execute(dsl -> dsl.selectCount().from(targetTable)
                                                        .where(targetTable.field("f_exam_record_id", String.class).eq(examRecordId)))
                                     .fetchOne()
                                     .getValue(0, Integer.class);

    }

    @Override
    @DataSource
    public Integer fullAnswerSubmit(Integer examRegion, String memberId, String examRecordId,List<AnswerRecordProcess> fullAnswerRecordList) {
        TableImpl<?> targetTable = getTargetTable(memberId);

        List<AnswerRecordProcess> answerRecordProcessList = getListByExamRecordId(examRegion, memberId,examRecordId, targetTable);

        Map<String, AnswerRecordProcess> fullAnswerMap = convertToAnswerRecordMap(examRecordId,fullAnswerRecordList);

        Map<String, AnswerRecordProcess> existingAnswerMap = convertToAnswerRecordMap(examRecordId,answerRecordProcessList);

        // 需要插入的记录
        List<AnswerRecordProcess> recordsToInsert = filterRecordToInsertOrDelete(fullAnswerRecordList, existingAnswerMap);

        // 需要删除的记录
        List<AnswerRecordProcess> recordsToDelete = filterRecordToInsertOrDelete(answerRecordProcessList, fullAnswerMap);

        // 需要更新的记录
        List<AnswerRecordProcess> recordsToUpdate = findRecordToUpdate(fullAnswerRecordList, existingAnswerMap);

        for (AnswerRecordProcess record : recordsToInsert){
            answerRecordProcessDao.execute(dsl -> insert(targetTable,record,dsl));
        }

        for (AnswerRecordProcess record : recordsToDelete){
            answerRecordProcessDao.execute(dsl -> delete(targetTable,dsl,record));
        }

        for (AnswerRecordProcess record : recordsToUpdate){
            answerRecordProcessDao.execute(dsl -> updated(dsl,record,targetTable));
        }
        return 0;
    }

    private List<AnswerRecordProcess> findRecordToUpdate(List<AnswerRecordProcess> fullAnswerRecordList, Map<String, AnswerRecordProcess> existingAnswerMap) {
        return fullAnswerRecordList.stream()
                                   .filter(record -> {
                                       String key = generateKey(record.getExamRecordId(), record.getQuestionId());
                                       AnswerRecordProcess existing = existingAnswerMap.get(key);
                                       return existing != null && !Objects.equals(existing.getAnswer(), record.getAnswer());
                                   }).collect(Collectors.toList());
    }

    private List<AnswerRecordProcess> filterRecordToInsertOrDelete(List<AnswerRecordProcess> source, Map<String, AnswerRecordProcess> targetMap) {
        return source.stream()
                                   .filter(record -> !targetMap.containsKey(generateKey(record.getExamRecordId(), record.getQuestionId())))
                                   .collect(Collectors.toList());
    }

    private Map<String, AnswerRecordProcess> convertToAnswerRecordMap(String examRecordId,List<AnswerRecordProcess> records) {
        return records.stream()
                      .collect(Collectors.toMap(
                              record -> generateKey(examRecordId, record.getQuestionId()),
                              Function.identity(),
                              (k1, k2) -> k1));
    }

    private String generateKey(String examRecordId, String questionId) {
        return examRecordId + "_" + questionId;
    }
    @Override
    @DataSource
    public List<AnswerRecordProcess> getListByExamRecordId(Integer examRegion, String memberId, String examRecordId, TableImpl<?> targetTable) {
        TableImpl<?> resolvedTargetTable = Optional.ofNullable(targetTable).orElseGet(() -> (TableImpl) getTargetTable(memberId));

        return answerRecordProcessDao.execute(dsl -> dsl.select(resolvedTargetTable.field("f_question_id", String.class),
                                                                resolvedTargetTable.field("f_answer", String.class),
                                                                QUESTION_COPY.TYPE))
                                     .from(resolvedTargetTable)
                                     .leftJoin(QUESTION_COPY)
                                        .on(QUESTION_COPY.ID.eq(resolvedTargetTable.field("f_question_id", String.class)))
                                     .where(resolvedTargetTable.field("f_exam_record_id", String.class).eq(examRecordId))
                                     .fetch(r -> {
                                         AnswerRecordProcess arp = new AnswerRecordProcess();
                                         arp.setAnswer(r.getValue(resolvedTargetTable.field("f_answer", String.class)));
                                         arp.setQuestionId(r.getValue(resolvedTargetTable.field("f_question_id", String.class)));
                                         QuestionCopy q = new QuestionCopy();
                                         q.setType(r.getValue(QUESTION_COPY.TYPE));

                                         arp.setQuestionCopy(q);
                                         return arp;
                                     });
    }

    @Override
    @DataSource
    public void cleanLastWeekData(Integer examRegion) {
        long lastWeekDate = LocalDateTime.now()
                                         .minusWeeks(1)
                                         .atZone(ZoneId.systemDefault())
                                         .toInstant()
                                         .toEpochMilli();

        LOGGER.info("考试流水表 开始删除一周前的数据 {}", lastWeekDate);

        Arrays.stream(tables).forEach(targetTable -> {

            int batchSize = 100;
            int currentBatchSize;

            do {
                List<Long> expiredIds = answerRecordProcessDao.execute(dsl -> dsl.select(targetTable.field("id", Long.class))
                                                                                 .from(targetTable)
                                                                                 .where(targetTable.field("f_create_time", Long.class).lt(lastWeekDate))
                                                                                 .limit(batchSize)
                                                                                 .fetch(targetTable.field("id", Long.class)));


                currentBatchSize = expiredIds.size();

                if (currentBatchSize > 0){
                    answerRecordProcessDao.execute(dsl -> dsl.delete(targetTable).where(targetTable.field("id", Long.class).in(expiredIds)).execute());
                }

            } while (batchSize == currentBatchSize);
        });
    }

    private void dataClassification(List<AnswerRecordProcess> answerRecordList, List<AnswerRecordProcess> deleteList, List<AnswerRecordProcess> insertList, List<AnswerRecordProcess> updateList) {
        answerRecordList.forEach(answerRecord ->{
            // 过滤表情符号
            answerRecord.setAnswer(filterEmoji(answerRecord.getAnswer()));
            if (answerRecord.getAnswer().trim().isEmpty()){
                deleteList.add(answerRecord);
            } else if (answerRecord.getHandleFlag().equals(AnswerRecordProcess.HANDLE_FLAG_INSERT)){
                insertList.add(answerRecord);
            } else if (answerRecord.getHandleFlag().equals(AnswerRecordProcess.HANDLE_FLAG_UPDATE)){
                updateList.add(answerRecord);
            }
        });
    }

    public String filterEmoji(String source) {
        if (StringUtils.isNotEmpty(source)) {
            return source.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "*");
        } else {
            return source;
        }
    }

    private void update(List<AnswerRecordProcess> updateList, TableImpl<?> targetTable, List<String> successQuestionId) {
        List<AnswerRecordProcess> failedRecord = new ArrayList<>();

        answerRecordProcessDao.execute(dsl -> {
            for (AnswerRecordProcess answerRecord : updateList) {
                int updated = updated(dsl, answerRecord, targetTable);

                if (updated == 0) {
                    failedRecord.add(answerRecord);
                } else {
                    successQuestionId.add(answerRecord.getQuestionId());
                }
            }
            int[] results = batchInsert(dsl, failedRecord, targetTable).execute();

            for (int i = 0; i < results.length; i++) {

                AnswerRecordProcess record = failedRecord.get(i);

                if (results[i] == 1) {
                    successQuestionId.add(record.getQuestionId());
                }
            }
            return null;
        });
    }

    private void insert(List<AnswerRecordProcess> insertList, TableImpl<?> targetTable, List<String> successQuestionId) {

        for (AnswerRecordProcess answerRecord : insertList) {
            TableRecord<?> record = (TableRecord<?>) targetTable.newRecord();
//            record.set(targetTable.field("f_exam_record_id", String.class), answerRecord.getExamRecordId());
//            record.set(targetTable.field("f_question_id", String.class), answerRecord.getQuestionId());
//            record.set(targetTable.field("f_answer", String.class), answerRecord.getAnswer());
//            record.set(targetTable.field("f_create_time",Long.class),System.currentTimeMillis());
            answerRecordProcessDao.execute(dsl -> {
                try {

                    int affectedNum = insert(targetTable, answerRecord, dsl);

                    if (affectedNum == 0) {
                        updated(dsl, answerRecord, targetTable);
                    } else {
                        successQuestionId.add(answerRecord.getQuestionId());
                    }
                    return null;
                } catch (DuplicateKeyException e) {
                    int affectedNum = updated(dsl, answerRecord, targetTable);

                    if (affectedNum == 1) {
                        successQuestionId.add(answerRecord.getQuestionId());
                    }
                    return null;
                }
            });
        }


    }

    private int insert(TableImpl<?> targetTable, AnswerRecordProcess answerRecord, DSLContext dsl) {
        return dsl.insertInto(targetTable, targetTable.field("f_exam_record_id", String.class),
                              targetTable.field("f_question_id", String.class),
                              targetTable.field("f_answer", String.class),
                              targetTable.field("f_create_time", Long.class))
                  .values(answerRecord.getExamRecordId(),
                          answerRecord.getQuestionId(),
                          answerRecord.getAnswer(),
                          System.currentTimeMillis()).execute();
    }

    private int updated(DSLContext dsl, AnswerRecordProcess answerRecord, TableImpl<?> targetTable) {
        return dsl.update(targetTable)
                  .set(targetTable.field("f_answer", String.class), answerRecord.getAnswer())
                  .where(
                          targetTable.field("f_exam_record_id", String.class).eq(answerRecord.getExamRecordId())
                                     .and(targetTable.field("f_question_id", String.class).eq(answerRecord.getQuestionId()))
                  ).execute();
    }

    private void handleFailedInsert(DSLContext dsl, int[] results, List<AnswerRecordProcess> insertList, TableImpl<?> targetTable,List<String> successQuestionId) {
        for (int i = 0; i < results.length; i++) {
            AnswerRecordProcess record = insertList.get(i);
            if (results[i] == 0) {
                updated(dsl, record, targetTable);
            }else{
                successQuestionId.add(record.getQuestionId());
            }
        }
    }

    private Batch batchInsert(DSLContext dsl, List<AnswerRecordProcess> insertList,  TableImpl<?> targetTable) {
        List<TableRecord<?>> records = new ArrayList<>();
        for (AnswerRecordProcess answerRecord : insertList) {
            TableRecord<?> record = (TableRecord<?>) targetTable.newRecord();
            record.set(targetTable.field("f_exam_record_id", String.class), answerRecord.getExamRecordId());
            record.set(targetTable.field("f_question_id", String.class), answerRecord.getQuestionId());
            record.set(targetTable.field("f_answer", String.class), answerRecord.getAnswer());
            record.set(targetTable.field("f_create_time", Long.class), System.currentTimeMillis());
            records.add(record);
        }
        return dsl.batchInsert(records);
    }

    private void deleteEmptyAnswer(List<AnswerRecordProcess> deleteList, TableImpl<?> targetTable,List<String> successQuestionId) {
        if (!deleteList.isEmpty()){
            answerRecordProcessDao.execute(dsl -> {
                for (AnswerRecordProcess answerRecordProcess : deleteList) {
                    int affectedNum = delete(targetTable, dsl, answerRecordProcess);

                    if (affectedNum == 1){
                        successQuestionId.add(answerRecordProcess.getQuestionId());
                    }
                }
                return null;
            });
        }
    }

    private int delete(TableImpl<?> targetTable, DSLContext dsl, AnswerRecordProcess answerRecordProcess) {
        return dsl.deleteFrom(targetTable)
                  .where(targetTable.field("f_exam_record_id", String.class).eq(answerRecordProcess.getExamRecordId()))
                  .and(targetTable.field("f_question_id", String.class).eq(answerRecordProcess.getQuestionId()))
                  .execute();
    }

    public static void main(String[] args) {
        System.out.println(Math.abs("883e064d-8caa-4ec6-b1ae-0f200de6bcb3".hashCode() % 10));
    }
    public TableImpl<?> getTargetTable(String memberId) {
        LOGGER.info(" getTargetTable memberId : {} " + memberId);
        int targetTable = Math.abs(memberId.hashCode() % 10);
        return tables[targetTable];
    }
}
