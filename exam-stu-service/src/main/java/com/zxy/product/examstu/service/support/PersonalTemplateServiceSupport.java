package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.PersonalTemplateService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.DesensitizationUtil;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class PersonalTemplateServiceSupport implements PersonalTemplateService {
	private CommonDao<PersonalTemplate> personalTemplateDao;
	private CommonDao<Member> memberDao;
	private CommonDao<Organization> organizationDao;
	private CommonDao<SignUp> signUpDao;
	private CommonDao<SignUpAuth> signUpAuthDao;




	@Autowired
	public void setSignUpAuthDao(CommonDao<SignUpAuth> signUpAuthDao) {
        this.signUpAuthDao = signUpAuthDao;
    }

    @Autowired
	public void setSignUpDao(CommonDao<SignUp> signUpDao) {
        this.signUpDao = signUpDao;
    }

    @Autowired
	public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }

    @Autowired
	public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
	public void setPersonalTemplateDao(CommonDao<PersonalTemplate> personalTemplateDao) {
		this.personalTemplateDao = personalTemplateDao;
	}

	@Override
	@DataSource
	public PersonalTemplate insert(Integer examRegion, PersonalTemplate personalTemplate) {
	    String memberId = personalTemplate.getMemberId();
	    personalTemplateDao.delete(PERSONAL_TEMPLATE.MEMBER_ID.eq(memberId));
		return personalTemplateDao.insert(personalTemplate);
	}

    private String filterHander (String value) {
        if (!StringUtils.isBlank(value)) {
            StringBuilder stringBuilder = new StringBuilder(value);
            Integer length = value.length();
            if (length < 3) {
                return stringBuilder.replace(0, 1, "*").toString();
            } else if (length == 3) {
                return stringBuilder.replace(1, 2, "*").toString();
            } else if (length == 4) {
                return stringBuilder.replace(1, 3, "**").toString();
            } else if (length > 4 && length < 8) {
                return stringBuilder.replace(2, 4, "**").toString();
            } else if (length >= 8 && length < 11) {
                return stringBuilder.replace(3, 6, "***").toString();
            } else if (length >= 11 && length < 15) {
                return stringBuilder.replace(4, 8, "****").toString();
            } else { // 大于15位
                return stringBuilder.replace(6, 12, "******").toString();
            }
        }
        return value;
    }



	@Override
	@DataSource
	public HashMap<String, Object> get(Integer examRegion,Optional<String> examId, String memberId,boolean isShield) {
	    // 如果没有考试id，说明是查询个人信息
	    if (!examId.isPresent()) {
	    	return getPersonalDetail(memberId);
        // 如果有考试id，说明是报名认证信息表
	    }else {
	    	return getAuthDetail(examId, memberId);
	    }
	}

	private HashMap<String, Object> getAuthDetail(Optional<String> examId, String memberId) {
		HashMap<String,Object> map = new HashMap<>();
		// 查询这个考生是否这场考试已经过报名了
		List<SignUp> signUpList = signUpDao.execute(e -> {
			return e.select(
					Fields.start()
							.add(SIGNUP.ID)
							.end()
			)
					.from(SIGNUP)
					.where(
							SIGNUP.MEMBER_ID.eq(memberId))
					.and(SIGNUP.EXAM_ID.eq(examId.get()))
					.fetch(r -> {
						SignUp signUp = new SignUp();
						signUp.setId(r.getValue(SIGNUP.ID));
						return signUp;
					});
		});
		// 有报表名
		if (CollectionUtils.isNotEmpty(signUpList)) {
			// 如果已经报过名，查询报名表
			SignUp signUp = signUpList.get(0);
			List<SignUpAuth> SignUpAuthList = signUpAuthDao.execute(e -> {
				return e.select(
						Fields.start()
								.add(SIGN_UP_AUTH)
								.end()
				)
						.from(SIGN_UP_AUTH)
						.where(
								SIGN_UP_AUTH.SIGN_UP_ID.eq(signUp.getId()))
						.fetch(r -> {
							return r.into(SignUpAuth.class);
						});
			});

			// 如果有报名表，返回报名认证信息+基本信息
			if (CollectionUtils.isNotEmpty(SignUpAuthList)) {
				SignUpAuth signUpAuth = SignUpAuthList.get(0);
				Member member = memberDao.get(memberId);
				if (member != null) {
					Organization organization = organizationDao.get(member.getOrganizationId());
					signUpAuth.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
						signUpAuth.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
						signUpAuth.setIdcard(DesensitizationUtil.desensitizeIdCartMiddle(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					signUpAuth.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
					signUpAuth.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
					if (organization != null) {
						signUpAuth.setOrgName(organization.getName());
					}
				}
				map.put("signUpAuth", signUpAuth);
				return map;
			} // 如果没有报名表，查询模板信息+基本信息
			else {
				// 如果这场考试没有报名记录，查询个人信息模板表
				List<PersonalTemplate> personalTemplateList = personalTemplateDao.execute(e -> {
					return e.select(
							Fields.start()
									.add(PERSONAL_TEMPLATE)
									.end()
					)
							.from(PERSONAL_TEMPLATE)
							.where(
									PERSONAL_TEMPLATE.MEMBER_ID.eq(memberId))
							.fetch(r -> {
								PersonalTemplate personalTemplate = r.into(PersonalTemplate.class);
								return personalTemplate;
							});
				});
				// 有模板信息表
				if (CollectionUtils.isNotEmpty(personalTemplateList)) {
					Member member = memberDao.get(memberId);
					PersonalTemplate personalTemplate = personalTemplateList.get(0);
					if (member != null) {
						Organization organization = organizationDao.get(member.getOrganizationId());
						personalTemplate.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
						if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
							personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
						}
						if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
							personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCartMiddle(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
						}
						personalTemplate.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
						personalTemplate.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
						if (organization != null) {
							personalTemplate.setOrgName(organization.getName());
						}
					}
					map.put("personalTemplate", personalTemplate);
					return map;
				}
				// 没有模板信息表,查询基本信息
				else{
					PersonalTemplate personalTemplate = new PersonalTemplate();
					Member member = memberDao.get(memberId);
					if (member != null) {
						Organization organization = organizationDao.get(member.getOrganizationId());
						personalTemplate.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
						if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
							personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
						}
						if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
							personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCartMiddle(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
						}
						personalTemplate.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
						personalTemplate.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
						if (organization != null) {
							personalTemplate.setOrgName(organization.getName());
						}
					}
					map.put("personalTemplate", personalTemplate);
					return map;
				}
			}
		} else{
			// 如果没有报过名
			// 查询模板信息
			List<PersonalTemplate> personalTemplateList = personalTemplateDao.execute(e -> {
				return e.select(
						Fields.start()
								.add(PERSONAL_TEMPLATE)
								.end()
				)
						.from(PERSONAL_TEMPLATE)
						.where(
								PERSONAL_TEMPLATE.MEMBER_ID.eq(memberId))
						.fetch(r -> {
							PersonalTemplate personalTemplate = r.into(PersonalTemplate.class);
							return personalTemplate;
						});
			});
			// 如果有模板信息，查询 模板信息+基本信息
			if (personalTemplateList != null && personalTemplateList.size() > 0) {
				PersonalTemplate personalTemplate = personalTemplateList.get(0);
				Member member = memberDao.get(memberId);
				if (member != null) {
					Organization organization = organizationDao.get(member.getOrganizationId());
					personalTemplate.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
						personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
						personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCartMiddle(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					personalTemplate.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
					personalTemplate.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
					if (organization != null) {
						personalTemplate.setOrgName(organization.getName());
					}
				}
				map.put("personalTemplate", personalTemplate);
				return map;
			}else{
				// 如果没有模板信息，查询基本信息
				PersonalTemplate personalTemplate = new PersonalTemplate();
				Member member = memberDao.get(memberId);
				if (member != null) {
					Organization organization = organizationDao.get(member.getOrganizationId());
					personalTemplate.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
						personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
						personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCartMiddle(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
					}
					personalTemplate.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
					personalTemplate.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
					if (organization != null) {
						personalTemplate.setOrgName(organization.getName());
					}
				}
				map.put("personalTemplate", personalTemplate);
				return map;
			}
		}
	}

	private HashMap<String, Object> getPersonalDetail(String memberId) {
		// 查询个人信息模板表
		List<PersonalTemplate> PersonalTemplateList = personalTemplateDao.execute(e -> {
			return e.select(
					Fields.start()
							.add(PERSONAL_TEMPLATE)
							.end()
			)
					.from(PERSONAL_TEMPLATE)
					.where(
							PERSONAL_TEMPLATE.MEMBER_ID.eq(memberId))
					.fetch(r -> {
						return r.into(PersonalTemplate.class);
					});
		});
		HashMap<String,Object> map = new HashMap<>();
		PersonalTemplate personalTemplate = CollectionUtils.isEmpty(PersonalTemplateList)?new PersonalTemplate():PersonalTemplateList.get(0);
		Member member = memberDao.get(memberId);
		if (member != null) {
			Organization organization = organizationDao.get(member.getOrganizationId());
			personalTemplate.setMemberId(DesensitizationUtil.desensitizeName(Optional.ofNullable(member.getName())));
			if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length()< 18) {
				personalTemplate.setIdcard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getIdentityNumber()))));
			}
			if (SM4Utils.decryptDataCBC(member.getIdentityNumber()) != null && SM4Utils.decryptDataCBC(member.getIdentityNumber()).length() == 18) {
				personalTemplate.setIdcard(com.zxy.product.examstu.util.DesensitizationUtil.desensitizeEmployeeId(SM4Utils.decryptDataCBC(member.getIdentityNumber())));
			}
			personalTemplate.setMail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getEmail()))));
			personalTemplate.setPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(SM4Utils.decryptDataCBC(member.getPhoneNumber()))));
			if (organization != null) {
				personalTemplate.setOrgName(organization.getName());
			}
		}
		map.put("personalTemplate", personalTemplate);
		return map;

	}

	@Override
	@DataSource
	public PersonalTemplate update(Integer examRegion, String id, String professionId, String subProfessionId, String equipmentTypeId,
			String workDepart, String workTime, Integer isGroupExpert, Integer isProvinExpert, String otherExamAppraisal,
			String awardSituation, String crossCondition, Optional<String> applyLevel, Optional<String> applyProfession,
			Optional<String> applySubProfession, Optional<String> applySupplier) {
		//获取个人模板信息
		PersonalTemplate personalTemplate=personalTemplateDao.get(id);
		personalTemplate.setProfessionId(professionId);
		personalTemplate.setSubProfessionId(subProfessionId);
		personalTemplate.setEquipmentTypeId(equipmentTypeId);
		personalTemplate.setWorkDepart(workDepart);
		personalTemplate.setWorkTime(workTime);
		personalTemplate.setIsGroupExpert(isGroupExpert);
		personalTemplate.setIsProvinExpert(isProvinExpert);
		personalTemplate.setOtherExamAppraisal(otherExamAppraisal);
		personalTemplate.setAwardSituation(awardSituation);
		personalTemplate.setCrossCondition(crossCondition);
		applyLevel.ifPresent(personalTemplate::setApplyLevel);
		applyProfession.ifPresent(personalTemplate::setApplyProfession);
		applySubProfession.ifPresent(personalTemplate::setApplySubProfession);
		applySupplier.ifPresent(personalTemplate::setApplySupplier);
		personalTemplateDao.update(personalTemplate);
		return personalTemplate;
	}

}
