package com.zxy.product.examstu.service.component.sequence.business;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.api.sequence.CodeMaxCallback;
import com.zxy.product.exam.entity.CertificateRecord;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.zxy.product.exam.jooq.Tables.CERTIFICATE_RECORD;

/**
 * 证书编号已有最大值查询回调（从DB读取）
 * <AUTHOR>
 * @date 2017年11月11日
 */
@Service
public class CertificateCodeMaxCallback implements CodeMaxCallback {

    private CommonDao<CertificateRecord> certificateRecordDao;

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }

    @Override
    public String getMaxCode(Optional<String> prefix) {
        return certificateRecordDao.execute(e ->  e.select(DSL.max(CERTIFICATE_RECORD.NUM))
                    .from(CERTIFICATE_RECORD)
                    .where(prefix.map(p -> CERTIFICATE_RECORD.NUM.like(p + "%", '!')).orElse(DSL.trueCondition()))
                    .fetchOne(DSL.max(CERTIFICATE_RECORD.NUM))
        );
    }
}
