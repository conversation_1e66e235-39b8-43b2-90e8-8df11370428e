package com.zxy.product.examstu.service.support;


import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ToDoService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;


/**
 * <AUTHOR>
 *
 */
@Service
public class ToDoServiceSupport implements ToDoService {

	private CommonDao<ToDo> toDoDao;

	private MessageSender messageSender;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }


	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

	@Autowired
	public void setToDoDao(CommonDao<ToDo> toDoDao) {
		this.toDoDao = toDoDao;
	}

	@Override
    @DataSource
	public List<ToDo> insert(Integer examRegion,String targetId, List<String> memberIds, int type, Map<String, Integer> memberMap) {

		List<String> ids = toDoDao.execute(e -> e.select(TO_DO.ID)
                .from(TO_DO)
                .where(TO_DO.TARGET_ID.eq(targetId))
                .and(TO_DO.TYPE.eq(type))
                .fetch(TO_DO.ID));

		for (String id : ids) {
	        toDoDao.delete(TO_DO.ID.eq(id));
        }

		List<ToDo> toDos = memberIds.stream().map(t -> {
			ToDo toDo = new ToDo();
			toDo.forInsert();
			toDo.setMemberId(t);
			toDo.setTargetId(targetId);
			toDo.setType(type);
			toDo.setIncludeType(memberMap.get(t));
			toDo.setAudited(ToDo.AUDITED_0);
			toDo.setSubmited(ToDo.SUBMITED_0);
			return toDo;
		}).collect(Collectors.toList());
		toDoDao.insert(toDos);
		return toDos;
	}

	@Override
    @DataSource
	public List<ToDo> find(Integer examRegion, String memberId) {

		return toDoDao.fetch(Stream.of(TO_DO.MEMBER_ID.eq(memberId)), TO_DO.CREATE_TIME.asc());
	}

	@Override
	public List<ToDo> findMarkPapers(String memberId) {

	    List<ToDo> toDoList = new ArrayList<ToDo>();

	    String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

	    for (String examRecordStringTable : allExamRecordStringTable) {

	        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

	        List<ToDo> toDoListForYear = toDoDao.execute(e ->
            e.selectDistinct(
                Fields.start()
                .add(TO_DO)
                .add(examRecordTable.field("f_id", String.class))
                .add(examRecordTable.field("f_status", Integer.class))
                .add(examRecordTable.field("f_last_submit_time", Long.class))
                .add(examRecordTable.field("f_score", Integer.class))
                .add(examRecordTable.field("f_submit_time", Long.class))
                .add(MEMBER.ID)
                .add(MEMBER.NAME)
                .add(MEMBER.FULL_NAME)
                .add(ORGANIZATION.ID)
                .add(ORGANIZATION.NAME)
                .add(EXAM.ID)
                .add(EXAM.NAME)
                .add(EXAM.SOURCE_TYPE)
                .end())
            .from(TO_DO)
                .leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class)))
                .leftJoin(MEMBER).on(MEMBER.ID.eq(examRecordTable.field("f_member_id", String.class)))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
            .where(TO_DO.TYPE.eq(ToDo.MARK_PAPER_TYPE),
                TO_DO.MEMBER_ID.eq(memberId),
                examRecordTable.field("f_status", Integer.class).ge(ExamRecord.STATUS_TIME_EXCEPTION))
            .fetch()).stream().map(t -> {
            ToDo toDo = t.into(ToDo.class);

            ExamRecord examRecord = new ExamRecord();
            examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
            examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
            examRecord.setLastSubmitTime(t.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
            examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
            examRecord.setScore(t.getValue(examRecordTable.field("f_score", Integer.class)));
            toDo.setExamRecord(examRecord);

            Member member = new Member();
            member.setId(t.getValue(MEMBER.ID));
            member.setName(t.getValue(MEMBER.NAME));
            member.setFullName(t.getValue(MEMBER.FULL_NAME));

            Organization organization = new Organization();
            organization.setId(t.getValue(ORGANIZATION.ID));
            organization.setName(t.getValue(ORGANIZATION.NAME));

            member.setOrganization(organization);
            examRecord.setMember(member);

            Exam exam = new Exam();
            exam.setId(t.getValue(EXAM.ID));
            exam.setName(t.getValue(EXAM.NAME));
            exam.setSourceType(t.getValue(EXAM.SOURCE_TYPE));
            examRecord.setExam(exam);

            toDo.setExamRecord(examRecord);

            return toDo;
        }).collect(Collectors.toList());

	        toDoList.addAll(toDoListForYear);
        }
	    toDoList = toDoList.stream().filter(x -> x != null && x.getExamRecord() != null && x.getExamRecord().getId() != null).collect(Collectors.toList());
	    toDoList = toDoList.stream().sorted(Comparator.comparing(ToDo::getCreateTime).reversed()).collect(Collectors.toList());
	    return toDoList;
	}

	@Override
	public String updateSubmitTime(String examRecordId, String memberId) {
		ToDo t = toDoDao.fetchOne(TO_DO.TARGET_ID.eq(examRecordId), TO_DO.MEMBER_ID.eq(memberId)).orElse(null);
		if (t != null) {
		    t.setSubmited(1);
			t.setSubmitTime(System.currentTimeMillis());
			toDoDao.update(t);
		}
		return t != null ? t.getId() : "";
	}

	@Override
    @DataSource
	public String updateAuditTime(Integer examRegion, String examRecordId, String memberId) {
	    ToDo t = toDoDao.fetchOne(TO_DO.TARGET_ID.eq(examRecordId), TO_DO.MEMBER_ID.eq(memberId)).orElse(null);
	    if (t != null) {
	        t.setAudited(1);
	        t.setAuditTime(System.currentTimeMillis());
	        toDoDao.update(t);
	    }
	    return t != null ? t.getId() : "";
	}

	@Override
	public PagedResult<ToDo> findPageForMarkPapers(
		Integer page,
		Integer pageSize,
		String currentUserId,
		Optional<String> name,
		Optional<Integer> wait) {

	    List<ToDo> toDoList = new ArrayList<ToDo>();

        String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

        for (String examRecordStringTable : allExamRecordStringTable) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

            List<ToDo> listForYear = toDoDao.execute(e -> {

                Field<String> memberName = MEMBER.NAME.as("membername");
                Field<String> memberId = MEMBER.ID.as("memberId");

                SelectSelectStep<Record> selectListField = e.selectDistinct(
                    Fields.start()
                    .add(TO_DO)
                    .add(examRecordTable.field("f_id", String.class))
                    .add(examRecordTable.field("f_status", Integer.class))
                    .add(examRecordTable.field("f_last_submit_time", Long.class))
                    .add(examRecordTable.field("f_score", Integer.class))
                    .add(examRecordTable.field("f_submit_time", Long.class))
                    .add(memberId)
                    .add(memberName)
                    .add(MEMBER.FULL_NAME)
                    .add(ORGANIZATION.ID)
                    .add(ORGANIZATION.NAME)
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.SOURCE_TYPE)
                    .add(EXAM.ANONYMITY_MARK)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .end()
                );

                Stream<Optional<Condition>> conditions = Stream.of(
                    name.map(EXAM.NAME::contains),
                    wait.map(t -> {
                        if (t == 1)
                            return ((TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_0).and(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0)))
                                    .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_1).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0)))
                                    .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_2).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0).or(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0))))
                                    );
                        return DSL.trueCondition();
                    })
                );

                Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
                    .reduce((acc,item) -> acc.and(item)).orElse(DSL.trueCondition());

                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                        SelectConditionStep<Record> select = a.from(TO_DO)
                                .leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class)))
                                .leftJoin(MEMBER).on(MEMBER.ID.eq(examRecordTable.field("f_member_id", String.class)))
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                                .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                                .where(c)
                                .and(TO_DO.TYPE.eq(ToDo.MARK_PAPER_TYPE))
                                .and(TO_DO.MEMBER_ID.eq(currentUserId));
                        return select;
                    };

                SelectConditionStep<Record> listSept = stepFunc.apply(selectListField);
                Result<Record> record = listSept.fetch();
                return record.stream().map(t -> {
                    ToDo toDo = t.into(ToDo.class);

                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setLastSubmitTime(t.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                    examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setScore(t.getValue(examRecordTable.field("f_score", Integer.class)));

                    Member member = new Member();
                    member.setId(t.getValue(memberId));
                    member.setName(t.getValue(memberName));
                    member.setFullName(t.getValue(MEMBER.FULL_NAME));

                    Organization organization = new Organization();
                    organization.setId(t.getValue(ORGANIZATION.ID));
                    organization.setName(t.getValue(ORGANIZATION.NAME));

                    member.setOrganization(organization);
                    examRecord.setMember(member);

                    Exam exam = new Exam();
                    exam.setId(t.getValue(EXAM.ID));
                    exam.setName(t.getValue(EXAM.NAME));
                    exam.setSourceType(t.getValue(EXAM.SOURCE_TYPE));
                    exam.setAnonymityMark(t.getValue(EXAM.ANONYMITY_MARK));
                    exam.setShowScoreTime(t.getValue(EXAM.SHOW_SCORE_TIME));
                    examRecord.setExam(exam);

                    toDo.setExamRecord(examRecord);

                    return toDo;
                }).collect(Collectors.toList());
            });

            toDoList.addAll(listForYear);
        }
//      1:没有成绩发布时间
//      2:有成绩发布时间 && 成绩发布时间 > 当前时间 && （未复核 || 未评卷 || 有评卷时间 || 有审核时间）
//      3:有成绩发布时间 && 成绩发布时间 < 当前时间  && （(其他主观题 && （未评卷 || 有评卷时间）) || (其他主观题+填空题 && （ 未评卷 || 有评卷时间 || 有复核时间 ）|| （填空题 && 有复核时间）)）
        toDoList = toDoList.stream().filter(todo ->
        todo != null && todo.getExamRecord() != null && todo.getExamRecord().getId() != null && todo.getExamRecord().getExam() != null
        && (
            (todo.getExamRecord().getExam().getShowScoreTime() == null)
            ||
            (todo.getExamRecord().getExam().getShowScoreTime() != null
            && todo.getExamRecord().getExam().getShowScoreTime() > System.currentTimeMillis()
            && (todo.getAudited() == ToDo.AUDITED_0 || todo.getAuditTime() != null ||
                    todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
            ||
            (todo.getExamRecord().getExam().getShowScoreTime() != null
            && todo.getExamRecord().getExam().getShowScoreTime() < System.currentTimeMillis()
            && ((todo.getIncludeType() == ToDo.INCLUDE_TYPE_0 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
                    ||
                    (todo.getIncludeType() == ToDo.INCLUDE_TYPE_2 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null || todo.getAuditTime() != null))
                    ||
                    (todo.getIncludeType() == ToDo.INCLUDE_TYPE_1 && todo.getAuditTime() != null)))

            )).collect(Collectors.toList());

        toDoList = toDoList.stream().sorted(Comparator.comparing(ToDo::getCreateTime).reversed()).collect(Collectors.toList());

        int total = toDoList.size();
        List<ToDo> list = toDoList.subList(pageSize * (page - 1), ((pageSize * page) > total ? total : (pageSize * page)));

        return PagedResult.create(total,list);
	}
	@Override
	public List<ToDo> findNoFinishedToDoByMemberIds(List<String> receiverIds) {

        List<ToDo> toDoList = new ArrayList<ToDo>();

        String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

        for (String examRecordStringTable : allExamRecordStringTable) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

            List<ToDo> listForYear = toDoDao.execute(e -> {

                Field<String> memberName = MEMBER.NAME.as("membername");
                Field<String> memberId = MEMBER.ID.as("memberId");

                SelectSelectStep<Record> selectListField = e.selectDistinct(
                    Fields.start()
                    .add(TO_DO)
                    .add(examRecordTable.field("f_id", String.class))
                    .add(examRecordTable.field("f_status", Integer.class))
                    .add(examRecordTable.field("f_last_submit_time", Long.class))
                    .add(examRecordTable.field("f_score", Integer.class))
                    .add(examRecordTable.field("f_submit_time", Long.class))
                    .add(memberId)
                    .add(memberName)
                    .add(MEMBER.FULL_NAME)
                    .add(ORGANIZATION.ID)
                    .add(ORGANIZATION.NAME)
                    .add(EXAM.ID)
                    .add(EXAM.NAME)
                    .add(EXAM.SOURCE_TYPE)
                    .add(EXAM.ANONYMITY_MARK)
                    .add(EXAM.SHOW_SCORE_TIME)
                    .end()
                );


                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                        SelectConditionStep<Record> select = a.from(TO_DO)
                                .leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class)))
                                .leftJoin(MEMBER).on(MEMBER.ID.eq(examRecordTable.field("f_member_id", String.class)))
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                                .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                                .where(TO_DO.TYPE.eq(ToDo.MARK_PAPER_TYPE))
                                .and(TO_DO.MEMBER_ID.in(receiverIds))
                                .and(examRecordTable.field("f_id", String.class).isNotNull())
                                .and((TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_0).and(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0)))
                                        .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_1).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0)))
                                        .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_2).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0).or(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0))))
                                        );
                        return select;
                    };

                SelectConditionStep<Record> listSept = stepFunc.apply(selectListField);
                Result<Record> record = listSept.fetch();
                return record.stream().map(t -> {
                    ToDo toDo = t.into(ToDo.class);

                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setLastSubmitTime(t.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                    examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setScore(t.getValue(examRecordTable.field("f_score", Integer.class)));

                    Member member = new Member();
                    member.setId(t.getValue(memberId));
                    member.setName(t.getValue(memberName));
                    member.setFullName(t.getValue(MEMBER.FULL_NAME));

                    Organization organization = new Organization();
                    organization.setId(t.getValue(ORGANIZATION.ID));
                    organization.setName(t.getValue(ORGANIZATION.NAME));

                    member.setOrganization(organization);
                    examRecord.setMember(member);

                    Exam exam = new Exam();
                    exam.setId(t.getValue(EXAM.ID));
                    exam.setName(t.getValue(EXAM.NAME));
                    exam.setSourceType(t.getValue(EXAM.SOURCE_TYPE));
                    exam.setAnonymityMark(t.getValue(EXAM.ANONYMITY_MARK));
                    exam.setShowScoreTime(t.getValue(EXAM.SHOW_SCORE_TIME));
                    examRecord.setExam(exam);

                    toDo.setExamRecord(examRecord);

                    return toDo;
                }).collect(Collectors.toList());
            });

            toDoList.addAll(listForYear);
        }
//      1:没有成绩发布时间
//      2:有成绩发布时间 && 成绩发布时间 > 当前时间 && （未复核 || 未评卷 || 有评卷时间 || 有审核时间）
//      3:有成绩发布时间 && 成绩发布时间 < 当前时间  && （(其他主观题 && （未评卷 || 有评卷时间）) || (其他主观题+填空题 && （ 未评卷 || 有评卷时间 || 有复核时间 ）|| （填空题 && 有复核时间）)）
        toDoList = toDoList.stream().filter(todo ->
        todo != null && todo.getExamRecord() != null && todo.getExamRecord().getId() != null && todo.getExamRecord().getExam() != null
        && (
            (todo.getExamRecord().getExam().getShowScoreTime() == null)
            ||
            (todo.getExamRecord().getExam().getShowScoreTime() != null
            && todo.getExamRecord().getExam().getShowScoreTime() > System.currentTimeMillis()
            && (todo.getAudited() == ToDo.AUDITED_0 || todo.getAuditTime() != null ||
                    todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
            ||
            (todo.getExamRecord().getExam().getShowScoreTime() != null
            && todo.getExamRecord().getExam().getShowScoreTime() < System.currentTimeMillis()
            && ((todo.getIncludeType() == ToDo.INCLUDE_TYPE_0 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
                    ||
                    (todo.getIncludeType() == ToDo.INCLUDE_TYPE_2 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null || todo.getAuditTime() != null))
                    ||
                    (todo.getIncludeType() == ToDo.INCLUDE_TYPE_1 && todo.getAuditTime() != null)))

            )).collect(Collectors.toList());

		return toDoList;
	}

	@Override
    @DataSource
	public String deleteByTargetId(Integer examRegion, String targetId, String memberId, String examId) {
		toDoDao.delete(TO_DO.TARGET_ID.eq(targetId), TO_DO.MEMBER_ID.eq(memberId));
		messageSender.send(MessageTypeContent.EXAM_MARK_PAPER_INSERT, MessageHeaderContent.ID, targetId,
		        MessageHeaderContent.EXAM_ID,examId);
		return targetId;
	}

    @Override
    @DataSource
    public void deleteToDoByUpdateMarkConfig(Integer examRegion, String examId, List<String> memberIds) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        List<String> todoIds = toDoDao.execute(e -> e.select(TO_DO.ID).from(TO_DO)
                .leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class)))
                .and(examRecordTable.field("f_exam_id", String.class).eq(examId))
                .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                .where(EXAM.ID.eq(examId), TO_DO.MEMBER_ID.in(memberIds)).fetch(TO_DO.ID));
        messageSender.send(MessageTypeContent.UPDATE_MARK_CONFIG, MessageHeaderContent.EXAM_ID, examId);
        toDoDao.delete(todoIds);
    }

    @Override
    @DataSource
    public List<ToDo> findNoFinishedToDoByMemberIds(Integer examRegion, List<String> receiverIds) {

        List<ToDo> toDoList = new ArrayList<ToDo>();

        String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_OTHER;

        for (String examRecordStringTable : allExamRecordStringTable) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

            List<ToDo> listForYear = toDoDao.execute(e -> {

                Field<String> memberName = MEMBER.NAME.as("membername");
                Field<String> memberId = MEMBER.ID.as("memberId");

                SelectSelectStep<Record> selectListField = e.selectDistinct(
                        Fields.start()
                                .add(TO_DO)
                                .add(examRecordTable.field("f_id", String.class))
                                .add(examRecordTable.field("f_status", Integer.class))
                                .add(examRecordTable.field("f_last_submit_time", Long.class))
                                .add(examRecordTable.field("f_score", Integer.class))
                                .add(examRecordTable.field("f_submit_time", Long.class))
                                .add(memberId)
                                .add(memberName)
                                .add(MEMBER.FULL_NAME)
                                .add(ORGANIZATION.ID)
                                .add(ORGANIZATION.NAME)
                                .add(EXAM.ID)
                                .add(EXAM.NAME)
                                .add(EXAM.SOURCE_TYPE)
                                .add(EXAM.ANONYMITY_MARK)
                                .add(EXAM.SHOW_SCORE_TIME)
                                .end()
                );


                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                    SelectConditionStep<Record> select = a.from(TO_DO)
                            .leftJoin(examRecordTable).on(TO_DO.TARGET_ID.eq(examRecordTable.field("f_id", String.class)))
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(examRecordTable.field("f_member_id", String.class)))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                            .where(TO_DO.TYPE.eq(ToDo.MARK_PAPER_TYPE))
                            .and(TO_DO.MEMBER_ID.in(receiverIds))
                            .and(examRecordTable.field("f_id", String.class).isNotNull())
                            .and((TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_0).and(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0)))
                                    .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_1).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0)))
                                    .or(TO_DO.INCLUDE_TYPE.eq(ToDo.INCLUDE_TYPE_2).and(TO_DO.AUDITED.eq(ToDo.AUDITED_0).or(TO_DO.SUBMITED.eq(ToDo.SUBMITED_0))))
                            );
                    return select;
                };

                SelectConditionStep<Record> listSept = stepFunc.apply(selectListField);
                Result<Record> record = listSept.fetch();
                return record.stream().map(t -> {
                    ToDo toDo = t.into(ToDo.class);

                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(t.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStatus(t.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setLastSubmitTime(t.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                    examRecord.setSubmitTime(t.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setScore(t.getValue(examRecordTable.field("f_score", Integer.class)));

                    Member member = new Member();
                    member.setId(t.getValue(memberId));
                    member.setName(t.getValue(memberName));
                    member.setFullName(t.getValue(MEMBER.FULL_NAME));

                    Organization organization = new Organization();
                    organization.setId(t.getValue(ORGANIZATION.ID));
                    organization.setName(t.getValue(ORGANIZATION.NAME));

                    member.setOrganization(organization);
                    examRecord.setMember(member);

                    Exam exam = new Exam();
                    exam.setId(t.getValue(EXAM.ID));
                    exam.setName(t.getValue(EXAM.NAME));
                    exam.setSourceType(t.getValue(EXAM.SOURCE_TYPE));
                    exam.setAnonymityMark(t.getValue(EXAM.ANONYMITY_MARK));
                    exam.setShowScoreTime(t.getValue(EXAM.SHOW_SCORE_TIME));
                    examRecord.setExam(exam);

                    toDo.setExamRecord(examRecord);

                    return toDo;
                }).collect(Collectors.toList());
            });

            toDoList.addAll(listForYear);
        }
//      1:没有成绩发布时间
//      2:有成绩发布时间 && 成绩发布时间 > 当前时间 && （未复核 || 未评卷 || 有评卷时间 || 有审核时间）
//      3:有成绩发布时间 && 成绩发布时间 < 当前时间  && （(其他主观题 && （未评卷 || 有评卷时间）) || (其他主观题+填空题 && （ 未评卷 || 有评卷时间 || 有复核时间 ）|| （填空题 && 有复核时间）)）
        toDoList = toDoList.stream().filter(todo ->
                todo != null && todo.getExamRecord() != null && todo.getExamRecord().getId() != null && todo.getExamRecord().getExam() != null
                        && (
                        (todo.getExamRecord().getExam().getShowScoreTime() == null)
                                ||
                                (todo.getExamRecord().getExam().getShowScoreTime() != null
                                        && todo.getExamRecord().getExam().getShowScoreTime() > System.currentTimeMillis()
                                        && (todo.getAudited() == ToDo.AUDITED_0 || todo.getAuditTime() != null ||
                                        todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
                                ||
                                (todo.getExamRecord().getExam().getShowScoreTime() != null
                                        && todo.getExamRecord().getExam().getShowScoreTime() < System.currentTimeMillis()
                                        && ((todo.getIncludeType() == ToDo.INCLUDE_TYPE_0 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null))
                                        ||
                                        (todo.getIncludeType() == ToDo.INCLUDE_TYPE_2 && (todo.getSubmited() == ToDo.SUBMITED_0 || todo.getSubmited() != null || todo.getAuditTime() != null))
                                        ||
                                        (todo.getIncludeType() == ToDo.INCLUDE_TYPE_1 && todo.getAuditTime() != null)))

                )).collect(Collectors.toList());

        return toDoList;
    }

    @Override
    @DataSource
    public void updateToDo(Integer examRegion, List<ToDo> toDos) {
        for (ToDo toDo : toDos) {
            toDo.setSubmited(1);
        }
        toDoDao.update(toDos);
    }

    @Override
    @DataSource
    public void deleteToDo(Integer examRegion, List<ToDo> toDos) {
        toDoDao.delete(toDos.stream().map(ToDo::getId).collect(Collectors.toList()));
    }

    @Override
    @DataSource
    public void update(Integer examRegion,List<ToDo> toDos) {
        toDoDao.update(toDos);
    }


}
