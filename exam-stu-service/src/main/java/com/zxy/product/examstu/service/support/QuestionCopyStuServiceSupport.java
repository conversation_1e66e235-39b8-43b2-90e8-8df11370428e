package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.QuestionCopyStuService;
import com.zxy.product.exam.entity.*;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class QuestionCopyStuServiceSupport implements QuestionCopyStuService {

    private CommonDao<QuestionCopy> questionCopyDao;


    @Autowired
    public void setQuestionCopyDao(CommonDao<QuestionCopy> questionCopyDao) {
        this.questionCopyDao = questionCopyDao;
    }

    @Override
    @DataSource
    public Map<String, QuestionCopy> getRightAnswer(Integer examRegion, String memberId, String examId) {

        Result<Record> records = questionCopyDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(QUESTION_COPY)
                                    .add(QUESTION_ATTR_COPY)
                                    .end()
                    )
                    .from(QUESTION_COPY)
                    .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_COPY.ID.eq(QUESTION_ATTR_COPY.QUESTION_COPY_ID))
                    .where(QUESTION_COPY.EXAM_ID.eq(examId))
                    .fetch();
        });
        List<QuestionAttrCopy> questionAttrCopys = records.into(QuestionAttrCopy.class).stream().filter(f -> f.getId() != null).collect(Collectors.toList());
        Map<String, List<QuestionAttrCopy>> questionAttrCopyMap = questionAttrCopys.stream().collect(Collectors.groupingBy(QuestionAttrCopy::getQuestionCopyId));

        Map<String, QuestionCopy> questionCopyMap = records
                .stream().map(t -> {
                    QuestionCopy questionCopy = new QuestionCopy();
                    questionCopy.setId(t.getValue(QUESTION_COPY.ID));
                    questionCopy.setType(t.getValue(QUESTION_COPY.TYPE));
                    questionCopy.setScore(t.getValue(QUESTION_COPY.SCORE));
                    questionCopy.setParentId(t.getValue(QUESTION_COPY.PARENT_ID));
                    questionCopy.setQuestionAttrCopys(questionAttrCopyMap.get(questionCopy.getId()));
                    return questionCopy;
                }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

        return questionCopyMap;
    }

    @Override
    public boolean compareAnswerInfo(List<QuestionAttrCopy> questionAttrCopys, String answer, Integer type) {
        boolean isRight = false;
        switch (type) {
            case 1:
                isRight = dealSingleChoose(questionAttrCopys, answer);
                break;
            case 2:
                isRight = dealMultipleChoose(questionAttrCopys, answer);
                break;
            case 3:
                isRight = dealJudgement(questionAttrCopys, answer);
                break;
            case 4:
                isRight = dealSentenceCompletion(questionAttrCopys, answer);
                break;
            case 8:
                isRight = dealSorting(questionAttrCopys, answer);
                break;
            default:
                break;
        }
        return isRight;
    }

    private boolean dealSingleChoose(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        for (int i = 0; i < questionAttrCopys.size(); i++) {
            QuestionAttrCopy attr = questionAttrCopys.get(i);
            if ((String.valueOf(QuestionAttr.ANSWER_TYPE).equals(attr.getType()))
                    && (!attr.getName().equals(answer))) {
                return false;
            }
        }
        return true;
    }

    private boolean dealMultipleChoose(List<QuestionAttrCopy> questionAttrCopys, String answer) {

        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }

        Set<String> answers = Arrays.stream(answer.split(","))
                                    .map(String::trim)
                                    .filter(s -> !s.isEmpty())
                                    .collect(Collectors.toSet());

        List<QuestionAttrCopy> rightAnswers = questionAttrCopys.stream()
                                                               .filter(t -> String.valueOf(QuestionAttr.ANSWER_TYPE).equals(t.getType()))
                                                               .collect(Collectors.toList());

        if (rightAnswers.isEmpty()) {
            return false;
        }

        if (answers.size() != rightAnswers.size()) {
            return false;
        }

        for (QuestionAttrCopy attr : rightAnswers) {
            if (!answers.contains(attr.getName())) {
                return false;
            }
        }

        return true;
    }

    private boolean validateParamEmpty(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (questionAttrCopys == null || answer == null) {
            return true;
        }
        return false;
    }

    private boolean dealJudgement(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }
        String correctAnswer = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue();
        return !Objects.isNull(correctAnswer) && correctAnswer.equals(answer);
    }

    private boolean dealSentenceCompletion(List<QuestionAttrCopy> questionAttrCopys, String answer) {

        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }
        String[] correctAnswerArr = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue().split("\\|");
        String[] answerArr = answer.split("@answer@");

        if(correctAnswerArr.length != answerArr.length)
            return false;

        for (int i = 0; i < correctAnswerArr.length; i++) {
            String[] tureAnswer = correctAnswerArr[i].trim().split("#");
            List<String> trueAn = Arrays.stream(tureAnswer).map(String::trim).collect(Collectors.toList());

            if (!trueAn.contains(answerArr[i].trim())) {
                return false;
            }

        }
        return true;
    }

    private boolean dealSorting(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }

        String correctAnswer = questionAttrCopys.stream()
                                                .filter(t -> t.getType().equals(String.valueOf(QuestionAttr.ANSWER_TYPE)))
                                                .collect(Collectors.toList()).get(0).getValue();

        return correctAnswer.equals(answer);
    }

}
