package com.zxy.product.examstu.service.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AfterAssessmentService;
import com.zxy.product.exam.entity.*;

import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;


@Service
public class AfterAssessmentSupport implements AfterAssessmentService{
	private CommonDao<ExamRegist> examRegistDao;
	private CommonDao<Exam> examDao;
	private CommonDao<QuestionDepot> questionDepotDao;
	private CommonDao<Member> memberDao;
	private CommonDao<Organization> organDao;


	private GetTableUtil getTableUtil;

	@Autowired
	public void setGetTableUtil(GetTableUtil getTableUtil) {
		this.getTableUtil = getTableUtil;
	}


	 private static final Double HUNDRED = 100.0;

	@Autowired
	public void setOrganDao(CommonDao<Organization> organDao) {
		this.organDao = organDao;
	}

	@Autowired
	public void setMemberDao(CommonDao<Member> memberDao) {
		this.memberDao = memberDao;
	}

	@Autowired
	public void setExamDao(CommonDao<Exam> examDao) {
		this.examDao = examDao;
	}

	@Autowired
	public void setExamRegistDao(CommonDao<ExamRegist> examRegistDao) {
		this.examRegistDao = examRegistDao;
	}

	@Autowired
	public void setQuestionDepotDao(CommonDao<QuestionDepot> questionDepotDao) {
		this.questionDepotDao = questionDepotDao;
	}


	/**
	 * 前端考试个人后评估
	 */
	@Override
	@DataSource
	public ExamRegist frontExamPersonalDetailList(Integer examRegion, String examId, String memberId) {

	    TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

		List<ExamRegist> returnList= new ArrayList<>();
		DecimalFormat    df   = new DecimalFormat("######0.00");
		return  examRegistDao.execute(x ->{
			SelectSelectStep<Record> selectListFields = x.select(
					Fields.start()
					.add(examRegistTable.field("f_member_id", String.class))
					.end()
					);

			List<Condition> params = Stream.of(

					Optional.of(examId).map(examRegistTable.field("f_exam_id", String.class)::eq),
					Optional.of(memberId).map(MEMBER.ID::eq)
					).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFuncs = a ->
			a.from(examRegistTable)
			.innerJoin(MEMBER).on(MEMBER.ID.eq(examRegistTable.field("f_member_id", String.class)))
			.where(params);
			//用户列表
			List<String> list = stepFuncs.apply(selectListFields).orderBy(examRegistTable.field("f_create_time", Long.class).desc()).fetch(examRegistTable.field("f_member_id", String.class));
			//查询该场考试所有知识点下所有人的答题正确数
			//List<ExamRegist> examRegistList=findRateforExam(memberName, memberFullName, id, list);
			//如果有用户答题数据则进行数据处理
			if(list.size()>0){
				List<QuestionDepot>  questionDepotList;
				//查询该考试总的试题数
				double getQuestionNum=getQuestionNum(examId);
				//获取知识点以及该知识点下的总试题数
				questionDepotList=getExamPersonalHeaderDetail(examId);
				Map<String, Object> map=new HashMap<String, Object>();
				//所有知识点下的总试题数
				//用户知识点下的总试题正确数
				for(QuestionDepot depot : questionDepotList){
					map.put(depot.getId(), depot.getQuestionNum());
				}
				Map<String, Object> userMap= new HashMap<>();

				//如果指定人员或者编号查询，则不需要人员去重
				for(int i=0;i<questionDepotList.size();i++){
					//当前用户该知识点正确数
					double num = getforentUserDepotNumById(examRegion, Optional.empty(),Optional.empty(), examId,questionDepotList.get(i).getId(),memberId);
					//该知识点题目总数
					double totalNumByDepot=Integer.parseInt(map.get(questionDepotList.get(i).getId()).toString());
					//该用户该知识点正确率
					double tempNum=(num/totalNumByDepot)*HUNDRED;
					//处理double NAN问题
					String userDepotRate;
					if(Double.isNaN(tempNum) || Double.isInfinite(tempNum)) {
						userDepotRate="0%";
					} else {
						userDepotRate=df.format(tempNum)+"%";
					}
					userMap.put(questionDepotList.get(i).getId(), userDepotRate);

				}
				//该用户该场考试总的正确数
			    double totalRateNum =  getUserDepotNumById(examRegion, Optional.empty(), Optional.empty(), examId, Optional.empty(),Optional.of(memberId));
				//该用户总的正确率
				double   tempNum=(totalRateNum/getQuestionNum)*HUNDRED;
				//处理double NAN问题
				String totalRate;
				if(Double.isNaN(tempNum) || Double.isInfinite(tempNum)) {
					totalRate="0%";
				} else {
					totalRate=df.format(tempNum)+"%";
				}
				for (String s : list) {
					ExamRegist examRegists = new ExamRegist();
					JSONObject json = JSON.parseObject(JSON.toJSONString(userMap));
					examRegists.setUserRateJson(json.toJSONString());
					examRegists.setCorrectRate(totalRate);
					Member mem = memberDao.get(s);
					Organization org = new Organization();
					if (mem.getOrganizationId() != null) {
						org = organDao.get(mem.getOrganizationId());
					}
					examRegists.setMember(mem);
					examRegists.setOrganization(org);
					returnList.add(examRegists);
				}

			}
			return  !CollectionUtils.isEmpty(returnList)? returnList.get(0) : new ExamRegist();
		});

	}


	/**
	 *
	 * getUserDepotNumById:通过知识点ID和考试ID获取用户该知识点的答题正确率数量. <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @param depotId
	 * @return
	 * @since JDK 1.8
	 * date: 2017年11月13日 下午3:31:12 <br/>
	 */
	public int getUserDepotNumById(Integer examRegion, Optional<String> memberName, Optional<String> memberFullName,String id,Optional<String> depotId,Optional<String> memId){

		TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(id));
		TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(id));

		return examRegistDao.execute(e ->{
			int isRight=1;
			SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(examRegistTable.field("f_id", String.class).count()).end());

			List<Condition> param = Stream.of(
					Optional.of(id).map(examRegistTable.field("f_exam_id", String.class)::eq),
					memberName.map(MEMBER.NAME::contains),
					Optional.of(isRight).map(table.field("f_is_right", Integer.class)::eq),
					memberFullName.map(MEMBER.FULL_NAME::contains),
					memId.map(examRegistTable.field("f_member_id", String.class)::eq),
					depotId.map(QUESTION.QUESTION_DEPOT_ID::eq)
			).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a ->
					a.from(examRegistTable)
							.innerJoin(table).on(table.field("f_exam_record_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
							.innerJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)))
							.innerJoin(QUESTION).on(QUESTION_COPY.QUESTION_ID.eq(QUESTION.ID))
							.innerJoin(MEMBER).on(MEMBER.ID.eq(examRegistTable.field("f_member_id", String.class)))
							.where(param);

			int count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0,Integer.class);

			return count;
		});
	}


	/**
	 *
	 * getforentUserDepotNumById:前端通过知识点ID和考试ID获取用户该知识点的答题正确率数量. <br/>
	 */
	public int getforentUserDepotNumById(Integer examRegion, Optional<String> memberName, Optional<String> memberFullName,String id,String depotId,String memId){

		TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(id));
		TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(id));

		return examRegistDao.execute(e ->{
			int isRight=1;
			SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(examRegistTable.field("f_id", String.class).count()).end());

			List<Condition> param = Stream.of(
					Optional.of(id).map(examRegistTable.field("f_exam_id", String.class)::eq),
					memberName.map(MEMBER.NAME::eq),
					Optional.of(isRight).map(table.field("f_is_right", Integer.class)::eq),
					memberFullName.map(MEMBER.FULL_NAME::eq),
					Optional.of(memId).map(examRegistTable.field("f_member_id", String.class)::eq),
					Optional.of(depotId).map(QUESTION.QUESTION_DEPOT_ID::eq)
			).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a ->
					a.from(examRegistTable)
							.innerJoin(table).on(table.field("f_exam_record_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
							.innerJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)))
							.innerJoin(QUESTION).on(QUESTION_COPY.QUESTION_ID.eq(QUESTION.ID))
							.innerJoin(MEMBER).on(MEMBER.ID.eq(examRegistTable.field("f_member_id", String.class)))
							.where(param);

			int count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0,Integer.class);

			return count;
		});
	}





	public List<QuestionDepot> getExamPersonalHeaderDetail(String id) {

		return questionDepotDao.execute(e ->{
			String parentId="";
			SelectSelectStep<Record> selectListField = e.select(
					Fields.start()
							.add(QUESTION_DEPOT.ID)
							.add(DSL.floor(DSL.sum(QUESTION_COPY.SCORE).div(100)))
							.add(QUESTION_DEPOT.NAME)
							.add(DSL.count(QUESTION_DEPOT.ID).as("num"))
							.end()
			);
			List<Condition> conditions = Stream.of(
					Optional.of(id).map(EXAM.ID::eq),
					Optional.of(parentId).map(s -> QUESTION_DEPOT.PARENT_ID.isNotNull()),
					Optional.of(EXAM.TYPE.in(Exam.EXAM_AUTHENTICATION_TYPE,Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))
			).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a ->
					a.from(QUESTION_DEPOT)
							.innerJoin(QUESTION_COPY).on(QUESTION_DEPOT.ID.eq(QUESTION_COPY.QUESTION_DEPOT_ID))
							.innerJoin(EXAM).on(EXAM.ID.eq(QUESTION_COPY.EXAM_ID))
							.where(conditions);

			SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
			listSetp.groupBy(QUESTION_DEPOT.ID, QUESTION_DEPOT.NAME);
			listSetp.orderBy(EXAM.CREATE_TIME.desc());
			Result<Record> record = listSetp.fetch();
			return record.stream().map(r -> {
				QuestionDepot questionDepot = new QuestionDepot();
				questionDepot.setId(r.getValue(QUESTION_DEPOT.ID));
				questionDepot.setName(r.getValue(QUESTION_DEPOT.NAME));
				BigDecimal big=r.getValue(DSL.floor(DSL.sum(QUESTION_COPY.SCORE).div(100)));
				String name = questionDepot.getName()+"("+big.intValue()+")";
				questionDepot.setQuestionNum(r.get("num", Integer.class));
				questionDepot.setDepotHeaderName(name);
				return questionDepot;
			}).collect(Collectors.toList());


		});
	}

	/**
	 *
	 * getQuestionNum:通过考试id获取该试题总数. <br/>
	 *
	 * <AUTHOR>
	 * @param id
	 * @return
	 * @since JDK 1.8
	 * date: 2017年12月20日 上午10:16:11 <br/>
	 */
	public  Integer getQuestionNum(String id){
		Integer count = examDao.execute(p -> {
			return p.select(
							Fields.start()
									.add(PAPER_CLASS.QUESTION_NUM)
									.end())
					.from(EXAM)
					.leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
					.where(EXAM.ID.eq(id))
					.fetchOne(PAPER_CLASS.QUESTION_NUM);
		});

		return Optional.ofNullable(count).orElse(0);

	}


}
