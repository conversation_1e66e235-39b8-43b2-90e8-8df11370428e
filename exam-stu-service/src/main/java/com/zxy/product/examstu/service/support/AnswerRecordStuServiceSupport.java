package com.zxy.product.examstu.service.support;


import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.examstu.api.AnswerRecordStuService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Service
public class AnswerRecordStuServiceSupport implements AnswerRecordStuService {


    private CommonDao<AnswerRecord> answerRecordDao;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
        this.answerRecordDao = answerRecordDao;
    }


    @Override
    @DataSource
    public int[] batchUpdateAnswerRecord(Integer examRegion, List<AnswerRecord> answerRecords, String examId) {
        String targetTable = getTableUtil.getAnswerRecordStringTable(examId);
        TableImpl<?> answerRecordTable = AnswerRecord.getAnswerRecordTable(targetTable);
        return answerRecordDao.execute(e -> {
            return e.batch(
                    answerRecords.stream().map(t -> {
                        return e.update(answerRecordTable)
                                .set(answerRecordTable.field("f_score", Integer.class), t.getScore())
                                .set(answerRecordTable.field("f_is_right", Integer.class), t.getIsRight())
                                .where(answerRecordTable.field("f_id", String.class).eq(t.getId()));
                    }).collect(Collectors.toList())
            ).execute();
        });
    }

    @Override
    @DataSource
    public Integer deleteReadingAnswer(Integer examRegion, List<String> shouldDeleteAnswerIds, String examId) {
        if (CollectionUtils.isEmpty(shouldDeleteAnswerIds)){
            return 0;
        }

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        return answerRecordDao.execute(dslContext ->
                                        dslContext.delete(table).where(
                                                table.field("f_id", String.class).in(shouldDeleteAnswerIds)
                                        ).execute());
    }



}
