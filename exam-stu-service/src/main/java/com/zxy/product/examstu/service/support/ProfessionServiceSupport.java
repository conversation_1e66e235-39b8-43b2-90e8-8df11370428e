package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ProfessionService;
import com.zxy.product.exam.entity.Profession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class ProfessionServiceSupport implements ProfessionService{

    private CommonDao<Profession> professionDao;

    @Autowired
    public void setProfessionDao(CommonDao<Profession> professionDao) {
        this.professionDao = professionDao;
    }


    @Override
    @DataSource
    public Profession getCloudProfession(Integer examRegion, String id) {
        List<Profession> list = professionDao.execute(e -> {
            return e.select(
                    Fields.start()
                    .add(CLOUD_PROFESSION.ID, CLOUD_PROFESSION.NAME, CLOUD_PROFESSION.CODE, CLOUD_PROFESSION.PARENT_ID)
                    .end())
                    .from(CLOUD_PROFESSION)
                    .where(CLOUD_PROFESSION.ID.eq(id)).fetch(r -> {
                        Profession profession = new Profession();
                        profession.setId(r.getValue(CLOUD_PROFESSION.ID));
                        profession.setName(r.getValue(CLOUD_PROFESSION.NAME));
                        profession.setCode(r.getValue(CLOUD_PROFESSION.CODE));
                        profession.setParentId(r.getValue(CLOUD_PROFESSION.PARENT_ID));
                        return profession;
                    });
        });
        return list != null && list.size() > 0 ? list.get(0) : new Profession();

    }

}
