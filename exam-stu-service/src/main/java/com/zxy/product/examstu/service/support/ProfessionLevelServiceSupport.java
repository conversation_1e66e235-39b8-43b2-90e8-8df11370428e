package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.api.ProfessionLevelService;
import com.zxy.product.exam.entity.ProfessionLevel;
import com.zxy.product.examstu.annotation.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 * @date 2017年11月24日
 */
@Service
public class ProfessionLevelServiceSupport implements ProfessionLevelService {

    private CommonDao<ProfessionLevel> levelDao;

    @Autowired
    public void setLevelDao(CommonDao<ProfessionLevel> levelDao) {
        this.levelDao = levelDao;
    }


    @Override
    @DataSource
    public ProfessionLevel getCloudLevel(Integer examRegion, String id) {
        List<ProfessionLevel> list = levelDao.execute(e -> {
            return e.select(
                    Fields.start()
                    .add(CLOUD_LEVEL.ID, CLOUD_LEVEL.LEVEL_NAME, CLOUD_LEVEL.LEVEL_CODE, CLOUD_LEVEL.LEVEL, CLOUD_LEVEL.VALID_DATE)
                    .end())
                    .from(CLOUD_LEVEL)
                    .where(CLOUD_LEVEL.ID.eq(id))
                    .fetch(r -> {
                        ProfessionLevel level = new ProfessionLevel();
                        level.setId(r.getValue(CLOUD_LEVEL.ID));
                        level.setLevelName(r.getValue(CLOUD_LEVEL.LEVEL_NAME));
                        level.setLevelCode(r.getValue(CLOUD_LEVEL.LEVEL_CODE));
                        level.setLevel(r.getValue(CLOUD_LEVEL.LEVEL));
                        level.setValidDate(r.getValue(CLOUD_LEVEL.VALID_DATE));
                        return level;
                    });
        });
        return list != null && list.size() > 0 ? list.get(0) : new ProfessionLevel();

    }

}
