package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamRecordFaceService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.ExamRecordFace;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: <EMAIL>
 * @Date: 2021/12/7 17:19
 * @Description:
 */
@Service
public class ExamRecordFaceServiceSupport implements ExamRecordFaceService {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamRecordFaceServiceSupport.class);

    private CommonDao<ExamRecordFace> examRecordFaceDao;

    private CommonDao<Exam> examDao;


    private CommonDao<ExamRecord> examRecordDao;

    private MessageSender sender;


    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setSender(MessageSender sender) {
        this.sender = sender;
    }


    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setExamRecordFaceDao(CommonDao<ExamRecordFace> examRecordFaceDao) {
        this.examRecordFaceDao = examRecordFaceDao;
    }



    @Override
    @DataSource
    public List<ExamRecordFace> getListByRecordId(Integer examRegion, String memberId, String examId, String examRecordId, Integer type, Optional<Integer> status) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        examRecordId = Objects.isNull(examRecordId) ? "" : examRecordId;
        List<Condition> conditions = Stream.of(
                Optional.of(examRecordId).map(examRecordFaceTable.field("f_exam_record_id", String.class)::eq),
                Optional.of(type).map(examRecordFaceTable.field("f_type", Integer.class)::eq),
                Optional.of(memberId).map(examRecordFaceTable.field("f_member_id", String.class)::eq),
                Optional.of(examId).map(examRecordFaceTable.field("f_exam_id", String.class)::eq),
                status.map(examRecordFaceTable.field("f_status", Integer.class)::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        return examRecordFaceDao.execute(e -> e.select(
                Fields.start()
                        .add(examRecordFaceTable.fields())
                        .end()
        )
                .from(examRecordFaceTable)
                .where(conditions)
                .fetch(r -> {
                    ExamRecordFace examRecordFace = new ExamRecordFace();
                    examRecordFace.setId(r.getValue(examRecordFaceTable.field("f_id", String.class)));
                    examRecordFace.setStatus(r.getValue(examRecordFaceTable.field("f_status", Integer.class)));
                    return examRecordFace;
                }));
    }




    @Override
    @DataSource
    public ExamRecordFace findReviewRecord(Integer examRegion, String memberId, String examId, String examRecordId) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        List<ExamRecordFace> list = examRecordFaceDao.execute(e ->
                e.select(Fields.start()
                                .add(examRecordFaceTable.field("f_id", String.class))
                                .add(examRecordFaceTable.field("f_status", Integer.class)).end())
                        .from(examRecordFaceTable)
                        .where(examRecordFaceTable.field("f_exam_id", String.class).eq(examId))
                        .and(examRecordFaceTable.field("f_member_id", String.class).eq(memberId))
                        .and(examRecordFaceTable.field("f_type", Integer.class).eq(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER))
                        .and(examRecordFaceTable.field("f_exam_record_id", String.class).eq(examRecordId))
                        .fetch(r -> {
                            ExamRecordFace recordFace = new ExamRecordFace();
                            recordFace.setId(r.get(examRecordFaceTable.field("f_id", String.class)));
                            recordFace.setStatus(r.get(examRecordFaceTable.field("f_status", Integer.class)));
                            return recordFace;
                        }));
        return CollectionUtils.isEmpty(list)?null:list.get(0);
    }



    @Override
    @DataSource
    public void refreshFaceRecord(Integer examRegion, String examId, String memberId, String examRecordId) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        ExamRecordFace needRefreshRecord = examRecordFaceDao.execute(e ->
                e.select(Fields.start()
                                .add(examRecordFaceTable.field("f_id", String.class))
                                .add(examRecordFaceTable.field("f_status", Integer.class)).end())
                        .from(examRecordFaceTable)
                        .where(Arrays.asList(
                                examRecordFaceTable.field("f_member_id", String.class).eq(memberId),
                                examRecordFaceTable.field("f_exam_id", String.class).eq(examId),
                                examRecordFaceTable.field("f_type", Integer.class).eq(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER),
                                examRecordFaceTable.field("f_exam_record_id", String.class).eq(StringUtils.EMPTY)
                        ))
                        .fetchOne(r -> {
                            ExamRecordFace recordFace = new ExamRecordFace();
                            recordFace.setId(r.get(examRecordFaceTable.field("f_id", String.class)));
                            recordFace.setStatus(r.get(examRecordFaceTable.field("f_status", Integer.class)));
                            return recordFace;
                        }));

        if (Objects.isNull(needRefreshRecord)) {
            LOGGER.info("=> 未查询到需要更新的recordFace记录。 无须更新 直接结束");
            return;
        }

        LOGGER.info("=> 查询到需要更新的recordFace记录:{}", needRefreshRecord);
        String faceRecordId = needRefreshRecord.getId();
        Integer status = needRefreshRecord.getStatus();

        //  对faceRecord表进行更新，设置f_exam_record_id的值
        examRecordFaceDao.execute(e ->
                e.update(examRecordFaceTable)
                        .set(examRecordFaceTable.field("f_exam_record_id", String.class), examRecordId)
                        .where(examRecordFaceTable.field("f_id", String.class).eq(faceRecordId))
                        .execute()
        );


        //  刷新examRecord表的状态
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        examRecordDao.execute(e ->
                e.update(examRecordTable)
                        .set(examRecordTable.field("f_face_status", Integer.class), status)
                        .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
                        .execute()
        );
    }



    /**
     * 人脸比对结果
     *
     */
    @Override
    @DataSource
    public ExamRecordFace faceInvigilation(Integer examRegion, String memberId, String examId, Optional<String> examRecordId, Integer type, String faceImageUrl, Integer status, Optional<String> respMsg) {
//        人脸进入考试
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER.equals(type)) {
            // 进入考试时，如果状态为异常只记录人脸数据，不更新状态，发起审核时，再更新状态为异常。防止3次人脸中途退出重进时无法继续人脸
            status = ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ABNORMAL==status?null:status;
            //如果有recordId，更新考试记录的人脸状态
            if (examRecordId.isPresent()) {
                updateExamRecordFaceStatus(examRegion, examId, examRecordId.get(), status);
                return insertOrUpdate(examRegion, memberId, examId, examRecordId.get(), type, faceImageUrl, status, respMsg);
            } else {
                //否则只更新examRecordFace表
                return insertOrUpdate(examRegion, memberId, examId, "", type, faceImageUrl, status, respMsg);
            }
        } else {
            //人脸监考过程中如何有一次人脸异常且recordId不为null就更新考试记录的人脸状态
            if (ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ABNORMAL.equals(status) && examRecordId.isPresent()) {
                updateExamRecordFaceStatus(examRegion, examId, examRecordId.get(), status);
            }
            return insertOrUpdate(examRegion, memberId, examId, examRecordId.orElse(""), type, faceImageUrl, status, respMsg);
        }
    }


    /**
     * 更新考试记录表的人脸状态
     *
     * <AUTHOR>
     * @date 2021/11/20
     */
    @Override
    @DataSource
    public ExamRecord updateExamRecordFaceStatus(Integer examRegion, String examId, String examRecordId, Integer faceStatus) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        examRecordDao.execute(dslContext ->
                dslContext.update(examRecordTable)
                        .set(examRecordTable.field("f_face_status", Integer.class), faceStatus)
                        .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
                        .execute());
        ExamRecord examRecord = new ExamRecord();
        examRecord.setId(examRecordId);
        examRecord.setFaceStatus(faceStatus);
        examRecord.setExamId(examId);
        return examRecord;
    }


    @Override
    @DataSource
    public void frontReview(Integer examRegion, String memberId, String examId) {
        // 申请复核时，更新人脸状态为异常
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        examRecordFaceDao.execute(dslContext ->
                dslContext.update(examRecordFaceTable)
                        .set(examRecordFaceTable.field("f_status", Integer.class), ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ABNORMAL)
                        .where(examRecordFaceTable.field("f_exam_id", String.class).eq(examId)
                                .and(examRecordFaceTable.field("f_member_id", String.class).eq(memberId))
                                .and(examRecordFaceTable.field("f_type", Integer.class).eq(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER))
                                .and(examRecordFaceTable.field("f_status", Integer.class).isNull())
                        )
                        .execute());

        examDao.getOptional(examId).ifPresent(exam -> sender.send(
                MessageTypeContent.FACE_EXAM_REVIEW_NOTICE,
                MessageHeaderContent.MEMBER_ID, memberId,
                MessageHeaderContent.EXAM_ID, exam.getId(),
                MessageHeaderContent.EXAM_NAME, exam.getName(),
                MessageHeaderContent.DATE_TIME, String.valueOf(System.currentTimeMillis())));
    }



    @Override
    @DataSource
    public ExamRecord backReview(Integer examRegion, String examId, String examRecordId, Integer faceStatus) {
        ExamRecord examRecord = updateExamRecordFaceStatus(examRegion, examId, examRecordId, faceStatus);
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        updateStatus(examRecordFaceTable, faceStatus, examRecordId);
        return examRecord;
    }


    private void updateStatus(TableImpl<?> examRecordFaceTable, Integer status, String examRecordId) {
        examRecordFaceDao.execute(dslContext ->
                dslContext.update(examRecordFaceTable)
                        .set(examRecordFaceTable.field("f_status", Integer.class), status)
                        .where(examRecordFaceTable.field("f_exam_record_id", String.class).eq(examRecordId))
                        .execute());
    }



    @Override
    @DataSource
    public String backReviewNew(Integer examRegion, String id, String examId, Optional<String> examRecordId, Integer faceStatus) {
        //examRecordId不为null，更新record表
        examRecordId.ifPresent(recordId-> updateExamRecordFaceStatus(examRegion, examId, recordId, faceStatus));
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        examRecordFaceDao.execute(dslContext ->
                dslContext.update(examRecordFaceTable)
                        .set(examRecordFaceTable.field("f_status", Integer.class), faceStatus)
                        .where(examRecordFaceTable.field("f_id", String.class).in(id.split(",")))
                        .execute());
        return id;
    }


    @Override
    @DataSource
    public Map<Integer, List<ExamRecordFace>> faceDetail(Integer examRegion, String memberId, String examId, Optional<String> examRecordId) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        List<ExamRecordFace> examRecordFaceList = examRecordFaceDao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(examRecordFaceTable.fields())
                                        .end())
                        .from(examRecordFaceTable)
                        .where(examRecordFaceTable.field("f_exam_record_id", String.class).eq(examRecordId.orElse(""))
                                .and(examRecordFaceTable.field("f_exam_id", String.class).eq(examId))
                                .and(examRecordFaceTable.field("f_member_id", String.class).eq(memberId)))
                        .orderBy(examRecordFaceTable.field("f_create_time", String.class).desc())
                        .fetch(r -> {
                            ExamRecordFace examRecordFace = new ExamRecordFace();
                            examRecordFace.setId(r.getValue(examRecordFaceTable.field("f_id", String.class)));
                            examRecordFace.setExamRecordId(r.getValue(examRecordFaceTable.field("f_exam_record_id", String.class)));
                            examRecordFace.setType(r.getValue(examRecordFaceTable.field("f_type", Integer.class)));
                            examRecordFace.setFaceImgUrl(r.getValue(examRecordFaceTable.field("f_face_img_url", String.class)));
                            examRecordFace.setStatus(r.getValue(examRecordFaceTable.field("f_status", Integer.class)));
                            examRecordFace.setCreateTime(r.getValue(examRecordFaceTable.field("f_create_time", Long.class)));

                            return examRecordFace;
                        })
        );

        if (examRecordFaceList == null) {
            return null;
        }

        Map<Integer, List<ExamRecordFace>> groupByType = examRecordFaceList.stream().collect(Collectors.groupingBy(ExamRecordFace::getType));
        //人脸监考照片只取最后的三条记录
        List<ExamRecordFace> examRecordFaceProctor = groupByType.get(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_PROCTOR);
        if (examRecordFaceProctor != null) {
            if (examRecordFaceProctor.size() > 3) {
                examRecordFaceProctor = examRecordFaceProctor.subList(0, 3);
            }
            Collections.reverse(examRecordFaceProctor);
            groupByType.put(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_PROCTOR, examRecordFaceProctor);
        }
        return groupByType;
    }



    @Override
    @DataSource
    public ExamRecordFace upFaceStatus(Integer examRegion, String memberId, String examId, Optional<String> examRecordId, Optional<Integer> type) {
        //如果有recordId，更新考试记录的人脸状态
        if (examRecordId.isPresent()) {
            updateExamRecordFaceStatus(examRegion, examId, examRecordId.get(), ExamRecordFace.STRING_EXAM_RECORD_FACE_STATUS_FOUR);
            return insertOrUpdate(examRegion, memberId, examId, examRecordId.get(), type.orElse(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER),  null, ExamRecordFace.STRING_EXAM_RECORD_FACE_STATUS_FOUR, Optional.of("undetected"));
        } else {
            //否则只更新examRecordFace表
            return insertOrUpdate(examRegion, memberId, examId, "", type.orElse(ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER), null, ExamRecordFace.STRING_EXAM_RECORD_FACE_STATUS_FOUR, Optional.of("undetected"));
        }
    }
    @Override
    @DataSource
    public ExamRecordFace insertOrUpdate(Integer examRegion,String memberId, String examId,  Integer type,  Integer status,Integer num, Optional<String> respMsg) {
        return insert(examRegion,memberId,examId, "", type,num , status, respMsg);
    }
    /**
     * @Description 新增人脸记录表（二机位认证数据）
     */
    public ExamRecordFace insert(Integer examRegion,String memberId, String examId, String examRecordId, Integer type, Integer num, Integer status, Optional<String> respMsg) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        ExamRecordFace examRecordFace = new ExamRecordFace();
        examRecordFace.setExamRecordId(examRecordId);
        respMsg.ifPresent(examRecordFace::setRespMsg);
        examRecordFace.setStatus(status);
        examRecordFace.setType(type);
        examRecordFace.setExamId(examId);
        examRecordFace.setMemberId(memberId);
        examRecordFace.setNum(num);
        examRecordFace.forInsert();
        insertExamRecordFace(examRecordFaceTable, examRecordFace);
        return examRecordFace;
    }


    /**
     * @Description 新增或修改人脸记录表
     * <AUTHOR>
     * @Date 2021/12/18 12:15
     * @Param
     * @Return
     * @Exception
     */
    public ExamRecordFace insertOrUpdate(Integer examRegion, String memberId, String examId, String examRecordId, Integer type, String faceImageUrl, Integer status, Optional<String> respMsg) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        ExamRecordFace examRecordFace = new ExamRecordFace();
        examRecordFace.setExamRecordId(examRecordId);
//        examRecordFace.setFaceImgUrl(faceImageUrl);
        respMsg.ifPresent(examRecordFace::setRespMsg);
        examRecordFace.setStatus(status);
        examRecordFace.setType(type);
        examRecordFace.setExamId(examId);
        examRecordFace.setMemberId(memberId);
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER.equals(type) || ExamRecordFace.STRING_EXAM_RECORD_FACE2_TYPE_ENTER.equals(type)) {
            if (existsByExamRecordIdAndTypeAndExamIdAndMemberId(examRegion, memberId, examId, examRecordId, type)) {
                updateExamRecordFace(examRecordFaceTable, examRecordFace);
            } else {
                examRecordFace.forInsert();
                insertExamRecordFace(examRecordFaceTable, examRecordFace);
            }
        } else {
            examRecordFace.forInsert();
            insertExamRecordFace(examRecordFaceTable, examRecordFace);
        }
        return examRecordFace;
    }

    private void insertExamRecordFace(TableImpl<?> examRecordFaceTable, ExamRecordFace examRecordFace) {

        examRecordFaceDao.execute(e ->
                        e.insertInto(examRecordFaceTable, examRecordFaceTable.field("f_id", String.class),
                                        examRecordFaceTable.field("f_create_time", Long.class),
                                        examRecordFaceTable.field("f_exam_record_id", String.class),
                                        examRecordFaceTable.field("f_type", Integer.class),
//                        examRecordFaceTable.field("f_face_img_url", String.class),
                                        examRecordFaceTable.field("f_resp_msg", String.class),
                                        examRecordFaceTable.field("f_status", Integer.class),
                                        examRecordFaceTable.field("f_exam_id", String.class),
                                        examRecordFaceTable.field("f_member_id", String.class),
                                        examRecordFaceTable.field("f_num", Integer.class)
                                )
                                .values(
                                        examRecordFace.getId(), examRecordFace.getCreateTime(),
                                        examRecordFace.getExamRecordId(), examRecordFace.getType(),
//                                examRecordFace.getFaceImgUrl(),
                                        examRecordFace.getRespMsg(),
                                        examRecordFace.getStatus(), examRecordFace.getExamId(), examRecordFace.getMemberId(),
                                        examRecordFace.getNum()
                                )
                                .execute()
        );

    }



    boolean existsByExamRecordIdAndTypeAndExamIdAndMemberId(Integer examRegion, String memberId, String examId, String examRecordId, Integer type) {

        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        return examRecordFaceDao.execute(e ->
                e.fetchCount(e.select(Fields.start()
                                .add(examRecordFaceTable.field("f_id", String.class))
                                .end())
                        .from(examRecordFaceTable)
                        .where(examRecordFaceTable.field("f_exam_record_id", String.class).eq(examRecordId))
                        .and(examRecordFaceTable.field("f_type", Integer.class).eq(type))
                        .and(examRecordFaceTable.field("f_exam_id", String.class).eq(examId))
                        .and(examRecordFaceTable.field("f_member_id", String.class).eq(memberId)))) > 0;
    }


    private void updateExamRecordFace(TableImpl<?> examRecordFaceTable, ExamRecordFace examRecordFace) {
        examRecordFaceDao.execute(dslContext ->
                dslContext.update(examRecordFaceTable)
//                        .set(examRecordFaceTable.field("f_face_img_url", String.class), examRecordFace.getFaceImgUrl())
                        .set(examRecordFaceTable.field("f_resp_msg", String.class), examRecordFace.getRespMsg())
                        .set(examRecordFaceTable.field("f_status", Integer.class), examRecordFace.getStatus())
                        .where(examRecordFaceTable.field("f_exam_record_id", String.class).eq(examRecordFace.getExamRecordId())
                                .and(examRecordFaceTable.field("f_type", Integer.class).eq(examRecordFace.getType()))
                                .and(examRecordFaceTable.field("f_exam_id", String.class).eq(examRecordFace.getExamId()))
                                .and(examRecordFaceTable.field("f_member_id", String.class).eq(examRecordFace.getMemberId())))
                        .execute());
    }

    @Override
    @DataSource
    public Map<Integer, List<ExamRecordFace>>   faceDetail2(Integer examRegion,String memberId, String examId,Integer... type) {
        TableImpl<?> examRecordFaceTable = getTableUtil.getExamRecordFaceTable(getTableUtil.getExamRecordFaceStringTable(examId));
        List<ExamRecordFace> examRecordFaceList = examRecordFaceDao.execute(e ->
                        e.select(
                                        Fields.start()
                                                .add(examRecordFaceTable.fields())
                                                .end())
                                .from(examRecordFaceTable)
                                .where(
                                        examRecordFaceTable.field("f_exam_id", String.class).eq(examId))
                                .and(examRecordFaceTable.field("f_member_id", String.class).eq(memberId))
                                .and(examRecordFaceTable.field("f_type", Integer.class).in(Arrays.asList(type))))
                .orderBy(examRecordFaceTable.field("f_create_time", String.class).desc())
                .fetch(r -> {
                    ExamRecordFace examRecordFace = new ExamRecordFace();
                    examRecordFace.setId(r.getValue(examRecordFaceTable.field("f_id", String.class)));
                    examRecordFace.setExamRecordId(r.getValue(examRecordFaceTable.field("f_exam_record_id", String.class)));
                    examRecordFace.setType(r.getValue(examRecordFaceTable.field("f_type", Integer.class)));
                    examRecordFace.setFaceImgUrl(r.getValue(examRecordFaceTable.field("f_face_img_url", String.class)));
                    examRecordFace.setStatus(r.getValue(examRecordFaceTable.field("f_status", Integer.class)));
                    examRecordFace.setRespMsg(r.getValue(examRecordFaceTable.field("f_resp_msg", String.class)));
                    examRecordFace.setCreateTime(r.getValue(examRecordFaceTable.field("f_create_time", Long.class)));
                    examRecordFace.setNum(r.getValue(examRecordFaceTable.field("f_num", Integer.class)));

                    return examRecordFace;
                });

        if (examRecordFaceList == null) {
            return null;
        }

        Map<Integer, List<ExamRecordFace>> groupByType = examRecordFaceList.stream().collect(Collectors.groupingBy(ExamRecordFace::getType));
        return groupByType;
    }
}
