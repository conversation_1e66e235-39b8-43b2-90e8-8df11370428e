package com.zxy.product.examstu.service.rule.preaudit;

import static com.zxy.product.exam.jooq.Tables.EXAM;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.ProfessionLevel;

/**
 * Created by ouyang on 2017/11/1.
 * 规则3，子专业内，首次申报可从L2开始,首次申报，以是否考试为准，如果申报L2但是没有考试，可以再次申报L2
 */
@Component("preAuditRule3")
public class PreAuditRule3 implements PreAuditRule {

    private CommonDao<ProfessionLevel> professionLevelDao;

    private CommonDao<ExamRecord> examRecordDao;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setProfessionLevelDao(CommonDao<ProfessionLevel> professionLevelDao) {
        this.professionLevelDao = professionLevelDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Override
    public boolean audit(Exam exam, String memberId) {
            //勾选了规则3，子专业内，首次申报可从L2开始,首次申报，以是否考试为准，如果申报L2但是没有考试，可以再次申报L2
            //这个考试的专业id，子专业id，等级id
            String professionId = exam.getProfessionId();
            String subProfessionId = exam.getSubProfessionId();
            String levelId = exam.getLevelId();
            //这个考试的等级
            Optional<ProfessionLevel> professionLevel = professionLevelDao.getOptional(levelId);
            if (professionLevel.isPresent()) {
                int level = professionLevel.get().getLevel().intValue();

                List<String> examRecordList = new ArrayList<String>();

                String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

                for (String examRecordStringTable : allExamRecordStringTable) {

                    TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

                    // 在我的考试记录里（进入时间不为空）是否有这个专业，子专业的考试(在这个专业，子专业内，我是否为首次申报)
                    List<String> examRecords = examRecordDao.execute(e -> {
                        return e.select(
                                Fields.start()
                                .add(examRecordTable.field("f_id", String.class))
                                .end()
                            )
                            .from(examRecordTable)
                            .leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
                            .where(
                                    examRecordTable.field("f_member_id", String.class).eq(memberId),
                                    examRecordTable.field("f_start_time", Long.class).isNotNull(),
                                    EXAM.PROFESSION_ID.eq(professionId),
                                    EXAM.SUB_PROFESSION_ID.eq(subProfessionId)
                                    )
                            .fetch(examRecordTable.field("f_id", String.class));
                        });
                    examRecordList.addAll(examRecords);
                }

                // 如果examRecordList（进入时间不为空）有记录，说明我不是首次申报，不满足这个规则
                if (examRecordList != null && examRecordList.size() > 0) {
                    return false;
                }else{
                    //如果没记录，是首次申报，看当前考试是否为L2,如果是L2审核通过
                    if (level == 2) {
                        return true;
                    }
                    return false;
                }

            }

        return false;
    }

}
