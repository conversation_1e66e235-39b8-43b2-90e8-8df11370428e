package com.zxy.product.examstu.service.config;


import com.zxy.product.examstu.api.sequence.CodeRule;
import com.zxy.product.examstu.service.component.sequence.RedisIncrementCodeRule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 流水编号规则配置
 * <AUTHOR>
 * @date 2017年11月11日
 */
@Configuration
public class CodeRuleConfig {

    /** 认证考试证书发放编号生成规则器：使用redis生成规则 */
    @Bean
    public CodeRule certificateAuthCodeRule(){
        RedisIncrementCodeRule codeRule = instanceCodeRule("", "Certificate", 4);
        return codeRule;
    }

    /** 普通证书发放编号生成规则器：使用redis生成规则 */
    @Bean
    public CodeRule certificateNormalCodeRule(){
        RedisIncrementCodeRule codeRule = instanceCodeRule("", "CertificateNormal", 6);
        return codeRule;
    }

    private RedisIncrementCodeRule instanceCodeRule(String prefix, String certificate, int length) {
        RedisIncrementCodeRule codeRule = new RedisIncrementCodeRule();
        codeRule.setPrefix(prefix);
        codeRule.setType(certificate);
        codeRule.setLength(length);
        return codeRule;
    }
}
