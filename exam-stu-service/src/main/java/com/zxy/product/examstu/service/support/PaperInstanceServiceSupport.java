package com.zxy.product.examstu.service.support;


import com.alibaba.fastjson.JSON;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.PaperInstanceService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class PaperInstanceServiceSupport implements PaperInstanceService{

	private final static int PAPER_INSTANCE_SIZE = 300;

	private CommonDao<PaperInstance> paperInstanceDao;

	private CommonDao<PaperClass> paperClassDao;

	private CommonDao<PaperClassTactic> paperTacticDao;

	private CommonDao<Question> questionDao;

	private CommonDao<PaperInstanceQuestionCopy> paperInstanceQuestionCopyDao;

	private static final String READ = "READ";

	private GetTableUtil getTableUtil;

	@Autowired
	public void setGetTableUtil(GetTableUtil getTableUtil) {
		this.getTableUtil = getTableUtil;
	}
	@Autowired
	public void setQuestionDao(CommonDao<Question> questionDao) {
		this.questionDao = questionDao;
	}

	@Autowired
	public void setPaperTacticDao(CommonDao<PaperClassTactic> paperTacticDao) {
		this.paperTacticDao = paperTacticDao;
	}

	@Autowired
	public void setPaperClassDao(CommonDao<PaperClass> paperClassDao) {
		this.paperClassDao = paperClassDao;
	}

	@Autowired
	public void setPaperInstanceQuestionCopyDao(CommonDao<PaperInstanceQuestionCopy> paperInstanceQuestionCopyDao) {
		this.paperInstanceQuestionCopyDao = paperInstanceQuestionCopyDao;
	}

    @Autowired
    public void setDao(CommonDao<PaperInstance> dao) {
        this.paperInstanceDao = dao;
    }

	@Autowired
	public void setPaperInstanceDao(CommonDao<PaperInstance> paperInstanceDao) {
		this.paperInstanceDao = paperInstanceDao;
	}

	@Override
	@DataSource
	public List<PaperInstanceQuestionCopy> findPaperInstanceQuestionCopiesByPaperId(Integer examRegion,String paperInstanceId, String examId) {

		TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

		List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceQuestionCopyDao.execute(e ->
																												   e.select(Fields.start()
																																  .add(paperInstanceQuestionCopyTable.fields())
																																  .end())
																													.from(paperInstanceQuestionCopyTable)
																													.where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId))
		).fetch(r -> {
			PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
			paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
			paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
			paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
			paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
			paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
			paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
			return paperInstanceQuestionCopy;
		});

		return paperInstanceQuestionCopies;
	}

    @Override
	@DataSource
    public PaperInstance get(Integer examRegion,String id, String examId) {

        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<String> questionCopyIds = paperInstanceDao.execute(e -> {
            return e.select(
                    paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)
            )
        	.from(paperInstanceQuestionCopyTable)
            .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
            .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(id)
            	.and(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).isNotNull()))
            	.and(QUESTION_COPY.PARENT_ID.isNull())
            .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
            .fetch(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class));
        });

        PaperInstance paper = paperInstanceDao.execute(e -> {
           return e.select(
    		   Fields.start()
    		   .add(PAPER_INSTANCE.fields())
    		  .add(PAPER_CLASS.QUESTION_NUM)
              .add(PAPER_CLASS.TOTAL_SCORE)
              .end()
           )
    	   .from(PAPER_INSTANCE)
           .leftJoin(PAPER_CLASS).on(PAPER_INSTANCE.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
           .where(PAPER_INSTANCE.ID.eq(id))
           .fetchOne(r -> {
               int questionNum = r.getValue(PAPER_CLASS.QUESTION_NUM, Integer.class);
               int totalScore = r.getValue(PAPER_CLASS.TOTAL_SCORE, Integer.class);
               PaperInstance paperInstance = r.into(PaperInstance.class);
               paperInstance.setQuestionNum(questionNum);
               paperInstance.setTotalScore(totalScore);
               return paperInstance;
           });
        });

        if (paper != null) {
            paper.setQuestionCopyIds(questionCopyIds);
            return paper;
        }
        return null;
    }
	private Integer getPaperInstanceSize(List<String> memberIds) {
		return (memberIds != null && memberIds.size() > 0)
			? (memberIds.size() > PAPER_INSTANCE_SIZE ? PAPER_INSTANCE_SIZE : memberIds.size()) : 1;
	}



	@Override
	@DataSource
	public PaperInstance getNewInstance(Integer examRegion, String id, String examId) {

		TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

		List<String> questionCopyIds = paperInstanceDao.execute(e -> {
			return e.select(
							paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)
					)
					.from(paperInstanceQuestionCopyTable)
					.leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
					.where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(id)
							.and(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).isNotNull()))
					// 和上面的方法相比，添加了阅读理解类型的题目,用于前端计算分数
//                    .and(QUESTION_COPY.PARENT_ID.isNull())
					.orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
					.fetch(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class));
		});

		PaperInstance paper = paperInstanceDao.execute(e -> {
			return e.select(
							Fields.start().
									add(PAPER_INSTANCE.fields())
									.add(PAPER_CLASS.QUESTION_NUM)
									.add(PAPER_CLASS.TOTAL_SCORE)
									.end()
					)
					.from(PAPER_INSTANCE)
					.leftJoin(PAPER_CLASS).on(PAPER_INSTANCE.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
					.where(PAPER_INSTANCE.ID.eq(id))
					.fetchOne(r -> {
						int questionNum = r.getValue(PAPER_CLASS.QUESTION_NUM, Integer.class);
						int totalScore = r.getValue(PAPER_CLASS.TOTAL_SCORE, Integer.class);
						PaperInstance paperInstance = r.into(PaperInstance.class);
						paperInstance.setQuestionNum(questionNum);
						paperInstance.setTotalScore(totalScore);
						return paperInstance;
					});
		});
		if (paper != null) {
			paper.setQuestionCopyIds(questionCopyIds);
			return paper;
		}
		return null;
	}


	@Override
	@DataSource
	public PaperInstance getWithRandomByExamId(Integer examRegion, String examId) {

		PaperClass paperClass = paperClassDao.execute(e ->
															  e.select(Fields.start().add(PAPER_CLASS).end())
															   .from(EXAM).leftJoin(PAPER_CLASS).on(EXAM.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
															   .where(EXAM.ID.eq(examId)).fetchOneInto(PaperClass.class));

		if (paperClass == null || paperClass.getId() == null) return new PaperInstance();

		//2017-09-10后期优化 不要用sql rand()
		int count = paperInstanceDao.count(PAPER_INSTANCE.EXAM_ID.eq(examId));
		//当试卷为空时，创建试卷
		// 兜底不在这里做，在前半小时或考试发布之后进行count判断，必须保证学员进入时，paperInstance表有数据
//		if (count < 1) {
//			createPaperInstance(examId);
//			count = paperInstanceDao.count(PAPER_INSTANCE.EXAM_ID.eq(examId));
//		};

		int random = new Random().nextInt(count);
		List<PaperInstance> paperInstances = paperInstanceDao.execute(e -> e.select(Fields.start()
																						  .add(PAPER_INSTANCE.ID)
																						  .add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
																						  .add(PAPER_INSTANCE.IS_SUBJECTIVE)
																						  .end()).from(PAPER_INSTANCE).where(PAPER_INSTANCE.EXAM_ID.eq(examId)).limit(random, 1).fetchInto(PaperInstance.class));

		if (paperInstances.size() > 0) return paperInstances.get(0);

		return paperInstanceDao.execute(d -> {
			Field<BigDecimal> rand = DSL.rand();
			return d.select(
							Fields.start()
								  .add(PAPER_INSTANCE.ID)
								  .add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
								  .add(PAPER_INSTANCE.IS_SUBJECTIVE)
								  .add(rand)
								  .end()
					)
					.from(PAPER_INSTANCE)
					.where(PAPER_INSTANCE.EXAM_ID.eq(examId))
					.orderBy(rand).limit(1)
					.fetchOneInto(PaperInstance.class);
		});
	}

	/**
     * 从策略中查询试题id
     * 返回的map 是一个key可以重复的map,每个key,value 对应的是 每一条策略对应的数量和题目
     * @return {策略中的数量:查询出的题库}
     */
    private Map<Integer, List<PaperClassQuestion>> findQuestionsByTactic(String paperClassId) {

    	Map<Integer, List<PaperClassQuestion>> questionMap = new IdentityHashMap<>();

        paperTacticDao.fetch(PAPER_CLASS_TACTIC.PAPER_CLASS_ID.eq(paperClassId)).forEach(t -> {
            questionMap.put(
                new Integer(t.getAmount()),
                questionDao.execute(d -> {
                    return d.select(
                		QUESTION.ID,
                		QUESTION.SCORE
                    )
                    .from(QUESTION)
                    .leftJoin(QUESTION_DEPOT).on(QUESTION.QUESTION_DEPOT_ID.eq(QUESTION_DEPOT.ID))
                    .where(QUESTION.DIFFICULTY.eq(t.getDifficulty()))
                    	.and(QUESTION.TYPE.eq(t.getType()))
                    	.and(QUESTION.QUESTION_DEPOT_ID.eq(t.getQuestionDepotId()))
                    	.and(QUESTION.PARENT_ID.isNull())
                    	.and(QUESTION.STATUS.eq(Question.PUBLISH))
                    .fetch(r -> {
                        PaperClassQuestion q = new PaperClassQuestion();
                        q.setQuestionId(r.getValue(QUESTION.ID));
                        q.setScore(null == t.getScore() ? r.getValue(QUESTION.SCORE) : t.getScore());
                        q.setSequence(0);
                        return q;
                    });
                })
            );

        });

        return questionMap;
    }

	@Override
	@DataSource
	public List<PaperInstance> findByExamId(Integer examRegion,String ... examId) {

		List<PaperInstance> list = new ArrayList<PaperInstance>();

		for (String eId : examId) {

			TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(eId));

			Result<Record> records = paperInstanceDao.execute(e -> {
				return e.select(
								Fields.start()
									  .add(PAPER_INSTANCE)
									  .add(PAPER_CLASS.QUESTION_NUM)
									  .add(PAPER_CLASS.TOTAL_SCORE)
									  .end()
						)
						.from(PAPER_INSTANCE)
						.leftJoin(PAPER_CLASS).on(PAPER_INSTANCE.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
						.where(PAPER_INSTANCE.EXAM_ID.eq(eId))
						.fetch();
			});

			List<PaperInstance> paperInstances = records.stream().map(r -> {
				PaperInstance paperInstance = r.into(PaperInstance.class);
				paperInstance.setQuestionNum(r.getValue(PAPER_CLASS.QUESTION_NUM));
				paperInstance.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
				return paperInstance;
			}).collect(Collectors.toList());


			List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceDao.execute( e -> {
				return e.select(
								paperInstanceQuestionCopyTable.fields()
						)
						.from(paperInstanceQuestionCopyTable)
						.leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
						.where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).in(paperInstances.stream().map(PaperInstance::getId).collect(Collectors.toList())),QUESTION_COPY.PARENT_ID.isNull()
						)
						.fetch(r -> {
							PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
							paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
							paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
							paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
							paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
							paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
							paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
							return paperInstanceQuestionCopy;
						});
			});

			Map<String, Set<String>> questionCopyIdsMap = new HashMap<>();
			paperInstanceQuestionCopies.forEach(copy -> {
				Set<String> ids = questionCopyIdsMap.getOrDefault(copy.getPaperInstanceId(), new HashSet<>());
				ids.add(copy.getQuestionCopyId());
				questionCopyIdsMap.put(copy.getPaperInstanceId(), ids);
			});

			Map<String, List<PaperInstanceQuestionCopy>> paperInstanceQuestionCopiesMap = paperInstanceQuestionCopies.stream().collect(Collectors.groupingBy(PaperInstanceQuestionCopy::getPaperInstanceId));

			List<PaperInstance> paperInstanceList = paperInstances.stream().map(t -> {
				if (paperInstanceQuestionCopiesMap.get(t.getId()) != null) {
					t.setQuestionCopyIds(paperInstanceQuestionCopiesMap.get(t.getId()).stream().map(PaperInstanceQuestionCopy::getQuestionCopyId).collect(Collectors.toList()));
				}
				return t;
			}).collect(Collectors.toList());

			list.addAll(paperInstanceList);
		}

		return list;

	}

	@Override
	@DataSource
	public List<PaperInstance> findNewPaperByExamId(Integer examRegion,String ... examId) {

		List<PaperInstance> list = new ArrayList<PaperInstance>();

		for (String eId : examId) {

			TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(eId));

			Result<Record> records = paperInstanceDao.execute(e -> {
				return e.select(
								Fields.start()
									  .add(PAPER_INSTANCE)
									  .add(PAPER_CLASS.QUESTION_NUM)
									  .add(PAPER_CLASS.TOTAL_SCORE)
									  .end()
						)
						.from(PAPER_INSTANCE)
						.leftJoin(PAPER_CLASS).on(PAPER_INSTANCE.PAPER_CLASS_ID.eq(PAPER_CLASS.ID))
						.where(PAPER_INSTANCE.EXAM_ID.eq(eId))
						.fetch();
			});

			List<PaperInstance> paperInstances = records.stream().map(r -> {
				PaperInstance paperInstance = r.into(PaperInstance.class);
				paperInstance.setQuestionNum(r.getValue(PAPER_CLASS.QUESTION_NUM));
				paperInstance.setTotalScore(r.getValue(PAPER_CLASS.TOTAL_SCORE));
				return paperInstance;
			}).collect(Collectors.toList());


			List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceDao.execute( e -> {
				return e.select(
								paperInstanceQuestionCopyTable.fields()
						)
						.from(paperInstanceQuestionCopyTable)
						.leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
						.where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).in(paperInstances.stream().map(PaperInstance::getId).collect(Collectors.toList()))
							   // 和上面的方法相比，添加了阅读理解类型的题目
							   //                    ,QUESTION_COPY.PARENT_ID.isNull()
						)
						.fetch(r -> {
							PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
							paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
							paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
							paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
							paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
							paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
							paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
							return paperInstanceQuestionCopy;
						});
			});

			Map<String, Set<String>> questionCopyIdsMap = new HashMap<>();
			paperInstanceQuestionCopies.forEach(copy -> {
				Set<String> ids = questionCopyIdsMap.getOrDefault(copy.getPaperInstanceId(), new HashSet<>());
				ids.add(copy.getQuestionCopyId());
				questionCopyIdsMap.put(copy.getPaperInstanceId(), ids);
			});

			Map<String, List<PaperInstanceQuestionCopy>> paperInstanceQuestionCopiesMap = paperInstanceQuestionCopies.stream().collect(Collectors.groupingBy(PaperInstanceQuestionCopy::getPaperInstanceId));

			List<PaperInstance> paperInstanceList = paperInstances.stream().map(t -> {
				if (paperInstanceQuestionCopiesMap.get(t.getId()) != null) {
					t.setQuestionCopyIds(paperInstanceQuestionCopiesMap.get(t.getId()).stream().map(PaperInstanceQuestionCopy::getQuestionCopyId).collect(Collectors.toList()));
				}
				return t;
			}).collect(Collectors.toList());

			list.addAll(paperInstanceList);
		}

		return list;

	}


	private List<PaperClassQuestion> clonePaperClassQuestions(List<PaperClassQuestion> paperClassQuestions) {
    	List<PaperClassQuestion> temp = new ArrayList<>();
    	temp.addAll(paperClassQuestions);
		return temp;
	}

	@SuppressWarnings("unchecked")
	@Override
	@DataSource
	public String createNewQuestionOrder(Integer examRegion, Optional<PaperInstance> paperInstance, String paperInstanceId, Integer paperSortRule) {
		PaperInstance p = null;

		if (paperInstance.isPresent()) {
			p = paperInstance.get();
		} else if (paperInstanceId != null) {
			p = paperInstanceDao.get(paperInstanceId);
		}

		if (paperInstanceId == null) return null;

		// 只有随机组卷才没有顺序
		if (p.getRemoteOrderContent() == null) return null;

		List<Map<String, Object>> orders = JSON.parseArray(p.getRemoteOrderContent()).stream().map(o -> {
			return (Map<String, Object>)o;
		}).collect(Collectors.toList());


		if (paperSortRule == PaperClass.PAPER_SORT_QUESTION) {
			return JSON.toJSONString(createQuestionSort(orders));
		}

		if (paperSortRule == PaperClass.PAPER_SORT_QUESTION_ATTR) {
			return JSON.toJSONString(createQuestionAttrSort(orders));
		}

		if (paperSortRule == PaperClass.PAPER_SORT_QUESTION_AND_ATTR) {
			List<Map<String, Object>> temp = createQuestionAttrSort(orders);
			return JSON.toJSONString(createQuestionSort(temp));
		}

		return p.getRemoteOrderContent();
	}

	@Override
	@DataSource
	public PaperInstance getSimpleData(Integer examRegion, String id) {
		return paperInstanceDao.execute(e -> e.select(
						Fields.start()
								.add(PAPER_INSTANCE.ID)
								.add(PAPER_INSTANCE.EXAM_ID)
								.add(PAPER_INSTANCE.PAPER_CLASS_ID)
								.add(PAPER_INSTANCE.IS_SUBJECTIVE)
								.add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
								.end())
				.from(PAPER_INSTANCE)
				.where(PAPER_INSTANCE.ID.eq(id))
				.fetchOptional(r -> {
					PaperInstance paperInstance = new PaperInstance();
					paperInstance.setId(r.getValue(PAPER_INSTANCE.ID));
					paperInstance.setExamId(r.getValue(PAPER_INSTANCE.EXAM_ID));
					paperInstance.setPaperClassId(r.getValue(PAPER_INSTANCE.PAPER_CLASS_ID));
					paperInstance.setIsSubjective(r.getValue(PAPER_INSTANCE.IS_SUBJECTIVE));
					paperInstance.setRemoteOrderContent(r.getValue(PAPER_INSTANCE.REMOTE_ORDER_CONTENT));
					return paperInstance;
				})).orElse(null);
	}



	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> createQuestionAttrSort(List<Map<String, Object>> orders) {
		return orders.stream().map(o -> {
			Integer type = (Integer) o.get(QuestionCopy.ORDER_QUESTION_TYPE);
			if (type == Question.SINGLE_CHOOSE || type == Question.MULTIPLE_CHOOSE || type == Question.SORTING) {
				List<Map<String, Object>> attrOrders = (List<Map<String, Object>>) o.get(QuestionCopy.ORDER_QUESTION_ATTR);
				o.put(QuestionCopy.ORDER_QUESTION_ATTR, createQuestionAttrSort0(attrOrders));
			}
			if (type == Question.READING_COMPREHENSION) {
				o.put(QuestionCopy.ORDER_QUESTION_SUBS, createSubQuestionAttr((List<Object>)o.get(QuestionCopy.ORDER_QUESTION_SUBS)));
			}
			return o;
		}).collect(Collectors.toList());
	}

	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> createSubQuestionAttr(List<Object> subs) {
		List<Map<String, Object>> subQuestions = subs.stream().map(s -> {
			return (Map<String, Object>)s;
		}).collect(Collectors.toList());

		return createQuestionAttrSort(subQuestions);
	}

	private List<Map<String, Object>> createQuestionAttrSort0(List<Map<String, Object>> attrOrders) {
		int size = attrOrders.size();
		List<Integer> numbers = createOrderNumbers(size);
		for (Map<String, Object> map : attrOrders) {
			map.put(QuestionCopy.ORDER_QUESTION_ATTR_ORDER, drawOffNumber(numbers));
		}
		return attrOrders;
	}

	private List<Map<String, Object>> createQuestionSort(List<Map<String, Object>> orders) {
		Map<Object, List<Map<String, Object>>> groups = orders.stream().collect(Collectors.groupingBy(o -> o.get(QuestionCopy.ORDER_QUESTION_TYPE)));
		List<Map<String, Object>> newOrder = new ArrayList<>();
		groups.keySet().stream().forEach(k -> {
			List<Map<String, Object>> typeQuestions = groups.get(k);
			newOrder.addAll(createQuestionSort0(typeQuestions));
		});

		return newOrder;
	}

	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> createQuestionSort0(List<Map<String, Object>> typeQuestions) {
		int size = typeQuestions.size();
		List<Integer> numbers = createOrderNumbers(size);
		for (Map<String, Object> map : typeQuestions) {
			map.put(QuestionCopy.ORDER_QUESTION_ORDER, drawOffNumber(numbers));
			if (map.get(QuestionCopy.ORDER_QUESTION_TYPE) == Question.READING_COMPREHENSION) {
				map.put(QuestionCopy.ORDER_QUESTION_SUBS, createSubQuestionSort((List<Object>)map.get(QuestionCopy.ORDER_QUESTION_SUBS)));
			}
		}
		return typeQuestions;
	}

	private Integer drawOffNumber(List<Integer> numbers) {
		Integer index = new Random().nextInt(numbers.size());
		Integer number = numbers.get(index);
		numbers.remove(number);
		return number;
	}


	private List<Integer> createOrderNumbers(int size) {
		List<Integer> orderNums = new ArrayList<>();
		for (int i = 0; i < size; i++) {
			orderNums.add(i);
		}
		return orderNums;
	}


	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> createSubQuestionSort(List<Object> subs) {
		List<Map<String, Object>> newOrders = new ArrayList<>();
		List<Map<String, Object>> subOrders = subs.stream().map(s -> {
			return (Map<String, Object>)s;
		}).collect(Collectors.toList());

		Map<Object, List<Map<String, Object>>> groups = subOrders.stream().collect(Collectors.groupingBy(s -> s.get(QuestionCopy.ORDER_QUESTION_TYPE)));
		groups.keySet().stream().forEach(k -> {
			List<Map<String, Object>> typeQuestions = groups.get(k);
			int size = typeQuestions.size();
			List<Integer> numbers = createOrderNumbers(size);
			for (Map<String, Object> map : typeQuestions) {
				map.put(QuestionCopy.ORDER_QUESTION_ORDER, drawOffNumber(numbers));
			}
			newOrders.addAll(typeQuestions);
		});
		return newOrders;
	}

	@SuppressWarnings("unchecked")
	private String doFormatOrder(Map<String, Object> question) {
		StringBuffer sb = new StringBuffer();
		sb.append(question.get(QuestionCopy.ORDER_QUESTION_ORDER))
			.append(":").append(formatAttrOrder(
				(List<Map<String, Object>>) question.get(QuestionCopy.ORDER_QUESTION_ATTR)));
		return sb.toString();
	}

	private String formatAttrOrder(List<Map<String, Object>> attrOrder) {
		StringBuffer sb = new StringBuffer();
		if (attrOrder != null && attrOrder.size() > 0) {
			for (Map<String, Object> attr : attrOrder) {
				sb.append(attr.get(QuestionCopy.ORDER_QUESTION_ATTR_ORDER)).append("|");
			}
			String attrStr = sb.toString();
			return attrStr.substring(0, attrStr.length() - 1);
		}
		return "";
	}
	private String formatSubQuestionOrder(List<Map<String, Object>> subs) {
		StringBuffer sb = new StringBuffer();
		sb.append("[");
		String subsStr = subs.stream().map(s -> {
			return doFormatOrder(s);
		}).collect(Collectors.joining(","));
		sb.append(subsStr).append("]");
		return sb.toString();
	}

	private void revertQuestionOrder(Map<String, Object> order, String formatOrder) {
		if (formatOrder.indexOf(READ) > -1) {
			revertReadingOrder(order, formatOrder);
		} else {
			String[] arr = formatOrder.split(":");
			order.put(QuestionCopy.ORDER_QUESTION_ORDER, arr[0]);
			if (arr.length > 2 && arr[1] != null) {
				revertAttrOrder(order, arr[1]);
			}
		}
	}

	@SuppressWarnings("unchecked")
	private void revertReadingOrder(Map<String, Object> order, String formatOrder) {
		String formatOrder0 = formatOrder.replace(READ, "");
		String[] arr = formatOrder0.split("@");
		order.put(QuestionCopy.ORDER_QUESTION_ORDER, arr[0]);
		List<Map<String, Object>> subsOrder = (List<Map<String, Object>>) order.get(QuestionCopy.ORDER_QUESTION_SUBS);
		revertSubsOrder(subsOrder, arr[1]);
	}

	private void revertSubsOrder(List<Map<String, Object>> subsOrder, String formatSubsOrder) {
		String formatSubsOrder0 = formatSubsOrder.replace("[", "").replace("]", "");
		List<String> formatSubs = Arrays.asList(formatSubsOrder0.split(","));
		if (subsOrder != null && subsOrder.size() > 0) {
			int i = 0;
			for (String formatOrder : formatSubs) {
				revertQuestionOrder(subsOrder.get(i++), formatOrder);
			}
		}
	}

	@SuppressWarnings("unchecked")
	private void revertAttrOrder(Map<String, Object> order, String attrOrder) {
		String[] arr = attrOrder.split("\\|");
		List<Map<String, Object>> attrOrders = (List<Map<String, Object>>) order.get(QuestionCopy.ORDER_QUESTION_ATTR);
		if (attrOrders != null && attrOrders.size() > 0) {
			int i = 0;
			for (Map<String, Object> ao : attrOrders) {
				ao.put(QuestionCopy.ORDER_QUESTION_ATTR_ORDER, arr[i++]);
			}
		}
	}

}
