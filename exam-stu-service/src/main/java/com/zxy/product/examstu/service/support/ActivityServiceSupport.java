package com.zxy.product.examstu.service.support;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.api.ActivityService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.Activity;
import com.zxy.product.exam.jooq.Tables;

        import static com.zxy.product.exam.jooq.Tables.*;

@Service
public class ActivityServiceSupport implements ActivityService{

    private CommonDao<Activity> activityDao;

    private MessageSender messageSender;

    @Autowired
    public void setDao(CommonDao<Activity> dao) {
        this.activityDao = dao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender){
        this.messageSender=messageSender;
    }

    @Override
    public Activity insert(
    	String coverId,
    	String organizationId,
    	String name,
    	Long startTime,
    	Long endTime,
        Integer duration,
        Integer passScore,
        Integer type,
        String description,
        String targetId) {

    	Activity activity = new Activity();
        activity.setOrganizationId(organizationId);
        activity.setName(name);
        activity.setType(type);
        activity.setTargetId(targetId);
        activity.forInsert();
        activityDao.insert(activity);

        messageSender.send(MessageTypeContent.ACTIVITY_INSERT, MessageHeaderContent.ID, activity.getId());

        return activity;
    }


    @Override
    public Activity getByTargetId(String targetId,int type) {
        return activityDao.fetchOne(Tables.ACTIVITY.TARGET_ID.eq(targetId).and(ACTIVITY.TYPE.eq(type))).orElse(null);
    }

}
