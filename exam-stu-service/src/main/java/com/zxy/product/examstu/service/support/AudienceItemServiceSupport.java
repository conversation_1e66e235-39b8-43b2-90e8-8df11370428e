package com.zxy.product.examstu.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.api.AudienceItemService;
import com.zxy.product.exam.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.zxy.product.exam.jooq.Tables.*;


@Service
public class AudienceItemServiceSupport implements AudienceItemService {

    private CommonDao<AudienceItem> audienceItemDao;

    @Autowired
    public void setAudienceItemDao(CommonDao<AudienceItem> audienceItemDao) {
        this.audienceItemDao = audienceItemDao;
    }

    @Override
    public List<AudienceItem> batchInsert(List<AudienceItem> audienceItems, Integer businessType, String businessId, boolean isPublish) {
        List<String> ids = new ArrayList<>();
        List<String> updateIds = new ArrayList<>();
        List<AudienceItem> addItems = new ArrayList<>();

        audienceItems.forEach(a -> {
            Optional<AudienceItem> item = getByJoin(a.getJoinType(), a.getJoinId());
            if(item.isPresent()){
                AudienceItem oldItem = item.get();
                a.setReferenceCount(oldItem.getReferenceCount() + 1);
                a.setId(oldItem.getId());
                a.setIsAdd(false);
                updateIds.add(oldItem.getId());
            } else {
                a.forInsert();
                a.setReferenceCount(1);
                a.setIsAdd(true);
                ids.add(a.getId());
                addItems.add(a);
            }
        });
        audienceItemDao.insert(addItems);
        changeReferenceCount(updateIds, true);

        return audienceItems;
    }

    @Override
    public Optional<AudienceItem> getByJoin(Integer joinType, String joinId) {
        return audienceItemDao.fetchOne(AUDIENCE_ITEM.JOIN_ID.eq(joinId).and(AUDIENCE_ITEM.JOIN_TYPE.eq(joinType)));
    }

    /**
     * 修改引用此次数
     * @param ids 需要修改的id
     * @param isAdd 添加还是减少 1
     * @return 被修改的条数
     */
    private int changeReferenceCount(List<String> ids, boolean isAdd) {
        int operatorNumber = isAdd ? 1 : -1;
        return audienceItemDao.execute(e -> {
           return e.update(AUDIENCE_ITEM)
           .set(AUDIENCE_ITEM.REFERENCE_COUNT, AUDIENCE_ITEM.REFERENCE_COUNT.add(operatorNumber))
           .where(AUDIENCE_ITEM.ID.in(ids)).execute();
        });
    }


    public List<AudienceItem> getAudienceItem(List<String> ids, int businessType, int offset, int limit) {
        return audienceItemDao.execute(e ->
                e.select(AUDIENCE_ITEM.ID,AUDIENCE_ITEM.JOIN_ID,AUDIENCE_ITEM.JOIN_TYPE,AUDIENCE_OBJECT.TARGET_ID)
                        .from(AUDIENCE_OBJECT)
                        .join(AUDIENCE_ITEM)
                        .on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_ITEM.ID).and(AUDIENCE_OBJECT.TYPE.eq(businessType)))
                        .where(AUDIENCE_OBJECT.TARGET_ID.in(ids).and(AUDIENCE_ITEM.ID.isNotNull()))
                        .limit(offset , limit)
                        .fetch(record -> {
                            String itemId = record.getValue(AUDIENCE_ITEM.ID);
                            String joinId = record.getValue(AUDIENCE_ITEM.JOIN_ID);
                            Integer joinType = record.getValue(AUDIENCE_ITEM.JOIN_TYPE);
                            String businessId = record.getValue(AUDIENCE_OBJECT.TARGET_ID);
                            AudienceItem audienceItem = new AudienceItem();
                            audienceItem.setId(itemId);
                            audienceItem.setJoinId(joinId);
                            audienceItem.setJoinType(joinType);
                            AudienceObject audienceObject = new AudienceObject();
                            audienceObject.setTargetId(businessId);
                            audienceItem.setAudienceObject(audienceObject);
                            return audienceItem;
                        })
        );
    }
}
