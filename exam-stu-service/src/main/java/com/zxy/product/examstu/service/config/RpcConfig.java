package com.zxy.product.examstu.service.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.zxy.common.rpc.spring.config.RemoteServicePackageConfig;

@Configuration
public class RpcConfig {

    @Bean
    public RemoteServicePackageConfig remoteServicePackageConfig() {
        return new RemoteServicePackageConfig(new String[] {
                "com.zxy.product.examstu.api",
                "com.zxy.product.examstu.api.sequence",
                "com.zxy.product.examstu.api.sequence.business"
        });
    }

}
