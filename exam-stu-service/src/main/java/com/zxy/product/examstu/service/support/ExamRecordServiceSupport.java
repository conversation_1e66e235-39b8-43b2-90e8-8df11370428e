package com.zxy.product.examstu.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;

import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.MessageConstant;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;

import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class ExamRecordServiceSupport implements ExamRecordService {

	private final static Logger LOGGER = LoggerFactory.getLogger(ExamRecordServiceSupport.class);

	private static final int DB_PER_SIZE = 100000;

	public final static int PAGE_SIZE = 500;

	private static final Integer PER_SIZE = 1000;

	private CommonDao<ExamRecord> examRecordDao;

	private CommonDao<PaperInstance> paperInstanceDao;

	private CommonDao<Member> memberDao;

	private CommonDao<Exam> examDao;

	private PaperInstanceService paperInstanceService;

	private SignUpService signUpService;

	private MessageSender messageSender;

	private CommonDao<QuestionCopy> questionCopyDao;

	private Cache examRecordTimeCache;

	private CommonDao<ExamRegist> examRegistDao;

	private CommonDao<CloudSignup> cloudSignupDao;
	private CommonDao<SignUp> signUpDao;
	private CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao;
	private CommonDao<AudienceObject> audienceObjectDao;
	private CommonDao<GridSignup> gridSignupDao;
	private CommonDao<AnswerRecord> answerRecordDao;

	private ExamNoticeService examNoticeService;

	private GetTableUtil getTableUtil;

	@Autowired
	public void setGetTableUtil(GetTableUtil getTableUtil) {
		this.getTableUtil = getTableUtil;
	}

	@Autowired
	public void setExamNoticeService(ExamNoticeService examNoticeService) {
		this.examNoticeService = examNoticeService;
	}

	@Autowired
	public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
		this.answerRecordDao = answerRecordDao;
	}

	@Autowired
	public void setGridSignupDao(CommonDao<GridSignup> gridSignupDao) {
		this.gridSignupDao = gridSignupDao;
	}

	@Autowired
	public void setSignUpService(SignUpService signUpService) {
		this.signUpService = signUpService;
	}

	@Autowired
	public void setAudienceObjectDao(CommonDao<AudienceObject> audienceObjectDao) {
		this.audienceObjectDao = audienceObjectDao;
	}

	@Autowired
	public void setExamStudyPlanConfigCommonDao(CommonDao<ExamStudyPlanConfig> examStudyPlanConfigCommonDao) {
		this.examStudyPlanConfigCommonDao = examStudyPlanConfigCommonDao;
	}

	@Autowired
	public void setSignUpDao(CommonDao<SignUp> signUpDao) {
		this.signUpDao = signUpDao;
	}

	@Autowired
	public void setCloudSignupDao(CommonDao<CloudSignup> cloudSignupDao) {
		this.cloudSignupDao = cloudSignupDao;
	}

	@Autowired
	public void setExamRegistDao(CommonDao<ExamRegist> examRegistDao) {
		this.examRegistDao = examRegistDao;
	}

	@Autowired
	public void setPaperInstanceDao(CommonDao<PaperInstance> paperInstanceDao) {
		this.paperInstanceDao = paperInstanceDao;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.examRecordTimeCache = cacheService.create("fenbiao", "exam-record-time");
	}

	@Autowired
	public void setQuestionCopyDao(CommonDao<QuestionCopy> questionCopyDao) {
		this.questionCopyDao = questionCopyDao;
	}


	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}



	@Autowired
	public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
		this.examRecordDao = examRecordDao;
	}


	@Autowired
	public void setExamDao(CommonDao<Exam> examDao) {
		this.examDao = examDao;
	}

	@Autowired
	public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
		this.paperInstanceService = paperInstanceService;
	}



	@Autowired
	public void setMemberDao(CommonDao<Member> memberDao) {
		this.memberDao = memberDao;
	}

	@Override
	@DataSource
	public Integer calculateExamTimes(Integer examRegion, String examId, String memberId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		return examRecordDao.execute(e ->
				e.select(DSL.count(examRecordTable.field("f_id", String.class)))
						.from(examRecordTable)
						.where(
								examRecordTable.field("f_exam_id", String.class).eq(examId),
								examRecordTable.field("f_member_id", String.class).eq(memberId),
								examRecordTable.field("f_status", Integer.class).gt(ExamRecord.STATUS_TIME_EXCEPTION)
						).fetchOne(DSL.count(examRecordTable.field("f_id", String.class)))
		);
	}

	@Override
	@DataSource
	public String updateBeforeRecordBeNoCurrent(Integer examRegion, ExamRecord examRecord) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examRecord.getExamId()));

		examRecordDao.execute(e -> e.update(examRecordTable)
									.set(examRecordTable.field("f_is_current", Integer.class), ExamRecord.IS_NOT_CURRENT)
									.where(examRecordTable.field("f_exam_id", String.class).eq(examRecord.getExamId()),
										   examRecordTable.field("f_member_id", String.class).eq(examRecord.getMemberId()),
										   examRecordTable.field("f_create_time", Long.class).lt(examRecord.getCreateTime()),
										   examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)).execute());
		return examRecord.getId();
	}

	private Member getMember(String memberId) {
		return memberDao.execute(e ->
										 e.select(
												  Fields.start()
														.add(MEMBER.ID)
														.add(MEMBER.NAME)
														.add(MEMBER.FULL_NAME)
														.end()
										  )
										  .from(MEMBER)
										  .where(MEMBER.ID.eq(memberId)).fetchOneInto(Member.class)
		);
	}

	@Override
	@DataSource
	public ExamRecord getNewestRecord(Integer examRegion, String examId, String memberId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> examRecords = examRecordDao.execute(e -> {
			return e.select(
							Fields.start()
									.add(examRecordTable.field("f_id", String.class))
									.add(examRecordTable.field("f_create_time", Long.class))
									.add(examRecordTable.field("f_start_time", Long.class))
									.add(examRecordTable.field("f_end_time", Long.class))
									.add(examRecordTable.field("f_submit_time", Long.class))
									.add(examRecordTable.field("f_last_submit_time", Long.class))
									.add(examRecordTable.field("f_is_reset", Integer.class))
									.add(examRecordTable.field("f_status", Integer.class))
									.add(examRecordTable.field("f_order_content", String.class))
									.add(examRecordTable.field("f_member_id", String.class))
									.add(examRecordTable.field("f_exam_id", String.class))
									.add(examRecordTable.field("f_paper_instance_id", String.class))
									.add(examRecordTable.field("f_exam_times", Integer.class))
									.add(examRecordTable.field("f_personal_code", Integer.class))
									.add(examRecordTable.field("f_face_status", Integer.class))
									.end()
					)
					.from(examRecordTable)
					.where(
							examRecordTable.field("f_exam_id", String.class).eq(examId),
							examRecordTable.field("f_member_id", String.class).eq(memberId),
							examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
					).orderBy(examRecordTable.field("f_create_time", Long.class).desc()).limit(0, 1).fetch(r -> {
						ExamRecord examRecord = new ExamRecord();
						examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
						examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
						examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
						examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
						examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
						examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
						examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
						examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
						examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
						examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
						examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
						examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
						examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
						examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
						examRecord.setFaceStatus(r.getValue(examRecordTable.field("f_face_status", Integer.class)));
						return examRecord;
					});
		});

		if (examRecords.size() > 0) return examRecords.get(0);

		return null;
	}


	@Override
	@DataSource
	public ExamRecord insert(
			Integer examRegion,
			String examId,
			String paperInstanceId,
			String memberId,
			Integer status,
			Optional<Long> startTime,
			Optional<Long> endTime,
			Optional<Integer> paperSortRule,
			Optional<Integer> examedTimes,
			Optional<Integer> personalCode) {

		ExamRecord examRecord = new ExamRecord();
		examRecord.forInsert();
		examRecord.setExamId(examId);
		examRecord.setPaperInstanceId(paperInstanceId);
		examRecord.setMemberId(memberId);
		examRecord.setStatus(status);
		examRecord.setExceptionOrder(null);
		examRecord.setIsCurrent(ExamRecord.CURRENT);
		examRecord.setPersonalCode(personalCode.orElse(null));
		startTime.ifPresent(examRecord::setStartTime);
		endTime.ifPresent(examRecord::setEndTime);
		examRecord.setExamTimes(examedTimes.map(e -> e + 1).orElseGet(() -> {
			Integer count = calculateExamTimes(examRegion, examId, memberId);
			return count == 0 ? 1 : count + 1;
		}));
		insertExamRecord(examRegion, examRecord);

		String newOrderJson = paperInstanceService.createNewQuestionOrder(examRegion,
				Optional.empty(), paperInstanceId, paperSortRule.orElseGet(() -> {
					return examDao.get(examId).getPaperSortRule();
				})
		);
		examRecord.setOrderContent(newOrderJson);
		examRecord.setMember(getMember(examRecord.getMemberId()));

		messageSender.send(
				MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
				MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
				MessageHeaderContent.ID, examRecord.getId(),
				MessageHeaderContent.IDS, examRecord.getMemberId(),
				MessageHeaderContent.EXAM_ID,examId,
				MessageHeaderContent.STATUS, String.valueOf(examRecord.getStatus()),
				MessageHeaderContent.QUESTION_ORDER, Optional.ofNullable(newOrderJson).orElse(""));

		return examRecord;
	}


	/**
	 * 非报名，推送个人中心的考试发布后需要自动生成考生记录
	 * ps: 考试可以多次撤销编辑，需要判断前状态是否开考中
	 *     对于开考中的考生记录是没有影响的，对于开考前的状态
	 *     已有的考生记录应该更改试卷，对新增的考生直接插入新的记录
	 */
	@Override
	@DataSource
	public List<ExamRecord> insert(Integer examRegion, String examId, List<String> memberIds) {

		if (CollectionUtils.isEmpty(memberIds)) {
			return new ArrayList<>();
		}

		Exam exam = examDao.get(examId);

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> existedRecords = examRecordDao.execute(e ->
				e.select(Fields.start()
								.add(examRecordTable.fields())
								.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId), examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
		).fetch(r -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
			examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
			examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
			examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
			examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
			examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
			examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
			examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
			examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
			examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
			examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
			examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
			examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
			examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
			examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
			examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
			examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
			examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
			examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
			examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
			examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
			examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
			examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
			examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
			examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
			examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
			examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
			return examRecord;
		});

		Map<String, ExamRecord> memberMap = existedRecords.stream().collect(Collectors.toMap(ExamRecord::getMemberId, e -> e, (t1, t2) -> t2));

		List<PaperInstance> paperInstances = paperInstanceDao.fetch(PAPER_INSTANCE.EXAM_ID.eq(exam.getId()));
//		if (CollectionUtils.isEmpty(paperInstances)) {
//			paperInstanceService.createPaperInstance(examId);
//		}
		//生成新的记录
		List<ExamRecord> result = memberIds.stream().map(t -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.forInsert();
			examRecord.setExamId(examId);
			PaperInstance paperInstance = getRandomInstance(paperInstances);
			if (paperInstance!=null){
				examRecord.setPaperInstanceId(paperInstance.getId());
				examRecord.setPaperInstance(paperInstance);
			}
			examRecord.setMemberId(t);
			examRecord.setStatus(ExamRecord.STATUS_TO_BE_STARTED);
			examRecord.setExceptionOrder(null);
			examRecord.setIsCurrent(ExamRecord.CURRENT);
			examRecord.setExamTimes(ExamRecord.FIRST_TIME);
			return examRecord;
		}).collect(Collectors.toList());

		//过滤需要新增的记录
		List<ExamRecord> inserts = result.stream().filter(t -> memberMap.get(t.getMemberId()) == null).map(e -> {
			if (CollectionUtils.isNotEmpty(paperInstances)){
				PaperInstance paperInstance = getRandomInstance(paperInstances);
				e.setOrderContent(
						paperInstanceService.createNewQuestionOrder(
								examRegion,
								Optional.ofNullable(paperInstance),
								e.getPaperInstanceId(), exam.getPaperSortRule()));
			}
			return e;
		}).collect(Collectors.toList());

		List<List<ExamRecord>> bigList = Lists.partition(inserts, 10000);

		for (List<ExamRecord> insertsTemp : bigList) {

			Field<?> [] fileds = {
					examRecordTable.field("f_id", String.class),
					examRecordTable.field("f_member_id", String.class),
					examRecordTable.field("f_organization_id", String.class),
					examRecordTable.field("f_start_time", Long.class),
					examRecordTable.field("f_end_time", Long.class),
					examRecordTable.field("f_last_submit_time", Long.class),
					examRecordTable.field("f_score", Integer.class),
					examRecordTable.field("f_client_type", Integer.class),
					examRecordTable.field("f_status", Integer.class),
					examRecordTable.field("f_exam_id", String.class),
					examRecordTable.field("f_paper_instance_id", String.class),
					examRecordTable.field("f_exam_number", Integer.class),
					examRecordTable.field("f_create_time", Long.class),
					examRecordTable.field("f_submit_time", Long.class),
					examRecordTable.field("f_duration", Long.class),
					examRecordTable.field("f_is_reset", Integer.class),
					examRecordTable.field("f_is_current", Integer.class),
					examRecordTable.field("f_is_finished", Integer.class),
					examRecordTable.field("f_exception_order", Integer.class),
					examRecordTable.field("f_order_content", String.class),
					examRecordTable.field("f_exam_times", Integer.class),
					examRecordTable.field("f_switch_times", Integer.class),
					examRecordTable.field("f_personal_code", Integer.class),
					examRecordTable.field("f_user_ip", String.class),
					examRecordTable.field("f_no_answer_count", Integer.class),
					examRecordTable.field("f_answered_count", Integer.class),
					examRecordTable.field("f_client_version", String.class)
			};

			examRecordDao.execute(e->{
				InsertValuesStepN<?> step = e.insertInto(examRecordTable,fileds);
				insertsTemp.forEach(examRecord->{
					step.values(
							examRecord.getId(),
							examRecord.getMemberId(),
							examRecord.getOrganizationId(),
							examRecord.getStartTime(),
							examRecord.getEndTime(),
							examRecord.getLastSubmitTime(),
							examRecord.getScore(),
							examRecord.getClientType(),
							examRecord.getStatus(),
							examRecord.getExamId(),
							examRecord.getPaperInstanceId(),
							examRecord.getExamNumber(),
							examRecord.getCreateTime(),
							examRecord.getSubmitTime(),
							examRecord.getDuration(),
							examRecord.getIsReset(),
							examRecord.getIsCurrent(),
							examRecord.getIsFinished(),
							examRecord.getExceptionOrder(),
							examRecord.getOrderContent(),
							examRecord.getExamTimes(),
							examRecord.getSwitchTimes(),
							examRecord.getPersonalCode(),
							examRecord.getUserIp(),
							examRecord.getNoAnswerCount(),
							examRecord.getAnsweredCount(),
							examRecord.getClientVersion()
					);
				});
				return step.execute();
			});
		}

		//指定考试 批量生成考试记录发送消息
		sendMessageForCreatingExamRecord(examRegion, examId, inserts.stream().map(ExamRecord::getMemberId).collect(Collectors.joining(",")));

		//判断是否非开考中的 考试  （如果考试前状态是已开考，对于在进行中的考试记录，不用更新）
		if (isStartingOfPreviousStatus(exam)) {
			List<ExamRecord> updates = existedRecords.stream().filter(t -> ExamRecord.STATUS_TO_BE_STARTED.equals(t.getStatus())).map(e -> {
				if (CollectionUtils.isNotEmpty(paperInstances)){
					PaperInstance paperInstance = getRandomInstance(paperInstances);
					if (paperInstance !=null) {
						e.setPaperInstanceId(paperInstance.getId());
						e.setOrderContent(
								paperInstanceService.createNewQuestionOrder(
										examRegion,
										Optional.of(paperInstance),
										paperInstance.getId(), exam.getPaperSortRule()));
					}
				}
				return e;
			}).collect(Collectors.toList());

			for (ExamRecord examRecord : updates) {
				examRecordDao.execute(dslContext ->
						dslContext.update(examRecordTable)
								.set(examRecordTable.field("f_member_id", String.class),examRecord.getMemberId())
								.set(examRecordTable.field("f_organization_id", String.class),examRecord.getOrganizationId())
								.set(examRecordTable.field("f_start_time", Long.class),examRecord.getStartTime())
								.set(examRecordTable.field("f_end_time", Long.class),examRecord.getEndTime())
								.set(examRecordTable.field("f_last_submit_time", Long.class),examRecord.getLastSubmitTime())
								.set(examRecordTable.field("f_score", Integer.class),examRecord.getScore())
								.set(examRecordTable.field("f_client_type", Integer.class),examRecord.getClientType())
								.set(examRecordTable.field("f_status", Integer.class),examRecord.getStatus())
								.set(examRecordTable.field("f_exam_id", String.class), examRecord.getExamId())
								.set(examRecordTable.field("f_paper_instance_id", String.class), examRecord.getPaperInstanceId())
								.set(examRecordTable.field("f_exam_number", Integer.class),examRecord.getExamNumber())
								.set(examRecordTable.field("f_create_time", Long.class),examRecord.getCreateTime())
								.set(examRecordTable.field("f_submit_time", Long.class), examRecord.getSubmitTime())
								.set(examRecordTable.field("f_duration", Long.class),examRecord.getDuration())
								.set(examRecordTable.field("f_is_reset", Integer.class),examRecord.getIsReset())
								.set(examRecordTable.field("f_is_current", Integer.class),examRecord.getIsCurrent())
								.set(examRecordTable.field("f_is_finished", Integer.class),examRecord.getIsFinished())
								.set(examRecordTable.field("f_exception_order", Integer.class),examRecord.getExceptionOrder())
								.set(examRecordTable.field("f_order_content", String.class), examRecord.getOrderContent())
								.set(examRecordTable.field("f_exam_times", Integer.class),examRecord.getExamTimes())
								.set(examRecordTable.field("f_switch_times", Integer.class),examRecord.getSwitchTimes())
								.set(examRecordTable.field("f_personal_code", Integer.class),examRecord.getPersonalCode())
								.set(examRecordTable.field("f_user_ip", String.class),examRecord.getUserIp())
								.set(examRecordTable.field("f_no_answer_count", Integer.class),examRecord.getNoAnswerCount())
								.set(examRecordTable.field("f_answered_count", Integer.class),examRecord.getAnsweredCount())
								.set(examRecordTable.field("f_client_version", String.class),examRecord.getClientVersion())
								.where(examRecordTable.field("f_id", String.class).eq(examRecord.getId()))
								.execute());
			}
		}

		return result;
	}

	private boolean isStartingOfPreviousStatus(Exam exam) {
		return exam.getPreviousStatus() != null && !Exam.STATUS_STARTING.equals(exam.getPreviousStatus());
	}



	/**
	 * * 推送转非推送，删除待考试的考试记录
	 */
	@Override
	@DataSource
	public void deleteExamRecordBySenderToCenterExam(Integer examRegion, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<String> examRecordIds = examRecordDao.execute(ar -> ar.select(Fields.start().add(examRecordTable.field("f_id", String.class)).end())
				.from(examRecordTable)
				.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
				.and(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED))
				.fetch(examRecordTable.field("f_id", String.class))
		);

		examRecordIds.forEach(examRecordId -> {
			examRecordDao.execute(dslContext ->
					dslContext.delete(examRecordTable).where(
							examRecordTable.field("f_id", String.class).eq(examRecordId)
					).execute());
		});
		messageSender.send(MessageTypeContent.EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER,
				MessageHeaderContent.EXAM_ID, examId,
				MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
		);
	}


	@Override
	@DataSource
	public ExamRecord getExamRecordSubmitTime(Integer examRegion, String memberId, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		List<ExamRecord> examRecordList = examRecordDao.execute(e ->
				e.select(Fields.start()
								.add(examRecordTable.field("f_submit_time", Long.class))
								.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
						.and(examRecordTable.field("f_member_id", String.class).eq(memberId))
						.and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
		).orderBy(examRecordTable.field("f_create_time", Long.class).desc()).limit(0, 1).fetch(r -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
			return examRecord;
		});
		return CollectionUtils.isNotEmpty(examRecordList)?examRecordList.get(0):new ExamRecord();
	}

	@Override
	@DataSource
	public List<String> findExamRecordMemberIds(Integer examRegion, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		return examRecordDao.execute(e ->
				e.selectDistinct(examRecordTable.field("f_member_id", String.class))
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
						.fetch(examRecordTable.field("f_member_id", String.class)));

	}

	/**
	 * 非报名，推送个人中心的考试发布后需要自动生成考生记录
	 * ps: 考试可以多次撤销编辑，需要判断前状态是否开考中
	 *     对于开考中的考生记录是没有影响的，对于开考前的状态
	 *     已有的考生记录应该更改试卷，对新增的考生直接插入新的记录
	 * @param examId
	 * @param memberIds
	 * @param paperInstances
	 * @return
	 */
	@Override
	@DataSource
	public void batchInsertExamRecord(Integer examRegion, String examId, List<String> memberIds, List<PaperInstance> paperInstances) {

		if (memberIds == null || memberIds.size() < 0) return;

		Exam exam = examDao.get(examId);

		//生成记录时先清除分表缓存
		examRecordTimeCache.clear(examId);
		//已存在的考试记录
		List<ExamRecord> existedRecords = findExistedExamRecords(examRegion, examId, memberIds);

		Map<String, ExamRecord> memberMap = existedRecords.stream()
				.collect(Collectors.toMap(ExamRecord::getMemberId, e -> e, (t1, t2) -> t2));

		//过滤已存在的考试记录
		List<ExamRecord> inserts = createInsertExamRecords(examId, paperInstances, memberIds, memberMap);

		batchInsertExamRecord(examRegion, inserts, examId);

		//指定考试 批量生成考试记录发送消息
		sendMessageForCreatingExamRecord(examRegion, examId,
				inserts.stream().map(ExamRecord::getMemberId).collect(Collectors.joining(",")));

		batchUpdateExamRecord(examRegion, exam, existedRecords, paperInstances);
	}


	@Override
	@DataSource
	public void batchOtherInsertExamRecord(Integer examRegion, String examId, List<String> memberIds, List<PaperInstance> paperInstances) {

		if (memberIds == null || memberIds.size() < 0) return;

		Exam exam = examDao.get(examId);

		//生成记录时先清除分表缓存
		examRecordTimeCache.clear(examId);
		//已存在的考试记录
		List<ExamRecord> existedRecords = findOtherExistedExamRecords(examRegion, examId);

		Map<String, ExamRecord> memberMap = existedRecords.stream()
				.collect(Collectors.toMap(ExamRecord::getMemberId, e -> e, (t1, t2) -> t2));

		//过滤已存在的考试记录
		List<ExamRecord> inserts = createInsertExamRecords(examId, paperInstances, memberIds, memberMap);

		batchInsertExamRecord(examRegion, inserts, examId);

		//指定考试 批量生成考试记录发送消息
		sendMessageForCreatingExamRecord(examRegion, examId,
				inserts.stream().map(ExamRecord::getMemberId).collect(Collectors.joining(",")));

		batchUpdateExamRecord(examRegion, exam, existedRecords, paperInstances);
	}

	/**
	 * 发送生成考试记录消息
	 * 我的任务的异步会监听该消息(taskListener)
	 * @param examId
	 * @param memberIds
	 */
	private void sendMessageForCreatingExamRecord(Integer examRegion, String examId, String memberIds) {
		messageSender.send(
				MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
				MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
				MessageHeaderContent.IDS,memberIds,
				MessageHeaderContent.EXAM_ID,examId,
				MessageHeaderContent.STATUS,String.valueOf(ExamRecord.STATUS_TO_BE_STARTED));
	}


	private void batchInsertExamRecord(Integer examRegion, List<ExamRecord> inserts, String examId) {
		//先清除分表缓存
		examRecordTimeCache.clear(examId);
		Long st,ed;
		List<List<ExamRecord>> examRecordInsertListPages = Lists.partition(inserts, DB_PER_SIZE);
		System.out.println("insert examRecordbigList PER_SIZE:100000,开始.., size:" +  inserts.size());
		for (List<ExamRecord> insertsTemp : examRecordInsertListPages) {
			st = System.currentTimeMillis();

			TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

			Field<?>[] fileds = {
					examRecordTable.field("f_id", String.class),
					examRecordTable.field("f_member_id", String.class),
					examRecordTable.field("f_organization_id", String.class),
					examRecordTable.field("f_start_time", Long.class),
					examRecordTable.field("f_end_time", Long.class),
					examRecordTable.field("f_last_submit_time", Long.class),
					examRecordTable.field("f_score", Integer.class),
					examRecordTable.field("f_client_type", Integer.class),
					examRecordTable.field("f_status", Integer.class),
					examRecordTable.field("f_exam_id", String.class),
					examRecordTable.field("f_paper_instance_id", String.class),
					examRecordTable.field("f_exam_number", Integer.class),
					examRecordTable.field("f_create_time", Long.class),
					examRecordTable.field("f_submit_time", Long.class),
					examRecordTable.field("f_duration", Long.class),
					examRecordTable.field("f_is_reset", Integer.class),
					examRecordTable.field("f_is_current", Integer.class),
					examRecordTable.field("f_is_finished", Integer.class),
					examRecordTable.field("f_exception_order", Integer.class),
					examRecordTable.field("f_order_content", String.class),
					examRecordTable.field("f_exam_times", Integer.class),
					examRecordTable.field("f_switch_times", Integer.class),
					examRecordTable.field("f_personal_code", Integer.class),
					examRecordTable.field("f_user_ip", String.class),
					examRecordTable.field("f_no_answer_count", Integer.class),
					examRecordTable.field("f_answered_count", Integer.class),
					examRecordTable.field("f_client_version", String.class)
			};
			examRecordDao.execute(e->{
				InsertValuesStepN<?> step = e.insertInto(examRecordTable,fileds);
				insertsTemp.forEach(examRecord->{
					step.values(
							examRecord.getId(),
							examRecord.getMemberId(),
							examRecord.getOrganizationId(),
							examRecord.getStartTime(),
							examRecord.getEndTime(),
							examRecord.getLastSubmitTime(),
							examRecord.getScore(),
							examRecord.getClientType(),
							examRecord.getStatus(),
							examRecord.getExamId(),
							examRecord.getPaperInstanceId(),
							examRecord.getExamNumber(),
							examRecord.getCreateTime(),
							examRecord.getSubmitTime(),
							examRecord.getDuration(),
							examRecord.getIsReset(),
							examRecord.getIsCurrent(),
							examRecord.getIsFinished(),
							examRecord.getExceptionOrder(),
							examRecord.getOrderContent(),
							examRecord.getExamTimes(),
							examRecord.getSwitchTimes(),
							examRecord.getPersonalCode(),
							examRecord.getUserIp(),
							examRecord.getNoAnswerCount(),
							examRecord.getAnsweredCount(),
							examRecord.getClientVersion()
					);
				});
				return step.execute();
			});
			ed = System.currentTimeMillis();
			System.out.println("insert examRecordbigList PER_SIZE:100000,耗时：" + (ed - st) + "ms");

		}
	}



	private List<ExamRecord> createInsertExamRecords(String examId, List<PaperInstance> paperInstances,
													 List<String> memberIds, Map<String, ExamRecord> memberMap) {

		List<ExamRecord> result = memberIds.stream().map(t -> {
			ExamRecord examRecord = new ExamRecord();

			examRecord.forInsert();
			examRecord.setExamId(examId);

			//判断是否有试卷（新增考试可以先不填试卷）
			PaperInstance paperInstance = null;
			if (paperInstances.size() > 0) {
				paperInstance = getRandomInstance(paperInstances);
				examRecord.setPaperInstanceId(paperInstance.getId());
				examRecord.setPaperInstance(paperInstance);
			}

			examRecord.setMemberId(t);
			examRecord.setStatus(ExamRecord.STATUS_TO_BE_STARTED);
			examRecord.setExceptionOrder(null);
			examRecord.setIsCurrent(ExamRecord.CURRENT);
			// 指定考试默认生成第一次的记录
			examRecord.setExamTimes(ExamRecord.FIRST_TIME);

			return examRecord;
		}).collect(Collectors.toList());

		//过滤已存在的考试记录
		List<ExamRecord> inserts = result.stream().filter(t -> memberMap.get(t.getMemberId()) == null).collect(Collectors.toList());
		return inserts;
	}

	/**
	 * 随机抽取试卷
	 * @param paperInstances
	 * @return
	 */
	private PaperInstance getRandomInstance(List<PaperInstance> paperInstances){
		if (paperInstances.isEmpty()) return null;
		Integer index = new Random().nextInt(paperInstances.size());
		PaperInstance paperInstance = paperInstances.get(index);
		return paperInstance;
	}


	private void batchUpdateExamRecord(Integer examRegion, Exam exam, List<ExamRecord> existedRecords,
									   List<PaperInstance> paperInstances) {

		//判断是否非开考中的 考试  （如果考试前状态是已开考，对于在进行中的考试记录，不用更新）
		if (previousStatusNotStarting(exam)) {

			List<ExamRecord> updates = existedRecords.stream().filter(t -> {
				return t.getStatus() == ExamRecord.STATUS_TO_BE_STARTED;
			}).map(e -> {
				PaperInstance paperInstance = getRandomInstance(paperInstances);
				if (paperInstance != null)
					e.setPaperInstanceId(paperInstance.getId());
				return e;
			}).collect(Collectors.toList());

			//先清除分表缓存
			examRecordTimeCache.clear(exam.getId());
			//批量更新
			Long st, ed;
			List<List<ExamRecord>> examRecordUpdateListPages = Lists.partition(updates, DB_PER_SIZE);
			for (List<ExamRecord> updatesTemp : examRecordUpdateListPages) {
				System.out.println("update examRecordbigList size:" + updatesTemp.size());
				st = System.currentTimeMillis();

				String order = null;

				TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(exam.getId()));

				examRecordDao.execute(e -> e.batch(updatesTemp.stream().map(u -> {
					return e.update(examRecordTable)
							.set(examRecordTable.field("f_paper_instance_id", String.class), u.getPaperInstanceId())
							.set(examRecordTable.field("f_order_content", String.class), order)
							.where(examRecordTable.field("f_id", String.class).eq(u.getId()));
				}).collect(Collectors.toList())).execute());

				ed = System.currentTimeMillis();
				System.out.println("update examRecordbigList PER_SIZE:100000,耗时：" + (ed - st) + "ms");
			}
		}

	}


	/**
	 * 判断前状态是否为开考中
	 * @param exam
	 * @return
	 */
	private boolean previousStatusNotStarting(Exam exam) {
		return null == exam.getPreviousStatus() || exam.getPreviousStatus() != Exam.STATUS_STARTING;
	}

	private List<ExamRecord> findExistedExamRecords(Integer examRegion, String examId, List<String> memberIds) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> existedRecords = examRecordDao.execute(e ->
				e.select(examRecordTable.field("f_id", String.class),
								examRecordTable.field("f_member_id", String.class),
								examRecordTable.field("f_status", Integer.class))
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId),
								examRecordTable.field("f_member_id", String.class).in(memberIds),
								examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
						.fetch(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
							examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
							return examRecord;
						}));

		return existedRecords;
	}

	private List<ExamRecord> findOtherExistedExamRecords(Integer examRegion, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> existedRecords = examRecordDao.execute(e ->
				e.select(examRecordTable.field("f_id", String.class),
								examRecordTable.field("f_member_id", String.class),
								examRecordTable.field("f_status", Integer.class))
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId),
								examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
						.fetch(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
							examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
							return examRecord;
						}));

		return existedRecords;
	}

	/**
	 * 根据答案ID查找相关题目
	 * @param fullAnswerRecords
	 * @return
	 */
	@Override
	@DataSource
	public List<AnswerRecord> findInculdeQuestionAnswers(Integer examRegion, List<AnswerRecord> fullAnswerRecords) {
		Result<Record> records = questionCopyDao.execute(e ->
				e.select(
								Fields.start()
										.add(QUESTION_COPY.ID.as("questionCopyId"))
										.add(QUESTION_COPY.TYPE)
										.add(QUESTION_COPY.SCORE)
										.add(QUESTION_COPY.PARENT_ID)
										.add(QUESTION.ID.as("questionId"))
										.add(QUESTION_ATTR_COPY.ID.as("attrId"))
										.add(QUESTION_ATTR_COPY.NAME)
										.add(QUESTION_ATTR_COPY.VALUE)
										.add(QUESTION_ATTR_COPY.TYPE)
										.add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
										.end())
						.from(QUESTION_COPY)
						.leftJoin(QUESTION_ATTR_COPY).on(QUESTION_COPY.ID.eq(QUESTION_ATTR_COPY.QUESTION_COPY_ID))
						.leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
						.where(QUESTION.ID.in(fullAnswerRecords.stream().map(AnswerRecord::getQuestionId).collect(Collectors.toList())))
						.fetch()
		);

		Map<String, AnswerRecord> fullAnswerRecordsMap = fullAnswerRecords.stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));

		Map<String, QuestionAttrCopy> questionAttrCopyMap = records.stream().map(t -> {
			QuestionAttrCopy attr = new QuestionAttrCopy();
			attr.setId(t.getValue(QUESTION_ATTR_COPY.ID.as("attrId")));
			attr.setName(t.getValue(QUESTION_ATTR_COPY.NAME));
			attr.setValue(t.getValue(QUESTION_ATTR_COPY.VALUE));
			attr.setType(t.getValue(QUESTION_ATTR_COPY.TYPE));
			attr.setQuestionCopyId(t.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
			return attr;
		}).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

		Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

		List<AnswerRecord> answerRecords = records
				.stream().map(t -> {
					QuestionCopy questionCopy = new QuestionCopy();
					questionCopy.setId(t.getValue(QUESTION_COPY.ID.as("questionCopyId")));
					questionCopy.setType(t.getValue(QUESTION_COPY.TYPE));
					questionCopy.setScore(t.getValue(QUESTION_COPY.SCORE));
					questionCopy.setParentId(t.getValue(QUESTION_COPY.PARENT_ID));
					questionCopy.setQuestionAttrCopys(questionAttrCopyListMap.get(questionCopy.getId()));
					Question question = new Question();
					question.setId(t.getValue(QUESTION.ID.as("questionId")));
					questionCopy.setQuestion(question);

					AnswerRecord answerRecord = new AnswerRecord();
					answerRecord.setQuestionId(question.getId());
					if (fullAnswerRecordsMap.get(question.getId()) == null)
						answerRecord.setAnswer(null);
					else
						answerRecord.setAnswer(fullAnswerRecordsMap.get(question.getId()).getAnswer());
					answerRecord.setQuestionCopy(questionCopy);

					return answerRecord;
				}).collect(Collectors.toList());

		return answerRecords;
	}

	private Map<String, List<QuestionAttrCopy>> reduceQuestionAttrCopyList(Collection<QuestionAttrCopy> questionAttrCopies) {

		Map<String, List<QuestionAttrCopy>> map = new HashMap<>();
		questionAttrCopies.stream().forEach(questionAttr -> {
			map.compute(questionAttr.getQuestionCopyId(), (k, v) -> {
				if (v == null) {
					v = new ArrayList<>();
				}
				v.add(questionAttr);
				return v;
			});
		});
		return map;
	}


	@Override
	@DataSource
	public void updateExamRecordOrderContent(Integer examRegion, String examRecordId, String orderJson, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		examRecordDao.execute(e ->
				e.update(examRecordTable).set(examRecordTable.field("f_order_content", String.class), orderJson).where(examRecordTable.field("f_id", String.class).eq(examRecordId)).execute());
	}



	@Override
	@DataSource
	public Optional<ExamRecord> getExamRecordForCourseStudy(Integer examRegion, String id, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		return examRecordDao.execute(x -> x.select(
						examRecordTable.field("f_id", String.class),
						examRecordTable.field("f_member_id", String.class),
						examRecordTable.field("f_exam_id", String.class),
						examRecordTable.field("f_score", Integer.class),
						examRecordTable.field("f_is_finished", Integer.class),
						examRecordTable.field("f_status", Integer.class),
						examRecordTable.field("f_submit_time", Long.class),
						examRecordTable.field("f_start_time", Long.class),
						examRecordTable.field("f_client_type", Integer.class)
				).from(examRecordTable).where(examRecordTable.field("f_id", String.class).eq(id)))
				.fetchOptional(r -> {
					ExamRecord record = new ExamRecord();
					record.setId(r.get(examRecordTable.field("f_id", String.class)));
					record.setMemberId(r.get(examRecordTable.field("f_member_id", String.class)));
					record.setExamId(r.get(examRecordTable.field("f_exam_id", String.class)));
					record.setScore(r.get(examRecordTable.field("f_score", Integer.class)));
					record.setIsFinished(r.get(examRecordTable.field("f_is_finished", Integer.class)));
					record.setStatus(r.get( examRecordTable.field("f_status", Integer.class)));
					record.setSubmitTime(r.get( examRecordTable.field("f_submit_time", Long.class)));
					record.setStartTime(r.get(examRecordTable.field("f_start_time", Long.class)));
					record.setClientType(r.get(examRecordTable.field("f_client_type", Integer.class)));
					return record;
				});
	}

	@Override
	@DataSource
	public ExamRecord getSimple(Integer examRegion, String examRecordId, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		return examRecordDao.execute(e ->
				e.select(
								Fields.start()
										.add(examRecordTable.fields())
										.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_id", String.class).eq(examRecordId)).fetchOne(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
							examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
							examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
							examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
							examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
							examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
							examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
							examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
							examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
							examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
							examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
							examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
							examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
							examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
							examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
							examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
							examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
							examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
							examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
							examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
							examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
							examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
							examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
							examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
							examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
							examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
							return examRecord;
						})
		);
	}

	@Override
	@DataSource
	public String update(
			Integer examRegion,
			String examRecordId,
			Optional<Long> startTime,
			Optional<Long> endTime,
			Optional<Integer> status,
			Optional<Long> submitTime,
			Integer isReset,
			Optional<String> paperInstanceId,
			Integer exceptionOrder,
			Optional<Long> lastSubmitTime,
			Optional<String> orderContent,
			Optional<Integer> personalCode, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		examRecordDao.execute(e -> {
			UpdateSetMoreStep<?> u = (UpdateSetMoreStep<?>) e.update(examRecordTable);
			if (startTime.isPresent()) {
				u = u.set(examRecordTable.field("f_start_time", Long.class), startTime.get());
			}
			if (endTime.isPresent()) {
				u = u.set(examRecordTable.field("f_end_time", Long.class), endTime.get());
			}
			if (status.isPresent()) {
				u = u.set(examRecordTable.field("f_status", Integer.class), status.get());
			}
			if (submitTime.isPresent()) {
				u = u.set(examRecordTable.field("f_submit_time", Long.class), submitTime.get());
			}
			u = u.set(examRecordTable.field("f_is_reset", Integer.class), isReset);
			if (paperInstanceId.isPresent()) {
				u = u.set(examRecordTable.field("f_paper_instance_id", String.class), paperInstanceId.get());
			}
			u = u.set(examRecordTable.field("f_exception_order", Integer.class),exceptionOrder);
			if (lastSubmitTime.isPresent()) {
				u = u.set(examRecordTable.field("f_last_submit_time", Long.class), lastSubmitTime.get());
			}
			if (orderContent.isPresent()) {
				u = u.set(examRecordTable.field("f_order_content", String.class), orderContent.get());
			}
			if (personalCode.isPresent()) {
				u = u.set(examRecordTable.field("f_personal_code", Integer.class), personalCode.get());
			}
			return u.where(examRecordTable.field("f_id", String.class).eq(examRecordId)).execute();
		});
		return examRecordId;
	}

	private void insertExamRecord(Integer examRegion, ExamRecord examRecord) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examRecord.getExamId()));

		examRecordDao.execute(e ->
				e.insertInto(examRecordTable, examRecordTable.field("f_id", String.class),
								examRecordTable.field("f_create_time", Long.class),
								examRecordTable.field("f_exam_id", String.class),
								examRecordTable.field("f_paper_instance_id", String.class),
								examRecordTable.field("f_order_content", String.class),
								examRecordTable.field("f_member_id", String.class),
								examRecordTable.field("f_status", Integer.class),
								examRecordTable.field("f_exception_order", Integer.class),
								examRecordTable.field("f_is_current", Integer.class),
								examRecordTable.field("f_start_time", Long.class),
								examRecordTable.field("f_end_time", Long.class),
								examRecordTable.field("f_exam_times", Integer.class),
								examRecordTable.field("f_personal_code", Integer.class)
						)
						.values(
								examRecord.getId(), examRecord.getCreateTime(),
								examRecord.getExamId(), examRecord.getPaperInstanceId(),
								examRecord.getOrderContent(), examRecord.getMemberId(),
								examRecord.getStatus(), examRecord.getExceptionOrder(),
								examRecord.getIsCurrent(), examRecord.getStartTime(),
								examRecord.getEndTime(), examRecord.getExamTimes(),
								examRecord.getPersonalCode()
						)
						.execute()
		);

	}


	/**
	 * 更新未开始的考试记录的试题顺序
	 */
	@Override
	@DataSource
	public void updateOrderContentOfNoStartExamRecords(Integer examRegion, String examId, Integer paperSortRule) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		List<ExamRecord> noStartExamRecords = examRecordDao.execute(e ->
				e.select(Fields.start().add(examRecordTable.field("f_id", String.class), examRecordTable.field("f_paper_instance_id", String.class)).end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId),
								examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_TO_BE_STARTED)).fetch(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
							return examRecord;
						}));

		if (noStartExamRecords.size() > 0) {
			PaperInstance paperInstance = paperInstanceDao.get(noStartExamRecords.get(0).getPaperInstanceId());
			examRecordDao.execute(e ->
					e.batch(
							noStartExamRecords.stream().map(t -> {
								return e.update(examRecordTable)
										.set(examRecordTable.field("f_order_content", String.class),
												paperInstanceService.createNewQuestionOrder(examRegion,
														Optional.of(paperInstance), paperInstance.getId(), paperSortRule)
										)
										.where(examRecordTable.field("f_id", String.class).eq(t.getId()));
							}).collect(Collectors.toList())
					).execute()
			);
		}
	}

	/**
	 * 更新超时异常记录
	 */
	@Override
	@DataSource
	public void doUpdateException(Integer examRegion) {

		String[] allExamRecordStringTable = ExamRecord.STRING_EXAM_RECORD_ALL;

		for (String examRecordStringTable : allExamRecordStringTable) {

			TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(examRecordStringTable);

			List<String> ids = new ArrayList<>();
			do {
				ids = examRecordDao.execute(e ->
						e.select(examRecordTable.field("f_id", String.class))
								.from(examRecordTable)
								.where(
										examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_DOING),
										examRecordTable.field("f_submit_time", Long.class).isNull(),
										examRecordTable.field("f_end_time", Long.class).lt(System.currentTimeMillis()-600000l)
								).limit(PAGE_SIZE).fetch(examRecordTable.field("f_id", String.class))
				);

				ids.stream().forEach(id -> {
					examRecordDao.execute(e ->
							e.update(examRecordTable)
									.set(examRecordTable.field("f_status", Integer.class), ExamRecord.STATUS_TIME_EXCEPTION)
									.set(examRecordTable.field("f_exception_order", Integer.class), ExamRecord.EXCEPTION_ORDER)
									.where(examRecordTable.field("f_id", String.class).eq(id))
									.execute()
					);
				});

			} while (ids.size() == PAGE_SIZE);

		}

	}

	/**
	 * 更新超时异常记录
	 */
	@Override
	@DataSource
	public void checkExamRecord(Integer examRegion, String examId) {
				TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
				List<String> recordIds = examRecordDao.execute(e ->
								e.select(examRecordTable.field("f_id", String.class)).from(examRecordTable)
										.where(examRecordTable.field("f_exam_id", String.class).eq(examId),
												examRecordTable.field("f_right_count", Integer.class).ne(0),
												examRecordTable.field("f_score", Integer.class).eq(0)))
						.fetch(examRecordTable.field("f_id", String.class));

				if (CollectionUtils.isNotEmpty(recordIds)) {
					recordIds.stream().forEach(id -> {
						examRecordDao.execute(e ->
								e.update(examRecordTable)
										.set(examRecordTable.field("f_status", Integer.class), ExamRecord.STATUS_TIME_EXCEPTION)
										.set(examRecordTable.field("f_exception_order", Integer.class), ExamRecord.EXCEPTION_ORDER)
										.where(examRecordTable.field("f_id", String.class).eq(id))
										.execute()
						);
					});
				}

	}

	@Override
	@DataSource
	public List<String> findUnfinishedRecord(Integer examRegion, String examId, Integer page) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		return examRecordDao.execute(e -> {
			return e.selectDistinct(
							Fields.start().add(examRecordTable.field("f_member_id", String.class)).end())
					.from(examRecordTable)
					.leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
					.where(
							examRecordTable.field("f_status", Integer.class).le(ExamRecord.STATUS_DOING),
							EXAM.STATUS.ne(Exam.STATUS_NOT_PUBLISH),
							EXAM.END_TIME.ge(System.currentTimeMillis()),
							EXAM.ID.eq(examId)
					)
					.limit((page - 1) * PER_SIZE, PER_SIZE)
					.fetch(examRecordTable.field("f_member_id", String.class));
		});
	}

	@Override
	@DataSource
	public Integer findUnfinishedRecordCount(Integer examRegion, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		Integer count = examRecordDao.execute(e -> e.select(DSL.count(examRecordTable.field("f_member_id", String.class)))
				.from(examRecordTable)
				.leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
				.where(
						examRecordTable.field("f_status", Integer.class).le(ExamRecord.STATUS_DOING),
						EXAM.STATUS.ne(Exam.STATUS_NOT_PUBLISH),
						EXAM.END_TIME.ge(System.currentTimeMillis()),
						EXAM.ID.eq(examId)
				)
				.fetchOne(DSL.count(examRecordTable.field("f_member_id", String.class))));
		return count;

	}


	@Override
	@DataSource
	public String addExaminee(Integer examRegion, String examId, List<String> ids) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		// db-存在的
		List<ExamRecord> examRecords = examRecordDao.execute(e -> {
			return e.selectDistinct(Fields.start()
							.add(examRecordTable.field("f_id", String.class))
							.add(MEMBER.ID)
							.add(MEMBER.NAME)
							.add(MEMBER.FULL_NAME)
							.end())
					.from(examRecordTable)
					.leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
					.where(
							examRecordTable.field("f_exam_id", String.class).eq(examId),
							examRecordTable.field("f_member_id", String.class).in(ids),
							examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
					).fetch(r -> {
						ExamRecord examRecord = new ExamRecord();
						examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
						Member member = new Member();
						member.setId(r.getValue(MEMBER.ID));
						member.setName(r.getValue(MEMBER.NAME));
						member.setFullName(r.getValue(MEMBER.FULL_NAME));
						examRecord.setMember(member);
						return examRecord;
					});
		});

		//返回已有考试记录的用户id
		if (examRecords != null && examRecords.size() > 0 && !examRecords.isEmpty()) {
			return examRecords.stream().map(e -> {
				Member member = e.getMember();
				return member.getFullName() == null ? member.getName() : member.getFullName();
			}).collect(Collectors.joining(","));
		}

		// 过滤完，添加还没有考试记录的用户
		List<ExamRecord> inserts = ids.stream().map(i -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.forInsert();
			examRecord.setIsCurrent(ExamRecord.CURRENT);
			examRecord.setExamId(examId);
			examRecord.setMemberId(i);
			examRecord.setStatus(ExamRecord.STATUS_TO_BE_STARTED);
			examRecord.setExceptionOrder(null);
			examRecord.setPaperInstanceId(paperInstanceService.getWithRandomByExamId(examRegion, examId).getId());
			examRecord.setExamTimes(ExamRecord.FIRST_TIME);
			return examRecord;
		}).collect(Collectors.toList());

		for (ExamRecord examRecord : inserts) {
			examRecordDao.execute(e ->
					e.insertInto(examRecordTable,
									examRecordTable.field("f_id", String.class),
									examRecordTable.field("f_create_time", Long.class),
									examRecordTable.field("f_is_current", Integer.class),
									examRecordTable.field("f_exam_id", String.class),
									examRecordTable.field("f_member_id", String.class),
									examRecordTable.field("f_status", Integer.class),
									examRecordTable.field("f_exception_order", Integer.class),
									examRecordTable.field("f_paper_instance_id", String.class),
									examRecordTable.field("f_exam_times", Integer.class)
							)
							.values(
									examRecord.getId(), examRecord.getCreateTime(),
									examRecord.getIsCurrent(),examRecord.getExamId(),
									examRecord.getMemberId(),examRecord.getStatus(),
									examRecord.getExceptionOrder(),examRecord.getPaperInstanceId(),
									examRecord.getExamTimes()
							)
							.execute());

		}

		// 2017-07最后一次决定不需要同时添加报名记录
		// 但要更新已报名的记录为已通过
		updateSignUpToPassedByAddUser(examRegion, examId, ids);
		//增加受众对象
		insertByBusinessId(examId, ids);

		Exam exam = examDao.get(examId);
		if (exam.getType() != Exam.EXAM_CLOUD_TYPE && exam.getType() != Exam.EXAM_GRID_TYPE) {
			List<ExamStudyPlanConfig> list = this.findStudyPlanConfig(examId, ExamStudyPlanConfig.BUSINESS_TYPE_EXAM);
			if (CollectionUtils.isNotEmpty(list)) {
				ExamStudyPlanConfig examStudyPlanConfig = list.get(0);
				if (examStudyPlanConfig.getPushLearningPlan() == ExamStudyPlanConfig.PUSH_LEARNING_PLAN_YES) {
					for (String id : ids) {
						//异步消息
						messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT,
								MessageHeaderContent.BUSINESS_ID, examId,
								MessageHeaderContent.CONFIG_ID, examStudyPlanConfig.getId(),
								MessageHeaderContent.EXAM_MEMBER_ID, id
						);
					}
				}
			}
		}

		messageSender.send(
				MessageTypeContent.EXAM_ADD_USER_TO_EXAM,
				MessageHeaderContent.IDS, ids.stream().collect(Collectors.joining(",")),
				MessageHeaderContent.EXAM_ID, examId,
				MessageHeaderContent.STATUS, String.valueOf(ExamRecord.STATUS_TO_BE_STARTED),
				MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion)
		);

		return "";
	}


	@Override
	@DataSource
	public ExamRecord delete(Integer examRegion, String id, String examId, String memberId){
		Integer type = examDao.execute(dsl ->
				dsl.select(Fields.start().add(EXAM.TYPE).end())
						.from(EXAM)
						.where(EXAM.ID.eq(examId))
						.fetchOne(EXAM.TYPE)
		);
		if (Exam.EXAM_CLOUD_TYPE.equals(type)) {
			CloudSignup up = signUpService.getByExamIdAndMemberIdForCloudExamInterface(examRegion, examId, memberId);
			if (up != null) {
				updateCloudSignupBeRejected(up.getId(), examId);
			}
		}else if (Exam.EXAM_GRID_TYPE.equals(type)) {
			GridSignup up = signUpService.getByExamIdAndMemberIdForGridExamInterface(examRegion, examId, memberId);
			if (up != null) {
				updateGridSignupBeRejected(up.getId(), examId);
			}
		}else {
			SignUp up = signUpService.getByExamIdAndMemberId(examRegion, examId, memberId);
			if (up != null) {
				updateSignUpBeRejected(up.getId(), examId);
			}
		}

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> examRecordList = examRecordDao.execute(e ->
				e.select(Fields.start()
								.add(examRecordTable.fields())
								.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_id", String.class).eq(id))
		).fetch(r -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
			examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
			examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
			examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
			examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
			examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
			examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
			examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
			examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
			examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
			examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
			examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
			examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
			examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
			examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
			examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
			examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
			examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
			examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
			examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
			examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
			examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
			examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
			examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
			examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
			examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
			examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
			return examRecord;
		});
		if (examRecordList == null || examRecordList.size() == 0)
			return null;
		ExamRecord examRecord = examRecordList.get(0);

		examRecordDao.execute(dslContext ->
				dslContext.delete(examRecordTable).where(
						examRecordTable.field("f_id", String.class).eq(id)
				).execute());

		//移除受众
		messageSender.send(MessageTypeContent.EXAM_RECORD_DELETE_AUDIENCE,
				MessageHeaderContent.EXAM_ID, examId,
				MessageHeaderContent.MEMBER_ID, memberId);

		messageSender.send(MessageTypeContent.EXAM_EXAM_RECORD_DELETE,
				MessageHeaderContent.ID, id,
				MessageHeaderContent.EXAM_ID, examId,
				MessageHeaderContent.MEMBER_ID, memberId,
				MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
		return examRecord;

	}

	@Override
	@DataSource
	public ExamRecord delayExamRecordEndTime(Integer examRegion, String examRecordId, Integer time, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		ExamRecord examRecord = get(examRegion, examRecordId, examId);
		Long afterAddTime = examRecord.getEndTime() + (time * 60 * 1000);

		examRecordDao.execute(dslContext ->
				dslContext.update(examRecordTable)
						.set(examRecordTable.field("f_end_time", Long.class), afterAddTime)
						.where(examRecordTable.field("f_id", String.class).eq(examRecordId))
						.execute());

		examRecord.setEndTime(afterAddTime);

		Exam exam = examDao.execute(x -> x.select(
						Fields.start()
								.add(EXAM.ID)
								.add(EXAM.ORGANIZATION_ID)
								.add(EXAM.NAME)
								.end())
				.from(EXAM)
				.where(EXAM.ID.eq(examId))
				.fetchOne(r -> {
					Exam exam2 = new Exam();
					exam2.setId(r.getValue(EXAM.ID));
					exam2.setOrganizationId(r.getValue(EXAM.ORGANIZATION_ID));
					exam2.setName(r.getValue(EXAM.NAME));
					return exam2;
				})
		);

		examNoticeService.insert(
				ExamNotice.BUSINESS_TYPE_EXAM, exam.getId(),
				ExamNotice.TYPE_DELAY, exam.getOrganizationId(), MessageConstant.EXAM_DELAY,
				Optional.of(new String[]{exam.getName(), String.valueOf(time)}),
				Optional.of(examRecord.getMemberId()));

		return examRecord;
	}


	private void updateCloudSignupBeRejected(String id, String examId) {
		cloudSignupDao.execute(e -> e.update(CLOUD_SIGNUP).set(CLOUD_SIGNUP.STATUS, SignUp.STATUS_REFUSE).where(CLOUD_SIGNUP.ID.eq(id)).execute());
	}

	private void updateGridSignupBeRejected(String id, String examId) {
		gridSignupDao.execute(e -> e.update(GRID_SIGNUP).set(GRID_SIGNUP.STATUS, SignUp.STATUS_REFUSE).where(GRID_SIGNUP.ID.eq(id)).execute());
	}

	private void updateSignUpBeRejected(String id, String examId) {
		signUpDao.execute(e -> e.update(SIGNUP).set(SIGNUP.STATUS, SignUp.STATUS_REFUSE).set(SIGNUP.MODIFY_DATE,new Timestamp(System.currentTimeMillis())).where(SIGNUP.ID.eq(id)).execute());
	}


	public List<ExamStudyPlanConfig> findStudyPlanConfig(String businessId, Integer type) {
		return examStudyPlanConfigCommonDao.execute(w -> w.select(Fields.start().add(EXAM_STUDY_PLAN_CONFIG).end())
				.from(EXAM_STUDY_PLAN_CONFIG)
				.where(EXAM_STUDY_PLAN_CONFIG.BUSINESS_ID.eq(businessId))
				.and(EXAM_STUDY_PLAN_CONFIG.BUSINESS_TYPE.eq(type))
				.fetch()).into(ExamStudyPlanConfig.class);
	}


	public String insertByBusinessId(String businessId, List<String> memberIds) {
		//考试原有受众对象
		if (businessId == null) {
			return null;
		}
		// 查询该场考试受众对象对应的人员表是否有存在此次选中的人员，如果有，过滤掉这部分人员，防止重复插入这些受众对象
		List<String> existMemberIds = audienceObjectDao.execute(e -> e.select(AUDIENCE_MEMBER.MEMBER_ID)
						.from(AUDIENCE_OBJECT)
						.innerJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
						.where(AUDIENCE_OBJECT.TARGET_ID.eq(businessId).and(AUDIENCE_MEMBER.MEMBER_ID.in(memberIds))))
				.fetch(AUDIENCE_MEMBER.MEMBER_ID);
		List<String> needToInsertMemberIds;
		if (existMemberIds.isEmpty()) {
			needToInsertMemberIds = memberIds;
		} else {
			needToInsertMemberIds = memberIds.stream().filter(e -> !existMemberIds.contains(e)).collect(Collectors.toList());
		}
		if (!needToInsertMemberIds.isEmpty()) {
			createAudienceData(needToInsertMemberIds, businessId);
		}
		return needToInsertMemberIds.stream().collect(Collectors.joining(","));
	}


	private void createAudienceData(List<String> needAddAudiences, String businessId) {
		messageSender.send(MessageTypeContent.EXAM_RECORD_INSERT_AUDIENCE,
				MessageHeaderContent.IDS, needAddAudiences.stream().collect(Collectors.joining(",")),
				MessageHeaderContent.BUSINESS_ID, businessId);

	}


	/**
	 * 添加考生，如果是报名考试，
	 * 没有报名的，直接添加考试记录
	 * 有报名的，更新已通过
	 * @param examId
	 * @param ids
	 */
	private void updateSignUpToPassedByAddUser(Integer examRegion, String examId, List<String> ids ) {
		Exam exam = examDao.get(examId);
		if (exam.getNeedApplicant() == Exam.EXAM_YES) {

			if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {

				for (String memberId : ids) {
					List<CloudSignup> list = cloudSignupDao.fetch(CLOUD_SIGNUP.EXAM_ID.eq(examId), CLOUD_SIGNUP.MEMBER_ID.eq(memberId));
					if (list != null && list.size() > 0) {
						for (CloudSignup cloudSignup : list) {
							cloudSignup.setStatus(SignUp.STATUS_PASSED);
							cloudSignupDao.update(cloudSignup);
							messageSender.send(MessageTypeContent.EXAM_SIGNUP_PASS,
									MessageHeaderContent.IDS, cloudSignup.getMemberId(),
									MessageHeaderContent.EXAM_ID, examId,
									MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
						}
					}else {
						CloudSignup cloudSignup = new CloudSignup();
						cloudSignup.forInsert();
						cloudSignup.setExamId(examId);
						cloudSignup.setMemberId(memberId);
						cloudSignup.setOrganizationId(exam.getOrganizationId());
						cloudSignup.setStatus(SignUp.STATUS_PASSED);
						cloudSignup.setAuditMemberId("system_robot");
						cloudSignupDao.insert(cloudSignup);
					}
				}


			} else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {

				for (String memberId : ids) {
					List<GridSignup> list = gridSignupDao.fetch(GRID_SIGNUP.EXAM_ID.eq(examId), GRID_SIGNUP.MEMBER_ID.eq(memberId));
					if (list != null && list.size() > 0) {
						for (GridSignup gridSignup : list) {
							gridSignup.setStatus(SignUp.STATUS_PASSED);
							gridSignupDao.update(gridSignup);
							messageSender.send(MessageTypeContent.EXAM_SIGNUP_PASS,
									MessageHeaderContent.IDS, gridSignup.getMemberId(),
									MessageHeaderContent.EXAM_ID, examId,
									MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
						}
					}else {
						GridSignup gridSignup = new GridSignup();
						gridSignup.forInsert();
						gridSignup.setExamId(examId);
						gridSignup.setMemberId(memberId);
						gridSignup.setStatus(SignUp.STATUS_PASSED);
						gridSignup.setAuditMemberId("system_robot");
						gridSignupDao.insert(gridSignup);
					}
				}
			}
			else {
				List<SignUp> existeds = signUpDao.fetch(SIGNUP.EXAM_ID.eq(examId), SIGNUP.MEMBER_ID.in(ids));
				if (CollectionUtils.isNotEmpty(existeds)) {
					Map<String, SignUp> map = existeds.stream().collect(Collectors.toMap(SignUp::getMemberId, e -> e, (k, v) -> v));
					List<SignUp> updates = existeds.stream().filter(t -> map.get(t.getMemberId()) != null).collect(Collectors.toList());
					updates.forEach(t -> {
						t.setStatus(SignUp.STATUS_PASSED);
						t.setModifyDate(null);
					});
					signUpDao.update(updates);
					messageSender.send(MessageTypeContent.EXAM_SIGNUP_PASS,
							MessageHeaderContent.IDS, updates.stream().map(SignUp::getMemberId).collect(Collectors.joining(",")),
							MessageHeaderContent.EXAM_ID, examId,
							MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
				}

			}

//			updateExamApplicantNum(examId);
		}
	}



	/**
	 * 查询考试的参考人数与人次
	 */
	@Override
	@DataSource
	public HashMap<String, Integer> doExamJoinMemberAndTimes(Integer examRegion, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		return examRecordDao.execute(ar -> ar.select(
						Fields.start()
								.add(DSL.count(examRecordTable.field("f_id", String.class)))
								.add(DSL.countDistinct(examRecordTable.field("f_member_id", String.class)))
								.end())
				.from(examRecordTable)
				.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
				.and(examRecordTable.field("f_status", Integer.class).ne(ExamRecord.STATUS_TO_BE_STARTED))
				.fetchOne(r -> {
					Integer count = r.getValue(DSL.countDistinct(examRecordTable.field("f_member_id", String.class)));
					Integer times = r.getValue(DSL.count(examRecordTable.field("f_id", String.class)));
					HashMap<String, Integer> map = new HashMap<>();
					map.put("count", count);
					map.put("times", times);
					return map;
				}));
	}


	@Override
	@DataSource
	public ExamRecord getCurrentRecord(Integer examRegion, String examId, String memberId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> list = examRecordDao.execute(e ->
				e.selectDistinct(
								Fields.start()
										.add(examRecordTable.fields())
										.add(MEMBER.ID)
										.add(MEMBER.NAME)
										.add(MEMBER.FULL_NAME)
										.add(MEMBER.ORGANIZATION_ID)
										.add(MEMBER.COMPANY_ID)
										.add(MEMBER.FROM)
										.add(PAPER_INSTANCE.ID)
										.add(PAPER_INSTANCE.PAPER_CLASS_ID)
										.add(PAPER_INSTANCE.IS_SUBJECTIVE)
										.add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
										.add(PAPER_INSTANCE.EXAM_ID)
										.end())
						.from(examRecordTable)
						.leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
						.leftJoin(PAPER_INSTANCE).on(PAPER_INSTANCE.ID.eq(examRecordTable.field("f_paper_instance_id", String.class)))
						.where(
								examRecordTable.field("f_exam_id", String.class).eq(examId),
								examRecordTable.field("f_member_id", String.class).eq(memberId),
								examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
						)
						.fetch(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
							examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
							examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
							examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
							examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
							examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
							examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
							examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
							examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
							examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
							examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
							examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
							examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
							examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
							examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
							examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
							examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
							examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
							examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
							examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
							examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
							examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
							examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
							examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
							examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
							examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));

							Member member = new Member();
							member.setId(r.getValue(MEMBER.ID));
							member.setName(r.getValue(MEMBER.NAME));
							member.setFullName(r.getValue(MEMBER.FULL_NAME));
							member.setOrganizationId(r.getValue(MEMBER.ORGANIZATION_ID));
							member.setCompanyId(r.getValue(MEMBER.COMPANY_ID));
							member.setFrom(r.getValue(MEMBER.FROM));
							examRecord.setMember(member);

							PaperInstance paperInstance = new PaperInstance();
							paperInstance.setId(r.getValue(PAPER_INSTANCE.ID));
							paperInstance.setPaperClassId(r.getValue(PAPER_INSTANCE.PAPER_CLASS_ID));
							paperInstance.setIsSubjective(r.getValue(PAPER_INSTANCE.IS_SUBJECTIVE));
							paperInstance.setRemoteOrderContent(r.getValue(PAPER_INSTANCE.REMOTE_ORDER_CONTENT));
							paperInstance.setExamId(r.getValue(PAPER_INSTANCE.EXAM_ID));
							examRecord.setPaperInstance(paperInstance);

							return examRecord;
						})
		);
		if (list != null && list.size() > 0)
			return list.get(0);
		return new ExamRecord();
	}



	@Override
	@DataSource
	public ExamRecord get(Integer examRegion, String id, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		List<ExamRecord> examRecordList = examRecordDao.execute(e ->
				e.select(
								Fields.start()
										.add(examRecordTable.fields())
										.add(MEMBER.ID)
										.add(MEMBER.NAME)
										.add(MEMBER.FULL_NAME)
										.add(PAPER_INSTANCE.ID)
										.add(PAPER_INSTANCE.REMOTE_ORDER_CONTENT)
										.end())
						.from(examRecordTable)
						.leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
						.leftJoin(PAPER_INSTANCE).on(PAPER_INSTANCE.ID.eq(examRecordTable.field("f_paper_instance_id", String.class)))
						.where(examRecordTable.field("f_id", String.class).eq(id)).fetch(r -> {
							ExamRecord examRecord = new ExamRecord();
							examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
							examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
							examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
							examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
							examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
							examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
							examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
							examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
							examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
							examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
							examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
							examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
							examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
							examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
							examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
							examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
							examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
							examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
							examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
							examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
							examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
							examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
							examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
							examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
							examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
							examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
							examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
							Member member = new Member();
							member.setId(r.getValue(MEMBER.ID));
							member.setName(r.getValue(MEMBER.NAME));
							member.setFullName(r.getValue(MEMBER.FULL_NAME));
							examRecord.setMember(member);
							PaperInstance paperInstance = new PaperInstance();
							paperInstance.setId(r.getValue(PAPER_INSTANCE.ID));
							paperInstance.setRemoteOrderContent(r.getValue(PAPER_INSTANCE.REMOTE_ORDER_CONTENT));
							examRecord.setPaperInstance(paperInstance);
							return examRecord;
						})
		);
		if(examRecordList != null && examRecordList.size() > 0)
			return examRecordList.get(0);
		return new ExamRecord();
	}


	@Override
	@DataSource
	public List<ExamRecord> findExamRecordByMemberIdsAndExamId(Integer examRegion, List<String> memberIds, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		List<ExamRecord> examRecordList = examRecordDao.execute(e ->
				e.select(Fields.start()
								.add(examRecordTable.field("f_id", String.class))
								.add(examRecordTable.field("f_member_id", String.class))
								.add(examRecordTable.field("f_exam_times", Integer.class))
								.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
						.and(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_PASS)
								.or(examRecordTable.field("f_status", Integer.class).eq(ExamRecord.STATUS_NOT_PASS)))
						.and(examRecordTable.field("f_member_id", String.class).in(memberIds))
		).fetch(r -> {
			ExamRecord examRecord = new ExamRecord();
			examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
			examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
			examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
			return examRecord;
		});
		return examRecordList;
	}



	@Override
	@DataSource
	public String decreaseSwitchTimes(Integer examRegion, String examRecordId, Integer times, String examId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		examRecordDao.execute(e -> e.update(examRecordTable).set(examRecordTable.field("f_switch_times", Integer.class), times)
				.where(examRecordTable.field("f_id", String.class).eq(examRecordId)).execute());

		return examRecordId;
	}


	@Override
	@DataSource
	public PagedResult<ExamRecord> findPagedResult(Integer examRegion, Integer page, Integer pageSize, String examId, String memberId) {

		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		return examRecordDao.execute(e -> {

			SelectSelectStep<Record> selectListField = e.selectDistinct(Fields.start()
					.add(examRecordTable.field("f_id", String.class))
					.add(examRecordTable.field("f_start_time", Long.class))
					.add(examRecordTable.field("f_submit_time", Long.class))
					.add(examRecordTable.field("f_status", Integer.class))
					.add(examRecordTable.field("f_score", Integer.class))
					.add(examRecordTable.field("f_exam_id", String.class))
					.add(EXAM.SHOW_ANSWER_RULE)
					.add(EXAM.SHOW_SCORE_TIME)
					.end()
			);

			SelectSelectStep<Record> selectCountField = e.select(
					Fields.start().add(examRecordTable.field("f_id", String.class).countDistinct()).end()
			);

			Stream<Optional<Condition>> conditions = Stream.of(
					Optional.of(examId).map(examRecordTable.field("f_exam_id", String.class)::eq),
					Optional.of(memberId).map(examRecordTable.field("f_member_id", String.class)::eq)
			);

			Condition c = conditions.filter(Optional::isPresent).map(Optional::get)
					.reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
					.and(examRecordTable.field("f_status", Integer.class).gt(ExamRecord.STATUS_TIME_EXCEPTION));

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

				SelectConditionStep<Record> select = a.from(examRecordTable)
						.leftJoin(EXAM).on(EXAM.ID.eq(examRecordTable.field("f_exam_id", String.class)))
						.where(c);

				return select;
			};


			int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

			SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
			listSetp.orderBy(examRecordTable.field("f_start_time", Long.class).desc());
			Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

			return PagedResult.create(count, record.stream().map(i -> {
				ExamRecord examRecord = new ExamRecord();
				examRecord.setId(i.getValue(examRecordTable.field("f_id", String.class)));
				examRecord.setStartTime(i.getValue(examRecordTable.field("f_start_time", Long.class)));
				examRecord.setSubmitTime(i.getValue(examRecordTable.field("f_submit_time", Long.class)));
				examRecord.setStatus(i.getValue(examRecordTable.field("f_status", Integer.class)));
				examRecord.setScore(i.getValue(examRecordTable.field("f_score", Integer.class)));
				examRecord.setExamId(i.getValue(examRecordTable.field("f_exam_id", String.class)));
				Exam exam = new Exam();
				exam.setShowAnswerRule(i.getValue(EXAM.SHOW_ANSWER_RULE));
				exam.setShowScoreTime(i.getValue(EXAM.SHOW_SCORE_TIME));
				examRecord.setExam(exam);
				return examRecord;
			}).collect(Collectors.toList()));
		});
	}



	@Override
	@DataSource
	public List<ExamRecord> getMemberExamRecordScore(Integer examRegion, String memberId, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

		return examRecordDao.execute(x -> x.select(
						examRecordTable.field("f_id", String.class),
						examRecordTable.field("f_status", Integer.class)
				).from(examRecordTable).where(examRecordTable.field("f_exam_id", String.class).eq(examId))
				.and((examRecordTable.field("f_member_id", String.class).eq(memberId)))
				.fetch(r -> {
					ExamRecord record = new ExamRecord();
					record.setId(r.get(examRecordTable.field("f_id", String.class)));
					record.setStatus(r.get( examRecordTable.field("f_status", Integer.class)));
					return record;
				}));
	}


	@Override
	@DataSource
	public void nullifyExamRecord(Integer examRegion, String examRecordId, String examId) {
		// 更新考试记录表为作废
		updateExamRecordNullify(examRegion, examRecordId, examId);
		// 更新答题记录表分数为0
		updateAnswerRecordNullify(examRegion, examRecordId, examId);
		// 更新考试注册表
		updateExamRegistNullify(examRegion, examRecordId, examId);
	}

	@Override
	@DataSource
	public void certificateRecordNullify(Integer examRegion, String memberId, String examId) {
		// 更新q全部考试记录表为作废
		updateAllExamRecordNullify(examRegion, memberId, examId);
		// 更新q全部答题记录表分数为0
		updateAllAnswerRecordNullify(examRegion, memberId, examId);
		// 更新q全部考试注册表
		updateAllExamRegistNullify(examRegion, memberId, examId);
	}


	private void updateAllExamRegistNullify(Integer examRegion, String memberId, String examId) {
		TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

		List<String> ids = examRegistDao.execute(e -> e.select(
						Fields.start()
								.add(examRegistTable.field("f_id", String.class))
								.end())
				.from(examRegistTable)
				.where(examRegistTable.field("f_exam_id", String.class).eq(examId))
				.and(examRegistTable.field("f_member_id", String.class).eq(memberId))
				.limit(1)
				.fetch(examRegistTable.field("f_id", String.class)));

		if (CollectionUtils.isNotEmpty(ids)) {
			// 更新考试注册表
			examRegistDao.execute(dslContext ->
					dslContext.update(examRegistTable)
							.set(examRegistTable.field("f_pass_status", Integer.class), ExamRegist.PASS_STATUS_NULLIFY)
							.set(examRegistTable.field("f_top_score", Integer.class), 0)
							.set(examRegistTable.field("f_certificate_issue", Integer.class), ExamRegist.CERTIFICATE_ISSUE_NO)
							.where(examRegistTable.field("f_id", String.class).eq(ids.get(0)))
							.execute());
		}
	}

	private void updateAllAnswerRecordNullify(Integer examRegion, String memberId, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		List<String> ids = examRecordDao.execute(e ->
				e.select(Fields.start()
								.add(examRecordTable.field("f_id", String.class))
								.end())
						.from(examRecordTable)
						.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
						.and(examRecordTable.field("f_member_id", String.class).eq(memberId))
		).fetch(examRecordTable.field("f_id", String.class));
		ids.forEach(id -> updateAnswerRecordNullify(examRegion, id, examId));
	}


	private void updateAllExamRecordNullify(Integer examRegion, String memberId, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		examRecordDao.execute(e -> e.update(examRecordTable)
				.set(examRecordTable.field("f_score", Integer.class), 0)
				.set(examRecordTable.field("f_status", Integer.class), ExamRecord.STATUS_NULLIFY)
				.set(examRecordTable.field("f_right_count", Integer.class), 0)
				.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
				.and(examRecordTable.field("f_member_id", String.class).eq(memberId))
				.execute());
	}


	private void updateExamRegistNullify(Integer examRegion, String examRecordId, String examId) {
		TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

		List<ExamRegist> examRegistList = examRegistDao.execute(e -> e.select(
						Fields.start()
								.add(examRegistTable.fields())
								.end())
				.from(examRegistTable)
				.where(examRegistTable.field("f_top_score_record_id", String.class).eq(examRecordId))
				.limit(1)
				.fetch(r -> {
					ExamRegist examRegist = new ExamRegist();
					examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
					examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
					examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
					examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
					examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
					examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
					examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
					examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
					examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));
					examRegist.setCreateTime(r.getValue(examRegistTable.field("f_create_time", Long.class)));
					examRegist.setType(r.getValue(examRegistTable.field("f_type", Integer.class)));
					return examRegist;
				}));
		// 如果作废的记录就是最高分的记录，需要处理考试注册表
		if (CollectionUtils.isNotEmpty(examRegistList)) {
			ExamRegist examRegist = examRegistList.get(0);
			// 查询除本次考试记录外，最高分的考试记录
			TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
			List<ExamRecord> examRecordList = examRecordDao.execute(e ->
					e.select(Fields.start()
									.add(examRecordTable.field("f_id", String.class))
									.add(examRecordTable.field("f_score", Integer.class))
									.add(examRecordTable.field("f_status", Integer.class))
									.end())
							.from(examRecordTable)
							.where(examRecordTable.field("f_exam_id", String.class).eq(examId))
							.and(examRecordTable.field("f_member_id", String.class).eq(examRegist.getMemberId()))
							.and(examRecordTable.field("f_id", String.class).ne(examRecordId))
							.and(examRecordTable.field("f_status", Integer.class).in(ExamRecord.STATUS_PASS, ExamRecord.STATUS_NOT_PASS, ExamRecord.STATUS_FINISHED))
			).orderBy(examRecordTable.field("f_score", Integer.class).desc()).limit(0, 1).fetch(r -> {
				ExamRecord examRecord = new ExamRecord();
				examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
				examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
				examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
				return examRecord;
			});

			// 如果有其他最高分记录，更新考试注册表的数据为剩下的最高分的记录
			if (CollectionUtils.isNotEmpty(examRecordList)) {
				ExamRecord examRecord = examRecordList.get(0);
				examRegist.setTopScoreRecordId(examRecord.getId());
				// 及格状态：0及格 1不及格 2已完成
				// 已完成
				Integer registPassStatus = ExamRegist.PASS_STATUS_FINISH;
				if (ExamRecord.STATUS_PASS.equals(examRecord.getStatus())) {
					// 及格
					registPassStatus =  ExamRegist.PASS_STATUS_YES;
				} else if (ExamRecord.STATUS_NOT_PASS.equals(examRecord.getStatus())) {
					// 不及格
					registPassStatus =  ExamRegist.PASS_STATUS_NO;
				}
				examRegist.setPassStatus(registPassStatus);
				examRegist.setTopScore(examRecord.getScore());
			} else {
				// 如果没有其他最高分的记录，更新考试注册表为作废
				examRegist.setPassStatus(ExamRegist.PASS_STATUS_NULLIFY);
				examRegist.setTopScore(0);
			}
			examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);

			// 先删除证书记录表
			deleteCertificateRecord(examId, examRegist.getMemberId());

			// 再更新考试注册表
			examRegistDao.execute(dslContext ->
					dslContext.update(examRegistTable)
							.set(examRegistTable.field("f_top_score_record_id", String.class),examRegist.getTopScoreRecordId())
							.set(examRegistTable.field("f_pass_status", Integer.class), examRegist.getPassStatus())
							.set(examRegistTable.field("f_top_score", Integer.class), examRegist.getTopScore())
							.set(examRegistTable.field("f_certificate_issue", Integer.class), examRegist.getCertificateIssue())
							.where(examRegistTable.field("f_id", String.class).eq(examRegist.getId()))
							.execute());
		}

	}

	private void deleteCertificateRecord(String examId, String memberId) {
		messageSender.send(MessageTypeContent.DELETE_CERTIFICATE_DATA,
				MessageHeaderContent.MEMBER_ID, memberId,
				MessageHeaderContent.EXAM_ID,examId);
	}



	private void updateAnswerRecordNullify(Integer examRegion, String examRecordId, String examId) {
		TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
		List<String> ids = answerRecordDao.execute(e ->
				e.select(Fields.start()
								.add(table.field("f_id", String.class))
								.end())
						.from(table)
						.where(table.field("f_exam_record_id", String.class).eq(examRecordId))
		).fetch(table.field("f_id", String.class));
		if (CollectionUtils.isNotEmpty(ids)) {
			for (String id: ids) {
				answerRecordDao.execute(e -> e.update(table)
						.set(table.field("f_score", Integer.class), 0)
						.set(table.field("f_is_right", Integer.class), 0)
						.where(table.field("f_id", String.class).eq(id))
						.execute());
			}
		}

	}

	/**
	 * 考试记录表的分数改为0分，及格状态改为“作废”，正确数量改为0，
	 */
	private void updateExamRecordNullify(Integer examRegion, String examRecordId, String examId) {
		TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
		examRecordDao.execute(e -> e.update(examRecordTable)
				.set(examRecordTable.field("f_score", Integer.class), 0)
				.set(examRecordTable.field("f_status", Integer.class), ExamRecord.STATUS_NULLIFY)
				.set(examRecordTable.field("f_right_count", Integer.class), 0)
				.where(examRecordTable.field("f_id", String.class).eq(examRecordId))
				.execute());
	}


}
