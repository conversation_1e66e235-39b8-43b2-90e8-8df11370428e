package com.zxy.product.examstu.service.component.sequence;

import com.zxy.product.examstu.api.sequence.AbstractIncrementCodeRule;
import com.zxy.product.examstu.api.sequence.CodeMaxCallback;
import com.zxy.product.examstu.api.sequence.SequenceGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * redis编号生成规则
 * <AUTHOR>
 * @date 2017年11月11日
 */
@Service
public class RedisIncrementCodeRule extends AbstractIncrementCodeRule {

    private final static Logger LOGGER = LoggerFactory.getLogger(RedisIncrementCodeRule.class);

    private SequenceGenerator sequenceGenerator;
    /** 是否已经加载到redis，key：序列号前缀 */
    private Map<String, Boolean> isLoad = new HashMap();

    @Autowired
    @Qualifier("redisSequenceGenerator")
    public void setSequenceGenerator(SequenceGenerator sequenceGenerator){
        this.sequenceGenerator = sequenceGenerator;
    }

    /**
     * 得到格式化之后的序列串
     * @param prefix 前缀
     * @return 格式化之后的序列串
     * @throws Exception 数据转换异常等
     */
    @Override
    public String getCode(Optional<String> prefix) throws Exception{
        // 获取序号，长度可以>=length
        Long seq = sequenceGenerator.getSequence(getType() + prefix.orElse(""));
        return getFormatterCode(prefix, seq);


    }

    /** 格式化序列编码 */
    private String getFormatterCode(Optional<String> prefix, Long sequence) {
        // 超出length前面需要替换为字母的序列号
        String codeSequence = "";
        // length以内的数字序列
        Long numSequence = sequence;
        String seqStr = String.valueOf(sequence);
        if (seqStr.length() > getLength()) {
            // 当长度大于length时，前面序列采用字母补齐，规则同Excel列号规则
            // length以内的数字序列
            String numStr = seqStr.substring(seqStr.length() - getLength());
            numSequence = Long.valueOf(numStr);
            // 超出length前面需要替换为字母的数字序列号，获取并转换为字符序列
            String codeStr = seqStr.substring(0, seqStr.length() - getLength());
            if (!StringUtils.isEmpty(codeStr)) {
                codeSequence = AlphaNumUtil.numToAlpha(Integer.valueOf(codeStr));
            }
        }

        // 最终返回格式化的序列号：module前缀 + 自定义前缀 + 字母序列号 + length之内（不足length前面补0）数字序列号
        return formatterCode(numSequence, getLength(),  getPrefix() + prefix.orElse("") + codeSequence);
    }

    /**
     * <ul>
     *     <li>redis正常情况，状态为初次加载：从DB中加载编码，设置已加载状态</li>
     *     <li>redis正常情况，redis获取序列为1：从DB中加载编码</li>
     *     <li>redis异常情况，即系统异常，无法再生成sequence</li>
     * </ul>
     */
    @Override
    public String getCode(CodeMaxCallback callback, Optional<String> prefix) throws Exception{
        // 获取序号，长度可以>=length
        Long seq = sequenceGenerator.getSequence(getType() + prefix.orElse(""));
        String prefixCode = getPrefix() + prefix.orElse("");
        Boolean prefixIsLoad = isLoad.get(prefixCode);
        if (prefixIsLoad == null || !prefixIsLoad) {
            // 初次加载，从DB获取
            String code = getCodeFromCallback(callback, prefix);
            isLoad.put(prefixCode, true);
            return code;
        } else if (Long.valueOf(1).equals(seq)) {
            // 如果从redis获取的序列为1，为了保险起见，从DB获取一次
            return getCodeFromCallback(callback, prefix);
        }
        return getFormatterCode(prefix, seq);
    }

    /**
     * 从回调获取序列号
     * @param callback 获取序列号回调
     * @param prefix 前缀
     * @return
     * @throws Exception
     */
    private String getCodeFromCallback(CodeMaxCallback callback, Optional<String> prefix) throws Exception{
        String type = getType() + prefix.orElse("");
        String prefixCode = getPrefix() + prefix.orElse("");
        String maxCode = callback.getMaxCode(prefix);
        Long seq = 0L;
        if(!StringUtils.isEmpty(maxCode)){
            // 序列号串，含字符序列，如A0001
            String seqStr = maxCode.substring(prefixCode.length());
            // 数字序列
            String numStr = seqStr.substring(seqStr.length() - getLength());
            // 字符序列 A-Z AA...，获取转换为数字序列
            String codeStr = seqStr.substring(0, seqStr.length() - getLength());
            if (!StringUtils.isEmpty(codeStr)) {
                int codeNum = AlphaNumUtil.alphaToNum(codeStr, codeStr.length());
                seq = Long.valueOf(String.valueOf(codeNum) + numStr);
            } else {
                seq = Long.valueOf(numStr);
            }
        }
        sequenceGenerator.setSequence(type, seq);
        return getCode(prefix);
    }

}
