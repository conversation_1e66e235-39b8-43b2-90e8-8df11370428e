package com.zxy.product.course.service.support.subAuthenticated;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.certificate.CertificateNormalCodeGenerator;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.subAuthenticated.SubAuthenticatedService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.AuthenticatedGroup;
import com.zxy.product.course.entity.BusinessCertificate;
import com.zxy.product.course.entity.CertificateRecord;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.entity.SubAuthenticated;
import com.zxy.product.course.entity.SubAuthenticatedCertificateRecord;
import com.zxy.product.course.entity.SubAuthenticatedContentConfigure;
import com.zxy.product.course.entity.SubAuthenticatedDimension;
import com.zxy.product.course.entity.SubAuthenticatedMemberDimension;
import com.zxy.product.course.entity.SubAuthenticatedProgress;
import com.zxy.product.course.entity.SubAuthenticatedRegister;
import com.zxy.product.course.entity.SubAuthenticatedResourceAuditRecord;
import com.zxy.product.course.entity.SubAuthenticatedStudyOnline;
import com.zxy.product.course.entity.SubAuthenticatedTmp;
import com.zxy.product.course.jooq.tables.pojos.CertificateRecordEntity;
import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCertificateRecordEntity;
import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedDimensionEntity;
import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedMemberDimensionEntity;
import com.zxy.product.course.service.util.DateUtil;
import com.zxy.product.course.service.util.OrgConditionUtil;
import com.zxy.product.course.util.DesensitizationUtil;
import com.zxy.product.course.util.EncryptUtil;
import com.zxy.product.course.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.entity.SubAuthenticatedStudyOnline.BUSINESS_TYPE_COURSE_NAME;
import static com.zxy.product.course.entity.SubAuthenticatedStudyOnline.BUSINESS_TYPE_SUBJECT_NAME;
import static com.zxy.product.course.jooq.Tables.AUDIENCE_MEMBER;
import static com.zxy.product.course.jooq.Tables.AUDIENCE_OBJECT;
import static com.zxy.product.course.jooq.Tables.AUTHENTICATED_GROUP;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.MEMBER;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_CERTIFICATE_RECORD;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_DIMENSION;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_MEMBER_DIMENSION;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_REGISTER;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_STUDY_ONLINE;
import static com.zxy.product.course.jooq.Tables.SUB_AUTHENTICATED_TMP;
import static com.zxy.product.course.jooq.tables.SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE;
import static com.zxy.product.course.service.util.SecurePathCdnUtils.generateSecurePathCdn;
import static java.util.stream.Collectors.groupingBy;

/**
 * @Classname SubAuthenticatedServiceSupport
 * @Description
 * @Date 2023/3/6
 * @Created by futzh
 */
@Service
public class SubAuthenticatedServiceSupport implements SubAuthenticatedService {
    private static final Logger log = LoggerFactory.getLogger(SubAuthenticatedServiceSupport.class);
    private CommonDao<SubAuthenticated> subAuthenticatedCommonDao;
    private CommonDao<SubAuthenticatedContentConfigure> configureCommonDao;
    private CommonDao<SubAuthenticatedStudyOnline> studyOnlineCommonDao;
    private CommonDao<SubAuthenticatedDimension> dimensionCommonDao;
    private CommonDao<SubAuthenticatedMemberDimension> memberDimensionCommonDao;
    private CommonDao<SubAuthenticatedRegister> registerCommonDao;
    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    @Resource
    private CommonDao<AuthenticatedGroup> groupDao;
    @Resource
    private CommonDao<SubAuthenticatedCertificateRecord> subAuthenticatedCertificateRecordCommonDao;
    @Resource
    private CommonDao<SubAuthenticatedResourceAuditRecord> resourceAuditRecordCommonDao;
    @Resource
    private Cache sequenceCache;
    @Resource
    private CertificateRecordService certificateRecordService;
    @Resource
    private CertificateNormalCodeGenerator normalCodeGenerator;
    private CourseCacheService courseCacheService;

    private CommonDao<SubAuthenticatedTmp> subAuthenticatedTmpCommonDao;

    @Autowired
    public void setSubAuthenticatedTmpCommonDao(CommonDao<SubAuthenticatedTmp> subAuthenticatedTmpCommonDao) {
        this.subAuthenticatedTmpCommonDao = subAuthenticatedTmpCommonDao;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }
    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }

    @Autowired
    public void setRegisterCommonDao(CommonDao<SubAuthenticatedRegister> registerCommonDao) {
        this.registerCommonDao = registerCommonDao;
    }

    @Autowired
    public void setStudyOnlineCommonDao(CommonDao<SubAuthenticatedStudyOnline> studyOnlineCommonDao) {
        this.studyOnlineCommonDao = studyOnlineCommonDao;
    }

    @Autowired
    public void setDimensionCommonDao(CommonDao<SubAuthenticatedDimension> dimensionCommonDao) {
        this.dimensionCommonDao = dimensionCommonDao;
    }

    @Autowired
    public void setSubAuthenticatedCommonDao(CommonDao<SubAuthenticated> subAuthenticatedCommonDao) {
        this.subAuthenticatedCommonDao = subAuthenticatedCommonDao;
    }

    @Autowired
    public void setConfigureCommonDao(CommonDao<SubAuthenticatedContentConfigure> configureCommonDao) {
        this.configureCommonDao = configureCommonDao;
    }

    @Autowired
    public void setMemberDimensionCommonDao(CommonDao<SubAuthenticatedMemberDimension> memberDimensionCommonDao) {
        this.memberDimensionCommonDao = memberDimensionCommonDao;
    }

    @Override
    public String save(List<SubAuthenticatedContentConfigure> contentConfigureList, Optional<String> name, Optional<String> certificateId,
                       Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag) {
        //处理子认证
        SubAuthenticated insert = insertSubAuthenticated(name, certificateId, organizationId, splitTrainingFlag, manageIgnoreFlag,authenticatedLevel, checkCertificateFlag);
        // 如果专题配置证书，需添加业务关联关系表
        certificateId.ifPresent(id -> certificateRecordService.insert(insert.getId(), id, BusinessCertificate.BUSINESS_TYPE_SUB_AUTHENTICATED));

        List<SubAuthenticatedStudyOnline> undoStudyOnlines = new ArrayList<>();
        List<SubAuthenticatedDimension> undoDimensions = new ArrayList<>();

        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            contentConfigureList.forEach(subAuthenticatedContentConfigure -> {
                //处理配置表
                SubAuthenticatedContentConfigure contentConfigure = insertContentConfigure(subAuthenticatedContentConfigure, insert.getId());
                //构建在线学习组
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY.equals(subAuthenticatedContentConfigure.getContentType())) {
                    List<SubAuthenticatedStudyOnline> onlineList = subAuthenticatedContentConfigure.getSubAuthenticatedStudyOnlines();
                    onlineList.forEach(online -> {
                        online.forInsert();
                        online.setSubAuthenticatedId(insert.getId());
                        online.setStudyGroupId(contentConfigure.getContentId());
                    });
                    undoStudyOnlines.addAll(onlineList);
                }
                //构建认证维度
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(subAuthenticatedContentConfigure.getContentType())) {
                    List<SubAuthenticatedDimension> dimensionList = subAuthenticatedContentConfigure.getSubAuthenticatedDimensions();
                    dimensionList.forEach(dimension -> {
                        dimension.forInsert();
                        dimension.setSubAuthenticatedId(insert.getId());
                    });
                    undoDimensions.addAll(dimensionList);
                }
            });

            undoStudyOnlines.forEach(online -> {
                studyOnlineCommonDao.insert(online);
            });

            undoDimensions.forEach(dimension -> {
                dimensionCommonDao.insert(dimension);
            });
        }
        return insert.getId();
    }

    @Override
    public String update(String id, List<SubAuthenticatedContentConfigure> contentConfigureList, Optional<String> name, Optional<String> certificateId,
                         Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag) {
        SubAuthenticated authenticatedInfo = subAuthenticatedCommonDao.getOptional(id).orElse(null);
        if (authenticatedInfo == null)
            return null;
        //更新子认证
        SubAuthenticated update = updateSubAuthenticated(authenticatedInfo, name, certificateId, organizationId, splitTrainingFlag, manageIgnoreFlag,authenticatedLevel, checkCertificateFlag);
        //查询子认证管理的配置表，在线学习组表，考试组，，删除
        configureCommonDao.delete(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.eq(id));
        studyOnlineCommonDao.delete(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(id));
        //删除证书业务管理表
        certificateRecordService.delete(id);
        // 如果专题配置证书，需添加业务关联关系表
        certificateId.ifPresent(cid -> certificateRecordService.insert(id, cid, BusinessCertificate.BUSINESS_TYPE_SUB_AUTHENTICATED));
        //需要新增的学习组列表
        List<SubAuthenticatedStudyOnline> undoStudyOnlines = new ArrayList<>();
        //需要对比的维度列表
        List<SubAuthenticatedDimension> newDimensionList = new ArrayList<>();

        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            contentConfigureList.forEach(subAuthenticatedContentConfigure -> {
                //处理配置表
                SubAuthenticatedContentConfigure contentConfigure = insertContentConfigure(subAuthenticatedContentConfigure, id);
                //构建在线学习组
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY.equals(subAuthenticatedContentConfigure.getContentType())) {
                    List<SubAuthenticatedStudyOnline> onlineList = subAuthenticatedContentConfigure.getSubAuthenticatedStudyOnlines();
                    onlineList.forEach(online -> {
                        online.forInsert();
                        online.setSubAuthenticatedId(id);
                        online.setStudyGroupId(contentConfigure.getContentId());
                    });
                    undoStudyOnlines.addAll(onlineList);
                }
                //构建认证维度,对比差异
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(subAuthenticatedContentConfigure.getContentType())) {
                    List<SubAuthenticatedDimension> dimensionList = subAuthenticatedContentConfigure.getSubAuthenticatedDimensions();
                    newDimensionList.addAll(dimensionList);
                }
            });

            undoStudyOnlines.forEach(online -> {
                studyOnlineCommonDao.insert(online);
            });

            //newDimensionList为空直接删除子认证的所有认证维度
            if (ObjectUtils.isEmpty(newDimensionList)) {
                dimensionCommonDao.delete(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(id));
                return update.getId();
            }

            List<SubAuthenticatedDimension> oldDimensionList = dimensionCommonDao.fetch(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(id));
            //old为空直接insert所有newList
            if (ObjectUtils.isEmpty(oldDimensionList)) {
                newDimensionList.forEach(dimension -> {
                    dimension.forInsert();
                    dimension.setSubAuthenticatedId(id);
                });
                newDimensionList.forEach(dimension -> {
                    dimensionCommonDao.insert(dimension);
                });
                return update.getId();
            }
            Map<String, SubAuthenticatedDimension> oldDimensionMap = oldDimensionList.stream().collect(Collectors.toMap(SubAuthenticatedDimension::getCode, x -> x));
            Map<String, SubAuthenticatedDimension> newDimensionMap = newDimensionList.stream().collect(Collectors.toMap(SubAuthenticatedDimension::getCode, x -> x));
            List<String> newCodes = newDimensionList.stream().map(r -> r.getCode()).collect(Collectors.toList());
            List<String> oldCodes = oldDimensionList.stream().map(r -> r.getCode()).collect(Collectors.toList());
            //新增
            Set addSet = Sets.difference(Sets.newHashSet(newCodes), Sets.newHashSet(oldCodes));
            //删除
            Set delSet = Sets.difference(Sets.newHashSet(oldCodes), Sets.newHashSet(newCodes));
            //更新
            Set updateSet = Sets.intersection(Sets.newHashSet(newCodes), Sets.newHashSet(oldCodes));

            if (!ObjectUtils.isEmpty(addSet)) {
                for (Map.Entry<String, SubAuthenticatedDimension> entry : newDimensionMap.entrySet()) {
                    if (addSet.contains(entry.getKey())) {
                        entry.getValue().forInsert();
                        entry.getValue().setSubAuthenticatedId(update.getId());
                        dimensionCommonDao.insert(entry.getValue());
                    }
                }
            }
            if (!ObjectUtils.isEmpty(delSet)) {
                for (Map.Entry<String, SubAuthenticatedDimension> entry : oldDimensionMap.entrySet()) {
                    if (delSet.contains(entry.getKey())) {
                        dimensionCommonDao.delete(entry.getValue().getId());
                    }
                }
            }
            if (!ObjectUtils.isEmpty(updateSet)) {
                for (Map.Entry<String, SubAuthenticatedDimension> entry : oldDimensionMap.entrySet()) {
                    if (updateSet.contains(entry.getKey())) {
                        entry.getValue().setName(newDimensionMap.get(entry.getKey()).getName());
                        entry.getValue().setLockCoverId(newDimensionMap.get(entry.getKey()).getLockCoverId());
                        entry.getValue().setUnlockCoverId(newDimensionMap.get(entry.getKey()).getUnlockCoverId());
                        entry.getValue().setLockCoverPath(newDimensionMap.get(entry.getKey()).getLockCoverPath());
                        entry.getValue().setUnlockCoverPath(newDimensionMap.get(entry.getKey()).getUnlockCoverPath());
                        entry.getValue().setOrder(newDimensionMap.get(entry.getKey()).getOrder());
                        dimensionCommonDao.update(entry.getValue());
                    }
                }
            }
        }
        return update.getId();
    }

    @Override
    public Boolean publish(String id, Integer publish, String currentMemberId) {
        SubAuthenticated authenticatedInfo = subAuthenticatedCommonDao.getOptional(id).orElse(null);
        if (authenticatedInfo == null)
            throw new UnprocessableException(ErrorCode.SubAuthenticatedIsNull);
        Long current = System.currentTimeMillis();
        authenticatedInfo.setFirstPublishTime(authenticatedInfo.getFirstPublishTime() == null ? current : authenticatedInfo.getFirstPublishTime());
        authenticatedInfo.setIsPublish(publish);
        if (SubAuthenticated.PUBLISH_YES == publish) {
            authenticatedInfo.setPublishMemberId(currentMemberId);
            authenticatedInfo.setPublishTime(current);
        }
        subAuthenticatedCommonDao.update(authenticatedInfo);
        return true;
    }

    @Override
    public Boolean delete(String id) {
        //查询子认证管理的配置表，在线学习组表，考试组，维度等，删除
        configureCommonDao.delete(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.eq(id));
        studyOnlineCommonDao.delete(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(id));
        List<String> dimensionIds = dimensionCommonDao.execute(dsl -> dsl
                .select(SUB_AUTHENTICATED_DIMENSION.ID)
                .from(SUB_AUTHENTICATED_DIMENSION)
                .where(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(id)).fetch(r -> r.getValue(SUB_AUTHENTICATED_DIMENSION.ID)));
        dimensionCommonDao.delete(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(id));
        memberDimensionCommonDao.delete(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID.in(dimensionIds));
        subAuthenticatedCommonDao.delete(id);
        return true;
    }

    @Override
    public String findMaxDimensionCode() {
        return dimensionCommonDao.execute(dsl -> dsl
                .select(SUB_AUTHENTICATED_DIMENSION.CODE.max())
                .from(SUB_AUTHENTICATED_DIMENSION).fetchOptional(SUB_AUTHENTICATED_DIMENSION.CODE).orElse(""));
    }

    @Override
    public SubAuthenticated findAdminDetail(String id) {
        SubAuthenticated subAuthenticated = subAuthenticatedCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED.ID)
                        .add(SUB_AUTHENTICATED.NAME)
                        .add(SUB_AUTHENTICATED.CERTIFICATE_ID)
                        .add(SUB_AUTHENTICATED.ORGANIZATION_ID)
                        .add(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG)
                        .add(ORGANIZATION.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(SUB_AUTHENTICATED.PUBLISH_MEMBER_ID).end())
                .from(SUB_AUTHENTICATED)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(SUB_AUTHENTICATED.ORGANIZATION_ID))
                .leftJoin(MEMBER).on(MEMBER.ID.eq(SUB_AUTHENTICATED.PUBLISH_MEMBER_ID))
                .where(SUB_AUTHENTICATED.ID.eq(id))
                .fetchOptional(r -> {
                    SubAuthenticated result = new SubAuthenticated();
                    result.setId(r.getValue(SUB_AUTHENTICATED.ID));
                    result.setName(r.getValue(SUB_AUTHENTICATED.NAME));
                    result.setOrganizationId(r.getValue(SUB_AUTHENTICATED.ORGANIZATION_ID));
                    result.setCertificateId(r.getValue(SUB_AUTHENTICATED.CERTIFICATE_ID));
                    result.setPublishMemberId(r.getValue(SUB_AUTHENTICATED.PUBLISH_MEMBER_ID));
                    result.setPublishMember(r.getValue(MEMBER.FULL_NAME));
                    result.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                    result.setManageIgnoreFlag(r.getValue(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG));
                    return result;
                }).orElse(null));
        Integer count = registerCommonDao.execute(x -> x.select(SUB_AUTHENTICATED_REGISTER.MEMBER_ID.countDistinct())
                .from(SUB_AUTHENTICATED_REGISTER)
                .where(SUB_AUTHENTICATED_REGISTER.SUB_AUTHENTICATED_ID.eq(id))).fetchOne().getValue(0, Integer.class);
        subAuthenticated.setRegisterNum(count);

        List<String> contentTypeList = configureCommonDao.execute(s -> s.selectDistinct(Fields.start()
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE).end())
                .from(SUB_AUTHENTICATED_CONTENT_CONFIGURE)
                .where(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.eq(id))
                .fetch(a -> a.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE, String.class)));
        subAuthenticated.setContentTypeList(contentTypeList);
        return subAuthenticated;
    }

    @Override
    public PagedResult<SubAuthenticatedProgress> page(String subAuthenticatedId, int pageNum, int pageSize, Optional<String> memberName, Optional<String> memberReadName,
                                                      Optional<String> organizationId, Optional<Integer> contain, Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate, Map<String, Set<String>> grantOrganizationMap) {

        int finalPageSize = pageSize;
        return registerCommonDao.execute(x -> {
            //查询字段
            SelectSelectStep<Record> selectListField = x.select(Fields.start()
                    .add(SUB_AUTHENTICATED_REGISTER.ID, SUB_AUTHENTICATED_REGISTER.CREATE_TIME)
                    .add(MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME)
                    .add(ORGANIZATION.ID, ORGANIZATION.NAME).end()); // 查询list
            String path = null;
            if (organizationId.isPresent() && contain.isPresent() && contain.get() == 1) {
                path = courseStudyProgressCommonDao.execute(t -> t.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId.get()))).fetchOne(ORGANIZATION.PATH);
            }
            Optional<String> pathOptioanl = Optional.ofNullable(path);
            //查询条件
            List<Condition> param = Stream
                    .of(memberName.map(MEMBER.NAME::contains), memberReadName.map(MEMBER.FULL_NAME::startsWith),
                            beginRegisterDate.map(SUB_AUTHENTICATED_REGISTER.CREATE_TIME::ge),
                            endRegisterDate.map(SUB_AUTHENTICATED_REGISTER.CREATE_TIME::le),
                            pathOptioanl.map(p -> ORGANIZATION.PATH.like(p + "%")))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            if (!organizationId.isPresent()) {
                param.add(OrgConditionUtil.grantOrgCondition(grantOrganizationMap, ORGANIZATION, Optional.empty()));
            }
            if (organizationId.isPresent() && contain.isPresent() && contain.get() == 0) {
                param.add(ORGANIZATION.ID.eq(organizationId.get()));
            }
            //关联表
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a
                    .from(SUB_AUTHENTICATED_REGISTER).leftJoin(MEMBER).on(SUB_AUTHENTICATED_REGISTER.MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param)
                    .and(SUB_AUTHENTICATED_REGISTER.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));
            // 查询count
            SelectSelectStep<Record> selectCountField = x
                    .select(Fields.start().add(SUB_AUTHENTICATED_REGISTER.ID.count()).end()); // 查询总条数
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a -> a
                    .from(SUB_AUTHENTICATED_REGISTER).leftJoin(MEMBER).on(SUB_AUTHENTICATED_REGISTER.MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param)
                    .and(SUB_AUTHENTICATED_REGISTER.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));
            int count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);
            //组装结果
            SelectConditionStep<Record> listStep = stepFunc.apply(selectListField);
            listStep.orderBy(SUB_AUTHENTICATED_REGISTER.CREATE_TIME.desc());
            List<SubAuthenticatedProgress> progressList = listStep.limit((pageNum - 1) * finalPageSize, finalPageSize).fetch(r -> {
                SubAuthenticatedProgress progress = new SubAuthenticatedProgress();
                progress.setMemberId(r.getValue(MEMBER.ID));
                progress.setMemberName(r.getValue(MEMBER.NAME));
                progress.setMemberFullName(r.getValue(MEMBER.FULL_NAME));
                progress.setOrganizationId(r.getValue(ORGANIZATION.ID));
                progress.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                progress.setSubAuthenticatedId(subAuthenticatedId);
                progress.setRegisterId(r.getValue(SUB_AUTHENTICATED_REGISTER.ID));
                progress.setRegisterDate(r.getValue(SUB_AUTHENTICATED_REGISTER.CREATE_TIME));
                return progress;
            });
            return PagedResult.create(count, progressList);
        });
    }

    @Override
    public PagedResult<SubAuthenticatedProgress> find(String subAuthenticatedId, int pageNum, int pageSize, Optional<String> memberName, Optional<String> memberReadName,
                                                      Optional<String> organizationId, Optional<Integer> contain, Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate, Map<String, Set<String>> grantOrganizationMap) {

        PagedResult<SubAuthenticatedProgress> page = page(subAuthenticatedId, pageNum, pageSize, memberName, memberReadName,
                organizationId, contain, beginRegisterDate, endRegisterDate, grantOrganizationMap);
        List<SubAuthenticatedProgress> progressList = page.getItems();
        List<String> memberIds = progressList.stream().map(SubAuthenticatedProgress::getMemberId).collect(Collectors.toList());
        //<memId,有受众范围的课程id>
        Map<String, List<String>> memBusinessIds = getBusinessIds(subAuthenticatedId, memberIds);
        //<memId,所有学习记录集合>
        Map<Object, List<CourseStudyProgress>> memberProgressMap = getMemberCourseList(memberIds, subAuthenticatedId, memBusinessIds);
        Map<String, Map<String, Integer>> memberTotalMap = getMemberTotal(memberProgressMap, memBusinessIds);
        if (ObjectUtils.isEmpty(memberTotalMap))
            return PagedResult.create(page.getRecordCount(), progressList);
        progressList.forEach(progress -> {
            progress.setStudyStatus(memberTotalMap.get(progress.getMemberId()).get("studyStatus"));
            progress.setStudyTotalTime(memberTotalMap.get(progress.getMemberId()).get("studyTotalTime"));
        });
        return PagedResult.create(page.getRecordCount(), progressList);
    }

    @Override
    public PagedResult<SubAuthenticated> pageList(int pageNum, int pageSize, Optional<String> subAuthenticatedName, Optional<String> subAuthenticatedCode,
                                                  List<String> grantOrganizationIds, Optional<Integer> status, Integer manageFlag, Optional<Long> firstPublishBeginDate,
                                                  Optional<Long> firstPublishEndDate, Optional<Long> lastPublishBeginDate, Optional<Long> lastPublishEndDate, Optional<Integer> authenticatedLevel) {
        //1代表查询共管子认证列表
        if (manageFlag == 1) {
            return managePageList(pageNum, pageSize, subAuthenticatedName, subAuthenticatedCode, status,
                    firstPublishBeginDate, firstPublishEndDate, lastPublishBeginDate, lastPublishEndDate);
        }
        return subPageList(pageNum, pageSize, subAuthenticatedName, subAuthenticatedCode, grantOrganizationIds, status,
                firstPublishBeginDate, firstPublishEndDate, lastPublishBeginDate, lastPublishEndDate,authenticatedLevel);
    }

    private PagedResult<SubAuthenticated> subPageList(int pageNum, int pageSize, Optional<String> subAuthenticatedName, Optional<String> subAuthenticatedCode,
                                                      List<String> grantOrganizationIds, Optional<Integer> status, Optional<Long> firstPublishBeginDate,
                                                      Optional<Long> firstPublishEndDate, Optional<Long> lastPublishBeginDate, Optional<Long> lastPublishEndDate, Optional<Integer> authenticatedLevel) {
        return subAuthenticatedCommonDao.execute(x -> {
            //查询字段
            SelectSelectStep<Record> selectListField = x.select(Fields.start()
                    .add(SUB_AUTHENTICATED.ID, SUB_AUTHENTICATED.NAME, SUB_AUTHENTICATED.CODE, SUB_AUTHENTICATED.IS_PUBLISH,
                            SUB_AUTHENTICATED.FIRST_PUBLISH_TIME, SUB_AUTHENTICATED.PUBLISH_TIME, SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG)
                    .add(ORGANIZATION.ID, ORGANIZATION.NAME,SUB_AUTHENTICATED.AUTHENTICATED_LEVEL).end()); // 查询list
            //查询条件
            List<Condition> param = Stream.of(subAuthenticatedName.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(SUB_AUTHENTICATED.NAME, " ", "").contains(str);
                            }),
                            subAuthenticatedCode.map(SUB_AUTHENTICATED.CODE::eq),
                            status.map(SUB_AUTHENTICATED.IS_PUBLISH::eq),
                            firstPublishBeginDate.map(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME::ge),
                            firstPublishEndDate.map(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME::le),
                            lastPublishBeginDate.map(SUB_AUTHENTICATED.PUBLISH_TIME::ge),
                            lastPublishEndDate.map(SUB_AUTHENTICATED.PUBLISH_TIME::le)
                            ,authenticatedLevel.map(SUB_AUTHENTICATED.AUTHENTICATED_LEVEL::eq))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(SUB_AUTHENTICATED.ORGANIZATION_ID.in(grantOrganizationIds));


            //关联表
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a
                    .from(SUB_AUTHENTICATED)
                    .leftJoin(ORGANIZATION).on(SUB_AUTHENTICATED.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(param);
            // 查询count
            SelectSelectStep<Record> selectCountField = x
                    .select(Fields.start().add(SUB_AUTHENTICATED.ID.count()).end()); // 查询总条数
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a -> a
                    .from(SUB_AUTHENTICATED)
                    .leftJoin(ORGANIZATION).on(SUB_AUTHENTICATED.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(param);
            int count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);
            //组装结果
            SelectConditionStep<Record> listStep = stepFunc.apply(selectListField);
            listStep.orderBy(SUB_AUTHENTICATED.CREATE_TIME.desc());
            List<SubAuthenticated> subList = listStep.limit((pageNum - 1) * pageSize, pageSize).fetch(r -> {
                SubAuthenticated sub = new SubAuthenticated();
                sub.setOrganizationId(r.getValue(ORGANIZATION.ID));
                sub.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                sub.setId(r.getValue(SUB_AUTHENTICATED.ID));
                sub.setName(r.getValue(SUB_AUTHENTICATED.NAME));
                sub.setCode(r.getValue(SUB_AUTHENTICATED.CODE));
                sub.setIsPublish(r.getValue(SUB_AUTHENTICATED.IS_PUBLISH));
                sub.setFirstPublishTime(r.getValue(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME));
                sub.setPublishTime(r.getValue(SUB_AUTHENTICATED.PUBLISH_TIME));
                sub.setManageIgnoreFlag(r.getValue(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG));
                sub.setAuthenticatedLevel(r.getValue(SUB_AUTHENTICATED.AUTHENTICATED_LEVEL));
                return sub;
            });
            return PagedResult.create(count, subList);
        });
    }

    private PagedResult<SubAuthenticated> managePageList(int pageNum, int pageSize, Optional<String> subAuthenticatedName, Optional<String> subAuthenticatedCode, Optional<Integer> status,
                                                         Optional<Long> firstPublishBeginDate, Optional<Long> firstPublishEndDate, Optional<Long> lastPublishBeginDate, Optional<Long> lastPublishEndDate) {
        return subAuthenticatedCommonDao.execute(x -> {
            //查询字段
            SelectSelectStep<Record> selectListField = x.select(Fields.start()
                    .add(SUB_AUTHENTICATED.ID, SUB_AUTHENTICATED.NAME, SUB_AUTHENTICATED.CODE, SUB_AUTHENTICATED.IS_PUBLISH,
                            SUB_AUTHENTICATED.FIRST_PUBLISH_TIME, SUB_AUTHENTICATED.PUBLISH_TIME, SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG)
                    .add(ORGANIZATION.ID, ORGANIZATION.NAME).end()); // 查询list
            //查询条件
            List<Condition> param = Stream.of(subAuthenticatedName.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(SUB_AUTHENTICATED.NAME, " ", "").contains(str);
                            }),
                            subAuthenticatedCode.map(SUB_AUTHENTICATED.CODE::contains),
                            status.map(SUB_AUTHENTICATED.IS_PUBLISH::eq),
                            firstPublishBeginDate.map(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME::ge),
                            firstPublishEndDate.map(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME::le),
                            lastPublishBeginDate.map(SUB_AUTHENTICATED.PUBLISH_TIME::ge),
                            lastPublishEndDate.map(SUB_AUTHENTICATED.PUBLISH_TIME::le))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(SUB_AUTHENTICATED.ORGANIZATION_ID.eq("1"));
            //关联表
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a
                    .from(SUB_AUTHENTICATED)
                    .leftJoin(ORGANIZATION).on(SUB_AUTHENTICATED.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param).and(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG.isNotNull());
            // 查询count
            SelectSelectStep<Record> selectCountField = x
                    .select(Fields.start().add(SUB_AUTHENTICATED.ID.count()).end()); // 查询总条数
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a -> a
                    .from(SUB_AUTHENTICATED)
                    .leftJoin(ORGANIZATION).on(SUB_AUTHENTICATED.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param).and(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG.isNotNull());
            int count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);
            //组装结果
            SelectConditionStep<Record> listStep = stepFunc.apply(selectListField);
            listStep.orderBy(SUB_AUTHENTICATED.CREATE_TIME.desc());
            List<SubAuthenticated> subList = listStep.limit((pageNum - 1) * pageSize, pageSize).fetch(r -> {
                SubAuthenticated sub = new SubAuthenticated();
                sub.setOrganizationId(r.getValue(ORGANIZATION.ID));
                sub.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                sub.setId(r.getValue(SUB_AUTHENTICATED.ID));
                sub.setName(r.getValue(SUB_AUTHENTICATED.NAME));
                sub.setCode(r.getValue(SUB_AUTHENTICATED.CODE));
                sub.setIsPublish(r.getValue(SUB_AUTHENTICATED.IS_PUBLISH));
                sub.setFirstPublishTime(r.getValue(SUB_AUTHENTICATED.FIRST_PUBLISH_TIME));
                sub.setPublishTime(r.getValue(SUB_AUTHENTICATED.PUBLISH_TIME));
                sub.setManageIgnoreFlag(r.getValue(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG));
                return sub;
            });
            return PagedResult.create(count, subList);
        });
    }


    @Override
    public Map<Integer, List<SubAuthenticatedContentConfigure>> findProgressDetail(String subAuthenticatedId, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        Map<Integer, List<SubAuthenticatedContentConfigure>> result = new HashMap<>();
        List<SubAuthenticatedContentConfigure> contentConfigureList = configureCommonDao.execute(s -> s.select(Fields.start()
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_ID)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_NAME)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER).end())
                .from(SUB_AUTHENTICATED_CONTENT_CONFIGURE)
                .where(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                        .and(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE.eq(SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY)))
                .orderBy(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER.asc())
                .fetchInto(SubAuthenticatedContentConfigure.class));

        List<String> contentIds = contentConfigureList.stream().map(SubAuthenticatedContentConfigure::getContentId).collect(Collectors.toList());
        Map<String, List<SubAuthenticatedStudyOnline>> map = studyOnlineCommonDao.execute(s -> s.selectDistinct(Fields.start()
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID)
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID)
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE)
                        .add(COURSE_INFO.NAME)
                        .add(cacheTable.field("f_begin_time",Long.class))
                        .add(cacheTable.field("f_study_total_time",Integer.class))
                        .add(cacheTable.field("f_finish_status",Integer.class))
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER).end())
                .from(SUB_AUTHENTICATED_STUDY_ONLINE)
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .leftJoin(SUB_AUTHENTICATED_REGISTER).on(SUB_AUTHENTICATED_REGISTER.SUB_AUTHENTICATED_ID.eq(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID)
                        .and(SUB_AUTHENTICATED_REGISTER.MEMBER_ID.eq(memberId)))
                .leftJoin(cacheTable).on(cacheTable.field("f_member_id",String.class).eq(SUB_AUTHENTICATED_REGISTER.MEMBER_ID))
                .and(cacheTable.field("f_course_id",String.class).eq(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID))
                .where(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                        .and(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID.in(contentIds)).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                )
                .orderBy(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER.asc())
                .fetch(r -> {
                    SubAuthenticatedStudyOnline online = new SubAuthenticatedStudyOnline();
                    online.setStudyGroupId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID));
                    online.setBusinessId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID));
                    online.setBusinessName(r.getValue(COURSE_INFO.NAME));
                    online.setBusinessType(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE));
                    online.setOrder(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER));
                    online.setBeginTime(r.getValue(cacheTable.field("f_begin_time",Long.class)));
                    online.setStudyTotalTime(r.getValue(cacheTable.field("f_study_total_time",Integer.class)));
                    online.setStatus(r.getValue(cacheTable.field("f_finish_status",Integer.class)) == null ? SubAuthenticatedProgress.STATUS_0 : r.getValue(COURSE_STUDY_PROGRESS.field("f_finish_status",Integer.class)));
                    online.setSubAuthenticatedId(subAuthenticatedId);
                    return online;
                }).stream().collect(Collectors.groupingBy(SubAuthenticatedStudyOnline::getStudyGroupId)));

        contentConfigureList.forEach(configure -> {
            Integer num = map.get(configure.getContentId()).size();
            Integer finishNum = map.get(configure.getContentId()).stream()
                    .filter(r -> Arrays.asList(SubAuthenticatedProgress.STATUS_2, SubAuthenticatedProgress.STATUS_4).contains(r.getStatus()))
                    .map(SubAuthenticatedStudyOnline::getStatus).collect(Collectors.toList()).size();
            Integer undoNum = map.get(configure.getContentId()).stream()
                    .filter(r -> SubAuthenticatedProgress.STATUS_0.equals(r.getStatus()))
                    .map(SubAuthenticatedStudyOnline::getStatus).collect(Collectors.toList()).size();
            configure.setSubAuthenticatedStudyOnlines(map.get(configure.getContentId()));
            configure.setTotalGroupStatus(getTotalStatus(num, finishNum, undoNum));
        });
        Integer finishNum = contentConfigureList.stream()
                .filter(r -> Arrays.asList(SubAuthenticatedProgress.STATUS_2, SubAuthenticatedProgress.STATUS_4).contains(r.getTotalGroupStatus()))
                .map(SubAuthenticatedContentConfigure::getTotalGroupStatus).collect(Collectors.toList()).size();
        Integer undoNum = contentConfigureList.stream()
                .filter(r -> SubAuthenticatedProgress.STATUS_0.equals(r.getTotalGroupStatus()))
                .map(SubAuthenticatedContentConfigure::getTotalGroupStatus).collect(Collectors.toList()).size();
        Integer status = getTotalStatus(contentConfigureList.size(), finishNum, undoNum);
        result.put(status, contentConfigureList);
        return result;
    }

    @Override
    public List<SubAuthenticatedStudyOnline> findStudyList(String subAuthenticatedId, String studyGroupId, String memberId) {
        return studyOnlineCommonDao.execute(s -> s.selectDistinct(Fields.start()
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID)
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE)
                        .add(COURSE_INFO.NAME)
                        .add(COURSE_INFO.COVER)
                        .add(COURSE_INFO.COVER_PATH)
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER).end())
                .from(SUB_AUTHENTICATED_STUDY_ONLINE)
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                        .and(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID.eq(studyGroupId)).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)))
                .orderBy(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER.asc())
                .fetch(r -> {
                    SubAuthenticatedStudyOnline online = new SubAuthenticatedStudyOnline();
                    Integer order = r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER);
                    String businessName = r.getValue(COURSE_INFO.NAME);
                    String businessCover = r.getValue(COURSE_INFO.COVER);
                    String coverPath = r.getValue(COURSE_INFO.COVER_PATH);
                    Integer businessType = r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE);
                    StringBuilder builder = new StringBuilder();
                    online.setStudyGroupId(studyGroupId);
                    online.setSubAuthenticatedId(subAuthenticatedId);
                    online.setBusinessId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID));
                    online.setBusinessName(builder.append(order).append(".【")
                            .append(SubAuthenticatedStudyOnline.BUSINESS_TYPE_COURSE.equals(businessType) ? BUSINESS_TYPE_COURSE_NAME : BUSINESS_TYPE_SUBJECT_NAME)
                            .append("】").append(businessName).toString());
                    online.setBusinessType(businessType);
                    online.setBusinessCover(businessCover);
                    online.setCoverPath(coverPath);
                    online.setOrder(order);
                    return online;
                }));
    }

    @Override
    public Boolean register(String subAuthenticatedId, String memberId) {
        boolean isRegister = judgeCourseRegistered(subAuthenticatedId, memberId);
        if (!isRegister) {
            SubAuthenticatedRegister register = new SubAuthenticatedRegister();
            register.forInsert();
            register.setSubAuthenticatedId(subAuthenticatedId);
            register.setMemberId(memberId);
            registerCommonDao.insert(register);
        }
        return true;
    }

    @Override
    public List<String> findSubIds(String groupId, String memberId) {
        Optional<AuthenticatedGroup> authenticatedGroup = groupDao.fetchOne(AUTHENTICATED_GROUP.ID.eq(groupId));
        if (!authenticatedGroup.isPresent()) {
            throw new UnprocessableException(ErrorCode.AuthenticatedGroupIsNull);
        }
        if (authenticatedGroup.get().getSubAuthenticatedIds() == null || "".equals(authenticatedGroup.get().getSubAuthenticatedIds())) {
            return new ArrayList<>();
        }
        return Arrays.asList(authenticatedGroup.get().getSubAuthenticatedIds().split(","));
    }

    @Override
    public List<SubAuthenticated> studentSubAuthList(String groupId, String memberId) {
        Optional<AuthenticatedGroup> authenticatedGroup = groupDao.fetchOne(AUTHENTICATED_GROUP.ID.eq(groupId));
        if (!authenticatedGroup.isPresent()) {
            throw new UnprocessableException(ErrorCode.AuthenticatedGroupIsNull);
        }
        if (authenticatedGroup.get().getSubAuthenticatedIds() == null || "".equals(authenticatedGroup.get().getSubAuthenticatedIds())) {
            return new ArrayList<>();
        }
        List<String> subAuthenticatedIds = Arrays.asList(authenticatedGroup.get().getSubAuthenticatedIds().split(","));
        //查询子认证列表
        List<SubAuthenticated> subAuthenticatedList = getSubAuthenticatedList(subAuthenticatedIds, memberId);
        //查询当前学员是否已有认证证书
        Map<String, List<SubAuthenticatedCertificateRecord>> certificatedMap = getCertificatedMap(memberId, subAuthenticatedIds);
        Collections.sort(subAuthenticatedList, (o1, o2) -> {
            int io1 = subAuthenticatedIds.indexOf(o1.getId());
            int io2 = subAuthenticatedIds.indexOf(o2.getId());
            return io1 - io2;
        });
        //查询配置列表
        Map<String, List<SubAuthenticatedContentConfigure>> contentConfigureMap = getContentConfigureMap(memberId, subAuthenticatedIds);
        //查询维度记录
        Map<String, List<SubAuthenticatedMemberDimension>> dimensionMap = getDimensionMap(memberId, subAuthenticatedIds);
        subAuthenticatedList.forEach(subAuthenticated -> {
            subAuthenticated.setMemberDimensions(dimensionMap.get(subAuthenticated.getId()));
            subAuthenticated.setContentConfigureList(contentConfigureMap.get(subAuthenticated.getId()));
            //该学员没有获得证书
            subAuthenticated.setCertificatedExistFlag(ObjectUtils.isEmpty(certificatedMap.get(subAuthenticated.getId())) ?
                    SubAuthenticated.CERTIFICATED_EXIST_FLAG_NO : SubAuthenticated.CERTIFICATED_EXIST_FLAG_YES);
            //该学员是否已发放
            if(Objects.nonNull(certificatedMap) && CollectionUtils.isNotEmpty(certificatedMap.get(subAuthenticated.getId()))){
                subAuthenticated.setSubCertificatedPublish(certificatedMap.get(subAuthenticated.getId()).stream().anyMatch(r->Objects.equals(r.getStatus(), SubAuthenticatedCertificateRecord.STATUS_YES)) ?
                        SubAuthenticated.PUBLISH_YES : SubAuthenticated.PUBLISH_NO);
                //设置子证书id，用于学员手动获取证书
                subAuthenticated.setSubCertificatedRecordId(certificatedMap.get(subAuthenticated.getId()).get(0).getId());
            } else {
                subAuthenticated.setCertificatedExistFlag(SubAuthenticated.CERTIFICATED_EXIST_FLAG_NO);
            }

        });

        return subAuthenticatedList;
    }

    private List<SubAuthenticated> getSubAuthenticatedList(List<String> subAuthenticatedIds, String memberId) {
        List<SubAuthenticated> subAuthenticatedList = subAuthenticatedCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED.ID)
                        .add(SUB_AUTHENTICATED.CERTIFICATE_ID)
                        .add(SUB_AUTHENTICATED.NAME).end())
                .from(SUB_AUTHENTICATED)
                .where(SUB_AUTHENTICATED.ID.in(subAuthenticatedIds))
                .and(SUB_AUTHENTICATED.IS_PUBLISH.eq(SubAuthenticated.PUBLISH_YES))
                .fetch(r -> {
                    SubAuthenticated result = new SubAuthenticated();
                    result.setId(r.getValue(SUB_AUTHENTICATED.ID));
                    result.setName(r.getValue(SUB_AUTHENTICATED.NAME));
                    result.setCertificateId(r.getValue(SUB_AUTHENTICATED.CERTIFICATE_ID));
                    return result;
                }));
        List<String> subIds = subAuthenticatedList.stream().map(SubAuthenticated::getId).collect(Collectors.toList());
        Map<String,List<CertificateRecord>> recordMap = certificateRecordService.findCertificateRecord(Arrays.asList(memberId), subIds, CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED)
                .stream().collect(Collectors.groupingBy(CertificateRecord::getBusinessId));
        subAuthenticatedList.forEach(a -> a.setCertificatedRecordId(ObjectUtils.isEmpty(recordMap.get(a.getId())) ? null : recordMap.get(a.getId()).get(0).getId()));
        return subAuthenticatedList;
    }

    private Map<String, List<SubAuthenticatedCertificateRecord>> getCertificatedMap(String memberId, List<String> subAuthenticatedIds) {
        return subAuthenticatedCertificateRecordCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID,SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS)
                        .add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID).end())
                .from(SUB_AUTHENTICATED_CERTIFICATE_RECORD)
                .where(SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.in(subAuthenticatedIds))
                .and(SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID.eq(memberId))
                .and(SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.isNull().or(SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.eq(SubAuthenticatedCertificateRecord.NOT_DELETE)))
                .fetch(r -> {
                    SubAuthenticatedCertificateRecord result = new SubAuthenticatedCertificateRecord();
                    result.setSubAuthenticatedId(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID));
                    result.setId(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID));
                    result.setStatus(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS));
                    return result;
                }).stream().collect(Collectors.groupingBy(SubAuthenticatedCertificateRecord::getSubAuthenticatedId)));
    }

    private Map<String, List<SubAuthenticatedContentConfigure>> getContentConfigureMap(String memberId, List<String> subAuthenticatedIds) {
        return configureCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_ID)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_NAME)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.STUDY_COVER)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.STUDY_COVER_PATH)
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE.IS_HIDE).end())
                .from(SUB_AUTHENTICATED_CONTENT_CONFIGURE)
                .where(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.in(subAuthenticatedIds))
                .orderBy(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER)
                .fetch(r -> {
                    SubAuthenticatedContentConfigure result = new SubAuthenticatedContentConfigure();
                    String contentId = r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_ID);
                    Integer contentType = r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_TYPE);
                    String authId = r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID);
                    result.setContentId(contentId);
                    result.setContentType(contentType);
                    result.setSubAuthenticatedId(authId);
                    result.setContentName(r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_NAME));
                    result.setStudyCover(r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.STUDY_COVER));
                    result.setStudyCoverPath(generateSecurePathCdn(r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.STUDY_COVER_PATH)));
                    result.setOrder(r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER));
                    result.setIsHide(r.getValue(SUB_AUTHENTICATED_CONTENT_CONFIGURE.IS_HIDE));
                    return result;
                }).stream().collect(Collectors.groupingBy(SubAuthenticatedContentConfigure::getSubAuthenticatedId)));
    }

    private Map<String, List<SubAuthenticatedMemberDimension>> getDimensionMap(String memberId, List<String> subAuthenticatedIds) {
        return memberDimensionCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED_DIMENSION.ID)
                        .add(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID)
                        .add(SUB_AUTHENTICATED_DIMENSION.NAME)
                        .add(SUB_AUTHENTICATED_DIMENSION.CODE)
                        .add(SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_PATH)
                        .add(SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_PATH)
                        .add(SUB_AUTHENTICATED_DIMENSION.ORDER)
                        .add(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED)
                        .end())
                .from(SUB_AUTHENTICATED_DIMENSION)
                .leftJoin(SUB_AUTHENTICATED_MEMBER_DIMENSION).on(SUB_AUTHENTICATED_DIMENSION.ID
                        .eq(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID).and(SUB_AUTHENTICATED_MEMBER_DIMENSION.MEMBER_ID.eq(memberId)))
                .where(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.in(subAuthenticatedIds))
                .orderBy(SUB_AUTHENTICATED_DIMENSION.ORDER.asc())
                .fetch(r -> {
                    SubAuthenticatedMemberDimension result = new SubAuthenticatedMemberDimension();
                    SubAuthenticatedDimension dimension = new SubAuthenticatedDimension();
                    dimension.setName(r.getValue(SUB_AUTHENTICATED_DIMENSION.NAME));
                    dimension.setId(r.getValue(SUB_AUTHENTICATED_DIMENSION.ID));
                    dimension.setCode(r.getValue(SUB_AUTHENTICATED_DIMENSION.CODE));
                    dimension.setUnlockCoverPath(r.getValue(SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_PATH));
                    dimension.setLockCoverPath(r.getValue(SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_PATH));
                    dimension.setOrder(r.getValue(SUB_AUTHENTICATED_DIMENSION.ORDER));
                    result.setSubAuthenticatedDimension(dimension);
                    result.setMemberId(memberId);
                    result.setIsLighted(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED));
                    result.setSubAuthenticatedId(r.getValue(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID));
                    return result;
                })).stream().collect(Collectors.groupingBy(SubAuthenticatedMemberDimension::getSubAuthenticatedId));
    }

    private boolean judgeCourseRegistered(String subAuthenticatedId, String memberId) {
        return registerCommonDao.count(SUB_AUTHENTICATED_REGISTER.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId).and(SUB_AUTHENTICATED_REGISTER.MEMBER_ID.eq(memberId))) > 0;
    }

    /**
     * 处理学习组总学习进度逻辑，全部完成为已完成，全部未开始为未开始，其他为学习中
     *
     * @param num       总数
     * @param finishNum 已完成状态的个数
     * @param undoNum   未开始状态的个数
     * @return
     */
    private Integer getTotalStatus(Integer num, Integer finishNum, Integer undoNum) {

        if (num == 0) return SubAuthenticatedProgress.STATUS_0;

        if (num == finishNum) {
            return SubAuthenticatedProgress.STATUS_2;
        }
        if (undoNum == num) {
            return SubAuthenticatedProgress.STATUS_0;
        }
        return SubAuthenticatedProgress.STATUS_1;
    }

    /**
     * 查询子认证下的业务id，根据受众范围过滤
     *
     * @param subAuthenticatedId 子认证id
     * @return
     */
    private Map<String, List<String>> getBusinessIds(String subAuthenticatedId, List<String> memberIds) {
        return studyOnlineCommonDao.execute(s -> s.select(Fields.start()
                        .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID).add(AUDIENCE_MEMBER.MEMBER_ID).end())
                .from(SUB_AUTHENTICATED_STUDY_ONLINE)
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId).and(AUDIENCE_MEMBER.MEMBER_ID.in(memberIds)))
                .fetch().intoGroups(AUDIENCE_MEMBER.MEMBER_ID, SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID));
    }

    /**
     * 查询每个分页下人员，所有课程/专题的学习记录
     *
     * @param memberIds
     * @return
     */
    private Map<Object, List<CourseStudyProgress>> getMemberCourseList(List<String> memberIds, String subAuthenticatedId, Map<String, List<String>> memBusinessIdsMap) {
        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String id : memberIds) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(),cacheTable);
        }
        List<CourseStudyProgress> progressList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(entry.getKey());
            List<String> ids = entry.getValue();
            List<CourseStudyProgress> list = courseStudyProgressCommonDao.execute(c -> c.select(Fields.start()
                            .add(table.field("f_member_id",String.class))
                            .add(table.field("f_course_id",String.class))
                            .add(table.field("f_finish_status",Integer.class))
                            .add(table.field("f_study_total_time",Integer.class)).end())
                    .from(table)
                    .leftJoin(SUB_AUTHENTICATED_STUDY_ONLINE).on(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID.eq(table.field("f_course_id",String.class)))
                    .where(table.field("f_member_id",String.class).in(ids)
                            .and(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)))
                    .fetch(r -> {
                        CourseStudyProgress map = new CourseStudyProgress();
                        map.setMemberId(r.getValue(table.field("f_member_id",String.class)));
                        map.setCourseId(r.getValue(table.field("f_course_id",String.class)));
                        map.setFinishStatus(r.getValue(table.field("f_finish_status",Integer.class)));
                        map.setStudyTotalTime(r.getValue(table.field("f_study_total_time",Integer.class)));
                        return map;
                    }));
            progressList.addAll(list);
        }

        //<memId,所有学习记录集合>
        Map<Object, List<CourseStudyProgress>> memberProgressMap = progressList.stream().filter(progress ->
                memBusinessIdsMap.get(progress.getMemberId()).contains(progress.getCourseId())).collect(Collectors.groupingBy(CourseStudyProgress::getMemberId));
        return memberProgressMap;
    }

    /**
     * 获取每个人汇总之后学习时长，学习状态
     *
     * @param memberProgressMap
     * @return Map<学员id, 学习时长 / 学习状态>
     */
    private Map<String, Map<String, Integer>> getMemberTotal(Map<Object, List<CourseStudyProgress>> memberProgressMap, Map<String, List<String>> memBusinessIds) {
        Map<String, Map<String, Integer>> memberTotalMap = new HashMap<>();
        for (Map.Entry<String, List<String>> m : memBusinessIds.entrySet()) {
            //已完成的数量
            List<String> finishBusinessIds = memberProgressMap.get(m.getKey()).stream().filter(r ->
                    Arrays.asList(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)
                            .contains(r.getFinishStatus())).map(CourseStudyProgress::getCourseId).collect(Collectors.toList());
            //未完成的数量
            List<String> undoBusinessIds = memberProgressMap.get(m.getKey()).stream().filter(r ->
                    CourseStudyProgress.FINISH_STATUS_STUDY == (r.getFinishStatus())).map(CourseStudyProgress::getCourseId).collect(Collectors.toList());
            Map<String, Integer> totalMap = new HashMap<>();
            Integer totalTime = memberProgressMap.get(m.getKey()).stream().filter(r -> r.getStudyTotalTime() != null)
                    .map(r -> r.getStudyTotalTime()).reduce(0, (a, b) -> a + b);
            totalMap.put("studyTotalTime", totalTime);
            totalMap.put("studyStatus", getTotalStatus(m.getValue().size(), finishBusinessIds.size(), undoBusinessIds.size()));
            memberTotalMap.put(m.getKey(), totalMap);
        }
        return memberTotalMap;
    }

    @Override
    public SubAuthenticated findSubAuthenticated(String id) {
        SubAuthenticated subAuthenticated = subAuthenticatedCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED).add(ORGANIZATION.NAME).end())
                .from(SUB_AUTHENTICATED)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(SUB_AUTHENTICATED.ORGANIZATION_ID))
                .where(SUB_AUTHENTICATED.ID.eq(id))
                .fetchOne(r -> {
                    SubAuthenticated sub = new SubAuthenticated();
                    sub.setCertificateId(r.getValue(SUB_AUTHENTICATED.CERTIFICATE_ID, String.class));
                    sub.setOrganizationId(r.getValue(SUB_AUTHENTICATED.ORGANIZATION_ID, String.class));
                    sub.setName(r.getValue(SUB_AUTHENTICATED.NAME, String.class));
                    sub.setName(r.getValue(SUB_AUTHENTICATED.NAME, String.class));
                    sub.setSplitTrainingFlag(r.getValue(SUB_AUTHENTICATED.SPLIT_TRAINING_FLAG, Integer.class));
                    sub.setOrganizationName(r.getValue(ORGANIZATION.NAME, String.class));
                    sub.setManageIgnoreFlag(r.getValue(SUB_AUTHENTICATED.MANAGE_IGNORE_FLAG));
                    sub.setAuthenticatedLevel(r.getValue(SUB_AUTHENTICATED.AUTHENTICATED_LEVEL));
                    sub.setCheckCertificate(r.getValue(SUB_AUTHENTICATED.CHECK_CERTIFICATE));
                    return sub;
                }));
        List<SubAuthenticatedContentConfigure> contentConfigureList = configureCommonDao.execute(x -> x.select(Fields.start()
                        .add(SUB_AUTHENTICATED_CONTENT_CONFIGURE).end())
                .from(SUB_AUTHENTICATED_CONTENT_CONFIGURE)
                .where(SUB_AUTHENTICATED_CONTENT_CONFIGURE.SUB_AUTHENTICATED_ID.eq(id))
                .orderBy(SUB_AUTHENTICATED_CONTENT_CONFIGURE.ORDER.asc())
                .fetchInto(SubAuthenticatedContentConfigure.class));
        List<String> studyContentIds = new ArrayList<>();
        List<String> dimensionIds = new ArrayList<>();
        contentConfigureList.forEach(configure -> {
            if (SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY.equals(configure.getContentType())) {
                studyContentIds.add(configure.getContentId());
            }
            if (SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(configure.getContentType())) {
                dimensionIds.add(configure.getContentId());
            }
        });
        //查询在线学习组
        if (!ObjectUtils.isEmpty(studyContentIds)) {
            Map<String, List<SubAuthenticatedStudyOnline>> contentSubOnlineMap = studyOnlineCommonDao.execute(x -> x.select(Fields.start().add(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER)
                            .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID)
                            .add(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE)
                            .add(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID)
                            .add(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID)
                            .add(COURSE_INFO.NAME).end())
                    .from(SUB_AUTHENTICATED_STUDY_ONLINE)
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID))
                    .where(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID.in(studyContentIds))
                    .orderBy(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER.asc())
                    .fetch(r -> {
                        SubAuthenticatedStudyOnline online = new SubAuthenticatedStudyOnline();
                        online.setBusinessName(r.getValue(COURSE_INFO.NAME));
                        online.setBusinessType(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_TYPE));
                        online.setBusinessId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.BUSINESS_ID));
                        online.setStudyGroupId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.STUDY_GROUP_ID));
                        online.setSubAuthenticatedId(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.SUB_AUTHENTICATED_ID));
                        online.setOrder(r.getValue(SUB_AUTHENTICATED_STUDY_ONLINE.ORDER));
                        return online;
                    })).stream().collect(groupingBy(SubAuthenticatedStudyOnline::getStudyGroupId));

            contentConfigureList.forEach(config -> {
                List<SubAuthenticatedStudyOnline> onlineList = contentSubOnlineMap.get(config.getContentId());
                if (!ObjectUtils.isEmpty(onlineList)) {
                    config.setSubAuthenticatedStudyOnlines(onlineList);
                }
            });
        }

        //查询认证维度（一个子认证下只会有一个认证维度）
        if (!ObjectUtils.isEmpty(dimensionIds)) {
            List<SubAuthenticatedDimension> dimensionList = dimensionCommonDao.execute(x -> x.select(Fields.start()
                            .add(SUB_AUTHENTICATED_DIMENSION.ORDER)
                            .add(SUB_AUTHENTICATED_DIMENSION.ID)
                            .add(SUB_AUTHENTICATED_DIMENSION.NAME)
                            .add(SUB_AUTHENTICATED_DIMENSION.CODE)
                            .add(SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_ID)
                            .add(SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_PATH)
                            .add(SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_PATH)
                            .add(SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_ID)
                            .add(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID).end())
                    .from(SUB_AUTHENTICATED_DIMENSION)
                    .where(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.in(id))
                    .orderBy(SUB_AUTHENTICATED_DIMENSION.ORDER.asc())
                    .fetchInto(SubAuthenticatedDimension.class));
            contentConfigureList.forEach(config -> {
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(config.getContentType())) {
                    config.setSubAuthenticatedDimensions(dimensionList);
                }
            });
        }
        subAuthenticated.setContentConfigureList(contentConfigureList);
        return subAuthenticated;
    }

    private SubAuthenticated insertSubAuthenticated(Optional<String> name, Optional<String> certificateId,
                                                    Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag) {
        SubAuthenticated subAuthenticated = new SubAuthenticated();
        subAuthenticated.forInsert();
        name.ifPresent(subAuthenticated::setName);
        certificateId.ifPresent(subAuthenticated::setCertificateId);
        organizationId.ifPresent(subAuthenticated::setOrganizationId);
        splitTrainingFlag.ifPresent(subAuthenticated::setSplitTrainingFlag);
        manageIgnoreFlag.ifPresent(subAuthenticated::setManageIgnoreFlag);
        authenticatedLevel.ifPresent(subAuthenticated::setAuthenticatedLevel);
        checkCertificateFlag.ifPresent(subAuthenticated::setCheckCertificate);
        subAuthenticated.setCode(getSequenceNumber());
        subAuthenticated.setIsPublish(SubAuthenticated.PUBLISH_NO);
        return subAuthenticatedCommonDao.insert(subAuthenticated);
    }

    private String getSequenceNumber() {
        String dateFormat = DateUtil.format(new Date(), DateUtil.DATE_YYYYMMDD);
        String fixedCharacters = "rz";
        String text = dateFormat + fixedCharacters;
        Long number = sequenceCache.increment(text, 1L);
        return text + fill(number);
    }

    private String fill(Long number) {
        NumberFormat instance = NumberFormat.getInstance();
        instance.setGroupingUsed(false);
        instance.setMinimumIntegerDigits(4);
        instance.setMaximumIntegerDigits(4);
        return instance.format(number);
    }

    private SubAuthenticated updateSubAuthenticated(SubAuthenticated authenticatedInfo, Optional<String> name, Optional<String> certificateId,
                                                    Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag) {
        name.ifPresent(authenticatedInfo::setName);
        certificateId.ifPresent(authenticatedInfo::setCertificateId);
        organizationId.ifPresent(authenticatedInfo::setOrganizationId);
        splitTrainingFlag.ifPresent(authenticatedInfo::setSplitTrainingFlag);
        manageIgnoreFlag.ifPresent(authenticatedInfo::setManageIgnoreFlag);
        authenticatedLevel.ifPresent(authenticatedInfo::setAuthenticatedLevel);
        authenticatedInfo.setName(name.isPresent() ? name.get() : null);
        authenticatedInfo.setCertificateId(certificateId.isPresent() ? certificateId.get() : null);
        authenticatedInfo.setOrganizationId(organizationId.isPresent() ? organizationId.get() : null);
        authenticatedInfo.setSplitTrainingFlag(splitTrainingFlag.isPresent() ? splitTrainingFlag.get() : null);
        authenticatedInfo.setManageIgnoreFlag(manageIgnoreFlag.isPresent() ? manageIgnoreFlag.get() : null);
        checkCertificateFlag.ifPresent(authenticatedInfo::setCheckCertificate);
        return subAuthenticatedCommonDao.update(authenticatedInfo);
    }

    @Override
    public SubAuthenticatedContentConfigure insertContentConfigure(SubAuthenticatedContentConfigure subAuthenticatedContentConfigure, String subAuthenticatedId) {
        subAuthenticatedContentConfigure.forInsert();
        subAuthenticatedContentConfigure.setSubAuthenticatedId(subAuthenticatedId);
        subAuthenticatedContentConfigure.setContentId(java.util.UUID.randomUUID().toString());
        return configureCommonDao.insert(subAuthenticatedContentConfigure);
    }


    @Override
    public SubAuthenticatedResourceAuditRecord saveMaterials(Integer type, String subAuthenticatedId, String attachmentId, String currentUserId) {

        SubAuthenticatedResourceAuditRecord subAuthenticatedResourceAuditRecord = new SubAuthenticatedResourceAuditRecord();
        Optional<SubAuthenticatedResourceAuditRecord> subAuthenticatedResourceAuditRecordOpt = resourceAuditRecordCommonDao.fetchOne(
                SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.TYPE.eq(type)
                        .and(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId))
                        .and(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.MEMBER_ID.eq(currentUserId))
        );
        if (subAuthenticatedResourceAuditRecordOpt.isPresent()) {
            subAuthenticatedResourceAuditRecord = subAuthenticatedResourceAuditRecordOpt.get();
            subAuthenticatedResourceAuditRecord.setAttachementId(attachmentId);
            resourceAuditRecordCommonDao.update(subAuthenticatedResourceAuditRecord);
            return subAuthenticatedResourceAuditRecord;
        }

        subAuthenticatedResourceAuditRecord.forInsert();
        subAuthenticatedResourceAuditRecord.setAttachementId(attachmentId);
        subAuthenticatedResourceAuditRecord.setSubAuthenticatedId(subAuthenticatedId);
        subAuthenticatedResourceAuditRecord.setType(type);
        subAuthenticatedResourceAuditRecord.setMemberId(currentUserId);
        resourceAuditRecordCommonDao.insert(subAuthenticatedResourceAuditRecord);
        return subAuthenticatedResourceAuditRecord;
    }

    @Override
    public PagedResult<Map<String, String>> findMaterials(Integer type, String subAuthenticatedId, Optional<String> name, Optional<String> code, List<String> orgId, Integer page, Integer pageSize, Optional<Long> beginDate, Optional<Long> endDate) {
        List<Condition> condition = Stream.of(
                name.map(MEMBER.FULL_NAME::contains),
                code.map(x -> MEMBER.IHR_CODE.contains(x).or(MEMBER.NAME.contains(x))),
                beginDate.map(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.CREATE_TIME::greaterOrEqual),
                endDate.map(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.CREATE_TIME::lessOrEqual)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        condition.add(MEMBER.ORGANIZATION_ID.in(orgId));
        condition.add(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.TYPE.eq(type));
        return resourceAuditRecordCommonDao.execute(dsl -> {
                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunction = x -> x.from(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD)
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.MEMBER_ID))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .where(condition)
                            .and(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));
                    SelectSelectStep<Record> countSelect = dsl.select(Fields.start().add(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ID.count()).end());
                    SelectSelectStep<Record> listSelect = dsl.select(Fields.start().add(
                            MEMBER.FULL_NAME, MEMBER.NAME,
                            SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ATTACHEMENT_ID,
                            ORGANIZATION.NAME,
                            SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.CREATE_TIME, SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ID).end());
                    Integer count = stepFunction.apply(countSelect).fetchOne().getValue(0, Integer.class);
                    List<Map<String, String>> list = stepFunction.apply(listSelect).orderBy(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize)
                            .fetch(r -> {
                                Map<String, String> resultMap = new HashMap<>();
                                resultMap.put("fullName", r.getValue(MEMBER.FULL_NAME));
                                resultMap.put("memberName", r.getValue(MEMBER.NAME));// 为啥是name呢;
                                resultMap.put("attachmentId", r.getValue(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ATTACHEMENT_ID));
                                resultMap.put("createTime", String.valueOf(r.getValue(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.CREATE_TIME)));
                                resultMap.put("id", r.getValue(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ID));
                                resultMap.put("orgName", r.getValue(ORGANIZATION.NAME));
                                return resultMap;
                            });


                    return PagedResult.create(count, list);

                }
        );
    }

    @Override
    public List<SubAuthenticatedResourceAuditRecord> findMaterialsByIds(List<String> ids) {
        return resourceAuditRecordCommonDao.get(ids);
    }

    @Override
    public List<SubAuthenticatedResourceAuditRecord> findMaterial(String subAuthenticatedId, String currentUserId) {

        return resourceAuditRecordCommonDao.fetch(
                SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.MEMBER_ID.eq(currentUserId)
                        .and(SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId))
        );
    }

    @Override
    public PagedResult<Map<String, Object>> getDimensions(Optional<Integer> status, Optional<String> name, Optional<String> code, String subAuthenticatedId, List<String> grantOrganizationIds, Integer page, Integer pageSize) {
        List<Condition> condition = Stream.of(
                name.map(MEMBER.FULL_NAME::contains),
                code.map(x -> MEMBER.IHR_CODE.contains(x).or(MEMBER.NAME.contains(x)))
//                status.map(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        condition.add(MEMBER.ORGANIZATION_ID.in(grantOrganizationIds));


        return resourceAuditRecordCommonDao.execute(dsl -> {
            // 先查询子认证下的维度列表
            List<SubAuthenticatedDimension> dimensions = dimensionCommonDao.fetch(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));
            condition.add(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID.in(dimensions.stream().map(SubAuthenticatedDimensionEntity::getId).collect(Collectors.toList())));
            Condition having = DSL.trueCondition();
            if (status.isPresent()) {
                if (status.get().equals(0)) {
                    having = DSL.count(MEMBER.ID).greaterOrEqual(dimensions.size()); // 全部点亮
                } else {
                    having = DSL.count(MEMBER.ID).lt(dimensions.size()); //未全部点亮
                }
            }
            // 查询维度的学员信息
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunction = x -> x.from(SUB_AUTHENTICATED_MEMBER_DIMENSION)
                    .leftJoin(MEMBER).on(MEMBER.ID.eq(SUB_AUTHENTICATED_MEMBER_DIMENSION.MEMBER_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                    .where(condition);
            SelectSelectStep<Record> groupSelect = dsl.select(Fields.start().add(MEMBER.ID).end());
            SelectSelectStep<Record> countSelect = dsl.select(Fields.start().add(MEMBER.ID.countDistinct()).end());
            SelectSelectStep<Record> listSelect = dsl.select(Fields.start().add(MEMBER.FULL_NAME, MEMBER.NAME, MEMBER.ID, SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED, SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID, ORGANIZATION.NAME).end());
            int count = dsl.fetchCount(stepFunction.apply(countSelect).groupBy(MEMBER.ID).having(having).asTable("temp"));

            if (0 == count) return PagedResult.create(0, new ArrayList<>());

            List<String> memberIds = stepFunction.apply(groupSelect).groupBy(MEMBER.ID).having(having).limit((page - 1) * pageSize, pageSize).fetch(record -> record.getValue(MEMBER.ID));

            Map<String, Map<String, Object>> tempMap = new HashMap<>();
            stepFunction.apply(listSelect).and(SUB_AUTHENTICATED_MEMBER_DIMENSION.MEMBER_ID.in(memberIds))
                    .fetch(r -> {
                        Map<String, Object> map = Optional.ofNullable(tempMap.get(r.getValue(MEMBER.ID))).orElse(new HashMap<>());
                        if (map.isEmpty()) {
                            map.put("fullName", EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
                            map.put("memberName", DesensitizationUtil.desensitizeEmployeeId(r.getValue(MEMBER.NAME)));// 为啥是name呢;
                            map.put("memberId", r.getValue(MEMBER.ID));// 为啥是name呢;
                            map.put("orgName", r.getValue(ORGANIZATION.NAME));

                            Map<String, Object> dimensionMap = new HashMap<>();
                            dimensionMap.put(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID), String.valueOf(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED)));
                            map.put("dimension", dimensionMap);
                        } else {
                            Map<String, Object> dimensionMap = (Map) map.get("dimension");
                            dimensionMap.put(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID), String.valueOf(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED)));
                            map.put("dimension", dimensionMap);
                        }

                        return tempMap.put(r.getValue(MEMBER.ID), map);
                    });

            List<Map<String, Object>> list = new ArrayList<>(tempMap.values());
            return PagedResult.create(count, list);
        });

    }

    @Override
    public List<SubAuthenticatedDimension> getDimensionsBySubId(String id) {
        List<SubAuthenticatedDimension> list = dimensionCommonDao.fetch(SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(id));
        list.sort(Comparator.comparingInt(SubAuthenticatedDimension::getOrder));
        return list;
    }


    @Override
    public List<SubAuthenticatedMemberDimension> upsetDimensionById(List<Map> list, String memberId, String subAuthenticatedId) {
        List<String> nonNullIds = new ArrayList<>();
        List<SubAuthenticatedMemberDimension> updateList = new ArrayList<>();
        List<SubAuthenticatedMemberDimension> insertList = new ArrayList<>();

        List<Map> nonNull = list.stream()
                .filter(map -> Objects.nonNull(map.get("id")))
                .peek(map -> nonNullIds.add(String.valueOf(map.get("id"))))
                .collect(Collectors.toList());
        List<Map> isNull = list.stream().filter(map -> Objects.isNull(map.get("id"))).collect(Collectors.toList());

        Map<String, SubAuthenticatedMemberDimension> dimensionMap = memberDimensionCommonDao.get(nonNullIds)
                .stream().collect(Collectors.toMap(SubAuthenticatedMemberDimensionEntity::getId, v -> v));

        for (Map map : nonNull) {
            Optional.ofNullable(dimensionMap.get(String.valueOf(map.get("id"))))
                    .ifPresent(subAuthenticatedMemberDimension -> {
                        subAuthenticatedMemberDimension.setIsLighted(Integer.valueOf(String.valueOf(map.get("isLighted"))));
                        updateList.add(subAuthenticatedMemberDimension);
                    });
        }
        for (Map map : isNull) {
            SubAuthenticatedMemberDimension insert = new SubAuthenticatedMemberDimension();
            insert.forInsert();
            insert.setSubAuthenticatedId(subAuthenticatedId);
            insert.setDimensionId(String.valueOf(map.get("dimensionId")));
            insert.setMemberId(memberId);
            insert.setIsLighted(Integer.valueOf(String.valueOf(map.get("isLighted"))));
            insertList.add(insert);
        }

        if (!updateList.isEmpty()) {
            memberDimensionCommonDao.update(updateList);
        }
        if (!insertList.isEmpty()) {
            memberDimensionCommonDao.insert(insertList);
        }
        updateList.addAll(insertList);
        return updateList;
    }

    @Override
    public Map<String, Member> getMemberCodeNameMap(List<String> memberCodes) {
        return memberDimensionCommonDao.execute(dsl -> dsl
                .select(MEMBER.NAME, MEMBER.FULL_NAME, MEMBER.ID, MEMBER.ORGANIZATION_ID)
                .from(MEMBER)
                .where(MEMBER.NAME.in(memberCodes))
                .fetchMap(MEMBER.NAME, r -> {
                    Member member = new Member();
                    member.setId(r.getValue(MEMBER.ID));
                    member.setFullName(r.get(MEMBER.FULL_NAME));
                    member.setOrganizationId(r.get(MEMBER.ORGANIZATION_ID));
                    return member;
                }));
    }

    @Override
    @Transactional
    public void upsetSubAuthenticatedMemberDimension(List<SubAuthenticatedMemberDimension> collect) {
        List<SubAuthenticatedMemberDimension> updateList = new ArrayList<>();
        List<SubAuthenticatedMemberDimension> insertList = new ArrayList<>();

        List<String> memberIds = collect.stream().map(SubAuthenticatedMemberDimensionEntity::getMemberId).collect(Collectors.toList());
        List<String> dimensionIds = collect.stream().map(SubAuthenticatedMemberDimensionEntity::getDimensionId).collect(Collectors.toList());

        Map<String, SubAuthenticatedMemberDimension> exist = memberDimensionCommonDao.fetch(
                SUB_AUTHENTICATED_MEMBER_DIMENSION.MEMBER_ID.in(memberIds).and(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID.in(dimensionIds))
        ).stream().collect(Collectors.toMap(k -> k.getMemberId().concat(k.getDimensionId()), v -> v, (v1, v2) -> v1));

        for (SubAuthenticatedMemberDimension subAuthenticatedMemberDimension : collect) {
            String key = subAuthenticatedMemberDimension.getMemberId().concat(subAuthenticatedMemberDimension.getDimensionId());
            SubAuthenticatedMemberDimension value = exist.get(key);
            if (Objects.nonNull(value)) {
                value.setIsLighted(subAuthenticatedMemberDimension.getIsLighted());
                updateList.add(value);
            } else {
                insertList.add(subAuthenticatedMemberDimension);
            }
        }
        if (!updateList.isEmpty()) {
            memberDimensionCommonDao.update(updateList);
        }
        if (!insertList.isEmpty()) {
            memberDimensionCommonDao.insert(insertList);
        }
    }

    @Override
    public List<SubAuthenticatedMemberDimension> queryMemberDimension(String memberId, String subAuthenticatedId) {
        return dimensionCommonDao.execute(dsl -> dsl.select(
                        SUB_AUTHENTICATED_DIMENSION.NAME,
                        SUB_AUTHENTICATED_DIMENSION.CODE,
                        SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_ID,
                        SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_ID,
                        SUB_AUTHENTICATED_DIMENSION.ORDER,
                        SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED,
                        SUB_AUTHENTICATED_MEMBER_DIMENSION.ID
                )
                .from(SUB_AUTHENTICATED_DIMENSION)
                .leftJoin(SUB_AUTHENTICATED_MEMBER_DIMENSION)
                .on(SUB_AUTHENTICATED_MEMBER_DIMENSION.DIMENSION_ID.eq(SUB_AUTHENTICATED_DIMENSION.ID))
                .where(
                        SUB_AUTHENTICATED_DIMENSION.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                                .and(SUB_AUTHENTICATED_MEMBER_DIMENSION.MEMBER_ID.eq(memberId))
                )
                .fetch(r -> {

                    SubAuthenticatedMemberDimension subAuthenticatedMemberDimension = new SubAuthenticatedMemberDimension();
                    SubAuthenticatedDimension subAuthenticatedDimension = new SubAuthenticatedDimension();
                    subAuthenticatedMemberDimension.setId(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.ID));
                    subAuthenticatedMemberDimension.setIsLighted(r.getValue(SUB_AUTHENTICATED_MEMBER_DIMENSION.IS_LIGHTED));
                    subAuthenticatedDimension.setName(r.getValue(SUB_AUTHENTICATED_DIMENSION.NAME));
                    subAuthenticatedDimension.setCode(r.getValue(SUB_AUTHENTICATED_DIMENSION.CODE));
                    subAuthenticatedDimension.setLockCoverId(r.getValue(SUB_AUTHENTICATED_DIMENSION.LOCK_COVER_ID));
                    subAuthenticatedDimension.setUnlockCoverId(r.getValue(SUB_AUTHENTICATED_DIMENSION.UNLOCK_COVER_ID));
                    subAuthenticatedDimension.setOrder(r.getValue(SUB_AUTHENTICATED_DIMENSION.ORDER));

                    subAuthenticatedMemberDimension.setSubAuthenticatedDimension(subAuthenticatedDimension);
                    return subAuthenticatedMemberDimension;
                }));
    }

    @Override
    public PagedResult<Map<String, Object>> getCertificate(Optional<String> name, Optional<String> code, String subAuthenticatedId, List<String> grantOrganizationIds, Integer page, Integer pageSize) {
        List<Condition> condition = Stream.of(
                name.map(MEMBER.FULL_NAME::contains),
                code.map(x -> MEMBER.IHR_CODE.contains(x).or(MEMBER.NAME.contains(x)))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        condition.add(MEMBER.ORGANIZATION_ID.in(grantOrganizationIds));
        condition.add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.eq(SubAuthenticatedCertificateRecord.NOT_DELETE));
        condition.add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));
        com.zxy.product.course.jooq.tables.Member operator = MEMBER.as("operator");

        return subAuthenticatedCertificateRecordCommonDao.execute(dsl -> {
                    SelectSelectStep<Record> countSelect = dsl.select(Fields.start().add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID.count()).end());
                    SelectSelectStep<Record> listSelect = dsl.select(Fields.start().add(MEMBER.FULL_NAME, MEMBER.NAME, ORGANIZATION.NAME, SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME, SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID, operator.FULL_NAME,
                            SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME, SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS).end());
                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunction = x -> x.from(SUB_AUTHENTICATED_CERTIFICATE_RECORD)
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .leftJoin(operator).on(operator.ID.eq(SUB_AUTHENTICATED_CERTIFICATE_RECORD.OPERATOR_ID))
                            .where(condition);
                    Integer count = stepFunction.apply(countSelect).fetchOne().getValue(0, Integer.class);
                    List<Map<String, Object>> list = stepFunction.apply(listSelect).orderBy(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        Map<String, Object> result = new HashMap<>(9);
                        result.put("name", r.getValue(MEMBER.FULL_NAME));
                        result.put("code", r.getValue(MEMBER.NAME));
                        result.put("orgName", r.getValue(ORGANIZATION.NAME));
                        Optional.ofNullable(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME)).ifPresent(x -> result.put("issueCertificateTime", x.toString()));
                        result.put("id", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID));
                        result.put("operatorName", r.getValue(operator.FULL_NAME));
                        result.put("importTime", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME));
                        result.put("status", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS));
                        return result;
                    });

                    return PagedResult.create(count, list);
                }
        );
    }

    @Override
    public void deleteCertificate(String id, String reasonForDeletion, Optional<String> fileId, String currentUserId, Boolean logicDelete) {
        SubAuthenticatedCertificateRecord subAuthenticatedCertificateRecord = subAuthenticatedCertificateRecordCommonDao.get(id);
        if (logicDelete) {
            subAuthenticatedCertificateRecord.setCancelOperatorId(currentUserId);
            subAuthenticatedCertificateRecord.setCancelTime(Instant.now().toEpochMilli());
            fileId.ifPresent(subAuthenticatedCertificateRecord::setAttachementId);
            subAuthenticatedCertificateRecord.setReason(reasonForDeletion);
            subAuthenticatedCertificateRecord.setDeleteFlag(SubAuthenticatedCertificateRecord.IS_DELETE);
            subAuthenticatedCertificateRecordCommonDao.update(subAuthenticatedCertificateRecord);
        } else {
            subAuthenticatedCertificateRecordCommonDao.delete(id);
        }
        //  delete certificate record
        Optional.of(certificateRecordService.findCertificateRecord(
                        Lists.newArrayList(subAuthenticatedCertificateRecord.getMemberId()),
                        Lists.newArrayList(subAuthenticatedCertificateRecord.getSubAuthenticatedId()),
                        CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED
                ).stream().map(CertificateRecordEntity::getId).collect(Collectors.toList()))
                .ifPresent(ids -> certificateRecordService.deleteCertificateRecord(ids));

    }

    @Override
    public void updateCertificate(String subAuthenticatedId) {
        subAuthenticatedCertificateRecordCommonDao.execute(e->e.update(SUB_AUTHENTICATED_CERTIFICATE_RECORD)
                .set(SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS, SubAuthenticatedCertificateRecord.STATUS_YES)
                .set(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME, System.currentTimeMillis())
                        .where(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID.in((subAuthenticatedId))).execute()
        );
    }

    @Override
    public void deleteCertificate(List<String> memberIds, String subAuthenticatedId) {
        subAuthenticatedCertificateRecordCommonDao.delete(
                SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                .and(SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID.in((memberIds)))
        );
        Optional.of(certificateRecordService.findCertificateRecord(
                        memberIds,
                        Lists.newArrayList(subAuthenticatedId),
                        CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED
                ).stream().map(CertificateRecordEntity::getId).collect(Collectors.toList()))
                .ifPresent(ids -> certificateRecordService.deleteCertificateRecord(ids));
    }

    @Override
    public PagedResult<Map<String, String>> getDeleteCertificate(Optional<String> name, Optional<String> code,
                                                                 String subAuthenticatedId, List<String> grantOrganizationIds,
                                                                 Optional<Long> issuanceOfCertificatesBeginDate, Optional<Long> issuanceOfCertificatesEndDate,
                                                                 Optional<Long> deleteBeginDate, Optional<Long> deleteEndDate, Integer page, Integer pageSize) {
        List<Condition> condition = Stream.of(
                name.map(MEMBER.FULL_NAME::contains),
                code.map(x -> MEMBER.IHR_CODE.contains(x).or(MEMBER.NAME.contains(x))),
                issuanceOfCertificatesBeginDate.map(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME::greaterOrEqual),
                issuanceOfCertificatesEndDate.map(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME::lessOrEqual),
                deleteBeginDate.map(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME::greaterOrEqual),
                deleteEndDate.map(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME::lessOrEqual)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        condition.add(MEMBER.ORGANIZATION_ID.in(grantOrganizationIds));
        condition.add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.eq(SubAuthenticatedCertificateRecord.IS_DELETE));
        condition.add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId));


        com.zxy.product.course.jooq.tables.Member operator = MEMBER.as("operator");

        return subAuthenticatedCertificateRecordCommonDao.execute(dsl -> {
                    SelectSelectStep<Record> countId = dsl.select(Fields.start().add(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID.count()).end());
                    SelectSelectStep<Record> select = dsl
                            .select(Fields.start().add(MEMBER.FULL_NAME, MEMBER.NAME,
                                    ORGANIZATION.NAME, SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME,
                                    SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME,
                                    SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON, operator.FULL_NAME,
                                    SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID,
                                    SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID
                            ).end());

                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = x -> x.from(SUB_AUTHENTICATED_CERTIFICATE_RECORD)
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .leftJoin(operator).on(operator.ID.eq(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_OPERATOR_ID))
                            .where(condition);

                    Integer count = stepFunc.apply(countId).fetchOne().getValue(0, Integer.class);
                    List<Map<String, String>> list = stepFunc.apply(select).orderBy(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        Map<String, String> result = new HashMap<>(15);
                        result.put("name", r.getValue(MEMBER.FULL_NAME));
                        result.put("code", r.getValue(MEMBER.NAME));
                        result.put("orgName", r.getValue(ORGANIZATION.NAME));
                        Optional.ofNullable(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME)).ifPresent(x -> result.put("issueCertificateTime", x.toString()));
                        Optional.ofNullable(r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME)).ifPresent(x -> result.put("deleteCertificateTime", x.toString()));
                        result.put("operatorName", r.getValue(operator.FULL_NAME));
                        result.put("reason", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON));
                        result.put("attachmentId", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID));
                        result.put("id", r.getValue(SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID));
                        return result;
                    });
                    return PagedResult.create(count, list);
                }
        );


    }


    @Override
    public void saveSubAuthenticatedCertificateRecord(List<SubAuthenticatedCertificateRecord> subAuthenticatedCertificateRecords) throws Exception {
        subAuthenticatedCertificateRecords = Optional.ofNullable(subAuthenticatedCertificateRecords).orElse(new ArrayList<>())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (subAuthenticatedCertificateRecords.size() > 0) {
            // 存在记录的
            String subAuthenticatedId = subAuthenticatedCertificateRecords.get(0).getSubAuthenticatedId();
            List<String> memberIds = subAuthenticatedCertificateRecords.stream().map(SubAuthenticatedCertificateRecordEntity::getMemberId).collect(Collectors.toList());
            List<String> exist = Optional.ofNullable(subAuthenticatedCertificateRecordCommonDao.fetch(
                            SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId)
                                    .and(SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID.in(memberIds)
                                            .and(SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.eq(SubAuthenticatedCertificateRecord.NOT_DELETE))
                                    )
                    )).orElse(new ArrayList<>())
                    .stream().map(SubAuthenticatedCertificateRecord::getId).collect(Collectors.toList());

            subAuthenticatedCertificateRecordCommonDao.delete(exist);
            subAuthenticatedCertificateRecordCommonDao.insert(subAuthenticatedCertificateRecords);
            // genera certificate record
            List<CertificateRecord> existCertificateRecords = certificateRecordService.findCertificateRecord(memberIds, Lists.newArrayList(subAuthenticatedId), CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED);
            List<String> existCertificationIds = existCertificateRecords.stream().map(CertificateRecordEntity::getId).collect(Collectors.toList());
            certificateRecordService.deleteCertificateRecord(existCertificationIds);
            certificateRecordService.insertRecords(initCertifications(subAuthenticatedCertificateRecords));
        }
    }

    @Override
    public Integer getCheckCertificateById(String subAuthenticatedId) {
        return subAuthenticatedCommonDao.execute(r->
                r.select(SUB_AUTHENTICATED.CHECK_CERTIFICATE)
                        .from(SUB_AUTHENTICATED)
                        .where(SUB_AUTHENTICATED.ID.eq(subAuthenticatedId))
                        .fetchOne(o-> o.getValue(SUB_AUTHENTICATED.CHECK_CERTIFICATE)));
    }

    public List<CertificateRecord> initCertifications(List<SubAuthenticatedCertificateRecord> subAuthenticatedCertificateRecords) throws Exception {
        List<CertificateRecord> list = new ArrayList<>();
        for (SubAuthenticatedCertificateRecord subAuthenticatedCertificateRecord : subAuthenticatedCertificateRecords) {
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setAccessType(CertificateRecord.ACCESS_TYPE_AUTO);
            record.setBusinessId(subAuthenticatedCertificateRecord.getSubAuthenticatedId());
            record.setMemberId(subAuthenticatedCertificateRecord.getMemberId());
            record.setFinishTime(System.currentTimeMillis());
            record.setType(CertificateRecord.ISSUE_CERTIFICATE_TYPE_QUALIFIED);
            record.setIssueTime(StringUtils.stringDate(System.currentTimeMillis()));
            record.setBusinessType(CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED);
            String num = normalCodeGenerator.getCodeForNormalSubject(CertificateRecord.BUSINESS_TYPE_SUB_AUTHENTICATED, BusinessCertificate.CERTIFICATE_NUMBER_PREFIX_SUBJECT, com.zxy.product.course.util.StringUtils.stampToDate(System.currentTimeMillis(), 0));
            record.setCertificateNumber(num);
            list.add(record);
        }
        return list;
    }


    @Override
    public void addTmp(List<SubAuthenticatedTmp> tmpDtos){
        subAuthenticatedTmpCommonDao.insert(tmpDtos);
    }

    @Override
    public PagedResult<SubAuthenticatedTmp> findTmp(Integer page, Integer pageSize, String fileId, String subAuthenticatedId, String memberId){
        return subAuthenticatedTmpCommonDao.fetchPage(page, pageSize, e->e.select(SUB_AUTHENTICATED_TMP.ID,SUB_AUTHENTICATED_TMP.NAME,SUB_AUTHENTICATED_TMP.CODE)
                .from(SUB_AUTHENTICATED_TMP)
                 .where(SUB_AUTHENTICATED_TMP.FILE_ID.eq(fileId), SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId),
                         SUB_AUTHENTICATED_TMP.DELETE_FLAG.eq(SubAuthenticatedTmp.DELETE_FLAG_NO), SUB_AUTHENTICATED_TMP.CREATOR.eq(memberId))
                ,x->x.into(SubAuthenticatedTmp.class));
    }

    @Override
    public void deleteTmp(String fileId, String subAuthenticatedId, String memberId){
        subAuthenticatedTmpCommonDao.delete(SUB_AUTHENTICATED_TMP.FILE_ID.eq(fileId), SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID.eq(subAuthenticatedId),
                SUB_AUTHENTICATED_TMP.DELETE_FLAG.eq(SubAuthenticatedTmp.DELETE_FLAG_NO), SUB_AUTHENTICATED_TMP.CREATOR.eq(memberId));
    }
}
