package com.zxy.product.examstu.web.controller;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.examstu.api.ExamRegisterSrvice;
import com.zxy.product.examstu.api.PaperClassService;
import com.zxy.product.examstu.api.PaperInstanceService;
import com.zxy.product.examstu.api.QuestionCopyService;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.exam.entity.*;
import com.zxy.product.human.api.MemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 考试注册表controller
 */
@Controller
@RequestMapping(value = "/exam-register")
public class ExamRegisterController {

    private ExamRegisterSrvice examRegisterSrvice;
    private PaperInstanceService paperInstanceService;
    private PaperClassService paperClassService;
    private QuestionCopyService questionCopyService;
    private MemberService memberService;

    private Cache cache;
    private Cache questionDepotCache;
    private Cache questionsTopErrorCache;
    private Cache answerRecordCache;
    private Cache personalExamIdCache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("newenergy","scoreDetail");
        this.personalExamIdCache = cacheService.create("newenergy","examIds");
        this.questionDepotCache = cacheService.create("newenergy","questionDepot");
        this.questionsTopErrorCache = cacheService.create("newenergy","questionsTopError");
        this.answerRecordCache = cacheService.create("newenergy","answerRecord");
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setPaperClassService(PaperClassService paperClassService) {
        this.paperClassService = paperClassService;
    }

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    public static final String PERSONAL_EXAM_IDS="personalExamIds";


    @RequestMapping(value = "/certification-list" , method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "status", type = Integer.class)
    @JSON("id,status,passStatus")
    @JSON("exam.(id,name)")
    @JSON("exam.profession.(id,name)")
    @JSON("exam.subProfession.(id,name)")
    @JSON("exam.level.(id,levelName)")
    @JSON("exam.equipmentType.(id,name)")
    public List<ExamRegist> getExamCertificationByMemberId(RequestContext requestContext){
        Integer examRegion = memberService.findExamRegion(requestContext.getString("memberId"));
        return this.examRegisterSrvice.getExamCertificationByMemberId(
                examRegion,
                requestContext.getString("memberId"),
                requestContext.getOptionalInteger("status")
                );
    }

    @RequestMapping(value = "/cloud-certification-list" , method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "status", type = Integer.class)
    @JSON("id,status,passStatus")
    @JSON("exam.(name)")
    @JSON("exam.cloudProfession.(id,name)")
    @JSON("exam.cloudLevel.(id,levelName)")
    public List<ExamRegist> getCloudExamCertificationByMemberId(RequestContext requestContext){
        Integer examRegion = memberService.findExamRegion(requestContext.getString("memberId"));
        return this.examRegisterSrvice.getCloudExamCertificationByMemberId(
                examRegion,
                requestContext.getString("memberId"),
                requestContext.getOptionalInteger("status")
                );
    }

    @RequestMapping(value = "/grid-certification-list" , method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "status", type = Integer.class)
    @JSON("id,status,passStatus")
    @JSON("exam.(name)")
    @JSON("exam.gridLevel.(id,levelName)")
    public List<ExamRegist> getGridExamCertificationByMemberId(RequestContext requestContext){
        Integer examRegion = memberService.findExamRegion(requestContext.getString("memberId"));
        return this.examRegisterSrvice.getGridExamCertificationByMemberId(
                examRegion,
                requestContext.getString("memberId"),
                requestContext.getOptionalInteger("status")
        );
    }

    @RequestMapping(value = "/list" , method = RequestMethod.GET)
    @Param(name = "examIds", type = String.class)
    @JSON("topScore,examId,createTime,status")
    @JSON("exam.(showAnswerRule,showScoreTime,name,startTime)")
    public List<ExamRegist> getExamRegistList(RequestContext requestContext, Subject<Member> subject){
        Integer examRegion = subject.examRegion();
        return examRegisterSrvice.getExamRegistList(
                examRegion,
                subject.getCurrentUserId(),
                Arrays.asList(requestContext.getString("examIds").split(","))
                );
    }


    /**
     * 新动能，查询所有专题的考试成绩
     */
    @RequestMapping(value = "/newenergy-score-list" , method = RequestMethod.GET)
    @Param(name = "examIds", type = String.class)
    @JSON("examId,examName,topScore")
    public List<ExamRegist> getNewenergyScoreList(RequestContext requestContext, Subject<Member> subject){
        Integer examRegion = subject.examRegion();
        return examRegisterSrvice.getNewenergyScoreList(
                examRegion,
                subject.getCurrentUserId(),
                Arrays.asList(requestContext.getString("examIds").split(","))
                );
    }

    /**
     * 新动能，查询某个专题的考试成绩
     */
    @RequestMapping(value = "/newenergy-score" , method = RequestMethod.GET)
    @Param(name = "examId", type = String.class, required = true)
    @JSON("examId,topScore")
    public ExamRegist getNewenergyScore(RequestContext requestContext, Subject<Member> subject){
        Integer examRegion = subject.examRegion();
        return examRegisterSrvice.getNewenergyScore(
                examRegion,
                subject.getCurrentUserId(),
                requestContext.getString("examId")
                );
    }

    /**
     * 新动能，考试详情
     */
    @RequestMapping(value = "/newenergy-score-detail", method = RequestMethod.GET)
    @Param(name = "examId", type = String.class, required = true)
    @JSON("id,examId")
    @JSON("paper.(questionNum,totalScore,isSubjective)")
    @JSON("paper.questions.(id,type,content,parentId,score,errorRate)")
    @JSON("paper.questions.questionAttrCopys.(id,name,value,type)")
    @JSON("paper.questions.answerRecord.(id,answer,score,isRight)")
    @JSON("paper.questions.subs.(id,type,content,parentId,score,errorRate)")
    @JSON("paper.questions.subs.answerRecord.(id,answer,score,isRight)")
    @JSON("paper.questions.subs.questionAttrCopys.(id,name,value,type,createTime)")
    @JSON("examRecord.(id,memberId,startTime,submitTime,score,status,paperInstanceId,orderContent)")
    @JSON("exam.(duration)")
//    @Permitted()
    public ExamRegist getWithScoreDetail(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        // 查询当前考生的考试信息
        ExamRegist examRegist = examRegisterSrvice.getExamRegisterByExamIdAndMemberId(
                examRegion,
                subject.getCurrentUserId(),
                requestContext.getString("examId"));

        String examId = requestContext.getString("examId");

        if (examRegist == null)
            throw new UnprocessableException(ErrorCode.ErrorViewScoreDetail);

        // 所有的专区考试id
        List<String> examList = personalExamIdCache.get(PERSONAL_EXAM_IDS, () -> {
            return examRegisterSrvice.getPersonalExamIds();
        }, Exam.CACHE_TIME);

        if (examList != null && examList.size() > 0 && !examList.contains(requestContext.getString("examId"))) {
            throw new UnprocessableException(ErrorCode.ExamRecordScoreDetailError);
        }

        // 试卷作答页面加缓存
        return cache.get(examRegist.getExamRecord().getId(), () -> {
            PaperInstance paperInstance = paperInstanceService.getSimpleData(examRegion, examRegist.getExamRecord().getPaperInstanceId());
            if (paperInstance != null) {
                PaperClass paperClass = paperClassService.getSimplePaperClassInfo(examRegion, paperInstance.getPaperClassId());
                List<QuestionCopy> questionCopy = questionCopyService.findQuestionsByPaperAndExamRecord(examRegion, paperInstance.getId(), examRegist.getExamRecord().getId(), examId);
                List<QuestionCopy> questionListSort = questionCopy.stream().sorted(Comparator.comparing(QuestionCopy::getType).thenComparing(QuestionCopy::getSequence)).collect(Collectors.toList());
                paperInstance.setQuestions(questionListSort);
                if (paperClass != null) {
                    paperInstance.setTotalScore(paperClass.getTotalScore());
                    paperInstance.setQuestionNum(paperClass.getQuestionNum());
                }
            }
            examRegist.setPaper(paperInstance);
            return examRegist;
        }, Exam.CACHE_TIME);
    }


    /**
     * 新动能，易错题top10
     */
    @RequestMapping(value = "/newenergy-top-error", method = RequestMethod.GET)
    @Param(name = "examId", type = String.class, required = true)
    @JSON("answerReocrd.(id,answer,score,isRight)")
    @JSON("id,errorRate,type,content,difficulty,score")
    @JSON("questionAttrs.(id,name,value,type)")
//    @Permitted()
    public List<Question> getWithTopError(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        String examId = requestContext.getString("examId");

        // 我的考试记录
        String examRecordId = examRegisterSrvice.getExamRecordIdByExamIdAndMemberId(
                examRegion,
                subject.getCurrentUserId(),
                requestContext.getString("examId"));

        // 新动能的试题目录
        List<String> questionDepotList = questionDepotCache.get(examId, () -> {
            return examRegisterSrvice.getQuestionDepotListByExamId(examId);
        }, Exam.CACHE_TIME);

        // 查询易错率top10
        List<Question> questionsTopErrorList= questionsTopErrorCache.get(examId, () -> {
            return questionCopyService.findQuestionsTopError(examRegion, questionDepotList);
        }, Exam.CACHE_TIME_ONE_DAY);

        // 我的作答记录
        List<Question> answerRecordList= answerRecordCache.get(examRecordId, () -> {
            return questionCopyService.findAnswerRecord(examRegion, examRecordId, examId);
        }, Exam.CACHE_TIME);

        // top10和我的作答记录
        Map<String, Question> questionsTopErrorMap = questionsTopErrorList.stream().collect(Collectors.toMap(Question::getId, t -> t, (o1, o2) -> o2));
        Map<String, Question> answerRecordMap = answerRecordList.stream().collect(Collectors.toMap(Question::getId, t -> t, (o1, o2) -> o2));

        for(String key : questionsTopErrorMap.keySet()){
            if (answerRecordMap.get(key) != null)
                questionsTopErrorMap.get(key).setAnswerReocrd(answerRecordMap.get(key).getAnswerReocrd());
        }

        List<Question> questionList = questionsTopErrorMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());

        List<Question> questionListSort = questionList.stream().sorted(Comparator.comparing(Question::getErrorRate).reversed()).collect(Collectors.toList());

        return questionListSort;
    }







    private List<String> getQuestionDepotListByExamId(String examId) {
        List<String> list = new ArrayList<String>();
        switch (examId) {
        case ExamRegist.NEWENERGY_EXAM_5G:
            questionDepotListFornewenergyExam5G(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_AQ:
            questionDepotListFornewenergyExamAQ(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_CDN:
            questionDepotListFornewenergyExamCDN(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_DSJ:
            questionDepotListFornewenergyExamDSJ(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_IT:
            questionDepotListFornewenergyExamIT(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_RJ:
            questionDepotListFornewenergyExamRJ(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_SDN:
            questionDepotListFornewenergyExamSDN(list);
            break;
        case ExamRegist.NEWENERGY_EXAM_WLW:
            questionDepotListFornewenergyExamWLW(list);
            break;
        default:
            questionDepotListFornewenergyExamYJS(list);
            break;
        }
        return list;
    }

    private void questionDepotListFornewenergyExamYJS(List<String> list) {
        list.add("dfc3346a-a6a5-4fc3-9d84-bebaea246c00");
        list.add("013f91f6-e239-4c1b-a726-5523e3ab04ce");
        list.add("f3f4918b-1f08-4f56-a60d-9d8d5ce6f46b");
        list.add("328fb885-65b4-4c6f-bd0e-beb597b44611");
        list.add("c4e86019-a4db-4e21-84a4-3235f6616acf");
        list.add("8e412622-3cf2-4366-84e2-82b2d3ef6843");
        list.add("6b979616-6599-433b-9096-1ae1d401fac8");
        list.add("291207a3-ce04-4a6c-9bf7-1afff1167fb3");
    }

    private void questionDepotListFornewenergyExamWLW(List<String> list) {
        list.add("4b0095a0-26f6-4b78-851c-c1f0a71d9e86");
        list.add("a6613ced-e2da-4ebb-9bd4-ae399879981c");
        list.add("1ab539b4-082d-4fad-9c50-fed1126eb35e");
        list.add("59fd9f30-912e-4352-a936-8ca89d457f3e");
    }

    private void questionDepotListFornewenergyExamSDN(List<String> list) {
        list.add("215a8671-85f0-4874-9792-fe60b3a96c6c");
        list.add("5ca0aeed-063e-4271-89d2-5c3c8a9b06bf");
        list.add("54177a62-4778-435a-88a2-52f41c8afdb0");
        list.add("61c5122a-cae8-415f-b318-5de111525baa");
        list.add("bdb66fa2-3653-4644-94a5-f121d20231c7");
        list.add("f69adee3-db1c-4831-bc94-d7f400fb5cd2");
        list.add("d3c5e35b-08e7-43d0-a8e7-0a87166c9701");
    }

    private void questionDepotListFornewenergyExamRJ(List<String> list) {
        list.add("4e32f730-ad99-402f-8e9a-05aa0fe932a7");
        list.add("cc5487e4-a0c2-46ab-bb69-47cc63d71238");
        list.add("4f59d9b5-0da5-40c4-b7d0-dd9912ef3cbf");
        list.add("71bb5e4a-8c49-4ea6-9a13-f89f7b595e78");
        list.add("1c51aa5d-5d44-4469-943a-8b8cefb12e36");
        list.add("31a5ed0b-2d60-4086-883f-f2d998c6d39d");
        list.add("adf4d14d-bab0-43a6-9ef4-dccba7f4cd08");
        list.add("975d36b3-1864-4867-8da1-0528202c12f0");
        list.add("20eed636-6582-4c88-910a-f90c638fdbe9");
        list.add("b0767caa-f359-455b-96ed-dc287476d973");
        list.add("bb50b735-b64d-4a8f-8c19-1548e7ff7b2c");
        list.add("0bec70ca-ec3d-4069-bfec-97a17696188e");
        list.add("818ce9d0-cb37-419d-9972-b95c5f347d01");
    }

    private void questionDepotListFornewenergyExamIT(List<String> list) {
        list.add("4f26178e-986c-4c58-9bbc-38d393a2160e");
        list.add("7cc8e6d3-7f0c-42a1-9143-a0d6f70d5c98");
        list.add("c1f393f8-9a87-426d-9111-d973ee302d5d");
        list.add("53fd5da7-0525-45cd-8a5f-fccb146b337c");
        list.add("4257a15f-53ff-43d1-9e86-c253f93b5116");
        list.add("3aaeabf7-f5c9-419c-856f-95dd0c7e8ba7");
        list.add("6d375971-c867-4f2f-a984-e5be6a67bf1a");
        list.add("f372d234-18c8-4035-a449-1374f18c83c4");
        list.add("48f16bee-76dd-4003-a071-ffec96664f3e");
    }

    private void questionDepotListFornewenergyExamDSJ(List<String> list) {
        list.add("8d318767-29fa-4102-9cd3-d30c7563c76b");
        list.add("3ae72f97-12ed-4945-92c1-444d52d38ebc");
        list.add("b42d2a95-a022-475b-86b6-f6475d5e836e");
    }

    private void questionDepotListFornewenergyExamCDN(List<String> list) {
        list.add("5a0186aa-c877-4abb-95ca-e0119a51e936");
    }

    private void questionDepotListFornewenergyExamAQ(List<String> list) {
        list.add("1718ce3d-a850-464f-8e85-4956e66aa9f9");
    }

    private void questionDepotListFornewenergyExam5G(List<String> list) {
        list.add("e889fb04-0b52-4903-a7f6-f524008a737a");
    }

    @Autowired
    public void setExamRegisterSrvice(ExamRegisterSrvice examRegisterSrvice) {
        this.examRegisterSrvice = examRegisterSrvice;
    }


}
