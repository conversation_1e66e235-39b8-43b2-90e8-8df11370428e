package com.zxy.product.examstu.web.log;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.google.common.collect.Maps;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.log.LogItem;
import com.zxy.common.restful.log.LogItem.Profile;
import com.zxy.common.restful.log.LogItem.ThreadInfo;
import com.zxy.common.restful.log.RequestLogItem;
import com.zxy.common.restful.log.support.AbstractLogStorage;
import com.zxy.product.human.content.MessageTypeContent;

/**
 * @user tianjun
 * @date 16/7/12
 */

public class DefaultLogStorage extends AbstractLogStorage<Map<String, Object>> {
    
	private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    protected Map<String, Object> convert(LogItem logItem) {
        RequestLogItem requestLogItem = (RequestLogItem) logItem;
        Map<String, Object> map = Maps.newHashMap();
        map.put("NAMESPACE", requestLogItem.getGroup());
        map.put("IDENTIFIER", requestLogItem.getId());
        map.put("THREAD", thread2Map(requestLogItem.getThread()));
        map.put("REQUEST", Maps.newHashMap(requestLogItem.getRequest()));
        map.put("RESPONSE", Maps.newHashMap(requestLogItem.getResponse()));
        map.put("PROFILE", profile2Map(requestLogItem.getProfile()));
        map.put("ERRORS", requestLogItem.getErrors().stream().map(t -> {
            Map<String, Object> throwableMap = Maps.newHashMap();
            throwableMap.put("class", t.getClass().getName());
            throwableMap.put("message", t.getMessage());
            return throwableMap;
        }).collect(Collectors.toCollection(ArrayList::new)));
        return map;
    }

    @Override
    protected void store(Map<LogItem, Map<String, Object>> items) {
//        items.forEach((k, v) -> messageSender.send(MessageTypeContent.LOG_RESTFUL_INSERT, v));
    }

    private static Map<String,Object> thread2Map(ThreadInfo thread) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("id", thread.getId());
        map.put("group", thread.getGroup());
        map.put("name", thread.getName());
        return map;
    }

    private static Map<String,Object> profile2Map(Profile profile) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("start", profile.getStart());
        map.put("end", profile.getEnd());
        return map;
    }



}
