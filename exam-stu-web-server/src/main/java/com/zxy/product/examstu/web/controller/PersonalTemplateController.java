package com.zxy.product.examstu.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.examstu.api.PersonalTemplateService;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.PersonalTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Optional;

/**
 *
 * ClassName: PersonalTemplateController <br/>
 * Reason: 认证考试个人信息模板类<br/>
 * date: 2017年10月19日 上午11:01:43 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@Controller
@RequestMapping("/personal-template")
public class PersonalTemplateController {
   private PersonalTemplateService personalTemplateService;
   @Autowired
	public void setPersonalTemplateService(PersonalTemplateService personalTemplateService) {
	this.personalTemplateService = personalTemplateService;
}

	/**
	 *
	 * insert:新增,修改个人信息模板. <br/>
	 *
	 * <AUTHOR>
	 * @param requestContext
	 * @param subject
	 * @return
	 * @since JDK 1.8
	 * date: 2017年10月19日 下午3:49:55 <br/>
	 */
	@RequestMapping(method = RequestMethod.POST)
    @Param(name = "professionId", type = String.class, required = true)
    @Param(name = "subProfessionId", type = String.class, required = true)
	@Param(name = "equipmentTypeId", type = String.class, required = true)
    @Param(name = "workDepart", type = String.class, required = true)
	@Param(name = "workTime", type = String.class, required = true)
    @Param(name = "isGroupExpert", type = Integer.class, required = true)
	@Param(name = "isProvinExpert", type = Integer.class, required = true)
    @Param(name = "otherExamAppraisal", type = String.class, required = true)
	@Param(name = "awardSituation", type = String.class, required = true)
	@Param(name = "crossCondition", type = String.class, required = true)
	@Param(name = "applyLevel", type = String.class)
	@Param(name = "applyProfession", type = String.class)
	@Param(name = "applySubProfession", type = String.class)
	@Param(name = "applySupplier", type = String.class)
	@Param(name = "province", type = String.class) // 省份
	@Param(name = "city", type = String.class) // 城市
	@JSON("id")
//	@Permitted()
	public PersonalTemplate insert(RequestContext requestContext,Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		PersonalTemplate personalTemplate=new PersonalTemplate();
		personalTemplate.forInsert();
		personalTemplate.setMemberId(subject.getCurrentUserId());
		personalTemplate.setProfessionId(requestContext.get("professionId", String.class));
		personalTemplate.setSubProfessionId(requestContext.get("subProfessionId", String.class));
		personalTemplate.setEquipmentTypeId(requestContext.get("equipmentTypeId", String.class));
		personalTemplate.setWorkDepart(requestContext.get("workDepart", String.class));
		personalTemplate.setWorkTime(requestContext.get("workTime", String.class));
		personalTemplate.setIsGroupExpert(requestContext.get("isGroupExpert", Integer.class));
		personalTemplate.setIsProvinExpert(requestContext.get("isProvinExpert", Integer.class));
		personalTemplate.setOtherExamAppraisal(requestContext.get("otherExamAppraisal", String.class));
		personalTemplate.setAwardSituation(requestContext.get("awardSituation", String.class));
		personalTemplate.setCrossCondition(requestContext.get("crossCondition", String.class));
		personalTemplate.setApplyLevel(requestContext.getOptional("applyLevel", String.class).orElse(""));
		personalTemplate.setApplyProfession(requestContext.getOptional("applyProfession", String.class).orElse(""));
		personalTemplate.setApplySubProfession(requestContext.getOptional("applySubProfession", String.class).orElse(""));
		personalTemplate.setApplySupplier(requestContext.getOptional("applySupplier", String.class).orElse(""));
		personalTemplate.setProvince(requestContext.getOptional("province", String.class).orElse(""));
		personalTemplate.setCity(requestContext.getOptional("city", String.class).orElse(""));
		return  personalTemplateService.insert(examRegion, personalTemplate);
	}


   /**
    *
    * get:根据用户ID获取个人信息模板<br/>
    *
    * <AUTHOR>
    * @param requestContext
    * @return
    * @since JDK 1.8
    * date: 2017年10月19日 下午4:52:51 <br/>
    */
    @RequestMapping(value = "/get-template" , method = RequestMethod.GET)
    @Param(name = "examId", type = String.class)
	@Param(name = "shield",type = Boolean.class)
    @JSON("signUpAuth.(id,professionId,subProfessionId,equipmentTypeId,workDepart,workTime,isGroupExpert,isProvinExpert,otherExamAppraisal)")
    @JSON("signUpAuth.(awardSituation,crossCondition,applyLevel,applyProfession,applySubProfession,applySupplier)")
    @JSON("signUpAuth.(memberId,idcard,mail,phone,orgName,province,city)")
    @JSON("personalTemplate.(id,professionId,subProfessionId,equipmentTypeId,workDepart,workTime,isGroupExpert,isProvinExpert,otherExamAppraisal)")
    @JSON("personalTemplate.(awardSituation,crossCondition,applyLevel,applyProfession,applySubProfession,applySupplier)")
    @JSON("personalTemplate.(memberId,idcard,mail,phone,orgName,province,city)")
//    @Permitted()
    public HashMap<String, Object> get(RequestContext requestContext,Subject<Member> subject) {
		Integer examRegion = subject.examRegion();

		Optional<Boolean> shield = requestContext.getOptionalBoolean("shield");


		return personalTemplateService.get(examRegion, requestContext.getOptional("examId", String.class),subject.getCurrentUserId(),shield.orElse(false));
    }

    /**
     *
     * update:修改个人模板信息. <br/>
     *
     * <AUTHOR>
     * @param requestContext
     * @param subject
     * @return
     * @since JDK 1.8
     * date: 2017年10月19日 下午4:56:36 <br/>
     */
	@RequestMapping(method = RequestMethod.PUT)
	@Param(name = "id", type = String.class, required = true)
    @Param(name = "professionId", type = String.class, required = true)
    @Param(name = "subProfessionId", type = String.class, required = true)
	@Param(name = "equipmentTypeId", type = String.class, required = true)
    @Param(name = "workDepart", type = String.class, required = true)
	@Param(name = "workTime", type = String.class, required = true)
    @Param(name = "isGroupExpert", type = Integer.class, required = true)
	@Param(name = "isProvinExpert", type = Integer.class, required = true)
    @Param(name = "otherExamAppraisal", type = String.class, required = true)
	@Param(name = "awardSituation", type = String.class, required = true)
	@Param(name = "crossCondition", type = String.class, required = true)
	@Param(name = "applyLevel", type = String.class)
	@Param(name = "applyProfession", type = String.class)
	@Param(name = "applySubProfession", type = String.class)
	@Param(name = "applySupplier", type = String.class)
	@JSON("id")
//	@Permitted()
    public PersonalTemplate update(RequestContext requestContext,Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		return personalTemplateService.update
    			(examRegion, requestContext.getString("id"), requestContext.get("professionId", String.class), requestContext.get("subProfessionId", String.class),
    					requestContext.get("equipmentTypeId", String.class), requestContext.get("workDepart", String.class),
    					requestContext.get("workTime", String.class), requestContext.get("isGroupExpert", Integer.class), requestContext.get("isProvinExpert", Integer.class),
    					requestContext.get("otherExamAppraisal", String.class), requestContext.get("awardSituation", String.class),
    					requestContext.get("crossCondition", String.class), requestContext.getOptional("applyLevel", String.class),
    					requestContext.getOptional("applyProfession", String.class),
    					requestContext.getOptional("applySubProfession", String.class),
    					requestContext.getOptional("applySupplier", String.class));
    }

}
