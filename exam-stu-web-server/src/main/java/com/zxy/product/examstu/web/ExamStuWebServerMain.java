package com.zxy.product.examstu.web;


import com.zxy.product.examstu.web.config.CacheConfig;
//import com.zxy.product.examstu.web.config.ExamWebSocketConfig;
//import com.zxy.product.examstu.web.config.MessageConfig;
import com.zxy.product.examstu.web.config.RestfulConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import com.zxy.common.restful.websocket.WebSocketConfig;
import com.zxy.product.examstu.web.config.RPCClientConfig;
import com.zxy.product.examstu.web.config.WebConfig;

@SpringBootApplication
@Import({
    RestfulConfig.class, RPCClientConfig.class,WebConfig.class,
//    MessageConfig.class,
        CacheConfig.class,
    WebSocketConfig.class
//        , ExamWebSocketConfig.class
})
public class ExamStuWebServerMain {

    public static void main(String[] args) {
        SpringApplication.run(ExamStuWebServerMain.class, args);
    }

}
