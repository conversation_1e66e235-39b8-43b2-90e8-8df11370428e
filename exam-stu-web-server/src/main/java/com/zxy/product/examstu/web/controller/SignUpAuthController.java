package com.zxy.product.examstu.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.chbn.ChbnKnowledgeEmpowermentService;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.exam.entity.RelevanceCourseExam;
import com.zxy.product.examstu.api.*;
import com.zxy.product.exam.entity.*;
import com.zxy.product.human.api.MemberPositionInnerService;
import com.zxy.product.human.api.MemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * ClassName: SignAuthController <br/>
 * Reason: 报名认证信息<br/>
 * date: 2017年10月19日 下午3:12:41 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@Controller
@RequestMapping("/sign-auth")
public class SignUpAuthController {
	private SignUpAuthService signUpAuthService;
	private SignUpService signUpService;
	private MemberPositionInnerService memberPositionInnerService;
	private ChbnKnowledgeEmpowermentService chbnKnowledgeEmpowermentService;
	private CourseStudyProgressService courseStudyProgressService;
	private GridCourseService gridCourseService;
	private Cache gridSignupCache;
	private MemberService memberService;
	private Cache examCache;

	private ExamService examService;

	@Resource
	private RelevanceCourseExamService relevanceCourseExamService;


	private StrongBaseService strongBaseService;

	@Autowired
	public void setMemberPositionInnerService(MemberPositionInnerService memberPositionInnerService) {
		this.memberPositionInnerService = memberPositionInnerService;
	}
	@Autowired
	public void setStrongBaseService(StrongBaseService strongBaseService) {
		this.strongBaseService = strongBaseService;
	}

	@Autowired
	public void setExamService(ExamService examService) {
		this.examService = examService;
	}

	@Autowired
	public void setSignUpService(SignUpService signUpService) {
		this.signUpService = signUpService;
	}

	@Autowired
	public void setGridCourseService(GridCourseService gridCourseService) {
		this.gridCourseService = gridCourseService;
	}

	@Autowired
	   public void setSignUpAuthService(SignUpAuthService signUpAuthService) {
		this.signUpAuthService = signUpAuthService;
	}

	@Autowired
	public void setChbnKnowledgeEmpowermentService(ChbnKnowledgeEmpowermentService chbnKnowledgeEmpowermentService) {
		this.chbnKnowledgeEmpowermentService = chbnKnowledgeEmpowermentService;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.gridSignupCache = cacheService.create("grid-signup", "time");
		this.examCache = cacheService.create("cacheExam", "examEntity");
	}

	@Autowired
	public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
		this.courseStudyProgressService = courseStudyProgressService;
	}

	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}


	/**
	    *
	    * get:获取报名认证信息<br/>
	    *
	    * <AUTHOR>
	    * @param requestContext
	    * @return
	    * @since JDK 1.8
	    * date: 2017年10月19日 下午4:52:51 <br/>
	    */
	    @RequestMapping(value = "/{id}" , method = RequestMethod.GET)
	    @Param(name = "id", type = String.class, required = true)
	    @JSON("id,signUpId,examId,professionId,subProfessionId,equipmentTypeId,workDepart,workTime,isGroupExpert,isProvinExpert,otherExamAppraisal")
	    @JSON("awardSituation,crossCondition,applyLevel,applyProfession,applySubProfession,applySupplier")
//	    @Permitted()
	    public SignUpAuth get(RequestContext requestContext,Subject<Member> subject) {
			Integer examRegion = subject.examRegion();
			return signUpAuthService.get(examRegion, requestContext.get("id", String.class),subject.getCurrentUserId());

	    }

	/**
	 *
	 * findAuditList:认证报名审核列表 . <br/>
	 *
	 * <AUTHOR>
	 * @param requestContext
	 * @param subject
	 * @return
	 * @since JDK 1.8
	 * date: 2017年11月3日 上午11:02:56 <br/>
	 */
	@RequestMapping(value="audit-list",method= RequestMethod.GET)
	@JSON("recordCount")
	@JSON("items.(id,name,auditStartTime,auditEndTime,examBatch,preApproval,applicantStartTime,applicantEndTime)")
	@JSON("items.signUp.(id,status)")
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="status", type=Integer.class)
	@Param(name = "startTimeOrderBy", type = Integer.class)
	public PagedResult<Exam> findAuditList(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		//查询所有状态的列表(未过审核时间的，取消报名的不查)
		return signUpAuthService.findAuditList(examRegion, subject.getCurrentUserId(), requestContext.get("page", Integer.class), requestContext.get("pageSize", Integer.class), requestContext.getOptional("startTimeOrderBy", Integer.class));
	}

	/**
	 *
	 * get:获取剩余报名次数<br/>
	 *
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value = "/remain-signup-count" , method = RequestMethod.GET)
	@Param(name = "organizationId", type = String.class)
	@JSON("*")
	public Integer getSignUpCount(RequestContext requestContext,Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		String lastOrgId = memberPositionInnerService.getLastOrgIdByMemberId(subject.getCurrentUserId());
		return signUpService.getSignUpCount(examRegion, lastOrgId, subject.getCurrentUserId());
	}


	    /**
		 *
		 * 新增修改报名信息认证.以及报名 <br/>
		 *
		 * <AUTHOR>
		 * @param requestContext
		 * @param subject
		 * @return
		 * @since JDK 1.8
		 * date: 2017年10月19日 下午3:49:55 <br/>
		 */
		@RequestMapping(method = RequestMethod.POST)
		@Param(name = "examId", type = String.class, required = true)
		@Param(name = "ids", type = String.class)
	    @Param(name = "professionId", type = String.class)
	    @Param(name = "subProfessionId", type = String.class)
		@Param(name = "equipmentTypeId", type = String.class)
	    @Param(name = "workDepart", type = String.class)
		@Param(name = "workTime", type = String.class)
	    @Param(name = "isGroupExpert", type = Integer.class)
		@Param(name = "isProvinExpert", type = Integer.class)
	    @Param(name = "otherExamAppraisal", type = String.class)
		@Param(name = "awardSituation", type = String.class)
		@Param(name = "crossCondition", type = String.class)
		@Param(name = "applyLevel", type = String.class)
		@Param(name = "applyProfession", type = String.class)
		@Param(name = "applySubProfession", type = String.class)
		@Param(name = "applySupplier", type = String.class)
		@Param(name = "organizationId", type = String.class)
		@Param(name = "province", type = String.class)
		@Param(name = "city", type = String.class)
		@JSON("*.*")
//		@Permitted()
		public Map<String, Object> insert(RequestContext requestContext,Subject<Member> subject) {
			Integer examRegion = subject.examRegion();
			String examId = requestContext.getString("examId");

			Exam exam = getExamFromCacheOrDB(examRegion, examId);

			// 考试报名时要判断是否已经过了报名截止时间
			if (Objects.equals(exam.getNeedApplicant(), Exam.EXAM_NEED_APPLICANT_YES)) {
				ErrorCode.TheRegistrationPeriodHasPassed.throwIf(exam.getApplicantEndTime() != null && System.currentTimeMillis() > exam.getApplicantEndTime());
			}

			//是否是考试组里的考试
			if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1.equals(exam.getStrongBaseFlag())) {
				//校验考试组报名次数
				ErrorCode.NoSignUpCount.throwIf(strongBaseService.checkExamGroupSignUp(examRegion, subject.getCurrentUserId(), examId));
			}
			String companyId = null;
			SignUpAuth signUpAuth=new SignUpAuth();
			signUpAuth.forInsert();
			Optional<String> id =requestContext.getOptional("ids", String.class);
			id.ifPresent(signUpAuth::setId);
			signUpAuth.setExamId(examId);
			String memberId = subject.getCurrentUserId();
			signUpAuth.setMemberId(memberId);
			signUpAuth.setProfessionId(requestContext.getOptional("professionId", String.class).orElse(""));
			signUpAuth.setSubProfessionId(requestContext.getOptional("subProfessionId", String.class).orElse(""));
			signUpAuth.setEquipmentTypeId(requestContext.getOptional("equipmentTypeId", String.class).orElse(""));
			signUpAuth.setWorkDepart(requestContext.getOptional("workDepart", String.class).orElse(""));
			signUpAuth.setWorkTime(requestContext.getOptional("workTime", String.class).orElse(""));
			signUpAuth.setIsGroupExpert(requestContext.getOptional("isGroupExpert", Integer.class).orElse(0));
			signUpAuth.setIsProvinExpert(requestContext.getOptional("isProvinExpert", Integer.class).orElse(0));
			signUpAuth.setAwardSituation(requestContext.getOptional("awardSituation", String.class).orElse(""));
			signUpAuth.setCrossCondition(requestContext.getOptional("crossCondition", String.class).orElse(""));
			signUpAuth.setApplyLevel(requestContext.getOptional("applyLevel", String.class).orElse(""));
			signUpAuth.setApplyProfession(requestContext.getOptional("applyProfession", String.class).orElse(""));
			signUpAuth.setApplySubProfession(requestContext.getOptional("applySubProfession", String.class).orElse(""));
			signUpAuth.setApplySupplier(requestContext.getOptional("applySupplier", String.class).orElse(""));
			signUpAuth.setOtherExamAppraisal(requestContext.getOptional("otherExamAppraisal", String.class).orElse(""));
			signUpAuth.setProvince(requestContext.getOptional("province", String.class).orElse(""));
			signUpAuth.setCity(requestContext.getOptional("city", String.class).orElse(""));

			int preApprovalStatus = 1;
			boolean ordinaryExam = isOrdinaryExam(exam.getType());
			if (ordinaryExam) {
				HashMap<String, Object> map = new HashMap<>();
				Long time = gridSignupCache.get(examId+"#"+memberId, Long.class);
				if (time != null) {
					map.put("time", ((SignUp.CACHE_TIME * 1000)-(System.currentTimeMillis() - time)));
					return map;
				}
				preApprovalStatus = getPreApprovalStatus(examRegion, exam, memberId);
            }

			Map<String, Object> res = signUpService.signUpAuth(examRegion, signUpAuth, requestContext.getOptional("organizationId", String.class), companyId, preApprovalStatus);

			if (ordinaryExam){
				if (res != null && preApprovalStatus == SignUp.STATUS_REFUSE) {
					HashMap<String, Object> map = new HashMap<>();
					// 被拒绝后有2个小时的缓存时间，防止一直报名
					map.put("time", SignUp.CACHE_TIME * 1000);
					gridSignupCache.set(examId+"#"+memberId, System.currentTimeMillis(), SignUp.CACHE_TIME);
					return map;
				}
			}
			return res;
		}

	/**
	 * 缓存获取考试信息
	 * @param examId
	 * @return
	 */
	private Exam getExamFromCacheOrDB(Integer examRegion, String examId) {
		Exam exam = new Exam();
		try {
			exam = examCache.get(examId, () -> {
				return Optional.ofNullable(
								examService.getSimpleData(examRegion, examId))
						.orElseThrow(() ->
								new UnprocessableException(ErrorCode.ExamNullError));
			}, Exam.CACHE_TIME);

		} catch (Exception e) {
			examCache.clear(examId);
			exam = examCache.get(examId, () -> {
				return Optional.ofNullable(
								examService.getSimpleData(examRegion, examId))
						.orElseThrow(() ->
								new UnprocessableException(ErrorCode.ExamNullError));
			}, Exam.CACHE_TIME);
		}

		return exam;
	}

	   /**
        *
        * 移动云考试报名 <br/>
        *
        */
       @RequestMapping(value = "/for-cloud", method = RequestMethod.POST)
       @Param(name = "examId", type = String.class, required = true)
       @Param(name = "position", type = String.class, required = true)
       @Param(name = "workTime", type = String.class, required = true)
       @JSON("*.*")
//       @Permitted()
       public Map<String, Object> insertCloudSignup(RequestContext requestContext,Subject<Member> subject) {
		   Integer examRegion = subject.examRegion();
		   CloudSignup cloudSignup=new CloudSignup();
           cloudSignup.forInsert();
           cloudSignup.setExamId(requestContext.get("examId", String.class));
           cloudSignup.setMemberId(subject.getCurrentUserId());
           cloudSignup.setPosition(requestContext.get("position", String.class));
           cloudSignup.setWorkTime(requestContext.get("workTime", String.class));
           return signUpService.insertCloudSignup(examRegion, cloudSignup);
       }

	/**
	 *
	 * 网格长考试报名 <br/>
	 *
	 */
	@RequestMapping(value = "/for-grid", method = RequestMethod.POST)
	@Param(name = "examId", required = true)
	@JSON("*.*")
//	@Permitted()
	public Map<String, Object> insertGridSignup(RequestContext requestContext,Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		String examId = requestContext.get("examId", String.class);
		String memberId = subject.getCurrentUserId();

		HashMap<String, Object> map = new HashMap<>();
		Long time = gridSignupCache.get(examId+"#"+memberId, Long.class);
		if (time != null) {
			map.put("time", ((SignUp.CACHE_TIME*1000)-(System.currentTimeMillis() - time)));
			return map;
		}

		// 是否获得chbn证书
//		Boolean haveChbnCertificate = chbnKnowledgeEmpowermentService.haveChbnCertificate(Exam.ACTIVITY_TYPE_CHBN, subject.getCurrentUserId());
		// 是否完成学习
		List<String> courseIds = gridCourseService.findGridCourseByExamId(examRegion, examId).stream().map(GridCourse::getCourseId).collect(Collectors.toList());
		Boolean finishAllCourse = courseStudyProgressService.finishAllCourse(memberId, courseIds);

		GridSignup gridSignup=new GridSignup();
		gridSignup.forInsert();
		gridSignup.setExamId(requestContext.get("examId", String.class));
		gridSignup.setMemberId(subject.getCurrentUserId());
		Map<String, Object> signUpMap = signUpService.insertGridSignup(examRegion, gridSignup, finishAllCourse);

		GridSignup signup = (GridSignup) signUpMap.get("signUp");
		if (signup != null && signup.getStatus() != null && signup.getStatus().intValue() == SignUp.STATUS_REFUSE) {
			// 被拒绝后有2个小时的缓存时间，防止一直报名
			map.put("time", SignUp.CACHE_TIME*1000);
			gridSignupCache.set(examId+"#"+memberId, System.currentTimeMillis(), SignUp.CACHE_TIME);
			return map;
		}
		return signUpMap;
	}

		/**
		 *
		 * cancelSignUp:取消报名接口. <br/>
		 *
		 * <AUTHOR>
		 * @param requestContext
		 * @param subject
		 * @return
		 * @since JDK 1.8
		 * date: 2017年10月21日 上午10:41:18 <br/>
		 */
		@RequestMapping(value = "/cancel-sign/{id}", method= RequestMethod.PUT)
	    @Param(name="id", required=true)
		@JSON("id,status")
	    public SignUp cancelSignUp(RequestContext requestContext,Subject<Member> subject) {
			Integer examRegion = subject.examRegion();
			return signUpAuthService.cancelSign(
					examRegion,
	    			requestContext.get("id",String.class),
	    			SignUp.STATUS_CANCEL,subject.getCurrentUserId());
	    }



	    /** 根据报名id查找报名认证信息 */
        @RequestMapping(value = "/signup-detail" , method = RequestMethod.GET)
        @Param(name = "signUpId", type = String.class, required = true)
        @Param(name = "memberId", type = String.class, required = true) // 该条报名数据的学员id
        @JSON("id,signUpId,examId,professionId,subProfessionId,equipmentTypeId,workDepart,workTime,isGroupExpert,isProvinExpert,otherExamAppraisal,awardSituation,crossCondition,applyLevel,applyProfession,applySubProfession,applySupplier")
        @JSON("member.(id,name,fullName,identityNumber,email,phoneNumber,headPortraitPath)") // 人员
        @JSON("member.organization.(id,name,companyName)") // 人员组织
        @JSON("profession.(id,name)") // 专业
        @JSON("subProfession.(id,name)") // 子专业
        @JSON("equipmentType.(id,name)") // 设备
        @Permitted
        @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.MANAGE, fisrtAction = "详细信息", desc = "对{0}操作查看报名详细信息于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"})
        public SignUpAuth signupDetail(RequestContext requestContext) {
			String memberId = requestContext.get("memberId", String.class);
			Integer examRegion = memberService.findExamRegion(memberId);
			return signUpAuthService.findBySignup(examRegion, requestContext.get("signUpId", String.class));
        }

        /** 移动云考试-根据报名id查找报名认证信息 */
        @RequestMapping(value = "/cloud-signup-detail" , method = RequestMethod.GET)
        @Param(name = "signUpId", type = String.class, required = true)
		@Param(name = "memberId", type = String.class, required = true) // 该条报名数据的学员id
		@JSON("id,createTime,examId,memberId,organizationId,position,workTime,status,auditMemberId")
        @JSON("member.(id,name,fullName,identityNumber,email,phoneNumber,headPortraitPath)") // 人员
        @JSON("member.organization.(id,name,companyName)") // 人员组织
        @JSON("cloudProfession.(name)") // 专业
        @JSON("cloudLevel.(levelName)") // 等级
        @Permitted
        @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.MANAGE, fisrtAction = "详细信息", desc = "对{0}操作查看报名详细信息于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"})
        public CloudSignup cloudSignupDetail(RequestContext requestContext) {
			String memberId = requestContext.get("memberId", String.class);
			Integer examRegion = memberService.findExamRegion(memberId);
            return signUpAuthService.findCloudBySignup(examRegion, requestContext.get("signUpId", String.class));
        }


	private int getPreApprovalStatus(Integer examRegion, Exam exam, String memberId) {
		int preStatusWaitApproval = 1;
		int preStatusPass = 2;
		int preStatusRefuse = 3;
		String preApprovalRule = exam.getPreApprovalRule();
		if (preApprovalRule != null) {
			int page = 1;
			String allCoursesCompleted = "r1";
			String allApplicantsHavePassedTheReview = "r2";
			// 解析预审规则
			JSONObject jsonObject = JSONObject.parseObject(preApprovalRule);
			// 勾选了规则 全部报名人员均通过审核
			if (Exam.PRE_APPROVAL_RULE_YES == jsonObject.getIntValue(allApplicantsHavePassedTheReview)) {
				return preStatusPass;
			}
			//  勾选了规则完成考试关联课程
			if (Exam.PRE_APPROVAL_RULE_YES == jsonObject.getIntValue(allCoursesCompleted)) {

				List<RelevanceCourseExam> relevanceCourseExamList = relevanceCourseExamService.findRelevanceCourseExamList(examRegion, exam.getId());
				if (!relevanceCourseExamList.isEmpty()) {
					// 课程和专题的id
					List<String> courseIds = relevanceCourseExamList.stream().map(RelevanceCourseExam::getCourseId).collect(Collectors.toList());
					if (courseStudyProgressService.finishAllCourse(memberId, courseIds)) {
						return preStatusPass;
					}
				}
			}
			return preStatusRefuse;
		}
		return preStatusWaitApproval;
	}

	/**
	 * 是否普通考试
	 * @param type
	 * @return
	 */
	private  boolean isOrdinaryExam(Integer type) {
		return Exam.EXAM_OFFICIAL_TYPE.equals(type) || Exam.EXAM_UN_OFFICIAL_TYPE.equals(type);
	}
}
