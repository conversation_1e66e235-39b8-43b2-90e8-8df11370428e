/**
 * *

package com.zxy.product.examstu.web.listener;

import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.examstu.api.ExamRecordService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.examstu.web.websocket.ExamWebSocketHandler;

@Component
public class SubmitListener extends AbstractMessageListener{
	
	private static final Logger LOGGER = LoggerFactory.getLogger(SubmitListener.class);

    private ExamWebSocketHandler examWebSocketHandler;
    
    private ExamRecordService examRecordService;
    
    private CacheService cacheService;
    
    private Cache examRecordCache;
    
    @Autowired
    public void setCacheService(CacheService cacheService) {
		this.cacheService = cacheService;
		examRecordCache = this.cacheService.create("cacheExamRecord", "examRecordEntity");
	}
    
    
    @Autowired
	public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}
    
    @Autowired
    public void setExamWebSocketHandler(ExamWebSocketHandler examWebSocketHandler) {
        this.examWebSocketHandler = examWebSocketHandler;
    }

    @Override
    protected void onMessage(Message message) {
    	LOGGER.info("exam/forceSubmitPaper:{}", message);
        String idsString = message.getHeader(MessageHeaderContent.IDS);
        String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
        String [] ids = idsString.split(",");
        Arrays.stream(ids).forEach(t -> {
        	if (canSendMessageToClient(examId, t)) {
        		examWebSocketHandler.sendMessageToUser(examId, Arrays.asList(t), ExamWebSocketHandler.SUBMIT_PAPER);
        	}
        });
    }
    

    private boolean canSendMessageToClient(String examId, String memberId) {
    	String key = createExamUserKey(examId, memberId);
    	return examWebSocketHandler.isSessionOpen(key)
    		&& examWebSocketHandler.hasExamUserCache(key, examId);
    }
    
    

    private boolean canSubmitTempAnswerRecord(String examId, String memberId) {
    	String key = createExamUserKey(examId, memberId);
    	return !examWebSocketHandler.isSessionOpen(key)
    			&& !examWebSocketHandler.hasExamUserCache(key, examId);
    }

    @Override
    public int[] getTypes() {
    	return new int[]{
    		MessageTypeContent.EXAM_EXAM_RECORD_SUBMIT
    	};
    }
    
    private String createExamUserKey(String examId, String userId) {
		return ExamRecord.getExamRecordKey(examId, userId);
	}
    

}

 */