package com.zxy.product.examstu.web.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.zxy.common.restful.log.LogQueue;
import com.zxy.common.restful.log.RequestLogger;
import com.zxy.common.restful.log.support.DefaultLogQueue;
import com.zxy.common.restful.log.support.DefaultRequestLogger;
import com.zxy.common.restful.log.support.RequestLogInterceptor;
import com.zxy.common.restful.security.interceptor.PermissionCheckInterceptor;
import com.zxy.product.examstu.web.converter.CustomerLongConverter;

/**
 * @user tianjun
 * @date 16/7/12
 */
@Configuration
public class WebConfig extends WebMvcConfigurerAdapter {



//	private RequestLogInterceptor requestLogInterceptor;
	private PermissionCheckInterceptor permissionCheckInterceptor;

//	@Autowired
//	public void setRequestLogInterceptor(RequestLogInterceptor requestLogInterceptor) {
//        this.requestLogInterceptor = requestLogInterceptor;
//    }

	@Autowired
    public void setPermissionCheckInterceptor(PermissionCheckInterceptor permissionCheckInterceptor) {
        this.permissionCheckInterceptor = permissionCheckInterceptor;
    }

    @Override
	public void addInterceptors(InterceptorRegistry registry) {
//		registry.addInterceptor(requestLogInterceptor);
		registry.addInterceptor(permissionCheckInterceptor);
	}

//	@Bean
//    public RequestLogInterceptor requestLogInterceptor(){
//        return new RequestLogInterceptor();
//    }

	@Bean
	public PermissionCheckInterceptor permissionCheckInterceptor(){
	    return new PermissionCheckInterceptor();
	}

//	@Bean
//	public LogQueue logQueue(){
//		return new DefaultLogQueue();
//	}
//
//	@Bean
//	public RequestLogger requestLogger(){
//		return new DefaultRequestLogger();
//	}

	@Bean
	public CustomerLongConverter customerLongCunverter() {
	    return new CustomerLongConverter();
	}
}
