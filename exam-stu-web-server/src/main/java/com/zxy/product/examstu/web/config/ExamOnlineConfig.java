package com.zxy.product.examstu.web.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.product.examstu.content.FlowLimitEnum;
import com.zxy.product.examstu.dto.exam.online.EchoLimitRuleConfig;
import com.zxy.product.examstu.dto.exam.online.ExamMemory;
import com.zxy.product.examstu.dto.exam.online.FlowRuleConfig;
import com.zxy.product.examstu.dto.exam.online.OnlineFlowRule;
import com.zxy.product.system.api.home.HomeSystemService;
import com.zxy.product.system.entity.RuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.Optional;

import static com.zxy.product.examstu.content.ExamLimitConstants.*;

/**
 * 考试最大在线人数配置类
 * <AUTHOR>
 * @date 2025年02月17日 10:05
 */
@Component
@EnableScheduling
public class ExamOnlineConfig {
    private static final Logger logger= LoggerFactory.getLogger(ExamOnlineConfig.class);
    private HomeSystemService homeSystemService;
    public static ExamMemory examMemory;
    private Cache ruleConfigCache;

    @Autowired
    public void setHomeSystemService(HomeSystemService homeSystemService){this.homeSystemService=homeSystemService;}

    @Autowired
    public void setRuleConfigCache(CacheService cacheService){ this.ruleConfigCache=cacheService.create(CacheKeyOfSystem,CacheKeyOfSystem); }

    /**构建默认的考试内存对象*/
    public ExamMemory doDefaultMemory(){
        examMemory = new ExamMemory();
        examMemory.setMaxOnline( 0 );
        examMemory.setMaxOnlineSwitch( false );
        examMemory.setApplicationSwitch( false );
        examMemory.setMainSwitch( false );
        return examMemory;
    }


    /**初始化考试JVM内存对象*/
    @PostConstruct
    public void initExamMemory(){
        examMemory = this.doDefaultMemory();
        logger.info("初始化考试JVM内存对象{}", JSON.toJSONString( ExamOnlineConfig.examMemory ));
    }

    /**定时获取考试限流规则且放入内存*/
    @Scheduled(cron = "0 0/1 * * * ?")
    public void scheduleExamMemory(){ this.doOnlineExam( ruleConfigCache ); }

    /**
     * 1、管理端在修改规则时，会清空当前缓存对应的KEY对象
     * 2、常规校验规则数据，状态，是否为空，以此修改考试内存对象属性
     * 3、限流规则比较特殊，需要判断主开关，模块开关以及对应属性开关，以此修改考试内存对象开关
     * 4、考试内存对象开关为基准，校验考试在线限流逻辑
     *
     * @param ruleConfigCache Spring注入的缓存对象
     */
    private void doOnlineExam(Cache ruleConfigCache){
        RuleConfig ruleConfig = ruleConfigCache.get(CacheKeyRuleConfigSuffix, () -> homeSystemService.singleRuleConfig("FLOW_LIMIT_CONFIG"));
        EchoLimitRuleConfig echoLimitRuleConfig = JSONObject.parseObject(ruleConfig.getValue(), EchoLimitRuleConfig.class);
        Optional<FlowRuleConfig> examRuleOpt  = echoLimitRuleConfig.getRules().parallelStream()
                .filter(ew1 -> Objects.equals(ew1.getApplicationName(), FlowLimitEnum.ExamSubmit.getApplicationName()))
                .findFirst();
        examMemory = Optional.of(echoLimitRuleConfig)
                .filter(ew1->Objects.equals(FlowLimitEnable,ew1.getMainSwitch()) && examRuleOpt.isPresent())
                .map(ew2->{
                    examMemory.setMainSwitch(Objects.equals(FlowLimitEnable,ew2.getMainSwitch()));
                    Object examOnlineConfig = examRuleOpt.get();
                    Integer applicationSwitch = JSONObject.parseObject(JSON.toJSONString(examOnlineConfig)).getInteger("mainSwitch");
                    examMemory.setApplicationSwitch(Objects.equals(FlowLimitEnable,applicationSwitch));
                    String examConfig = JSONObject.parseObject(JSON.toJSONString(examOnlineConfig)).getString("flowRule");
                    OnlineFlowRule onlineFlowRule = JSON.parseObject(examConfig, OnlineFlowRule.class);
                    Integer maxOnline = onlineFlowRule.getMaxOnlineSwitchType() ? onlineFlowRule.getMaxOnline() : 0;
                    examMemory.setMaxOnline(maxOnline);
                    examMemory.setMaxOnlineSwitch(onlineFlowRule.getMaxOnlineSwitchType());
                    return examMemory;
                }).orElseGet(this::doDefaultMemory);
        logger.info("从系统中获取的限流配置数据集合成功且复制JVM内存，获取的考试限流数据信息{}",JSON.toJSONString(examMemory));
    }
}
