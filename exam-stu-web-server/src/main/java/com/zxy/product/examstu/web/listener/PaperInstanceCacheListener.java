/**
 * *

package com.zxy.product.examstu.web.listener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.api.ExamRecordService;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.api.PaperInstanceService;
import com.zxy.product.exam.api.QuestionCopyService;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.PaperInstance;
import com.zxy.product.exam.entity.PaperInstanceQuestionCopy;
import com.zxy.product.exam.entity.QuestionCopy;


@Component
public class PaperInstanceCacheListener  extends AbstractMessageListener {

	private static final Logger LOGGER = LoggerFactory.getLogger(PaperInstanceCacheListener.class);


    private static final int TWO_DAYS = 60 * 60 * 24 * 2;

	private PaperInstanceService paperInstanceService;

	private CacheService cacheService;

	private ExamService examService;

	private ExamRecordService examRecordService;

	private QuestionCopyService questionCopyService;

	private Cache examCache;

	private Cache paperCache;

	private Cache paperNewCache;

	private Cache recordCache;

	private Cache questionCache;

	private Cache answerCache;

	private Cache paperInstanceQuestionCopyCache;

	@Autowired
	public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
		this.paperInstanceService = paperInstanceService;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.cacheService = cacheService;
		this.examCache = cacheService.create("cacheExam", "examEntity");
		this.paperCache = cacheService.create("cachePaperInstance", "paperInstanceEntity");
		this.paperNewCache = cacheService.create("cachePaperInstance", "NEW-paperInstanceEntity");
		this.recordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
		this.questionCache = cacheService.create("cacheQuestionCopy", "questionCopyEntity");
		this.answerCache = cacheService.create(Exam.PAPER_ANSWER_CACHE_KEY);
		this.paperInstanceQuestionCopyCache = cacheService.create("cachePaperInstanceQuestionCopy", "paperInstanceQuestionCopyEntity");
	}

	@Autowired
	public void setExamService(ExamService examService) {
		this.examService = examService;
	}

	@Autowired
	public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}

	@Autowired
	public void setQuestionCopyService(QuestionCopyService questionCopyService) {
		this.questionCopyService = questionCopyService;
	}

	@Override
	protected void onMessage(Message message) {
		LOGGER.info("exam/paperInstanceCache :" + message.toString());
		String ids = message.getHeader(MessageHeaderContent.IDS);

		switch (message.getType()) {
		case MessageTypeContent.EXAM_TIME_TO_CACHE_PAPER:
			if (ids != null&& !ids.equals("")) cachePapersByExamIds(ids.split(","), TWO_DAYS);
			break;
		case MessageTypeContent.EXAM_RELOAD_PAPER_CACHE:
			String id = message.getHeader(MessageHeaderContent.ID);
			clearExamCache(id);
			if (id != null&& !id.equals("")) cachePapersByExamIds(id.split(","), TWO_DAYS);
			break;
		default:
			break;
		}

	}

	private void clearExamCache(String examId) {
		examCache.clear(examId);
	}

	@Override
	protected boolean supported(int type) {
		return MessageTypeContent.EXAM_TIME_TO_CACHE_PAPER == type
				|| MessageTypeContent.EXAM_RELOAD_PAPER_CACHE == type;
	}


	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_TIME_TO_CACHE_PAPER,
			MessageTypeContent.EXAM_RELOAD_PAPER_CACHE
		};
	}

	private List<PaperInstance> cachePapersByExamIds(String[] examIds, int expiredTime) {

		List<String> needCacheExamIds = new ArrayList<>();

		//考试
		//判断哪些考试已经缓存过
		Arrays.stream(examIds).forEach(t -> {
			if (examCache.get(t, Exam.class) == null) {
				needCacheExamIds.add(t);
			}
		});

		String[] array = new String[needCacheExamIds.size()];
		examIds = needCacheExamIds.toArray(array);

        examService.findBasicByIds(examIds).forEach(exam -> {
            examCache.set(exam.getId(), exam, TWO_DAYS);
        });

        //试卷
        List<PaperInstance> paperList = paperInstanceService.findByExamId(examIds);
        paperList.forEach(p -> {
            paperCache.set(p.getExamId()+p.getId(), p, TWO_DAYS);
            // 试卷试题关系数据
            List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceService.findPaperInstanceQuestionCopiesByPaperId(p.getId(), p.getExamId());
            paperInstanceQuestionCopyCache.set(p.getId(), paperInstanceQuestionCopies, TWO_DAYS);
        });

        // 添加paper缓存（包含阅读理解题型，用于前端计算分数）
        List<PaperInstance> paperNewList = paperInstanceService.findNewPaperByExamId(examIds);
        paperNewList.forEach(p -> {
            paperNewCache.set("NEW-"+p.getExamId()+p.getId(), p, TWO_DAYS);
        });

        // 按考试对应的试题实例缓存
        Arrays.stream(examIds).forEach(id -> {
        	List<QuestionCopy> questionCopys = questionCopyService.findQuestionCopysByExamId(id);
        	questionCache.set(id, questionCopys, TWO_DAYS);
        });

        //已经发布了，到了预加载时间的考试，不会走下面方法的逻辑
        examService.updateExamToPublished(Arrays.stream(examIds).collect(Collectors.toList()));


        LOGGER.info("考试ID：{}， 已预加载..", Arrays.stream(examIds).collect(Collectors.joining(",")));
		return paperList;
	}

	private String getEncryptContent(PaperInstance paper) throws Exception {
    	String key = getEncryptAnswerKey(paper.getExamId());
    	return questionCopyService.encryptAnswer(com.alibaba.fastjson.JSON.toJSONString(questionCopyService.getQuestionAnswerList(paper.getQuestions())), key);
    }


	private String getEncryptAnswerKey(String examId) {
		String key = cacheService.create(Exam.ENCRYPT_ANSWER_KEY_CACHE_KEY + "/" + examId, Exam.ANSWER_KEY_CACHE_TIME).get(examId, () -> {
			StringBuffer sb = new StringBuffer();
	    	Random r = new Random();
	    	for (int i = 0; i < 16; i++) {
				sb.append(r.nextInt(10));
			}
	    	return sb.toString();
		});
		return key;
	}


}
*/