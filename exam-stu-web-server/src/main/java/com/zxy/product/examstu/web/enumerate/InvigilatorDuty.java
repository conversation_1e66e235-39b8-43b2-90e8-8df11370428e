package com.zxy.product.examstu.web.enumerate;

public enum InvigilatorDuty {

    r1("根据报名表现场核对考生信息", 0), r2("宣读考场纪律", 0), r3("宣读考生注意事项：包括网大账号登录、IE批处理文件运行、IE兼容性及安全设置等", 0), r4("对本场内未参加过考试也没参加模拟考试的考生重点关注", 0),
    r5("为考试提供异常退出的考试验证码查询服务", 0),r6("根据监考手册对考生在考试过程中遇到的问题进行解决，如问题无法解决则按照规范在监考飞信群中反馈问题",0),
    r7("确认所有考生交卷后，完成监考老师填报表格并提交",0),
    r8("考试监考设备问题处理、人脸识别未通过处理",0)
    ;


    private String name ;
    private int value ;

    private InvigilatorDuty( String name , int value ){
        this.name = name ;
        this.value = value ;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public int getValue() {
        return value;
    }
    public void setValue(int value) {
        this.value = value;
    }
}
