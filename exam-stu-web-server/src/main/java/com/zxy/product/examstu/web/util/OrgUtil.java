package com.zxy.product.examstu.web.util;

import com.zxy.product.exam.entity.Question;
import com.zxy.product.exam.entity.QuestionDepot;
import com.zxy.product.system.jooq.tables.Collect;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class OrgUtil {

    public static List<com.zxy.product.system.entity.Organization> treeOrgProcess(List<com.zxy.product.system.entity.Organization> organizations, String parentId, String prex) {
        List<com.zxy.product.system.entity.Organization> result = new ArrayList<>();

        List<com.zxy.product.system.entity.Organization> list = organizations.stream()
                .filter(x -> {
                    if(parentId ==null) return x.getParentId() == null || !organizations.stream().anyMatch(y-> y.getId().equals(x.getParentId()));
                    return parentId.equals(x.getParentId());
                })
                .sorted((x, y) -> {
                    if (x.getOrder() == null) return 1;
                    if (y.getOrder() == null) return -1;
                    return x.getOrder() - y.getOrder();
                })
                .map(x -> {
                    if (prex != null) x.setName(prex + "->" + x.getName());
                    return x;
                })
                .collect(Collectors.toList());
        if (!list.isEmpty()) {
            list.forEach(x -> {
                result.add(x);
                result.addAll(treeOrgProcess(organizations, x.getId(), x.getName()));
            });
        }
        return result;
    }

    public static List<QuestionDepot> buildDepotTree(List<QuestionDepot> questionDepots, String parentId, String prex) {

        List<QuestionDepot> result = new ArrayList<>();

        List<QuestionDepot> list = questionDepots.stream()
                .filter(x -> {
                    if(parentId ==null) return x.getParentId() == null || !questionDepots.stream().anyMatch(y-> y.getId().equals(x.getParentId()));
                    return parentId.equals(x.getParentId());
                })
                .sorted((x, y) -> {
                    if (x.getCreateTime() == null) return 1;
                    if (y.getCreateTime() == null) return -1;
                    return (int)(x.getCreateTime() - y.getCreateTime());
                })
                .map(x -> {
                    if (prex != null) x.setName(prex + "->" + x.getName());
                    return x;
                })
                .collect(Collectors.toList());
        if (!list.isEmpty()) {
            list.forEach(x -> {
                result.add(x);
                result.addAll(buildDepotTree(questionDepots, x.getId(), x.getName()));
            });
        }
        return result;
    }
}
