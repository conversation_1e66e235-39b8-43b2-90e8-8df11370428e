package com.zxy.product.examstu.web.swagger;
/**
import com.fasterxml.classmate.TypeResolver;
import com.google.common.base.Optional;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.service.contexts.OperationContext;
import springfox.documentation.spring.web.readers.operation.AbstractOperationParameterRequestConditionReader;
import springfox.documentation.swagger.common.SwaggerPluginSupport;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;

@Component
@Order(SwaggerPluginSupport.SWAGGER_PLUGIN_ORDER + 10000)
public class ParamOperationReader extends AbstractOperationParameterRequestConditionReader {

    @Autowired
    private TypeResolver paramTypeResolver;

    public ParamOperationReader(TypeResolver resolver) {
        super(resolver);
    }

    @Override
    public void apply(OperationContext context) {
        Optional<Params> paramsAnnotation =  context.findAnnotation(Params.class);
        Optional<Param> paramAnnotation =  context.findAnnotation(Param.class);

        if (paramsAnnotation.isPresent()) {
            Params params = paramsAnnotation.get();
            List<Parameter> parameters = Arrays.stream(params.value()).map(param -> {
                Parameter parameter = createParameter(param);
                return parameter;
            }).collect(Collectors.toList());
            context.operationBuilder().parameters(parameters);
        }

        if (paramAnnotation.isPresent()) {
            Param param = paramAnnotation.get();
            List<Parameter> parameters = newArrayList();
            parameters.add(createParameter(param));
            context.operationBuilder().parameters(parameters);
        }
    }

    private Parameter createParameter(Param param) {
        Parameter parameter = (new ParameterBuilder())
                .name(param.name())
                .description(null)
                .defaultValue("")
                .required(param.required())
                .type(paramTypeResolver.resolve(param.type()))
                .modelRef(new ModelRef(param.type().toString()))
                .parameterType("param").build();
        return parameter;
    }
}
 */
