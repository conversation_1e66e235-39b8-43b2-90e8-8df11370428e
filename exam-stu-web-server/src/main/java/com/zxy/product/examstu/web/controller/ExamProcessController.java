package com.zxy.product.examstu.web.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.examstu.content.FlowLimitEnum;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.web.config.SwitchConfig;
import com.zxy.product.examstu.web.util.ExamFlowVerify;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zxy.product.exam.entity.Exam.CACHE_TIME;
import static com.zxy.product.examstu.content.ExamLimitConstants.*;
import static com.zxy.product.examstu.content.MessageHeaderContent.*;
import static com.zxy.product.examstu.content.MessageTypeContent.EXAM_DISTRIBUTE_LIMIT_RELEASE;
import static com.zxy.product.system.content.SwitchEnum.FlowLimitingSwitch;

@Controller
@RequestMapping("/exam-process")
public class ExamProcessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExamProcessController.class);

    private final static String AUDIENT_CACHE = "audient-cache";
    private final static String ENTER_EXAM_CODE_CACHE = "enter-exam-code-cache";
    private final static String EXAM_USERS_CACHE = "exam_users";
    public static final String EXAM_USER = "exam_user";

    private final static String FILTER_ANSWER_QUESTION_ATTR = "-1";
    private static final String RIGHT_ANSWER_CACHE = String.join("#", "exam-stu","paper", "right-answer-cache");

    private ExamService examService;

    private ExamRecordService examRecordService;

    private ExamStuRecordService examStuRecordService;

    private PaperInstanceService paperInstanceService;

    private AudienceObjectService audienceObjectService;

    private StrongBaseService strongBaseService;

    private SignUpService signUpService;

    private AnswerRecordProcessService answerRecordProcessService;

    private ExamPaperAttachmentService examPaperAttachmentService;

    private QuestionCopyService questionCopyService;

    private QuestionCopyStuService questionCopyStuService;

    private AnswerRecordService answerRecordService;

    private Cache examCache;

    private Cache examRecordCache;

    private Cache audienceCache;

    private Cache enterExamCodeCache;

    private Cache paperCache;

    private Cache questionCache;

    private Cache paperInstanceQuestionCopyCache;

    private MessageSender messageSender;

    private MessageSender examMessageSender;

    private Redis redis;

    private Cache examUserCache;

    private ExamFlowVerify examFlowVerify;

    private Cache onlineExamCache;

    private Cache examPaperRightAnswerCache;

    private MemberService memberService;

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.examCache = cacheService.create("cacheExam", "examEntity");
        this.examRecordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
        this.audienceCache = cacheService.create("cacheAudience", AUDIENT_CACHE);
        this.enterExamCodeCache = cacheService.create("cacheEnterExamCode", ENTER_EXAM_CODE_CACHE);
        this.paperCache = cacheService.create("cachePaperInstance", "paperInstanceEntity");
        this.questionCache = cacheService.create("cacheQuestionCopy", "questionCopyEntity");
        this.paperInstanceQuestionCopyCache = cacheService.create("cachePaperInstanceQuestionCopy", "paperInstanceQuestionCopyEntity");
        this.examUserCache = cacheService.create("cacheExamUser", EXAM_USERS_CACHE);
        this.onlineExamCache = cacheService.create( CacheKeyApplication,CacheKeyModule );
        this.examPaperRightAnswerCache = cacheService.create("exam-stu", "paper#" + "right-answer-cache");
    }

    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }

    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService) {
        this.examRecordService = examRecordService;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setAudienceObjectService(AudienceObjectService audienceObjectService) {
        this.audienceObjectService = audienceObjectService;
    }

    @Autowired
    public void setStrongBaseService(StrongBaseService strongBaseService) {
        this.strongBaseService = strongBaseService;
    }

    @Autowired
    public void setSignUpService(SignUpService signUpService) {
        this.signUpService = signUpService;
    }

    @Autowired
    public void setExamStuRecordService(ExamStuRecordService examStuRecordService) {
        this.examStuRecordService = examStuRecordService;
    }

    @Autowired
    public void setExamPaperAttachmentService(ExamPaperAttachmentService examPaperAttachmentService) {
        this.examPaperAttachmentService = examPaperAttachmentService;
    }

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    @Autowired
    public void setAnswerRecordProcessService(AnswerRecordProcessService answerRecordProcessService) {
        this.answerRecordProcessService = answerRecordProcessService;
    }

    @Autowired
    public void setQuestionCopyStuService(QuestionCopyStuService questionCopyStuService) {
        this.questionCopyStuService = questionCopyStuService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setExamMessageSender(MessageSender examMessageSender) {
        this.examMessageSender = examMessageSender;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Autowired
    public void setExamFlowVerify( ExamFlowVerify examFlowVerify ){ this.examFlowVerify=examFlowVerify; }

    @RequestMapping(value = "/enter-exam", method = RequestMethod.GET)
    @Param(name = "examId")
    @Param(name = "whetherContinue")
    @JSON("id,name,startTime,endTime,duration,passScore,type,examNotes,paperPath")
    @JSON("supportApp,isAllowSwitch,isShowAnswerImmed,indefinite")
    @JSON("allowSwitchTimes,allowExamTimes,paperSortRule,paperShowRule,showAnswerRule,isSetPersonalCode,isPermitViewCode,showScoreTime")
    @JSON("paper.(encryptContent,questionNum,totalScore,isSubjective)")
    @JSON("paper.questions.(id,type,content,parentId,score,sequence,difficulty)")
    @JSON("paper.questions.questionAttrCopys.(id,name,value,type)")
    @JSON("paper.questions.subs.(id,type,content,parentId,score,difficulty)")
    @JSON("paper.questions.subs.questionAttrCopys.(id,name,value,type)")
    @JSON("paper.answerRecords.(id,answer,score,isRight,questionId)")
    @JSON("paper.answerRecords.questionCopy.(type)")
    @JSON("examRecord.(id,startTime,endTime,isReset,lastCacheTime,lastSubmitTime,currentTime,status,orderContent,switchTimes,personalCode,paperInstanceId,faceStatus)")
    @JSON("examRecord.member.(id,name,fullName)")
    @JSON("faceMonitor,faceEnter,indefinite")
//    @Permitted()
    public Exam enterExam(RequestContext requestContext, Subject<Member> member){
        Integer examRegion = member.examRegion();

        LOGGER.error("进入考试，获取用户examRegion：{} , 用户id：{}" , examRegion,member.getCurrentUserId());
        Long st = System.currentTimeMillis();

        // 20250317限流方案，用户开始考试且因为浏览器异常退出，导致没有交卷，无法触发自减考试在线人数的操作
        Optional<Boolean> whetherContinueOpt = requestContext.getOptionalBoolean("whetherContinue");

        // 考试在线限流前置校验，若上述属性不存在，则校验限流，否则可以判定用户继续考试，不能调用限流校验
        if(!whetherContinueOpt.isPresent() && SwitchConfig.getSwitchStatus( FlowLimitingSwitch )){
            examFlowVerify.verify( examRegion, requestContext.getString("examId"), member.getCurrentUserId() );
        }


        //最新考试信息
        Exam newestExam = getExamFromCacheOrDB(examRegion, requestContext.getString("examId"));

        //校验是否允许进入考试
        validateAllowEnterExam(newestExam, st);

        //考试记录信息
        ExamRecord record = getExamRecordFromCacheOrDB(examRegion,newestExam, member, true);

        //试卷信息
        Optional<ExamPaperAttachment> examPaperAttachmentOpt = examPaperAttachmentService.getByPaperInstanceId(record.getPaperInstanceId());

        if (examPaperAttachmentOpt.isPresent()){
            newestExam.setPaperPath(examPaperAttachmentOpt.get().getPaperPath());
        }else{
            //试卷信息
            PaperInstance paper = getPaperByCache(examRegion,newestExam.getId(), record.getPaperInstanceId());
            //试题信息
            getQuestionsByCache(examRegion,paper, newestExam, record);
            newestExam.setPaper(paper);
        }

        //答题记录信息
        //getAnswerRecordsByExamRecordId(record);

        //如果用户在考试撤销之前已经拥有考试信息，就继续沿用自己的考试信息(对于正在进行中的考试记录不影响修改后的考试信息)
        Exam realReturnToUserExam = getRealReturnToUserExam(newestExam, record);
        realReturnToUserExam.setExamRecord(record);

        return realReturnToUserExam;
    }

    @RequestMapping(value = "/answer-submit", method = RequestMethod.POST)
    @Param(name = "answerRecord", required = true)
//    @Permitted()
    @JSON("*")
    public List<String> answerSubmit(RequestContext requestContext, Subject<Member> member){
        String answerRecordStr = requestContext.getString("answerRecord");

        Integer examRegion = member.examRegion();

        if (!StringUtils.isEmpty(answerRecordStr)) {

            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(answerRecordStr);

            JSONArray answerRecordsArray = jsonObject.getJSONArray("answerRecords");

            String examRecordId = jsonObject.getString("examRecordId");

            List<AnswerRecordProcess> answerRecordList = new ArrayList<>();

            for (int i = 0; i < answerRecordsArray.size(); i++) {
                AnswerRecordProcess answerRecord = answerRecordsArray.getObject(i, AnswerRecordProcess.class);
                answerRecord.setExamRecordId(examRecordId);
                answerRecordList.add(answerRecord);
            }

            if (!ObjectUtils.isEmpty(answerRecordList)) {
                return answerRecordProcessService.answerSubmit(examRegion, member.getCurrentUserId(), answerRecordList);
            }
        }
        return Collections.EMPTY_LIST;
    }

    @RequestMapping(value = "/answer-record-count", method = RequestMethod.GET)
    @Param(name = "examRecordId", required = true)
//    @Permitted()
    @JSON("*")
    public Integer answerRecordCount(RequestContext requestContext, Subject<Member> member){
        Integer examRegion = member.examRegion();
        String currentUserId = member.getCurrentUserId();
        String examRecordId = requestContext.getString("examRecordId");
        return answerRecordProcessService.answerRecordCount(examRegion, currentUserId, examRecordId);
    }

    @RequestMapping(value = "/full-submit", method = RequestMethod.POST)
    @Param(name = "fullAnswerRecord", required = true)
//    @Permitted()
    @JSON("*")
    public Integer fullSubmit(RequestContext requestContext, Subject<Member> member){
        Integer examRegion = member.examRegion();
        String currentUserId = member.getCurrentUserId();
        String fullAnswerRecordStr = requestContext.getString("fullAnswerRecord");

        if (!StringUtils.isEmpty(fullAnswerRecordStr)) {

            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(fullAnswerRecordStr);

            JSONArray answerRecordsArray = jsonObject.getJSONArray("answerRecords");

            String examRecordId = jsonObject.getString("examRecordId");

            List<AnswerRecordProcess> fullAnswerRecordList = new ArrayList<>();

            for (int i = 0; i < answerRecordsArray.size(); i++) {
                AnswerRecordProcess answerRecord = answerRecordsArray.getObject(i, AnswerRecordProcess.class);
                answerRecord.setExamRecordId(examRecordId);
                fullAnswerRecordList.add(answerRecord);
            }

            if (!ObjectUtils.isEmpty(fullAnswerRecordList)) {
              return answerRecordProcessService.fullAnswerSubmit(examRegion, currentUserId, examRecordId, fullAnswerRecordList);
            }
        }
        return 0;
    }

    @RequestMapping(value = "/score", method = RequestMethod.GET)
    @Param(name = "examRecordId", required = true)
    @Param(name = "examId", required = true)
//    @Permitted()
    @JSON("encryptScore")
    public ExamRecord getScore(RequestContext requestContext, Subject<Member> member){
        String examId = requestContext.get("examId", String.class);
        String examRecordId = requestContext.get("examRecordId", String.class);
        String currentUserId = member.getCurrentUserId();
        Integer examRegion = member.examRegion();

        ExamRecord examRecord = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examId, currentUserId), ExamRecord.class);

        if (examRecord == null) {
            examRecord = examRecordService.getExamRecordSubmitTime(examRegion, currentUserId, examId);
        }

        if (examRecord.getSubmitTime() == null) {
            examRecord.setEncryptScore(null);
            return examRecord;
        }

        AtomicInteger totalScore = new AtomicInteger(0);

        List<AnswerRecordProcess> dbAnswerRecordProcessList = answerRecordProcessService.getListByExamRecordId(examRegion,currentUserId,
                                                                                                               examRecordId,null);
        Map<String, QuestionCopy> rightAnswer = getRightAnswer(examRegion, currentUserId, examId);

        if (!CollectionUtils.isEmpty(dbAnswerRecordProcessList)) {
            dbAnswerRecordProcessList.stream().forEach(answerRecordProces -> {
                if (rightAnswer.containsKey(answerRecordProces.getQuestionId())) {
                        QuestionCopy questionCopy = rightAnswer.get(answerRecordProces.getQuestionId());

                    if (questionCopy != null && questionCopy.getType() != Question.QUESTION_ANWSER
                            && questionCopy.getType() != Question.READING_COMPREHENSION) {
                        List<QuestionAttrCopy> questionAttrCopys = questionCopy.getQuestionAttrCopys();

                        boolean isRight = compareAnswerInfo(questionAttrCopys, answerRecordProces.getAnswer(), questionCopy.getType());

                        if (isRight){
                            totalScore.addAndGet(questionCopy.getScore());
                        }
                    }
                }
            });
        }
        examRecord.setEncryptScore(aesEncryptScore(examId, examRecordId, totalScore.get()));

        return examRecord;
    }

    @RequestMapping(value = "/submit-paper", method = RequestMethod.POST)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId", required = true)
    @Param(name = "submitType", required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @Param(name = "lastCacheTime", type = Long.class)
    @Param(name = "noAnswerCount", type = Integer.class)
    @Param(name = "answeredCount", type = Integer.class)
    @Param(name = "submitDetailType", type = Integer.class)
    @Param(name = "clientVersion")
    @JSON("*")
//    @Permitted()
    public Map<String, Object> submitPaper(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        String currentUserId = subject.getCurrentUserId();
        String examRecordId = requestContext.get("examRecordId", String.class);
        String examId = requestContext.get("examId", String.class);

        //限流限频：释放考试在线 手动交卷
        sendMsgRelease( examRegion, examId, currentUserId );

        ExamRecord examRecord = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examId, currentUserId), ExamRecord.class);

        if (examRecord == null) {
            String cachekey = ExamRecord.getExamRecordKey(examId, currentUserId);
            examRecord = examRecordService.getSimple(examRegion, examRecordId, examId);
            examRecordCache.set(cachekey, examRecord, Exam.CACHE_TIME);
        }

        // 防止手动提交回调过程中再次临时提交，判断submittime非空，直接返回，这里前端也做了相应处理判断
        if (examRecord.getSubmitTime() != null) {
            return ImmutableMap.of("status", 1, "msg", "success");
        }

        Long currentTime = System.currentTimeMillis();
        beforeSubmit(examRegion, examRecord, requestContext.getString("submitType"),
                     requestContext.getOptional("lastCacheTime", Long.class).orElse(System.currentTimeMillis()), currentTime);

        messageSender.send(
                MessageTypeContent.SUBMIT_PAPER_STU,
                ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID, requestContext.getString("examRecordId"),
                ExamRecord.SUBMIT_PAPER_TYPE, requestContext.getString("submitType"),
                ExamRecord.SUBMIT_PAPER_CLIENT_TYPE, String.valueOf(requestContext.getInteger("clientType")),
                ExamRecord.SUBMIT_PAPER_TIME, String.valueOf(currentTime),
                ExamRecord.SUBMIT_USER_IP, getIpAddr(requestContext.getRequest()),
                ExamRecord.SUBMIT_PAPER_NO_ANSWER_COUNT,
                String.valueOf(requestContext.getOptionalInteger("noAnswerCount").orElse(0)),
                ExamRecord.SUBMIT_PAPER_ANSWERED_COUNT,
                String.valueOf(requestContext.getOptionalInteger("answeredCount").orElse(0)),
                ExamRecord.SUBMIT_DETAIL_TYPE,
                String.valueOf(requestContext.getOptionalInteger("submitDetailType").orElse(ExamRecord.SUBMIT_TYPE_HAND)),
                ExamRecord.SUBMIT_CLIENT_VERSION,
                requestContext.getOptionalString("clientVersion").orElse(""),
                ExamRecord.SUBMIT_PAPER_EXAM_ID, examId,
                MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
                MessageHeaderContent.MEMBER_ID,currentUserId);


        LOGGER.info("submitPaper, examRecordId:{}", examRecord.getId());


        return ImmutableMap.of("status", 1, "msg", "success");

    }

    /**
     * 限流限频：考试在线释放流水
     * @param region 用户所属数据库
     * @param examId 考试Id
     * @param memberId 用户Id
     */
    private void sendMsgRelease( Integer region, String examId, String memberId ){
        if(SwitchConfig.getSwitchStatus(FlowLimitingSwitch)){
            String cacheKey = CacheKeyOnline + FlowLimitEnum.ExamSubmit.getBusinessType();
            Long examNum = onlineExamCache.increment(cacheKey, 0L);
            examNum = examNum > 0L ? onlineExamCache.increment( cacheKey, -1L ) : 0L;
            messageSender.send( EXAM_DISTRIBUTE_LIMIT_RELEASE,
                    EXAM_ONLINE_REGION, String.valueOf(region), EXAM_ONLINE_MEMBER_ID, memberId,
                    EXAM_ONLINE_BUSINESS_ID, examId );
            LOGGER.info("考试在线释放流水，当前考试Id{}，用户Id{}，缓存值{}", examId, memberId, examNum);
        }
    }

    /**
     * 考试答题记录信息
     * @param requestContext
     * @param member
     * @return
     */
    @RequestMapping(value = "/answer-record", method = RequestMethod.GET)
    @Param(name = "examId",required = true)
    @Param(name = "examRecordId",required = true)
    @JSON("id,answer,score,isRight,questionId")
    @JSON("questionCopy.(type)")
//    @Permitted()
    public List<AnswerRecord> getAnswerRecords (RequestContext requestContext, Subject<Member> member) {
        String examId = requestContext.getString("examId");
        String examRecordId = requestContext.getString("examRecordId");
        Integer examRegion = member.examRegion();
        return answerRecordService.findByExamRecordId(examRegion,examId, examRecordId);
    }



    /**
     * 强制交卷
     * 1.客户端打开，客户端接受ws消息，提示交卷成功
     * 2.客户端关闭，判断session不存在，直接把临时答题记录进行计算
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/force-submit", method = RequestMethod.POST)
    @Param(name = "userId", type = String.class, required = true)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "memberName", type = String.class)
    @Param(name = "examName", type = String.class)
    @Param(name = "force", type = Integer.class)
    @JSON("*")
    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.MANAGE, fisrtAction = "管理-考生管理", secondAction = "强制交卷", desc = "对{0}操作强制交卷于考试《{1}》", params = {"memberName", "examName"})
    public Map<String, Object> forceSubmitPaper(RequestContext requestContext,Subject<Member> subject) {

        String examId = requestContext.getString("examId");
        String userId = requestContext.getString("userId");

        Integer examRegion = memberService.findExamRegion(userId);

        // 限流限频：释放考试流水 管理员强制交卷
        sendMsgRelease( examRegion, examId, userId );

        Optional<Integer> force = requestContext.getOptionalInteger("force");
        if (!force.isPresent() && !hadCacheSession(ExamRecord.getExamRecordKey(examId, userId))) {
            return ImmutableMap.of("status", "0");
        }

        //如果force为1，进行离线强制交卷(把临时提交的答案算分)
        force.map(f -> {
            offLineForceSubmitPaper(examRegion,examId, userId);
            return true;
        }).orElseGet(() -> {
            //异步进行在线强制交卷
            examMessageSender.send(
                    MessageTypeContent.EXAM_EXAM_RECORD_SUBMIT,
                    MessageHeaderContent.IDS, requestContext.getString("userId"),
                    MessageHeaderContent.EXAM_ID, requestContext.getString("examId"));
            return true;
        });
        return ImmutableMap.of("status", "1");
    }



    @RequestMapping(value = "/exam-record", method = RequestMethod.POST)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId", required = true)
    @Param(name = "noAnswerCount", type = Integer.class, required = true)
    @Param(name = "answeredCount", type = Integer.class, required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @Param(name = "clientVersion")
    @JSON("*")
//    @Permitted()
    public Integer updateExamRecord(RequestContext requestContext, Subject<Member> subject) {
        String examId = requestContext.getString("examId");
        String examRecordId = requestContext.getString("examRecordId");
        Integer noAnswerCount = requestContext.getInteger("noAnswerCount");
        Integer answeredCount = requestContext.getInteger("answeredCount");
        Integer clientType = requestContext.getInteger("clientType");
        String clientVersion = requestContext.getOptionalString("clientVersion").orElse("");
        String ipAddr = getIpAddr(requestContext.getRequest());

        Integer examRegion = subject.examRegion();
        String memberId = subject.getCurrentUserId();
        long lastSubmitTime = System.currentTimeMillis();

        Integer result = examStuRecordService.updateExamRecord(examRegion, examId, examRecordId, lastSubmitTime, noAnswerCount, answeredCount, clientType, clientVersion, ipAddr);

       resetCacheTime(examId, memberId, result, lastSubmitTime, noAnswerCount, answeredCount, clientType, clientVersion, ipAddr);

        return result;
    }

    private void resetCacheTime(String examId, String memberId, Integer result, long lastSubmitTime, Integer noAnswerCount, Integer answeredCount, Integer clientType, String clientVersion, String ipAddr) {
        ExamRecord record = examRecordCache.get(ExamRecord.getExamRecordKey(examId, memberId), ExamRecord.class);
        if (record != null) {
            record.setLastSubmitTime(lastSubmitTime);
            record.setNoAnswerCount(noAnswerCount);
            record.setAnsweredCount(answeredCount);
            record.setClientType(clientType);
            record.setClientVersion(clientVersion);
            record.setUserIp(ipAddr);
            examRecordCache.set(ExamRecord.getExamRecordKey(examId, memberId), record, Exam.CACHE_TIME);
        }
    }

    /**
     * 考试答题记录信息
     * @param requestContext
     * @param member
     * @return
     */
    @RequestMapping(value = "/answer-process", method = RequestMethod.GET)
    @Param(name = "examRecordId",required = true)
    @JSON("answer,questionId")
    @JSON("questionCopy.(type)")
//    @Permitted()
    public List<AnswerRecordProcess> getAnswerRecordProcess (RequestContext requestContext, Subject<Member> member) {
        String examRecordId = requestContext.getString("examRecordId");
        String memberId = member.getCurrentUserId();
        Integer examRegion = member.examRegion();
        return answerRecordProcessService.getListByExamRecordId(examRegion,memberId, examRecordId, null);
    }


    private boolean hadCacheSession(String key) {
        String value = examUserCache.get(EXAM_USER + "_" + key, String.class);
        return value != null;
    }

    /**
     * 判断离线状态后，直接强制交卷
     * @param examId
     * @param userId
     */
    private void offLineForceSubmitPaper(Integer examRegion,String examId, String userId) {
        examRecordCache.clear(ExamRecord.getExamRecordKey(examId, userId));
        LOGGER.info("强制交卷，临时试题算分：exmId:{}, userId:{}", examId, userId);
        examStuRecordService.normalForceSubmitPaper(examRegion,examId, userId);
    }

    private String aesEncryptScore(String examId, String examRecordId, Integer totalScore) {
        long time = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        sb.append(examId).append("+").append(examRecordId).append("+").append(time).append("+").append("score:").append(totalScore);
        try {
            return Encrypt.aesEncrypt(sb.toString(), "d8cg8gVakEq9Agup");
        } catch (Exception e) {
            LOGGER.info("submitPaper-getScore, error:{}",e);
        }
        return null;
    }

    private Map<String, QuestionCopy> getRightAnswer(Integer examRegion, String memberId, String examId) {
//        String cacheKey = RIGHT_ANSWER_CACHE + "#" + examId;
//        return Optional.ofNullable(redis.process(jedis -> jedis.get(cacheKey)))
//                       .map(data -> {
//                           try {
//                               return (Map) new ObjectMapper().readValue(data, new TypeReference<Map<String, QuestionCopy>>() {
//                               });
//                           } catch (IOException e) {
//                               e.printStackTrace();
//                           }
//                           return new HashMap<String, QuestionCopy>();
//                       })
//                       .orElseGet(() -> {
//                          return setCache(examRegion, memberId, examId, cacheKey);
//                       });
        return examPaperRightAnswerCache.get(examId, () -> {
            return questionCopyStuService.getRightAnswer(examRegion, memberId, examId);
        }, CACHE_TIME);
    }

    private Map<String, QuestionCopy> setCache(Integer examRegion, String memberId, String examId, String cacheKey) {
        Map<String, QuestionCopy> rightAnswer = questionCopyStuService.getRightAnswer(examRegion, memberId, examId);
        // 将数据存入Redis缓存
        redis.process(jedis -> {
            try {
                String jsonString =  new ObjectMapper().writeValueAsString(rightAnswer);
                jedis.set(cacheKey, jsonString, "NX", "EX", 60 * 60 * 24 * 2);
            } catch (JsonProcessingException e) {
                LOGGER.error("Failed to set cache for examId: {}", examId, e);
            }
            return null;
        });
        return rightAnswer;
    }


    private void validateAllowEnterExam(Exam newestExam, Long st) {
        //考试未开始，不能进入
        if (newestExam.getStartTime() != null && st < newestExam.getStartTime()) {
            throw new UnprocessableException(ErrorCode.ExamNoStart);
        }
        if (Exam.STATUS_NOT_PUBLISH == newestExam.getStatus() || Exam.STATUS_PUBLISHING == newestExam.getStatus()) {
            throw new UnprocessableException(ErrorCode.ExamNoStart);
        }
        //考试已结束，不能进入
        if (newestExam.getEndTime() != null && newestExam.getDuration() != null
                && st > (newestExam.getEndTime() + (newestExam.getDuration() * 60000))) {
            throw new UnprocessableException(ErrorCode.ExamIsOver);
        }
    }

    private Exam getExamFromCacheOrDB(Integer examRegion, String examId) {
        Exam exam = new Exam();
        try {
            exam = examCache.get(examId, () -> {
                return Optional.ofNullable(
                                       examService.getSimpleData(examRegion, examId))
                               .orElseThrow(() ->new UnprocessableException(ErrorCode.ExamNullError));
            }, CACHE_TIME);
        } catch (Exception e) {
            examCache.clear(examId);
            exam = examCache.get(examId, () -> {
                return Optional.ofNullable(examService.getSimpleData(examRegion, examId))
                               .orElseThrow(() ->new UnprocessableException(ErrorCode.ExamNullError));}, CACHE_TIME);
        }
        return exam;
    }

    private ExamRecord getExamRecordFromCacheOrDB(Integer examRegion,Exam exam, Subject<Member> member, boolean checkCode) {
        ExamRecord record = examRecordCache.get(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()), () -> {

            ExamRecord er = examStuRecordService.getNewestRecord(member.examRegion(), exam.getId(), member.getCurrentUserId());

            if (isOtherModuleExam(exam)) {
                return getOtherModuleExamRecord(examRegion, er, exam, member.getCurrentUserId());
            }

            return validateExamRecord(examRegion,exam, er, member.getCurrentUserId());

        }, CACHE_TIME);

        //每次提交试卷，考试记录不会清除缓存，考试记录的提交试卷会更新
        //多次考的时候，再次考试，先从缓存获取考试记录，判断提交时间是否已更新

        // 线上出现examRecord的status为null的情况，在此增加判断，并清空缓存，让用户重新进入
        if (record == null || record.getStatus() == null) {
            examRecordCache.clear(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()));
            throw new UnprocessableException(ErrorCode.ExamRecordStatusRequired);
        }

        if (alreadySubmited(record)) {

            record = validateByCacheRecord(examRegion,exam, record, member);

            examRecordCache.set(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()), record, CACHE_TIME);
        }

        if (checkCode && !isOtherModuleExam(exam) && needCheckCode(exam)  && !checkCodeFromCache(exam.getId(), member.getCurrentUserId())) {
            //由于密码错误进不去，必须更新以前记录为非当前记录
            examRecordService.updateBeforeRecordBeNoCurrent(examRegion, record);
            throw new UnprocessableException(ErrorCode.ExamPasswordOrPersonalCodeError);
        }

        //指定考试，报名考试，手动添加考生
        //需要更新开始时间，结束时间，试题排序等字段，个人密码
        // checkCode 为true,代表是在enterExam方法调用该获取考试记录的方法，为false, 其他方法调用，有一些方法不需再执行多一次
        if (checkCode) {
            updateExamRecordFieldValue(examRegion,record, exam);
        }
        return record;
    }

    private boolean isOtherModuleExam(Exam exam) {
        return exam.getSourceType() != Exam.EXAM_ACTIVITY_SOURCE_TYPE;
    }

    private ExamRecord getOtherModuleExamRecord(Integer examRegion,ExamRecord er, Exam exam, String memberId) {
        if (er == null) return createNewExamRecord(examRegion,er, exam, memberId);
        return validateOtherModuleExamReocrd(examRegion,er, exam, memberId);
    }

    private ExamRecord validateOtherModuleExamReocrd(Integer examRegion,ExamRecord examRecord, Exam exam, String memberId) {
        if (otherModuleExamMore(examRegion, examRecord, exam, memberId)) return createNewExamRecord(examRegion,examRecord, exam, memberId);
        return examRecord;
    }

    private boolean otherModuleExamMore(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
        if (exam.getCreateTime() != null && exam.getCreateTime() > 1541001600000l
                && exam.getAllowExamTimes() != null) {
            //判断是否超出考试次数限制
            if (validateOverLimitExamTimes(examRegion, er, exam, memberId)) {
                return true;
            }
            return false;
        }
        return exam.getIsOverByPassExam() != null
                && Exam.IS_OVER_BY_PASS_EXAM == exam.getIsOverByPassExam()
                && er.getStatus() == ExamRecord.STATUS_NOT_PASS;
    }

    private boolean validateOverLimitExamTimes(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
        if (er.getStatus() >= ExamRecord.STATUS_TO_BE_OVER || er.getSubmitTime() != null) {
            //是否能再次考试
            if (isCanExamAgain(examRegion, exam, er, memberId)) {
                return true;
            }
            throw new ValidationException(ErrorCode.CanNotExamMore);
        }
        return false;
    }

    private boolean isCanExamAgain(Integer examRegion, Exam exam, ExamRecord er, String memberId) {

        // 如果考试是强基计划认证专区用的，判断考试组的次数是否满足
        if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1 == exam.getStrongBaseFlag()) {
            // 只有考试组中是认证考试类型并且已发布才看是否满足考试组的次数
            if (strongBaseService.authType(examRegion, exam.getId())) {
                // 查询是否允许再次考试
                return strongBaseService.examAgain(examRegion, exam.getId(), memberId);
            }
        }

        int examedTime = Optional.ofNullable(er.getExamTimes()).orElseGet(() -> {
            return examRecordService.calculateExamTimes(examRegion, exam.getId(), er.getMemberId());
        });
        return exam.getAllowExamTimes() == Exam.NO_LIMIT_TIME
                || (exam.getAllowExamTimes() != Exam.NO_LIMIT_TIME && exam.getAllowExamTimes() > examedTime);
    }
    private ExamRecord createNewExamRecord(Integer examRegion,ExamRecord examRecord, Exam exam, String memberId) {

        Long startTime = System.currentTimeMillis();
        Long endTime = System.currentTimeMillis() + (1000 * 60 * exam.getDuration());
        if (Exam.RANDOM_TYPE_TWO.equals(exam.getRandomType())) {
            // 其他模块，随机组卷，多次试卷随机，生成examRecord时，随机抽取一张试卷
            if (examRecord != null) {
                examRecord.setPaperInstanceId(paperInstanceService.getWithRandomByExamId(examRegion, exam.getId()).getId());
            }
        }
        ExamRecord newExamRecord = examStuRecordService.insert(examRegion,
                                                               exam.getId(), getPaperInstanceId(examRegion, examRecord, exam), memberId, ExamRecord.STATUS_DOING,
                                                               Optional.ofNullable(startTime), Optional.ofNullable(endTime),
                                                               Optional.ofNullable(exam.getPaperSortRule()), Optional.ofNullable(getExamRecordExamTimes(examRecord)), getPersonalCode(exam));
        return newExamRecord;
    }

    private ExamRecord validateExamRecord(Integer examRegion,Exam exam, ExamRecord examRecord, String memberId) {
        // 撤销
        validateExamCanceled(exam);

        //是否受众范围内
        validateAudience(examRegion, examRecord, exam, memberId);

        //考试记录为空的场景判断
        if (validateExamRecordForNull(examRegion, examRecord, exam, memberId)) {
            return createNewExamRecord(examRegion,examRecord, exam, memberId);
        }

        //是否超时
        validateOverTime(examRecord, exam);

        //判断是否超出考试次数限制
        if (validateOverLimitExamTimes(examRegion, examRecord, exam, memberId)) {
            return createNewExamRecord(examRegion,examRecord, exam, memberId);
        }

        return examRecord;
    }

    private void validateExamCanceled(Exam exam) {
        if (exam.getStatus() == Exam.STATUS_NOT_PUBLISH)
            throw new UnprocessableException(ErrorCode.ExamStatusError);
    }

    private void validateAudience(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
        if (!noNeedCheckIsAuient(er) && !isAudient(examRegion, exam.getId(), memberId))
            throw new UnprocessableException(ErrorCode.IsNotAudient);
    }

    private boolean noNeedCheckIsAuient(ExamRecord er) {
        return er != null && er.getStatus() >= ExamRecord.STATUS_DOING &&
                er.getStatus() <= ExamRecord.STATUS_TIME_EXCEPTION;
    }

    private boolean isAudient(Integer examRegion, String examId, String memberId) {
        Map<String, String> audientMap = audienceCache.get(examId, () -> {
            return new HashMap<>();
        }, CACHE_TIME);
        if (audientMap.get(memberId) != null) return true;

        boolean isAudient = audienceObjectService.isAudient(examRegion, examId, memberId);
        if (isAudient) {
            audientMap.put(memberId, "true");
            audienceCache.set(examId, audientMap, CACHE_TIME);
        }
        return isAudient;
    }

    private boolean alreadySubmited(ExamRecord record) {
        return record.getSubmitTime() != null
                || record.getStatus() >= ExamRecord.STATUS_TO_BE_OVER;
    }

    private ExamRecord validateByCacheRecord(Integer examRegion,Exam exam, ExamRecord record, Subject<Member> member) {
        if (isOtherModuleExam(exam)) return getOtherModuleExamRecord(examRegion,record, exam, member.getCurrentUserId());
        return validateExamRecord(examRegion,exam, record, member.getCurrentUserId());
    }

    private boolean needCheckCode(Exam exam) {
        return (exam.getIsSetPersonalCode() != null && exam.getIsSetPersonalCode() == Exam.EXAM_YES)
                || (exam.getIsSetPassword() != null && exam.getIsSetPassword() == Exam.EXAM_YES);
    }

    private boolean checkCodeFromCache(String examId, String memberId) {
        String value = enterExamCodeCache.get(ExamRecord.getExamRecordKey(examId, memberId), String.class);
        if (value != null && value.equals("1")) {
            enterExamCodeCache.clear(ExamRecord.getExamRecordKey(examId, memberId));
            return true;
        }
        return false;
    }

    private void updateExamRecordFieldValue(Integer examRegion,ExamRecord record, Exam exam) {
        Optional.ofNullable(record.getStartTime()).orElseGet(() -> {

            record.setStatus(ExamRecord.STATUS_DOING);
            record.setStartTime(System.currentTimeMillis());

            Optional.ofNullable(record.getEndTime()).orElseGet(() -> {
                record.setEndTime(System.currentTimeMillis() + (1000 * 60 * exam.getDuration()));
                return record.getEndTime();
            });

            Optional.ofNullable(record.getOrderContent()).orElseGet(() -> {
                String orderContent = paperInstanceService.createNewQuestionOrder(examRegion,
                        Optional.empty(), record.getPaperInstanceId(), exam.getPaperSortRule());
                record.setOrderContent(orderContent);
                return record.getOrderContent();
            });

            Optional<Integer> personalCode = getPersonalCode(exam);

            examStuRecordService.update(examRegion, record.getId(),
                                          Optional.of(record.getStartTime()), Optional.of(record.getEndTime()),
                                          Optional.of(ExamRecord.STATUS_DOING), Optional.empty(), null, Optional.empty(),
                                          null, Optional.empty(), Optional.ofNullable(record.getOrderContent()), personalCode, exam.getId());

            messageSender.send(MessageTypeContent.EXAM_RECORD_UPDATE_STARTING,
                               ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID, record.getId(),
                               MessageHeaderContent.EXAM_ID, exam.getId(),
                               MessageHeaderContent.MEMBER_ID, record.getMemberId());

            record.setPersonalCode(personalCode.orElse(null));
            return record.getStartTime();
        });

        //其他业务模块的考试每次进入考试都要发出消息，监听进度
        sendOtherModuleExamRecordUpdateStatus(exam.getSourceType(), record.getId(), exam.getId());

        //currentTime:当前服务器的时间
        //第一次请求考试信息的时候，以startTime为currentTime
        //之后如果用户刷新试卷信息，直接返回服务器当前时间
        Optional.ofNullable(record.getStartTime()).map(startTime -> {
            Optional.ofNullable(record.getCurrentTime()).map(ct -> {
                record.setCurrentTime(System.currentTimeMillis());
                return ct;
            }).orElseGet(() -> {
                record.setCurrentTime(record.getStartTime());
                return record.getCurrentTime();
            });
            return startTime;
        }).orElseGet(() -> {
            record.setCurrentTime(System.currentTimeMillis());
            return record.getCurrentTime();
        });

        if (!isOtherModuleExam(exam) && record.getStartTime() != null) {
            Optional<Integer> personalCode = getPersonalCode(exam);
            examStuRecordService.update(examRegion, record.getId(),
                                          Optional.empty(), Optional.empty(),
                                          Optional.empty(), Optional.empty(), null, Optional.empty(),
                                          null, Optional.empty(), Optional.empty(), personalCode, exam.getId());
            record.setPersonalCode(personalCode.orElse(null));
        }

        //存储缓存
        record.setExamId(exam.getId());
        examRecordCache.set(ExamRecord.getExamRecordKey(
                record.getExamId(), record.getMemberId()), record, CACHE_TIME);
    }

    private String getPaperInstanceId(Integer examRegion, ExamRecord examRecord, Exam exam) {
        return Optional.ofNullable(examRecord).map(e -> {
            return e.getPaperInstanceId();
        }).orElseGet(() -> {
            return paperInstanceService.getWithRandomByExamId(examRegion, exam.getId()).getId();
        });
    }

    private Integer getExamRecordExamTimes(ExamRecord examRecord) {
        return Optional.ofNullable(examRecord).map(e -> {
            return e.getExamTimes();
        }).orElse(null);
    }

    private boolean validateExamRecordForNull(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
        return Optional.ofNullable(er).map(e -> false).orElseGet(() -> {
            //指定考试
            if (specifyExam(exam))
                throw new UnprocessableException(ErrorCode.IsNotAudient);
            // 报名考试验证
            if (!applicantExamNeedApproving(examRegion, exam, memberId, er))
                throw new UnprocessableException(ErrorCode.ExamApplicantNoPass);

            return true;
        });
    }

    private void validateOverTime(ExamRecord er, Exam exam) {
        Long nowtime = System.currentTimeMillis();
        if (er.getIsReset() == null || er.getIsReset() != ExamRecord.IS_RESET) {
            if (exam.getSourceType() == Exam.EXAM_ACTIVITY_SOURCE_TYPE) {
                if (!validateExamTimeRange(nowtime, exam, er)) {
                    throw new ValidationException(ErrorCode.OverExam);
                }
            }
        }
    }

    private Optional<Integer> getPersonalCode(Exam exam) {
        if (!isOtherModuleExam(exam)
                && (exam.getIsSetPersonalCode() != null
                && exam.getIsSetPersonalCode() == Exam.EXAM_YES)) {
            return randomThreeCode();
        }
        return Optional.empty();
    }

    private void sendOtherModuleExamRecordUpdateStatus(Integer sourceType, String examRecordId, String examId) {
        if (sourceType != Exam.EXAM_ACTIVITY_SOURCE_TYPE)
            messageSender.send(
                    MessageTypeContent.OTHER_MODULE_EXAM_RECORD_UPDATE_STATUS,
                    MessageHeaderContent.ID, examRecordId,
                    MessageHeaderContent.EXAM_ID,examId);
    }

    private boolean specifyExam(Exam exam) {
        return exam.getSendToCenter() != null && exam.getSendToCenter() == Exam.EXAM_YES;
    }

    private boolean validateExamTimeRange(Long nowTime, Exam exam, ExamRecord er) {
        if (er.getStartTime() == null) {
            return exam.getStartTime() <= nowTime && exam.getEndTime() >= nowTime;
        }
        return exam.getStartTime() <= nowTime;
    }

    private boolean applicantExamNeedApproving(Integer examRegion, Exam exam, String memberId, ExamRecord examRecord) {
        if (exam.getNeedApplicant() == Exam.EXAM_YES) {
            return signUpService.getOptional(examRegion, exam.getId(), memberId).map(t -> {
                return t.getStatus() != null && t.getStatus().intValue() == SignUp.STATUS_PASSED;
            }).orElse(false);
        }
        return true;
    }

    private Optional<Integer> randomThreeCode() {
        List<String> str = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            Integer temp = new Random().nextInt(9);
            temp = temp == 0 ? (temp + 1) : temp;
            str.add(String.valueOf(temp));
        }
        return Optional.of(Integer.valueOf(str.stream().collect(Collectors.joining(""))));
    }

    private PaperInstance getPaperByCache(Integer examRegion,String examId, String paperInstanceId) {
       PaperInstance paper = paperCache.get(examId + paperInstanceId, () -> {
            return paperInstanceService.get(examRegion, paperInstanceId, examId);
        }, CACHE_TIME);
        return paper;
    }

    private void getQuestionsByCache(Integer examRegion,PaperInstance paper, Exam exam, ExamRecord record) {
        packQuestionsFromCacheOrDBToPaper(examRegion,paper, exam);
        if (paper.getQuestions().isEmpty()) findQuestionsByPaperId(examRegion,paper, record, exam.getId());
        updateQuestionAnswerToNegativeOne(paper);
    }

    private void packQuestionsFromCacheOrDBToPaper(Integer examRegion,PaperInstance paper, Exam exam) {

        //从缓存或数据库查询该考试对应的所有题目
        List<QuestionCopy> questionCopys = questionCache.get(exam.getId(), () -> {
            return questionCopyService.findQuestionCopysByExamId(exam.getId());
        }, CACHE_TIME);
        //试题映射
        Map<String, QuestionCopy> questionCopyMap = questionCopys.stream()
                                                                 .collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
        //子题目映射
        Map<String, List<QuestionCopy>> subsMap = questionCopys.stream()
                                                               .filter(f -> f.getParentId() != null)
                                                               .collect(Collectors.groupingBy(QuestionCopy::getParentId));

        //根据paperId查出试卷试题的顺序(随机组卷使用)
        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceQuestionCopyCache.get(paper.getId(), () -> {
            return paperInstanceService.findPaperInstanceQuestionCopiesByPaperId(examRegion, paper.getId(), exam.getId());
        }, CACHE_TIME);

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopiesMap = paperInstanceQuestionCopies.stream().collect(
                Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

        //根据paper持有的paperQuestionCopyIds组装对应的试题
        List<QuestionCopy> paperQuestions = paper.getQuestionCopyIds().stream().map(id -> {
            QuestionCopy questionCopy = questionCopyMap.get(id);
            //设置对应的默认试题顺序
            if (questionCopy != null && paperInstanceQuestionCopiesMap.get(questionCopy.getId()) != null)
                questionCopy.setSequence(paperInstanceQuestionCopiesMap.get(questionCopy.getId()).getSequence());
            return questionCopy;
        }).collect(Collectors.toList());

        // 组装阅读题
        try {
            if (paperQuestions != null && paperQuestions.size() > 0)
                paperQuestions.stream().filter(f -> f !=null && Question.READING_COMPREHENSION == f.getType()).forEach(q -> {
                    if (subsMap != null) {
                        List<QuestionCopy> subs = subsMap.get(q.getId());
                        if (subs != null) {
                            subs.forEach(s -> {
                                if (paperInstanceQuestionCopiesMap.get(s.getId()) != null)
                                    s.setSequence(paperInstanceQuestionCopiesMap.get(s.getId()).getSequence());
                            });
                        }
                        if (q!=null){
                            q.setSubs(subs);
                        }
                    }
                });
            Collections.sort(paperQuestions);
            paper.setQuestions(new ArrayList<>());
            paper.getQuestions().addAll(paperQuestions);
        } catch (Exception e2) {
            e2.printStackTrace();
        }
    }

    private void findQuestionsByPaperId(Integer examRegion,PaperInstance paper, ExamRecord record, String examId) {
        List<QuestionCopy> questions = questionCopyService.findQuestionsByPaperId(examRegion,
                paper.getId(), record.getId(), examId);
        Collections.sort(questions);
        paper.getQuestions().addAll(questions);
    }

    private void updateQuestionAnswerToNegativeOne(PaperInstance paper) {
        paper.getQuestions().stream().forEach(q -> {
            filterQuestionAttrWithNoAnswer(q);
        });
    }

    /**
     * 过滤  答案属性
     * 把答案都变为-1
     * 1.单选，多选，把questionAttrCopy.type=0的type变为-1
     * 2.判断，填空，问答，questionAttrCopy.value 变为-1
     * 3.排序 questionAttrCopy.type=0 的 value 变为-1
     * @param questionCopy
     * @return
     */
    private QuestionCopy filterQuestionAttrWithNoAnswer(QuestionCopy questionCopy) {
        //单选
        if (questionCopy.getType() == Question.SINGLE_CHOOSE || questionCopy.getType() == Question.MULTIPLE_CHOOSE) {
            questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithChoose(questionCopy.getQuestionAttrCopys()));
        }
        //判断，问答，填空
        if (questionCopy.getType() == Question.JDUGEMENT || questionCopy.getType() == Question.QUESTION_ANWSER || questionCopy.getType() == Question.SENTENCE_COMPLETION) {
            questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithJdugement(questionCopy.getQuestionAttrCopys()));
        }

        //阅读
        if (questionCopy.getType() == Question.READING_COMPREHENSION) {
            questionCopy.setSubs(questionCopy.getSubs().stream().map(t -> {
                if (t.getType() == Question.SINGLE_CHOOSE || t.getType() == Question.MULTIPLE_CHOOSE) {
                    t.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithChoose(t.getQuestionAttrCopys()));
                }
                if (t.getType() == Question.QUESTION_ANWSER) {
                    t.setQuestionAttrCopys(new ArrayList<>());
                }
                return t;
            }).collect(Collectors.toList()));
        }
        //排序
        if (questionCopy.getType() == Question.SORTING) {
            questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithSort(questionCopy.getQuestionAttrCopys()));
        }
        return questionCopy;
    }

    /**
     * 单选多选
     * questionAttrCopy.type=0 -> type=-1
     * @param questionAttrCopys
     * @return
     */
    private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithChoose(List<QuestionAttrCopy> questionAttrCopys) {
        return questionAttrCopys.stream().map(t -> {
            try {
                t.setType(FILTER_ANSWER_QUESTION_ATTR);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return t;
        }).collect(Collectors.toList());
    }

    /**
     * 填空，问答，判断
     * 把name,value =-1
     * @param questionAttrCopys
     * @return
     */
    private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithJdugement(List<QuestionAttrCopy> questionAttrCopys) {
        return questionAttrCopys.stream().map(t -> {
            try {
                t.setName(FILTER_ANSWER_QUESTION_ATTR);
                t.setValue(FILTER_ANSWER_QUESTION_ATTR);
            } catch (Exception e) {
                e.printStackTrace();
            }

            return t;
        }).collect(Collectors.toList());
    }

    /**
     * 排序
     * 把questionAttrCopys type=0的记录 的name 变为-1
     * @param questionAttrCopys
     * @return
     */
    private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithSort(List<QuestionAttrCopy> questionAttrCopys) {
        return questionAttrCopys.stream().map(t -> {
            if (Integer.valueOf(t.getType()) == QuestionAttr.ANSWER_TYPE) {
                try {
                    t.setName(FILTER_ANSWER_QUESTION_ATTR);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return t;
        }).collect(Collectors.toList());
    }

    /**
     * 撤销考试，不影响正在进行中的考试记录的考试信息
     * 从缓存获取的examRecord里面应该持有创建时所对应的考试信息
     * @param newestExam
     * @param record
     * @return
     */
    private Exam getRealReturnToUserExam(Exam newestExam, ExamRecord record) {
        return Optional.ofNullable(record.getExam()).map(exam -> exam).orElseGet(() -> {
            record.setExam(newestExam);
            examRecordCache.set(ExamRecord.getExamRecordKey(
                                        record.getExamId(), record.getMemberId()),
                                record, CACHE_TIME);
            return newestExam;
        });
    }

    private void beforeSubmit(Integer examRegion, ExamRecord examRecord, String submitType, Long lastCacheTime, Long currentTime) {
        if (isHandSubmitType(submitType)) {
            handleHandSubmiting(examRegion, examRecord, currentTime);
        } else {
            handleAutoSubmiting(examRecord, lastCacheTime);
        }
    }

    private boolean isHandSubmitType(String submitType) {
        return ExamRecord.SubmitType.valueOf(submitType) == ExamRecord.SubmitType.Hand;
    }

    private void handleAutoSubmiting(ExamRecord examRecord, Long lastCacheTime) {

        ExamRecord cache = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examRecord.getExamId(),
                        examRecord.getMemberId()), ExamRecord.class);

        if (cache != null)
            examRecord = cache;

        examRecord.setLastCacheTime(lastCacheTime);
        examRecord.setLastSubmitTime(lastCacheTime);
        examRecord.setCurrentTime(System.currentTimeMillis());

        examRecordCache.set(
                ExamRecord.getExamRecordKey(
                        examRecord.getExamId(),
                        examRecord.getMemberId()),
                examRecord, Exam.CACHE_TIME);

    }

    private void handleHandSubmiting(Integer examRegion, ExamRecord examRecord, Long currentTime) {

        ExamRecord cache = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examRecord.getExamId(),
                        examRecord.getMemberId()), ExamRecord.class);

        if (cache != null)
            examRecord = cache;

        examRecord.setSubmitTime(currentTime);
        examRecord.setIsReset(null);

        examRecordService.update(examRegion, examRecord.getId(),
                                 Optional.empty(), Optional.empty(),
                                 Optional.empty(), Optional.of(currentTime), null,
                                 Optional.empty(), null, Optional.empty(), Optional.empty(), Optional.empty(), examRecord.getExamId());

        if (examService.isOtherModuleExam(examRegion, examRecord.getExamId())) {
            examRecordCache.clear(
                    ExamRecord.getExamRecordKey(
                            examRecord.getExamId(),
                            examRecord.getMemberId()));
        } else {
            examRecordCache.set(
                    ExamRecord.getExamRecordKey(
                            examRecord.getExamId(),
                            examRecord.getMemberId()),
                    examRecord, Exam.CACHE_TIME);
        }
    }

    /**
     * 取得真实地址IP(优先取x-forwarded-for)
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        //此方式用于nginx服务器参数设置
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        if (request.getHeader("X-Real-IP") != null) {
            return request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private boolean compareAnswerInfo(List<QuestionAttrCopy> questionAttrCopys, String answer, Integer type) {
        boolean isRight = false;
        switch (type) {
            case 1:
                isRight = dealSingleChoose(questionAttrCopys, answer);
                break;
            case 2:
                isRight = dealMultipleChoose(questionAttrCopys, answer);
                break;
            case 3:
                isRight = dealJudgement(questionAttrCopys, answer);
                break;
            case 4:
                isRight = dealSentenceCompletion(questionAttrCopys, answer);
                break;
            case 8:
                isRight = dealSorting(questionAttrCopys, answer);
                break;
            default:
                break;
        }
        return isRight;
    }

    private boolean dealSingleChoose(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        for (int i = 0; i < questionAttrCopys.size(); i++) {
            QuestionAttrCopy attr = questionAttrCopys.get(i);
            if ((String.valueOf(QuestionAttr.ANSWER_TYPE).equals(attr.getType()))
                    && (!attr.getName().equals(answer))) {
                return false;
            }
        }
        return true;
    }

    private boolean dealMultipleChoose(List<QuestionAttrCopy> questionAttrCopys, String answer) {

        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }

        Set<String> answers = Arrays.stream(answer.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());

        List<QuestionAttrCopy> rightAnswers = questionAttrCopys.stream()
                .filter(t -> String.valueOf(QuestionAttr.ANSWER_TYPE).equals(t.getType()))
                .collect(Collectors.toList());

        if (rightAnswers.isEmpty()) {
            return false;
        }

        if (answers.size() != rightAnswers.size()) {
            return false;
        }

        for (QuestionAttrCopy attr : rightAnswers) {
            if (!answers.contains(attr.getName())) {
                return false;
            }
        }

        return true;
    }

    private boolean validateParamEmpty(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (questionAttrCopys == null || answer == null) {
            return true;
        }
        return false;
    }

    private boolean dealJudgement(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }
        String correctAnswer = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue();
        return !Objects.isNull(correctAnswer) && correctAnswer.equals(answer);
    }

    private boolean dealSentenceCompletion(List<QuestionAttrCopy> questionAttrCopys, String answer) {

        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }
        String[] correctAnswerArr = questionAttrCopys.get(AnswerRecord.ZERO_SCORE).getValue().split("\\|");
        String[] answerArr = answer.split("@answer@");

        if(correctAnswerArr.length != answerArr.length)
            return false;

        for (int i = 0; i < correctAnswerArr.length; i++) {
            String[] tureAnswer = correctAnswerArr[i].trim().split("#");
            List<String> trueAn = Arrays.stream(tureAnswer).map(String::trim).collect(Collectors.toList());

            if (!trueAn.contains(answerArr[i].trim())) {
                return false;
            }

        }
        return true;
    }

    private boolean dealSorting(List<QuestionAttrCopy> questionAttrCopys, String answer) {
        if (validateParamEmpty(questionAttrCopys, answer)) {
            return false;
        }

        String correctAnswer = questionAttrCopys.stream()
                .filter(t -> t.getType().equals(String.valueOf(QuestionAttr.ANSWER_TYPE)))
                .collect(Collectors.toList()).get(0).getValue();

        return correctAnswer.equals(answer);
    }

}
