
package com.zxy.product.examstu.web.util;

import com.zxy.common.restful.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 *
 */
public class BrowserUtil {

	public static final Logger logger = LoggerFactory.getLogger(BrowserUtil.class);

    /** 是否IE系浏览器  */
	public static boolean isMSBrowser(String userAgent) {
		String[] IEBrowserSignals = { "MSIE", "Trident", "Edge" };
		for (String signal : IEBrowserSignals) {
			if (userAgent.contains(signal))
				return true;
		}
		return false;
	}

	public static String getStringCode(String userAgent) {
		if (userAgent.indexOf("Mozilla") > -1) {
			return "utf-8";
		}
		return "gb2312";
	}

	/** 根据浏览器类型转换文件名编码类型  */
	public static String getBrowserFileName(RequestContext requestContext, String attachmentName) throws UnsupportedEncodingException {
		if (BrowserUtil.isMSBrowser(requestContext.getRequest().getHeader("User-Agent"))) {
			attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
		} else {
			attachmentName = new String(attachmentName.getBytes("UTF-8"), "ISO-8859-1");
		}
		return attachmentName;
	}

	/** 根据文件名处理excel模板下载response  */
	public static HttpServletResponse fileDownloadResponse(RequestContext requestContext, String attachmentName) throws UnsupportedEncodingException{
		HttpServletResponse response = requestContext.getResponse();
		String fileName = BrowserUtil.getBrowserFileName(requestContext, attachmentName);
		response.setContentType("application/octet-stream;charset=utf-8");
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response;
    }

	private static String encodeURIComponent(String value) {
		try {
			return URLEncoder.encode(value, "UTF-8").replaceAll("\\+", "%20");
		} catch (UnsupportedEncodingException e) {
			logger.error("error", e);
			return null;
		}
	}

	public static HttpServletResponse setHttpServletResponse(RequestContext requestContext, String attachmentName) throws IOException {
		HttpServletResponse response = requestContext.getResponse();
		String agent = requestContext.getRequest().getHeader("User-Agent");
		if (com.zxy.product.human.util.BrowserUtil.isMSBrowser(agent))
			attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
		if (agent.contains("Safari")) {
			String headerValue = "\"" + encodeURIComponent(attachmentName) + "\";";
			headerValue += " filename*=utf-8''" + encodeURIComponent(attachmentName);
			attachmentName = headerValue;
		} else attachmentName = new String(attachmentName.getBytes("UTF-8"), "ISO-8859-1");
		response.setContentType("application/octet-stream;charset=utf-8");
		response.setHeader("Content-Disposition", "attachment;filename=" + attachmentName);
		return response;
	}
}
