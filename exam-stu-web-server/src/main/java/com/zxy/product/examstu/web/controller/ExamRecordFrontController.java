package com.zxy.product.examstu.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.examstu.api.ExamRecordService;
import com.zxy.product.examstu.api.ExamService;
import com.zxy.product.examstu.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/exam-record/front")
public class ExamRecordFrontController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ExamRecordFrontController.class);

	private ExamRecordService examRecordService;

	private ExamService examService;

	private MessageSender messageSender;

	private Cache examRecordCache;

	private Cache fullAnswerCache;

	@Autowired
	public void setExamService(ExamService examService) {
		this.examService = examService;
	}

	@Autowired
	public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.examRecordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
		this.fullAnswerCache = cacheService.create("common", ExamRecord.FULL_ANSWER_JSON);
	}

	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

	/**
	 * 提交试卷
	 * 1.临时提交，设置最新缓存时间lastCacheTime
	 * 2.最终提交，更新提交时间，清楚该考试纪录缓存
	 *
	 * examId 考试ID
	 * examRecordId 考试记录ID
	 * submitType 提交类型-第一版参数，{手动，临时}
	 * clientType 客户端类型- {APP，PC}
	 * answerRecords 答题记录json字符串
	 * lastCacheTime 最新客户端缓存时间 - 为了解决跨客户端考试获取最细答题记录
	 * noAnswerCount 未答数
	 * answeredCount 已答数
	 * submitDetailType  这个字段定义常量就保持和试卷页面的submitTipsType一致 交卷类型-{ 1：自动，2：手动，3：超时，4：强制，5：切屏 }
	 * clientVersion 终端版本
	 * @return
	 */
	@RequestMapping(value = "/submitPaper", method = RequestMethod.POST)
	@Param(name = "examId", type = String.class, required = true)
	@Param(name = "examRecordId", type = String.class, required = true)
	@Param(name = "submitType", type = String.class, required = true)
	@Param(name = "clientType", type = Integer.class, required = true)
	@Param(name = "answerRecords", type = String.class, required = true)
	@Param(name = "fullAnswerRecords", type = String.class)
	@Param(name = "lastCacheTime", type = Long.class)
	@Param(name = "noAnswerCount", type = Integer.class)
	@Param(name = "answeredCount", type = Integer.class)
	@Param(name = "submitDetailType", type = Integer.class)
	@Param(name = "clientVersion", type = String.class)
	@JSON("*")
	@Permitted()
	public Map<String, Object> submitPaper(RequestContext requestContext, Subject<Member> subject) {

		Integer examRegion = subject.examRegion();

		String examRecordId = requestContext.get("examRecordId", String.class);

		String examId = requestContext.get("examId", String.class);

		ExamRecord examRecord = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examId, subject.getCurrentUserId()), ExamRecord.class);

		if (examRecord == null) {
		    String cachekey = ExamRecord.getExamRecordKey(examId, subject.getCurrentUserId());
		    examRecord = examRecordService.getSimple(examRegion, examRecordId, examId);
            examRecordCache.set(cachekey, examRecord, Exam.CACHE_TIME);
        }

		// 防止手动提交回调过程中再次临时提交，判断submittime非空，直接返回，这里前端也做了相应处理判断
		if (examRecord.getSubmitTime() != null) {
			return ImmutableMap.of("status", 1, "msg", "success");
		}

		Long currentTime = System.currentTimeMillis();
		beforeSubmit(examRegion, examRecord,
				requestContext.getString("submitType"),
				requestContext.getOptional("lastCacheTime", Long.class)
						.orElse(System.currentTimeMillis()), currentTime);

		//缓存全量答题记录到redis
		saveFullAnswers2Cache(examRecord, requestContext.getOptionalString("fullAnswerRecords"));
		//缓存增量答题记录到redis
//		saveModifyAnswers2Cache(examRecord, requestContext.getString("answerRecords"),
//				String.valueOf(requestContext.getOptionalInteger("answeredCount").orElse(0)));

		messageSender.send(
				MessageTypeContent.SUBMIT_PAPER,
				ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID, requestContext.getString("examRecordId"),
				ExamRecord.SUBMIT_PAPER_TYPE, requestContext.getString("submitType"),
				ExamRecord.SUBMIT_PAPER_CLIENT_TYPE, String.valueOf(requestContext.getInteger("clientType")),
				ExamRecord.SUBMIT_PAPER_TIME, String.valueOf(currentTime),
				ExamRecord.SUBMIT_PAPER_ANSWER_RECORD, requestContext.getString("answerRecords"),
				ExamRecord.SUBMIT_USER_IP, getIpAddr(requestContext.getRequest()),
				ExamRecord.SUBMIT_PAPER_NO_ANSWER_COUNT,
					String.valueOf(requestContext.getOptionalInteger("noAnswerCount").orElse(0)),
				ExamRecord.SUBMIT_PAPER_ANSWERED_COUNT,
					String.valueOf(requestContext.getOptionalInteger("answeredCount").orElse(0)),
				ExamRecord.SUBMIT_DETAIL_TYPE,
					String.valueOf(requestContext.getOptionalInteger("submitDetailType").orElse(ExamRecord.SUBMIT_TYPE_HAND)),
				ExamRecord.SUBMIT_CLIENT_VERSION,
					requestContext.getOptionalString("clientVersion").orElse(""),
				ExamRecord.SUBMIT_PAPER_EXAM_ID, examId);


		LOGGER.info("submitPaper, examRecordId:{}", examRecord.getId());


		return ImmutableMap.of("status", 1, "msg", "success");
	}


	/**
	 * 用于更新新员工考试，自动提交后，修改状态
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/update-status", method = RequestMethod.PUT)
	@Param(name = "examId", type = String.class, required = true)
	@Param(name = "examRecordId", type = String.class, required = true)
	@Param(name = "status", type = Integer.class, required = true)
	@JSON("result")
	public Map<String, String> updateStatus(RequestContext requestContext, Subject<Member> subject){
		Integer examRegion = subject.examRegion();

		String examRecordId = requestContext.get("examRecordId", String.class);

		String examId = requestContext.get("examId", String.class);

		ExamRecord examRecord = examRecordCache.get(
				ExamRecord.getExamRecordKey(
						examId, subject.getCurrentUserId()), ExamRecord.class);

		if (examRecord == null) {
			String cachekey = ExamRecord.getExamRecordKey(examId, subject.getCurrentUserId());
			examRecord = examRecordService.getSimple(examRegion, examRecordId, examId);
			examRecordCache.set(cachekey, examRecord, Exam.CACHE_TIME);
		}
		examRecordService.update(examRegion, examRecord.getId(),
				Optional.empty(), Optional.empty(),
				Optional.of(requestContext.getInteger("status")), Optional.empty(), null,
				Optional.empty(), null, Optional.empty(), Optional.empty(), Optional.empty(), examRecord.getExamId());

		return ImmutableMap.of("result", "success");
	}
	/**
	 * 取得真实地址IP(优先取x-forwarded-for)
	 *
	 * @param request
	 * @return
	 */
	private String getIpAddr(HttpServletRequest request) {
		//此方式用于nginx服务器参数设置
		String ip = request.getHeader("x-forwarded-for");
		if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
			return ip.split(",")[0];
		}
		if (request.getHeader("X-Real-IP") != null) {
			return request.getHeader("X-Real-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}


	/**
	 * 每次临时提交都会覆盖最新的全量json答题记录
	 * @param examRecord
	 * @param fullAnswerRecords
	 */
	private void saveFullAnswers2Cache(ExamRecord examRecord, Optional<String> fullAnswerRecords) {
		fullAnswerRecords.ifPresent(json -> {
			fullAnswerCache.set(
				ExamRecord.getExamRecordKeyByParms(
						examRecord.getExamId(),
						examRecord.getMemberId(),
						examRecord.getId()),
				json, ExamRecord.CACHE_TIME);

			LOGGER.info("saveFullAnswers2Cache, examRecordId:{}", examRecord.getId());
		});
	}


	/**
	 * a、处理临时提交
	 * b、处理手动提交
	 * @param examRecord
	 * @param submitType
	 * @param lastCacheTime
	 * @param currentTime
	 */
	private void beforeSubmit(Integer examRegion, ExamRecord examRecord, String submitType, Long lastCacheTime, Long currentTime) {
		if (isHandSubmitType(submitType)) {
			handleHandSubmiting(examRegion, examRecord, currentTime);
		} else {
			handleAutoSubmiting(examRecord, lastCacheTime);
		}
	}

	private boolean isHandSubmitType(String submitType) {
		return ExamRecord.SubmitType.valueOf(submitType) == ExamRecord.SubmitType.Hand;
	}

	private void handleAutoSubmiting(ExamRecord examRecord, Long lastCacheTime) {

		ExamRecord cache = examRecordCache.get(
				ExamRecord.getExamRecordKey(
						examRecord.getExamId(),
						examRecord.getMemberId()), ExamRecord.class);

		if (cache != null)
		    examRecord = cache;

		examRecord.setLastCacheTime(lastCacheTime);
		examRecord.setLastSubmitTime(lastCacheTime);
		examRecord.setCurrentTime(System.currentTimeMillis());

		examRecordCache.set(
				ExamRecord.getExamRecordKey(
						examRecord.getExamId(),
						examRecord.getMemberId()),
				examRecord, Exam.CACHE_TIME);

	}

	private void handleHandSubmiting(Integer examRegion, ExamRecord examRecord, Long currentTime) {

	    ExamRecord cache = examRecordCache.get(
				ExamRecord.getExamRecordKey(
						examRecord.getExamId(),
						examRecord.getMemberId()), ExamRecord.class);

        if (cache != null)
            examRecord = cache;

		examRecord.setSubmitTime(currentTime);
		examRecord.setIsReset(null);

		examRecordService.update(examRegion, examRecord.getId(),
				Optional.empty(), Optional.empty(),
				Optional.empty(), Optional.of(currentTime), null,
				Optional.empty(), null, Optional.empty(), Optional.empty(), Optional.empty(), examRecord.getExamId());

		if (examService.isOtherModuleExam(examRegion, examRecord.getExamId())) {
			examRecordCache.clear(
					ExamRecord.getExamRecordKey(
							examRecord.getExamId(),
							examRecord.getMemberId()));
		} else {
			examRecordCache.set(
					ExamRecord.getExamRecordKey(
							examRecord.getExamId(),
							examRecord.getMemberId()),
					examRecord, Exam.CACHE_TIME);
		}
	}


	/**
	 * 切屏
	 *
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value = "/switch-screen", method = RequestMethod.POST)
	@JSON("*")
	@Param(name = "examRecordId", type = String.class, required = true)
	@Param(name = "examId", type = String.class, required = true)
	@Param(name = "times", type = Integer.class, required = true)
	public Map<String, String> switchScreen(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		examRecordService.decreaseSwitchTimes(
				examRegion,
				requestContext.getString("examRecordId"),
				requestContext.getInteger("times"),
				requestContext.getString("examId"));

		String cachekey = ExamRecord.getExamRecordKey(
				requestContext.getString("examId"),
				subject.getCurrentUserId());

		ExamRecord examRecord = examRecordCache.get(cachekey, ExamRecord.class);

		if (examRecord != null) {
			examRecord.setSwitchTimes(requestContext.getInteger("times"));
			examRecordCache.set(cachekey, examRecord, Exam.CACHE_TIME);
		}

		return ImmutableMap.of("status", "1");
	}


	/**
	 * 考试记录-分页
	 *
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value = "/page", method = RequestMethod.GET)
	@JSON("recordCount")
	@JSON("items.(id,status,score,startTime,endTime,submitTime,examId)")
	@JSON("items.exam.(showAnswerRule,showScoreTime)")
	@Param(name = "page", type = Integer.class, required = true)
	@Param(name = "pageSize", type = Integer.class, required = true)
	@Param(name = "examId", type = String.class, required = true)
	public PagedResult<ExamRecord> find(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		return examRecordService.findPagedResult(
				examRegion,
				requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class),
				requestContext.get("examId", String.class),
				subject.getCurrentUserId()
		);
	}
	

}
