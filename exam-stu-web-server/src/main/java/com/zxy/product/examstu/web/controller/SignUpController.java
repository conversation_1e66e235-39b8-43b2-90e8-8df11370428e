package com.zxy.product.examstu.web.controller;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.examstu.api.ExamService;
import com.zxy.product.examstu.api.RelevanceCourseExamService;
import com.zxy.product.examstu.api.SignUpService;
import com.zxy.product.examstu.content.DataSourceEnum;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.util.DesensitizationUtil;
import com.zxy.product.examstu.web.util.BrowserUtil;
import com.zxy.product.examstu.web.util.ImportExportUtil;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.system.api.permission.GrantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/sign-up")
public class SignUpController {

    private SignUpService sigUpService;
    private MemberService memberService;
    @Resource
    private CourseStudyProgressService courseStudyProgressService;
    @Resource
	private RelevanceCourseExamService relevanceCourseExamService;
    @Resource
    private ExamService examService;
    private Cache gridSignupCache;
    private Cache examCache;

    @Autowired
    public void setCacheService(CacheService cacheService){
        this.gridSignupCache = cacheService.create("grid-signup", "time");
        this.examCache = cacheService.create("cacheExam", "examEntity");

    }

    @Autowired
    public void setSigUpService(SignUpService sigUpService) {
        this.sigUpService = sigUpService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    /**
     * 普通考试报名，
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "examId", type = String.class, required=true)
    @JSON("id,status")
    public SignUp doSignUp(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        // 查询预审核状态
        String examId = requestContext.getString("examId");
        String currentUserId = subject.getCurrentUserId();

        Exam exam = getExamFromCacheOrDB(examRegion, examId);
        boolean ordinaryExam = isOrdinaryExam(exam.getType());
        int preApprovalStatus = 1;
        SignUp signUp = new SignUp();

        if (ordinaryExam) {
            HashMap<String, Object> map = new HashMap<>();
            Long time = gridSignupCache.get(examId+"#"+currentUserId, Long.class);
            if (time != null) {
                signUp.setTime(((SignUp.CACHE_TIME*1000)-(System.currentTimeMillis() - time)));
                return signUp;
            }
            preApprovalStatus = getPreApprovalStatus(examRegion, exam, currentUserId);
        }

        signUp = sigUpService.ordinaryInsert(examRegion, examId, currentUserId, preApprovalStatus);

        if (ordinaryExam){
            if (signUp != null && preApprovalStatus == SignUp.STATUS_REFUSE) {
                HashMap<String, Object> map = new HashMap<>();
                // 被拒绝后有2个小时的缓存时间，防止一直报名
                gridSignupCache.set(examId+"#"+currentUserId, System.currentTimeMillis(), SignUp.CACHE_TIME);
                signUp.setTime((long) (SignUp.CACHE_TIME * 1000));
                return signUp;
            }
        }
        return signUp;
    }

    /**
     * 缓存获取考试信息
     * @param examId
     * @return
     */
    private Exam getExamFromCacheOrDB(Integer examRegion, String examId) {
        Exam exam = new Exam();
        try {
            exam = examCache.get(examId, () -> {
                return Optional.ofNullable(
                                examService.getSimpleData(examRegion, examId))
                        .orElseThrow(() ->
                                new UnprocessableException(ErrorCode.ExamNullError));
            }, Exam.CACHE_TIME);

        } catch (Exception e) {
            examCache.clear(examId);
            exam = examCache.get(examId, () -> {
                return Optional.ofNullable(
                                examService.getSimpleData(examRegion, examId))
                        .orElseThrow(() ->
                                new UnprocessableException(ErrorCode.ExamNullError));
            }, Exam.CACHE_TIME);
        }

        return exam;
    }


    /**
     * 审核报名
     * 通过 拒绝
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(method= RequestMethod.PUT)
    @JSON("*.(id,createTime,status)")
    @Param(name="signUpIds", type=String.class, required=true)
    @Param(name="signUpId", type=String.class)
	//审核状态,2:通过;3:拒绝
    @Param(name="status", type=Integer.class, required = true)
    @Param(name = "noticeUser", type = Integer.class)
    @Param(name = "noticeUserText", type = String.class)
    @Param(name = "noticeUserContent", type = String.class)
    @Param(name = "uri", type = String.class)
    //是否批量操作,1:是;0:否
    @Param(name = "isBatch", type=Integer.class)
    //是否报名管理入口,1:报名管理入口;0:考试管理中入口
    @Param(name = "isManage", type=Integer.class)
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.AUDIT, fisrtAction = "管理-报名审核", secondAction = "审核通过", desc = "对{0}操作报名审核通过并通知于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="2,0,0")
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.AUDIT, fisrtAction = "管理-报名审核", secondAction = "审核拒绝" , desc = "对{0}操作报名审核拒绝并通知于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="3,0,0")
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.AUDIT, fisrtAction = "管理-报名审核", secondAction = "批量通过", desc = "操作报名审核通过并通知于考试《{0}》", ids = {"signUpId"}, jsons = {"examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="2,1,0")
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.AUDIT, fisrtAction = "管理-报名审核", secondAction = "批量拒绝", desc = "操作报名审核拒绝并通知于考试《{0}》", ids = {"signUpId"}, jsons = {"examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="3,1,0")
    @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.AUDIT, fisrtAction = "审核通过", desc = "对{0}操作报名审核通过并通知于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="2,0,1")
    @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.AUDIT, fisrtAction = "审核拒绝", desc = "对{0}操作报名审核拒绝并通知于考试《{1}》", ids = {"signUpId"}, jsons = {"memberName", "examName"}, keys = {"exam-sign-up-name"}, businessType="status,isBatch,isManage", businessValue="3,0,1")
    @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.AUDIT, fisrtAction = "批量通过", desc = "操作报名审核通过并通知", businessType="status,isBatch,isManage", businessValue="2,1,1")
    @Audit(module = "考试管理", subModule = "报名管理", action = Audit.Action.AUDIT, fisrtAction = "批量拒绝", desc = "操作报名审核拒绝并通知", businessType="status,isBatch,isManage", businessValue="3,1,1")
    public List<SignUp> update(RequestContext requestContext,  Subject<Member> subject) {
    	Integer status = requestContext.getInteger("status");
    	String uri = requestContext.getOptional("uri", String.class).orElse("exam/exam");
    	List<String> signUpIds = new ArrayList<>();
    	signUpIds.addAll(com.alibaba.fastjson.JSON.parseArray(requestContext.get("signUpIds", String.class), String.class));

        List<SignUp> result = new ArrayList<>();

        // update方法根据所传examRegion调用最多2次即可，不用特意区分signUpIds是南北区。
        List<SignUp> southSignUpList = sigUpService.update(DataSourceEnum.SOUTH.getType(),
                                                           signUpIds, status, subject.getCurrentUserId(),
                                                           requestContext.getOptionalInteger("noticeUser"),
                                                           requestContext.getOptionalString("noticeUserText"),
                                                           requestContext.getOptionalString("noticeUserContent"), uri);

        result.addAll(southSignUpList);

        List<SignUp> northSignUpList = sigUpService.update(DataSourceEnum.NORTH.getType(),
                                                           signUpIds, status, subject.getCurrentUserId(),
                                                           requestContext.getOptionalInteger("noticeUser"),
                                                           requestContext.getOptionalString("noticeUserText"),
                                                           requestContext.getOptionalString("noticeUserContent"), uri);

        result.addAll(northSignUpList);

        return result;
    }

	/**
	 * 取消报名
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/{id}", method= RequestMethod.DELETE)
    @JSON("id,status")
    @Param(name="id", required=true)
    public SignUp cancelSignUp(RequestContext requestContext,  Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return sigUpService.update(
                examRegion,
    			requestContext.get("id",String.class),
    			SignUp.STATUS_CANCEL,subject.getCurrentUserId());
    }

    /**
     * 根据条件分页查询用户
     */
    @RequestMapping(value = "/record",method= RequestMethod.GET)
    @JSON("id,createTime,status")
    @Param(name="memberId", type=String.class,required = true)
    public List<SignUp> findByMemberId(RequestContext requestContext) {
        Integer examRegion = memberService.findExamRegion(requestContext.getString("memberId"));
        return sigUpService.findByMemberId(examRegion, requestContext.getString("memberId"));
    }

    /**
     * 判断交叉时间接口
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/overlap-time",method = RequestMethod.GET)
    @Param(name = "examId", type = String.class, required=true)
    @Param(name = "organizationId", type = String.class, required=true)
    @JSON("id")
    public Exam overlapTime(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        Exam signUpAuth = sigUpService.overlapTime(examRegion, requestContext.getString("examId"),
                                subject.getCurrentUserId(),
                                requestContext.getString("organizationId")

                 );

        return signUpAuth;
    }


    /**
     * 获取报名详情
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{id}", method= RequestMethod.GET)
    @Param(name="id", type=String.class, required=true)
    @JSON("id,examId,memberId,auditMemberId,createTime,organizationId,status")
    @JSON("exam.(id,name)")
    @JSON("member.(id,name,fullName)")
    public SignUp getSignUp(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
    	return sigUpService.get(examRegion, requestContext.get("id",String.class));
    }


    /**
     * 移动云-报名状态
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/cloud-status",method = RequestMethod.GET)
    @Param(name = "examIds", type = String.class, required=true)
    @JSON("member.(phoneNumber,email)")
    @JSON("cloudSignup.(examId,status,id)")
    public Map<String, Object> getCloudStatus(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        Map<String, Object> cloudSignup = sigUpService.getCloudStatus(examRegion, subject.getCurrentUserId(),
                Arrays.asList(requestContext.getString("examIds").split(",")));
        return cloudSignup;
    }


	private int getPreApprovalStatus(Integer examRegion, Exam exam, String memberId) {
		int preStatusWaitApproval = 1;
		int preStatusPass = 2;
		int preStatusRefuse = 3;
		String preApprovalRule = exam.getPreApprovalRule();
		if (preApprovalRule != null) {
			int page = 1;
			String allCoursesCompleted = "r1";
			String allApplicantsHavePassedTheReview = "r2";
			// 解析预审规则
			JSONObject jsonObject = JSONObject.parseObject(preApprovalRule);
			// 勾选了规则 全部报名人员均通过审核
			if (Exam.PRE_APPROVAL_RULE_YES == jsonObject.getIntValue(allApplicantsHavePassedTheReview)) {
				return preStatusPass;
			}
			//  勾选了规则完成考试关联课程
			if (Exam.PRE_APPROVAL_RULE_YES == jsonObject.getIntValue(allCoursesCompleted)) {

				List<RelevanceCourseExam> relevanceCourseExamList = relevanceCourseExamService.findRelevanceCourseExamList(examRegion, exam.getId());
				if (!relevanceCourseExamList.isEmpty()) {
					// 课程和专题的id
					List<String> courseIds = relevanceCourseExamList.stream().map(RelevanceCourseExam::getCourseId).collect(Collectors.toList());
					if (courseStudyProgressService.finishAllCourse(memberId, courseIds)) {
						return preStatusPass;
					}
				}
			}
			return preStatusRefuse;
		}
		return preStatusWaitApproval;
	}
    /**
     * 是否普通考试
     * @param type
     * @return
     */
    private  boolean isOrdinaryExam(Integer type) {
        return Exam.EXAM_OFFICIAL_TYPE.equals(type) || Exam.EXAM_UN_OFFICIAL_TYPE.equals(type);
    }

}
