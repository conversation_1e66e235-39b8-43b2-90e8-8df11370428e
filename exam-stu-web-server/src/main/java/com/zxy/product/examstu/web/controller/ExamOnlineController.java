package com.zxy.product.examstu.web.controller;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.examstu.content.ExamLimitConstants;
import com.zxy.product.examstu.content.FlowLimitEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import java.util.HashMap;
import java.util.Map;
import static com.zxy.product.examstu.content.ExamLimitConstants.CacheKeyOnline;

/**
 * 考试最大在线控制器
 * <AUTHOR>
 * @date 2025年03月16日 5:19
 */
@Controller
@RequestMapping(value = "/exam-online")
public class ExamOnlineController {

    private Cache examOnlineCache;

    @Autowired
    public void setExamOnlineCache(CacheService cacheService){
        this.examOnlineCache= cacheService.create( ExamLimitConstants.CacheKeyApplication, ExamLimitConstants.CacheKeyModule );
    }


    /** 查询当前考试在线人数 */
    @JSON("examOnline")
    @RequestMapping(value = "/do-cache-online",method = RequestMethod.GET)
    public Map<String,Long> doCacheOnline(){
        Map<String,Long> onlineMap=new HashMap<>(2);
        String cacheKey = CacheKeyOnline + FlowLimitEnum.ExamSubmit.getBusinessType();
        Long examOnline = examOnlineCache.increment(cacheKey, 0L);
        onlineMap.put( "examOnline", examOnline );
        return onlineMap;
    }
}
