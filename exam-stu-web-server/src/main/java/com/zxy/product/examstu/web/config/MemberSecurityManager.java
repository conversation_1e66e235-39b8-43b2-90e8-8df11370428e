package com.zxy.product.examstu.web.config;

import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.cache.redis.RedisCacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.security.support.oauth.OAuthSecurityManager;
import com.zxy.common.serialize.Serializer;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.Organization;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.api.OrganizationService;
import com.zxy.product.human.content.MessageHeaderContent;
import com.zxy.product.human.content.MessageTypeContent;
import com.zxy.product.system.api.permission.MenuService;
import com.zxy.product.system.api.permission.RoleService;
import com.zxy.product.system.jooq.tables.pojos.MenuEntity;
import com.zxy.product.system.jooq.tables.pojos.RoleEntity;
/**
 * <AUTHOR>
 *
 */
@Service
public class MemberSecurityManager extends OAuthSecurityManager<Member> {

    public static final String OAUTH_PROVIDER_APPLICATION_NAME = "oauth-provider";
    public static final String OAUTH_PROVIDER_MODULE_NAME = "oauth";

    private MemberService memberService = null;
    private MenuService menuService;
    private RoleService roleService;
    private OrganizationService organizationService;
    private Cache cache;

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setMenuService(MenuService menuService) {
        this.menuService = menuService;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    public void setOAuthRedisCacheService(Redis redis, Serializer ser){
        RedisCacheService redisCacheService = new RedisCacheService(OAUTH_PROVIDER_APPLICATION_NAME);
        redisCacheService.setRedis(redis);
        redisCacheService.setSerializer(ser);
        this.cache = redisCacheService.create(OAUTH_PROVIDER_MODULE_NAME);
    }

    @Override
    protected Member getCurrentUser(String id) {
        com.zxy.product.human.entity.Member humanMember = memberService.get(id);
        Member examMember = new Member();
        BeanUtils.copyProperties(humanMember, examMember);

        // 所属部门
        Organization systemOrganization = new Organization();
        com.zxy.product.human.entity.Organization humanOrganization = organizationService.get(humanMember.getOrganizationId()).orElse(null);
        BeanUtils.copyProperties(humanOrganization, systemOrganization);
        examMember.setOrganization(systemOrganization);

        // 所属机构
        Organization systemCompanyOrganization = new Organization();
        if(null != humanOrganization.getCompanyId() && !"".equals(humanOrganization.getCompanyId().trim())) {
            organizationService.getBasic(humanOrganization.getCompanyId()).ifPresent(humanCompanyOrganization -> {
                BeanUtils.copyProperties(humanCompanyOrganization, systemCompanyOrganization);
                examMember.setCompanyOrganization(systemCompanyOrganization);
            });
        }

        // 所属根组织
        Organization systemRootOrganization = new Organization();
        com.zxy.product.human.entity.Organization humanRootOrganization = memberService.getCompanyOrganizationWithLevel2ByMemberId(humanMember.getId());
        BeanUtils.copyProperties(humanRootOrganization, systemRootOrganization);
        examMember.setRootOrganization(systemRootOrganization);

        return examMember;
    }

    @Override
    protected Cache getCache() {
        return cache;
    }

    @Override
    protected Set<String> getPermissions(String userId) {
        return menuService.findByMemberId(userId).stream().map(MenuEntity::getUri).collect(Collectors.toSet());
    }

    @Override
    protected Set<String> getRoles(String userId) {
        return roleService.findByMemberId(userId).stream().map(RoleEntity::getName).collect(Collectors.toSet());
    }

    @Override
	protected void sendExpiredTimeMessage(String userId, String terminalType) {
		messageSender.send(MessageTypeContent.OAUTH_REFRESH_EXPIRED_TIME,
	            MessageHeaderContent.MEMBER_ID, userId,
	            MessageHeaderContent.TYPE, terminalType);
	}
}
