package com.zxy.product.examstu.web.config;


import com.zxy.common.restful.websocket.DefaultHandshakeInterceptor;
import com.zxy.product.examstu.web.websocket.ExamWebSocketHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class ExamWebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(examWebSocketHandler(), "/ws/exam").addInterceptors(new DefaultHandshakeInterceptor()).setAllowedOrigins("*");
    }

    @Bean
    public ExamWebSocketHandler examWebSocketHandler() {
        return new ExamWebSocketHandler();
    }

}
