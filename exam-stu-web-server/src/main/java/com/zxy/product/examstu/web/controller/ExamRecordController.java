package com.zxy.product.examstu.web.controller;


import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;

import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;

import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.examstu.api.*;

import com.zxy.product.exam.entity.*;

import com.zxy.product.examstu.web.util.BrowserUtil;
import com.zxy.product.examstu.web.util.DateUtil;
import com.zxy.product.examstu.web.websocket.ExamWebSocketHandler;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/exam-record")
public class ExamRecordController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ExamRecordController.class);

    public static final String EXAM_USER = "exam_user";

    private ExamRecordService examRecordService;

    private ExamStuRecordService examStuRecordService;

    private Cache examRecordCache;

    private Cache fullAnswerCache;

    private QuestionCopyService questionCopyService;

    private AnswerRecordService answerRecordService;

    private PaperInstanceService paperInstanceService;

    private Cache paperNewCache;

    private Cache questionCache;

    private Cache paperInstanceQuestionCopyCache;

    private MemberService memberService;
    private ExamService examService;

    private Cache examUserCache;

    private MessageSender examMessageSender;

    @Autowired
    public void setExamStuRecordService(ExamStuRecordService examStuRecordService) {
        this.examStuRecordService = examStuRecordService;
    }

    @Autowired
    public void setExamMessageSender(MessageSender examMessageSender) {
        this.examMessageSender = examMessageSender;
    }


    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }
    
    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }


    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService){
        this.examRecordService=examRecordService;
    }


    @Autowired
    public void setCacheService(CacheService cacheService){
        this.examRecordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
        this.fullAnswerCache = cacheService.create("common", ExamRecord.FULL_ANSWER_JSON);
        this.paperNewCache = cacheService.create("cachePaperInstance", "NEW-paperInstanceEntity");
        this.questionCache = cacheService.create("cacheQuestion", "questionEntity");
        this.paperInstanceQuestionCopyCache = cacheService.create("cachePaperInstanceQuestionCopy", "paperInstanceQuestionCopyEntity");
        this.examUserCache = cacheService.create("cacheExamUser", ExamWebSocketHandler.EXAM_USERS);
    }


    /**
     * 计算redis考试得分，用于前端提交试卷显示
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/score", method = RequestMethod.POST)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "examRecordId", type = String.class, required = true)
    @Param(name = "paperInstanceId", type = String.class, required = true)
    @Param(name = "fullAnswerRecords", type = String.class)
    @JSON("encryptScore")
    public ExamRecord getScore(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        String examId = requestContext.get("examId", String.class);
        String paperInstanceId = requestContext.get("paperInstanceId", String.class);
        String examRecordId = requestContext.get("examRecordId", String.class);
        Optional<String> fullAnswerRecordsByfront = requestContext.getOptional("fullAnswerRecords", String.class);

        ExamRecord examRecord = examRecordCache.get(
                ExamRecord.getExamRecordKey(
                        examId, subject.getCurrentUserId()), ExamRecord.class);

        if (examRecord == null) {
            examRecord = examRecordService.getExamRecordSubmitTime(examRegion, subject.getCurrentUserId(), examId);
        }

        if (examRecord.getSubmitTime() == null) {
            examRecord.setEncryptScore(null);
            return examRecord;
        }

        Integer totalScore = 0;
        String fulljson = null;

        fulljson = fullAnswerCache.get(ExamRecord.getExamRecordKeyByParms(examId, subject.getCurrentUserId(),examRecordId), String.class);

        if (fulljson == null && fullAnswerRecordsByfront.isPresent()) {
            fulljson = fullAnswerRecordsByfront.get();
        }
        if (fulljson != null) {
            List<AnswerRecord> fullAnswerRecords = com.alibaba.fastjson.JSON.parseArray(fulljson, AnswerRecord.class);
            List<AnswerRecord> AnswerRecordList = findFromCacheOrDb(examRegion, examId, paperInstanceId, examRecordId, fullAnswerRecords);
            List<AnswerRecord> updateList = new ArrayList<>();
            Integer rightCount = 0;
            //计算客观题分数
            // 填空题自动判分 2020-08
            AnswerRecordList.forEach(a -> {
                QuestionCopy q = a.getQuestionCopy();
                if (q != null && q.getType() != Question.QUESTION_ANWSER
//                        && q.getType() != Question.SENTENCE_COMPLETION
                        && q.getType() != Question.READING_COMPREHENSION) {
                    if (a.getQuestionId() != null) {
                        updateList.add(answerRecordService.updateScore(examRegion, a));
                    }
                }else if (a.getAnswer() == null || "".equals(a.getAnswer())) {
                    a.setScore(AnswerRecord.ZERO_SCORE);
                    updateList.add(a);
                }
            });
            totalScore = updateList.stream()
                    .filter(f -> f.getScore() != null)
                    .map(AnswerRecord::getScore).reduce(0, (a, b) -> a + b);
        }

        examRecord.setEncryptScore(aesEncryptScore(examId, examRecordId, totalScore));
        return examRecord;
    }

    private String aesEncryptScore(String examId, String examRecordId, Integer totalScore) {
        long time = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        sb.append(examId).append("+").append(examRecordId).append("+").append(time).append("+").append("score:").append(totalScore);
        try {
            return Encrypt.aesEncrypt(sb.toString(), "d8cg8gVakEq9Agup");
        } catch (Exception e) {
            LOGGER.info("submitPaper-getScore, error:{}",e);
        }
        return null;
    }

    /**
     * 从缓存或DB获取试卷试题
     * @param examId
     * @param paperInstanceId
     * @param examRecordId
     * @param fullAnswerRecords
     * @return
     */
    private List<AnswerRecord> findFromCacheOrDb(Integer examRegion, String examId, String paperInstanceId, String examRecordId, List<AnswerRecord> fullAnswerRecords) {
        PaperInstance paper = getPaperByCache(examRegion, examId, paperInstanceId);
        List<AnswerRecord> answerRecordList = packQuestionsFromCacheOrDBToPaper(examRegion, paper, examId, fullAnswerRecords);
        if (answerRecordList.isEmpty())
            answerRecordList = examRecordService.findInculdeQuestionAnswers(examRegion, fullAnswerRecords);
        return answerRecordList;
    }

    /**
     * 缓存获取试卷信息
     * @param examId
     * @param paperInstanceId
     * @return
     */
    private PaperInstance getPaperByCache(Integer examRegion, String examId, String paperInstanceId) {
        PaperInstance paper = paperNewCache.get("NEW-"+examId + paperInstanceId, () -> {
            return paperInstanceService.getNewInstance(examRegion, paperInstanceId, examId);
        }, Exam.CACHE_TIME);
        return paper;
    }

    /**
     * 组装试题
     * @param paper
     * @param fullAnswerRecords
     * @return
     */
    private List<AnswerRecord> packQuestionsFromCacheOrDBToPaper(Integer examRegion, PaperInstance paper, String examId, List<AnswerRecord> fullAnswerRecords) {

        //从缓存或数据库查询该考试对应的所有题目
        List<QuestionCopy> questionCopys = questionCache.get(examId, () -> {
            return questionCopyService.findQuestionCopysByExamId(examId);
        }, Exam.CACHE_TIME);
        //试题映射
        Map<String, QuestionCopy> questionCopyMap = questionCopys.stream()
                .collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
        //子题目映射
        Map<String, List<QuestionCopy>> subsMap = questionCopys.stream()
                .filter(f -> f.getParentId() != null)
                .collect(Collectors.groupingBy(QuestionCopy::getParentId));

        //根据paperId查出试卷试题的顺序(随机组卷使用)
        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceQuestionCopyCache.get(paper.getId(), () -> {
            return paperInstanceService.findPaperInstanceQuestionCopiesByPaperId(examRegion, paper.getId(), examId);
        }, Exam.CACHE_TIME);

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopiesMap = paperInstanceQuestionCopies.stream().collect(
                Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

      //根据paper持有的paperQuestionCopyIds组装对应的试题
        List<QuestionCopy> paperQuestions = paper.getQuestionCopyIds().stream().map(id -> {
            QuestionCopy questionCopy = questionCopyMap.get(id);
            //设置对应的默认试题顺序
            if (questionCopy != null && paperInstanceQuestionCopiesMap.get(questionCopy.getId()) != null)
            questionCopy.setSequence(paperInstanceQuestionCopiesMap.get(questionCopy.getId()).getSequence());
            return questionCopy;
        }).collect(Collectors.toList());

        // 组装阅读题
        try {
            if (paperQuestions == null || paperQuestions.size() == 0)
                return null;
            paperQuestions.stream().filter(f -> f !=null && Question.READING_COMPREHENSION == f.getType()).forEach(q -> {
                if (subsMap != null) {
                    List<QuestionCopy> subs = subsMap.get(q.getId());
                    if (subs != null) {
                        subs.forEach(s -> {
                            if (paperInstanceQuestionCopiesMap.get(s.getId()) != null)
                            s.setSequence(paperInstanceQuestionCopiesMap.get(s.getId()).getSequence());
                        });
                    }
                    if (q!=null){
                        q.setSubs(subs);
                    }
                }
            });
            Map<String, AnswerRecord> fullAnswerRecordsMap = fullAnswerRecords.stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));

            Collections.sort(paperQuestions);
            paper.setQuestions(new ArrayList<>());
            paper.getQuestions().addAll(paperQuestions);
            List<AnswerRecord> answerRecordList = paper.getQuestions().stream().map(t -> {
                    AnswerRecord answerRecord = new AnswerRecord();
                    answerRecord.setQuestionId(t.getId());
                    if (fullAnswerRecordsMap.get(t.getId()) == null)
                        answerRecord.setAnswer(null);
                    else
                        answerRecord.setAnswer(fullAnswerRecordsMap.get(t.getId()).getAnswer());
                    answerRecord.setQuestionCopy(t);

                    return answerRecord;
                }).collect(Collectors.toList());

            return answerRecordList;
        } catch (Exception e2) {
            e2.printStackTrace();
        }
        List<AnswerRecord> list = new ArrayList<AnswerRecord>();
        return list;

    }
    /**
     * 添加考生-考试管理
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/add-user-to-exam", method = RequestMethod.POST)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "ids", type = String.class, required = true)
    @JSON("*")
//    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.UPDATE, fisrtAction = "管理-考生管理", secondAction = "添加考生", desc = "操作添加考生至考试《{0}》", ids = {"examId"}, jsons = {"name"}, keys = {"exam-detail"})
    public Map<String, Object> addUserToExam(RequestContext requestContext, Subject<Member> subject) {
        String currentUserId = subject.getCurrentUserId();
        String[] ids = requestContext.getString("ids").split(",");
        List<String> memberIds = Arrays.asList(ids);
        // 查询南区的人
        List<String> southIds = memberService.findSouthMemberIds(memberIds);
        // 北区的人
        List<String> northIds = memberIds.stream()
                .filter(str -> southIds.stream().noneMatch(s -> s.equals(str)))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(southIds)) {
            examRecordService.addExaminee(
                    Exam.SOURCE,
                    requestContext.getString("examId"),
                    southIds);
        }
        if (CollectionUtils.isNotEmpty(northIds)) {
            examRecordService.addExaminee(
                    Exam.NORTH,
                    requestContext.getString("examId"),
                    northIds);
        }

        return ImmutableMap.of("success", "success");
    }


    /**
     * 删除考试纪录
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "memberName", type = String.class)
    @Param(name = "examName", type = String.class)
    @JSON("*")
    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.DELETE, fisrtAction = "管理-考生管理", secondAction = "删除", desc = "对{0}操作删除考试记录于考试《{1}》", params = {"memberName","examName"})
    public Map<String, Object> delete(RequestContext requestContext) {
        String memberId = requestContext.get("memberId", String.class);
        Integer examRegion = memberService.findExamRegion(memberId);
        ExamRecord record = examRecordService.delete(examRegion, requestContext.get("id", String.class),
                requestContext.get("examId", String.class),
                requestContext.get("memberId", String.class));
        examRecordCache.clear(ExamRecord.getExamRecordKey(record.getExamId(), record.getMemberId()));
        return ImmutableMap.of("id", record.getId());
    }


    /**
     * 导出个人中心考试
     * @param context
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/export-archivr-list", method = RequestMethod.GET)
    @Param(name="startTime", type=Long.class)
    @Param(name="endTime", type=Long.class)
//    @Permitted()
    public void exportArchivrList(RequestContext context, Subject<Member> subject) throws IOException {
        String title = "个人考试记录";

        HttpServletResponse response = context.getResponse();

        String attachmentName = title;

        response.setContentType("application/octet-stream;charset=utf-8");

        response.setHeader("Content-Disposition", "attachment;filename="
                + parseFileName(attachmentName, context.getRequest().getHeader("User-Agent")) + ".xlsx");

        Optional<Long> startTime = context.getOptional("startTime",Long.class);
        String examRegistStringTable = ExamRegist.STRING_EXAM_REGIST;
        String examRecordStringTable = ExamRecord.STRING_EXAM_RECORD;
        if (startTime.isPresent()) {
            examRegistStringTable += "_"+DateUtil.dateLongToString(startTime.get(), "yyyy");
            examRecordStringTable += "_"+DateUtil.dateLongToString(startTime.get(), "yyyy");
        }
        List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
        Integer examRegion = subject.examRegion();
        List<Exam> list = examService.findAllArchivrList(
                examRegion,
                context.getOptional("startTime",Long.class),
                context.getOptional("endTime",Long.class),
                subject.getCurrentUserId(),
                userIds,
                examRegistStringTable,
                examRecordStringTable);

        Writer writer = new ExcelWriter();
//        1: '未发布', 2: '发布中', 3: '未开始', 4: '报名中', 5: '开考中', 6: '已结束'
        Map<Integer,String> statusMap = new HashMap<>();
        statusMap.put(1, "未发布");
        statusMap.put(2, "发布中");
        statusMap.put(3, "未开始");
        statusMap.put(4, "报名中");
        statusMap.put(5, "开考中");
        statusMap.put(6, "已结束");
        //审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名
        Map<Integer,String> signupStatusMap = new HashMap<>();
        signupStatusMap.put(1, "待审核");
        signupStatusMap.put(2, "已通过");
        signupStatusMap.put(3, "被拒绝 ");
        signupStatusMap.put(4, "取消报名");
        Map<Integer,String> typeStatusMap = new HashMap<>();
        typeStatusMap.put(1, "正试考试");
        typeStatusMap.put(2, "非正式考试");
        typeStatusMap.put(3, "认证考试");
        typeStatusMap.put(4, "认证考试");
        writer.sheet(title, list)
                .field("考试名称", m -> m.getName())
                .field("考试类型", m -> typeStatusMap.get(m.getType()))
                .field("进入考试开始时间",m -> m.getStartTime() == null ? "" : DateUtil.dateLongToString(m.getStartTime(), DateUtil.YYYY_MM_DD_HH_MM))
                .field("截止进入考试时间",m -> m.getEndTime() == null ? "" : DateUtil.dateLongToString(m.getEndTime(), DateUtil.YYYY_MM_DD_HH_MM))
                .field("考试状态", m -> statusMap.get(m.getStatus()))
                .field("总分", m -> changeNumDecimal(m.getExamRecord().getTotalScore() != null
                        ? (((float)m.getExamRecord().getTotalScore() / 100) + "") : ""))
                .field("及格分", m -> m.getPassScore() == null ? "" : m.getPassScore().toString())

                .field("参考状态",m -> getJoinStatus(m))
                .field("交卷时间", m -> m.getExamRecord(), x-> x.getSubmitTime() == null ? "" : DateUtil.dateLongToString(x.getSubmitTime(), DateUtil.YYYY_MM_DD_HH_MM_SS))
                .field("成绩", m -> (Exam.NO_SHOW_ANYTHING == m.getShowAnswerRule() || (m.getShowScoreTime() != null && System.currentTimeMillis() < m.getShowScoreTime())) ? "-" : changeNumDecimal(getScore(m)))
                .field("是否及格", m -> (Exam.NO_SHOW_ANYTHING == m.getShowAnswerRule() || (m.getShowScoreTime() != null && System.currentTimeMillis() < m.getShowScoreTime())) ? "-" : getPassStatusStr(m));
        writer.write(response.getOutputStream());
    }



    private String getPassStatusStr(Exam exam) {
        ExamRecord examRecord = exam.getExamRecord();
        if (exam.getPassScore() != null && examRecord.getStatus() != null && examRecord.getStatus() > 5) {
            return (examRecord.getScore() / 100) >= exam.getPassScore() ? "是" : "否";
        }
        return "";
    }
    

    private String getScore(Exam exam) {
        if (exam.getExamRecord().getScore() != null) {
            return ((float)exam.getExamRecord().getScore().intValue() / 100) + "";
        }
        return "";
    }
    

    private String parseFileName(String fileName, String agent) throws UnsupportedEncodingException {
        if (BrowserUtil.isMSBrowser(agent)) {
            return URL.encode(fileName);
        }
        return new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
    }



    /**
     * 判断
     * 0.0 -> 0
     * 1.0 -> 1
     * 2.1 -> 2.1
     * @param num
     * @return
     */
    private String changeNumDecimal(String num) {
        if (num != null && !num.equals("")) {
            String[] attr = num.split("\\.");
            return attr[1].equals("0") ? attr[0] : num;
        }
        return "";
    }



    private String getJoinStatus(Exam exam) {
        Map<Integer,String> joinStatus = new HashMap<>();
        joinStatus.put(1, "待开始");
        joinStatus.put(2, "待审核");
        joinStatus.put(3, "被拒绝");
        joinStatus.put(4, "已完成");
        joinStatus.put(5, "待考试");
        joinStatus.put(6, "未参加");
        joinStatus.put(7, "异常");
        joinStatus.put(8, "");

        Long currentTime = System.currentTimeMillis();
        ExamRecord examRecord = exam.getExamRecord();
        SignUp signUp = exam.getSignUp();
        //被拒绝但是有考试成绩，则显示已完成
        if (signUp.getStatus() != null &&  signUp.getStatus() == SignUp.STATUS_REFUSE && examRecord.getScore()!=null)  return joinStatus.get(4);

        //  待审核
        if (signUp.getStatus() != null && signUp.getStatus() == SignUp.STATUS_APPROVE) return joinStatus.get(2);
        //  被拒绝
        if (signUp.getStatus() != null &&  signUp.getStatus() == SignUp.STATUS_REFUSE)  return joinStatus.get(3);
        //  待开始
        if (exam.getStartTime() > currentTime) return joinStatus.get(1);
        //  待考试
        if (exam.getStartTime() < currentTime
                && exam.getEndTime() > currentTime
                && (examRecord.getStatus() != null && examRecord.getStatus() == ExamRecord.STATUS_TO_BE_STARTED)) return joinStatus.get(5);
        //  已完成
        if (examRecord.getStatus() != null && examRecord.getStatus() > ExamRecord.STATUS_TIME_EXCEPTION) return joinStatus.get(4);
        //  未参加
        if (exam.getEndTime() < currentTime && examRecord.getStatus() != null && examRecord.getStatus() == ExamRecord.STATUS_TO_BE_STARTED) return joinStatus.get(6);
        //  异常
        if (examRecord.getStatus() != null && examRecord.getStatus() == ExamRecord.STATUS_TIME_EXCEPTION) return joinStatus.get(7);

        return joinStatus.get(8);
    }



    /**
     * 判断强制交卷，考生客户端是否属于离线状态
     */
    @RequestMapping(value = "/is-force", method = RequestMethod.POST)
    @Param(name = "userId", type = String.class, required = true)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "force", type = Integer.class)
    @JSON("*")
//    @Permitted()
    public Map<String, Object> IsforceSubmitPaper(RequestContext requestContext,
                                                  Subject<Member> subject) {

        String examId = requestContext.getString("examId");
        String userId = requestContext.getString("userId");
        String currentUserId = subject.getCurrentUserId();

        Optional<Integer> force = requestContext.getOptionalInteger("force");
        if (!force.isPresent() && !hadCacheSession(ExamRecord.getExamRecordKey(examId, userId))) {
            return ImmutableMap.of("status", "0");
        }
        return ImmutableMap.of("status", "1");
    }

    /**
     * 强制交卷
     * 1.客户端打开，客户端接受ws消息，提示交卷成功
     * 2.客户端关闭，判断session不存在，直接把临时答题记录进行计算
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/force-submit-paper", method = RequestMethod.POST)
    @Param(name = "userId", type = String.class, required = true)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "memberName", type = String.class)
    @Param(name = "examName", type = String.class)
    @Param(name = "force", type = Integer.class)
    @JSON("*")
    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.MANAGE, fisrtAction = "管理-考生管理", secondAction = "强制交卷", desc = "对{0}操作强制交卷于考试《{1}》", params = {"memberName", "examName"})
    public Map<String, Object> forceSubmitPaper(RequestContext requestContext,
                                                Subject<Member> subject) {

        String examId = requestContext.getString("examId");
        String userId = requestContext.getString("userId");

        Optional<Integer> force = requestContext.getOptionalInteger("force");
        if (!force.isPresent() && !hadCacheSession(ExamRecord.getExamRecordKey(examId, userId))) {
            return ImmutableMap.of("status", "0");
        }

        Integer examRegion = memberService.findExamRegion(userId);

        //如果force为1，进行离线强制交卷(把临时提交的答案算分)
        force.map(f -> {
            offLineForceSubmitPaper(examRegion, examId, userId);
            return true;
        }).orElseGet(() -> {
            //异步进行在线强制交卷
            examMessageSender.send(
                    MessageTypeContent.EXAM_EXAM_RECORD_SUBMIT,
                    MessageHeaderContent.IDS, requestContext.getString("userId"),
                    MessageHeaderContent.EXAM_ID, requestContext.getString("examId"));
            return true;
        });

        LOGGER.info("force-submit-paper, userId:{},examId:{}",
                requestContext.getString("userId"), requestContext.getString("examId"));

        return ImmutableMap.of("status", "1");
    }

    /**
     * 判断离线状态后，直接强制交卷
     */
    private void offLineForceSubmitPaper(Integer examRegion, String examId, String userId) {
        examRecordCache.clear(ExamRecord.getExamRecordKey(examId, userId));
        LOGGER.info("强制交卷，临时试题算分：exmId:{}, userId:{}", examId, userId);
        examStuRecordService.normalForceSubmitPaper(examRegion, examId, userId);
    }

    /**
     * 延时
     * 1.更改考试记录结束时间，
     * 2.客户端连着，提示用户被延时多少分钟，直接更改页面时间
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/add-submit-time", method = RequestMethod.POST)
    @Param(name = "time", type = Integer.class, required = true)
    @Param(name = "examRecordId", type = String.class, required = true)
    @Param(name = "examId", type = String.class, required = true)
    @Param(name = "memberId", required = true) // 该考试记录的人员id
    @Param(name = "force", type = Integer.class)
    @JSON("*")
    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.MANAGE, fisrtAction = "管理-考生管理", secondAction = "延时交卷", desc = "对{0}操作延时交卷于考试《{1}》", ids = {"examRecordId","examId"}, jsons = {"memberName","examName"}, keys = {"exam-record-name"})
    public Map<String, Object> addSubmitTime(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);

        String examRecordId = requestContext.getString("examRecordId");
        String examId = requestContext.getString("examId");
        ExamRecord examRecord = examRecordService.get(examRegion, examRecordId, examId);
        Optional<Integer> force = requestContext.getOptionalInteger("force");

        // 检查websocket连接
        if (!force.isPresent() && !hadCacheSession(
                ExamRecord.getExamRecordKey(
                        examRecord.getExamId(), examRecord.getMemberId()))) {
            return ImmutableMap.of("status", "0");
        }

        //修改DB记录结束时间
        ExamRecord recordDB = examRecordService.delayExamRecordEndTime(
                examRegion,
                requestContext.get("examRecordId", String.class),
                requestContext.get("time", Integer.class),
                examId
        );

        //获取缓存记录
        ExamRecord recordCache = examRecordCache.get(ExamRecord.getExamRecordKey(recordDB.getExamId(), recordDB.getMemberId()), () -> {
            return examRecordService.getCurrentRecord(examRegion, recordDB.getExamId(), recordDB.getMemberId());
        });
        //更改缓存记录结束时间
        recordCache.setEndTime(recordDB.getEndTime());
        examRecordCache.set(ExamRecord.getExamRecordKey(recordCache.getExamId(), recordCache.getMemberId()), recordCache, Exam.CACHE_TIME);

        examMessageSender.send(
                MessageTypeContent.EXAM_TIME_EXPAND,
                MessageHeaderContent.ID, examRecord.getMember().getId(),
                MessageHeaderContent.TIME, String.valueOf(requestContext.get("time", Integer.class)),
                MessageHeaderContent.EXAM_ID, examRecord.getExamId());

        return ImmutableMap.of("status", "1");
    }

    private boolean hadCacheSession(String key) {
        String value = examUserCache.get(EXAM_USER+"_"+key, String.class);
        return value != null;
    }



    /**
     * 作废成绩
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/nullify", method = RequestMethod.POST)
    @Param(name = "examRecordId", required = true)
    @Param(name = "examId", required = true)
    @Param(name = "memberName", required = true)
    @Param(name = "examName", required = true)
    @Param(name = "memberId", required = true) // 该考试记录的人员id
    @JSON("*")
    @Permitted()
    @Audit(module = "考试管理", subModule = "考试", action = Audit.Action.MANAGE, fisrtAction = "管理-考生管理", secondAction = "作废成绩", desc = "对{0}操作作废成绩于考试《{1}》", params = {"memberName", "examName"})
    public Map<String, Object> nullifyExamRecord(RequestContext requestContext) {
        String examRecordId = requestContext.getString("examRecordId");
        String examId = requestContext.getString("examId");
        String memberId = requestContext.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        examRecordService.nullifyExamRecord(examRegion, examRecordId, examId);
        return ImmutableMap.of("status", "1");
    }



}
