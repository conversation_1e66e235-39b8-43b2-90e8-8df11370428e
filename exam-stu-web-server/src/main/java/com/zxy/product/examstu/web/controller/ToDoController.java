package com.zxy.product.examstu.web.controller;


import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.examstu.api.ToDoService;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.ToDo;
import com.zxy.product.human.api.MemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/to-do")
public class ToDoController {

	private ToDoService toDoService;
	private MemberService memberService;


    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

	@Autowired
	public void setToDoService(ToDoService toDoService) {
		this.toDoService = toDoService;
	}

    @RequestMapping(method = RequestMethod.GET)
    @JSON("id,createTime,type,memberId,targetId")
    public List<ToDo> find(Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return toDoService.find(examRegion, subject.getCurrentUserId());
    }


    @RequestMapping(value="/mark-papers", method = RequestMethod.GET)
    @JSON("id,createTime,type,memberId,targetId,submitTime")
    @JSON("examRecord.(id,status,lastSubmitTime,score)")
    @JSON("examRecord.member.(id,name,fullName)")
    @JSON("examRecord.member.organization.(id,name)")
    @JSON("examRecord.exam.(id,name,sourceType)")
    public List<ToDo> findMarkPapers(Subject<Member> subject) {
    	return toDoService.findMarkPapers(subject.getCurrentUserId());
    }

    @RequestMapping(value="/mark-papers/page", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required=true)
    @Param(name = "pageSize", type = Integer.class, required=true)
    @Param(name = "name", type = Integer.class)
    @Param(name = "wait", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,createTime,type,memberId,targetId,submitTime,includeType,auditTime,audited,submited)")
    @JSON("items.examRecord.(id,status,lastSubmitTime,submitTime,score)")
    @JSON("items.examRecord.member.(id,name,fullName)")
    @JSON("items.examRecord.member.organization.(id,name)")
    @JSON("items.examRecord.exam.(id,name,sourceType,anonymityMark,showScoreTime)")
    public PagedResult<ToDo> findPageForMarkPapers(RequestContext requestContext, Subject<Member> subject) {
    	return toDoService.findPageForMarkPapers(
    		requestContext.getInteger("page"),
    	    requestContext.getInteger("pageSize"),
    	    subject.getCurrentUserId(),
    	    requestContext.getOptionalString("name"),
    	    requestContext.getOptionalInteger("wait")
    	);
    }

    @RequestMapping(value="/remove", method = RequestMethod.POST)
    @Param(name = "targetId", required=true)
    @Param(name = "examId", required=true)
    @Param(name = "memberId", required=true)
    @JSON("*")
    public Map<String, String> removeToDo(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        toDoService.deleteByTargetId(examRegion, requestContext.getString("targetId"), subject.getCurrentUserId(),requestContext.getString("examId"));
        return ImmutableMap.of("success", "ok");
    }


}
