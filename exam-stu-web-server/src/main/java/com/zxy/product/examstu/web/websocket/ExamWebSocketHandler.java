package com.zxy.product.examstu.web.websocket;

import com.alibaba.fastjson.JSON;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.websocket.MessageTemplate;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.examstu.web.config.MemberSecurityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ServletContextAware;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
public class ExamWebSocketHandler extends TextWebSocketHandler implements ServletContextAware {

	private static final Logger LOGGER = LoggerFactory.getLogger(ExamWebSocketHandler.class);

	public static final String EXAM_USERS = "exam_users";

	public static final String EXAM_USER = "exam_user";

	private static final String PING = "PING";

	private static final String PANG = "PANG";

	public static final String SUBMIT_PAPER = "submitPaper";

	public static final String TIME_EXPAND = "timeExpand";

	public static final String MUTIPLE_CLIENT_LOGIN = "mutipleClientLogin";

	private static Map<String, ExamUser> examUsers = new ConcurrentHashMap<>();

	private Cache examUserCache;

	private ServletContext context = null;

	private MemberSecurityManager memberSecurityManager;

	@Override
	protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {

		String payload = message.getPayload();

		MessageTemplate msg = JSON.parseObject(payload, MessageTemplate.class);
		HttpServletRequest request = msg.buildRequest(context, session.getAttributes(), session.getRemoteAddress(),session.getLocalAddress());
		Optional<Subject<Member>> optional = memberSecurityManager.getOptional(request);
		optional.map(t -> {
			try {
				String examId = "";
				Map<String, Object> data = msg.getData();

				if (data.get(PING) != null && data.get(PING).toString().equals(PING)) {
//					examId = msg.getData().get("examId").toString();
//					if (otherClientLogin(createExamUserKey(examId, t.getCurrentUserId()), examId, session)) {
//						LOGGER.info("********************otherClientLogin，examid:{}, userId:{} ", examId, t.getCurrentUserId());
//						session.sendMessage(new TextMessage(MUTIPLE_CLIENT_LOGIN));
//						return null;
//					}
					session.sendMessage(new TextMessage(PANG));
					return null;
				}

				examId = msg.getData().get("examId").toString();
				Long time = System.currentTimeMillis();
				addExamUser(createExamUser(session, examId, t.getCurrentUserId(), time));
				session.sendMessage(new TextMessage("joined:" + msg.getId()));


			} catch (Exception e) {
				LOGGER.error("fail to handle exam websocket request", e);
				e.printStackTrace();
			}
			return null;
		});
	}

	/**
	 * 判断多个客户端同时登陆
	 * @param key
	 * @return
	 */
	private boolean otherClientLogin(String key, String examId, WebSocketSession session) {
		String value = examUserCache.get(EXAM_USER+"_"+key, String.class);

		if (value == null) return false;//redis没有直接返回false

		return singleWebServerMutipleLogin(key, session) || !isSameSessionTimestamp(key, value);
	}

	/**
	 * 单台服务器时，判断传进的session和map的session不一样时，把传进来的session发送消息回客户端，强制下线
	 *
	 * 如果两个client请求到同一台web-server,那么后面来的会覆盖内存map里的session,然后原来的那个session
	 * 当请求心跳时发现和map里的不一致，就会处理相应逻辑
	 * @param key
	 * @param session
	 * @return
	 */
	private boolean singleWebServerMutipleLogin(String key, WebSocketSession session) {
		ExamUser examUser = examUsers.get(key);
		boolean f = !session.getId().equals(examUser.getSession().getId());
		if (f) {
			LOGGER.error("singleWebServerMutipleLogin, heart-session-id:{}, mapSessionId:{}", session.getId(), examUser.getSession().getId());
		}
		return f;
	}

	/**
	 * 判断是否相同时间创建的session
	 * 解决多台服务器集群-多客户端访问同一考试
	 *
	 * 原理：A,B两个client分别前后请求落在a,b两台web-server时，
	 * @param redisValue
	 * @return
	 */
	private boolean isSameSessionTimestamp(String key, String redisValue) {
		ExamUser examUser = examUsers.get(key);
		if (examUser != null) {
			String mapValue = createRedisSessionValue(examUser);
			boolean f = mapValue.equals(redisValue);
//			if (!f) {
//				LOGGER.error("*******isSameSessionTimestamp redisValue:{}, mapValue: {}", redisValue, mapValue);
//			}
			return f;
		}
		return false;
	}

	public boolean hasExamUserCache(String key, String examId) {
	    String value = examUserCache.get(EXAM_USER+"_"+key, String.class);
		return value != null;
	}

	/**
	 * 用户打开考试页面第一次链接websocket创建  examuser
	 * 缓存session
	 * @param session
	 * @param examId
	 * @param currentUserId
	 * @return
	 */
	private ExamUser createExamUser(WebSocketSession session, String examId, String currentUserId, Long timestamp) {
		ExamUser examUser = new ExamUser();
		examUser.setUserId(currentUserId);
		examUser.setExamId(examId);
		examUser.setSession(session);
		examUser.setTime(timestamp);
		return examUser;
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
		ExamUser examUser = removeExamUser(session.getId());
		if (examUser == null) return;
		removeExamUserCache(session.getId(), examUser);
	}

	/**
	 * 清除缓存
	 * @param sessionId
	 */
	private void removeExamUserCache(String sessionId, ExamUser examUser) {
		String redisSessionValue = examUserCache.get(EXAM_USER+"_"+examUser.getExamId()+"#"+examUser.getUserId(), String.class);//redis存的值
		String matchValue = createRedisSessionValue(examUser);//内存存的值
		if (redisSessionValue != null && matchValue != null && redisSessionValue.equals(matchValue)) {
			examUserCache.clear(EXAM_USER+"_"+examUser.getExamId()+"#"+examUser.getUserId());
			LOGGER.info("removeExamUserCache，value:{}", redisSessionValue);
		}
	}

	/**
	 * 发送消息给对应的客户端
	 * @param examId
	 * @param userIds
	 * @param message
	 */
	public void sendMessageToUser(String examId, List<String> userIds, String message) {
		userIds.stream().forEach(key -> {
			sendMessageToUser(createExamUserKey(examId, key), message);
		});
	}

	public void sendMessageToUser(String key, String message) {
		try {
			if (examUsers.containsKey(key)) {
				ExamUser examUser = examUsers.get(key);
				WebSocketSession session = examUser.getSession();
				if (session.isOpen()) {
					LOGGER.info("session is open,sessionId:{} message:{} ", session.getId(), message);
					session.sendMessage(new TextMessage(message));
				}
			}
		} catch (IOException e) {
			LOGGER.error("exam web socket send message failed", e);
		}
	}

	@SuppressWarnings("unchecked")
	public Set<String> getOnlineUserId(String examId) {
		Set<String> users = examUserCache.get(examId, HashSet.class);
		if (users == null) {
			return new HashSet<>();
		}
		return users;
	}

	private void addExamUser(ExamUser examUser) {
		examUsers.put(createExamUserKey(examUser.getExamId(), examUser.getUserId()), examUser);
		addExamUserCache(examUser);
		LOGGER.info("addRedisCacheWsSession，examid:{}, userId:{}, sessionId:{}, timeStamp:{}",
				examUser.getExamId(), examUser.getUserId(), examUser.getSession().getId(), examUser.getTime());
	}

	private void addExamUserCache(ExamUser examUser) {
		examUserCache.set(EXAM_USER+"_"+examUser.getExamId()+"#"+examUser.getUserId(), createRedisSessionValue(examUser), Exam.CACHE_TIME);
	}

	private String createRedisSessionValue(ExamUser examUser) {
		return examUser.getSession().getId() + "#" + examUser.getTime();
	}

	private String createExamUserKey(String examId, String userId) {
		return ExamRecord.getExamRecordKey(examId, userId);
	}

	private ExamUser removeExamUser(String sessionId) {
		List<ExamUser> list = examUsers.values().stream().filter((examUser) -> {
			WebSocketSession session = examUser.getSession();
			if (session != null && session.getId().equals(sessionId)) {
				return true;
			}
			return false;
		}).collect(Collectors.toList());

		if (!list.isEmpty()) {
			ExamUser examUser = list.get(0);
			examUsers.remove(createExamUserKey(examUser.getExamId(), examUser.getUserId()));
			LOGGER.info("removeExamUser，mapValue:{} ", createRedisSessionValue(examUser));
			return examUser;
		}
		return null;
	}

	public class ExamUser {
		private String examId;
		private String userId;
		private WebSocketSession session;
		private Long time;

		public WebSocketSession getSession() {
			return session;
		}

		public void setSession(WebSocketSession session) {
			this.session = session;
		}

		public String getExamId() {
			return examId;
		}

		public void setExamId(String examId) {
			this.examId = examId;
		}

		public String getUserId() {
			return userId;
		}

		public void setUserId(String userId) {
			this.userId = userId;
		}

		public void setTime(Long time) {
			this.time = time;
		}

		public Long getTime() {
			return time;
		}

	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.examUserCache = cacheService.create("cacheExamUser", EXAM_USERS);
	}

	@Override
	public void setServletContext(ServletContext servletContext) {
		context = servletContext;
	}

	@Autowired
	public void setMemberSecurityManager(MemberSecurityManager memberSecurityManager) {
		this.memberSecurityManager = memberSecurityManager;
	}

	public boolean isSessionOpen(String key) {
		LOGGER.info("isSessionOpen:{}", key);
		if (examUsers.containsKey(key)) {
			LOGGER.info("examUsers.containsKey:true:", key);
			ExamUser examUser = examUsers.get(key);
			WebSocketSession session = examUser.getSession();
			return session.isOpen();
		}
		return false;
	}

}
