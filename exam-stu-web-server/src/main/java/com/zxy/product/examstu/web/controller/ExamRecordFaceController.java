package com.zxy.product.examstu.web.controller;


import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.examstu.api.ExamRecordFaceService;
import com.zxy.product.exam.entity.ExamRecordFace;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/exam-record-face")
public class ExamRecordFaceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExamRecordFaceController.class);


    private ExamRecordFaceService examRecordFaceService;
    private MemberService memberService;


    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setExamRecordFaceService(ExamRecordFaceService examRecordFaceService) {
        this.examRecordFaceService = examRecordFaceService;
    }


    /**
     * 考试人脸监考过程
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/face/invigilation", method = RequestMethod.POST)
    @Param(name = "memberId", required = true)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId")
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "faceImageId", required = true)
    @Param(name = "status", type = Integer.class, required = true)
    @Param(name = "respMsg")
    @JSON("*")
//    @Permitted()
    public ExamRecordFace faceInvigilation(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examRecordFaceService.faceInvigilation(
                examRegion,
                requestContext.getString("memberId"),
                requestContext.getString("examId"),
                requestContext.getOptionalString("examRecordId"),
                requestContext.getInteger("type"),
                requestContext.getString("faceImageId"),
                requestContext.getInteger("status"),
                requestContext.getOptionalString("respMsg"));
    }


    /**
     * 个人中心-申请管理员复核
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/center/review", method = RequestMethod.POST)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId")
    @JSON("*")
//    @Permitted()
    public Map<String, Object> centerReview(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        examRecordFaceService.faceInvigilation(
                examRegion,
                subject.getCurrentUserId(),
                requestContext.getString("examId"),
                requestContext.getOptionalString("examRecordId"),
                ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ENTER,
                "",
                ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_ABNORMAL,
                Optional.empty());

        examRecordFaceService.frontReview(examRegion, subject.getCurrentUserId(),
                requestContext.getString("examId"));

        return ImmutableMap.of("success", "success");
    }



    /**
     * 个人中心-查询监考老师审核记录
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/center/review/record", method = RequestMethod.GET)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId")
    @JSON("id,status")
//    @Permitted()
    public ExamRecordFace findReviewRecord(RequestContext requestContext, Subject<Member> subject) {
        return examRecordFaceService.findReviewRecord(subject.examRegion(),subject.getCurrentUserId(),
                requestContext.getString("examId"),
                requestContext.getOptionalString("examRecordId").orElse(""));
    }

    /**
     * @author: <EMAIL>
     * @Date: 2021/12/9 17:35
     * @Description:获取人脸照片数量
     */
    @RequestMapping(value = "/face/count", method = RequestMethod.GET)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId",required = true)
    @Param(name = "type", type = Integer.class, required = true)
    @JSON("*")
//    @Permitted()
    public Integer faceCount(RequestContext context,Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        List<ExamRecordFace> list = examRecordFaceService.getListByRecordId(
                examRegion,
                subject.getCurrentUserId(),
                context.getString("examId"),
                context.getString("examRecordId"),
                context.getInteger("type"),
                Optional.empty());
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }



    /**
     * 后台复核(考试中)
     *
     * <AUTHOR>
     * @date 2021/11/20
     */
    @RequestMapping(value = "/back/review", method = RequestMethod.PUT)
    @Param(name = "examId", required = true)
    @Param(name = "memberId", required = true) //这条数据的memberId
    @Param(name = "examRecordId", required = true)
    @Param(name = "faceStatus", type = Integer.class, required = true)
    @JSON("id,examId,faceStatus")
    @Permitted()
    public ExamRecord backReview(RequestContext context) {
        String memberId = context.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        return examRecordFaceService.backReview(
                examRegion,
                context.getString("examId"),
                context.getString("examRecordId"),
                context.getInteger("faceStatus")
        );
    }



    /**
     * 后台复核（进入考试）
     *
     * <AUTHOR>
     * @date 2021/12/18
     */
    @RequestMapping(value = "/back/review-faceEnter", method = RequestMethod.PUT)
    @Param(name = "examId", required = true)
    @Param(name = "memberId", required = true) //这条数据的memberId
    @Param(name = "examRecordId")
    @Param(name = "faceId", required = true)
    @Param(name = "faceStatus", type = Integer.class, required = true)
    @JSON("*")
    @Permitted()
    public Map<String, Object> backReviewFaceEnter(RequestContext context) {
        String memberId = context.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        return ImmutableMap.of("faceId", examRecordFaceService.backReviewNew(
                examRegion,
                context.getString("faceId"),
                context.getString("examId"),
                context.getOptionalString("examRecordId"),
                context.getInteger("faceStatus")
        ));
    }


    /**
     * 前台申请复核
     *
     * <AUTHOR>
     * @date 2021/11/20
     */
    @RequestMapping(value = "/front/review", method = RequestMethod.GET)
    @Param(name = "examId", required = true)
    @JSON("*")
//    @Permitted()
    public Map<String, Object> frontReview(RequestContext context, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        examRecordFaceService.frontReview(examRegion, subject.getCurrentUserId(),
                context.getString("examId"));
        return ImmutableMap.of("success", "success");
    }


    /**
     * 考试监考学生人脸详情
     *
     * <AUTHOR>
     * @date 2021/12/07
     */
    @RequestMapping(value = "/face/detail", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId")
    @JSON("*.*")
    @Permitted()
    public Map<Integer, List<ExamRecordFace>> faceDetail(RequestContext context) {
        String memberId = context.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        return examRecordFaceService.faceDetail(examRegion,
                context.getString("memberId"),
                context.getString("examId"),
                context.getOptionalString("examRecordId"));
    }



    /**
     * 考试监考不检测直接进入考试
     */
    @RequestMapping(value = "/face/upFaceStatus", method = RequestMethod.POST)
    @Param(name = "memberId", required = true)
    @Param(name = "examId", required = true)
    @Param(name = "examRecordId")
    @Param(name = "type", type = Integer.class)
    @JSON("*.*")
//    @Permitted()
    public ExamRecordFace upFaceStatus(RequestContext context, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examRecordFaceService.upFaceStatus(examRegion,context.getString("memberId"),
                context.getString("examId"),
                context.getOptionalString("examRecordId"),
                context.getOptionalInteger("type")
                );
    }


    /**
     * 考试监考学生人脸详情 没有examRecordId版本
     */
    @RequestMapping(value = "/face/detail2", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "examId", required = true)
    @Param(name = "type", required = true)
    @JSON("*.*")
//    @Permitted()
    public Map<Integer, List<ExamRecordFace>> faceDetail2(RequestContext context,Subject<Member> subject) {
        Integer[] types =
                Arrays.stream(context.getString("type").split(","))
                        .map(Integer::parseInt)
                        .toArray(Integer[]::new);
        String memberId = context.getString("memberId");
        return examRecordFaceService.faceDetail2(memberService.findExamRegion(memberId), memberId,
                context.getString("examId"),types);
    }

}
