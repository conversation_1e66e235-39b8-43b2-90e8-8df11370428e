package com.zxy.product.examstu.web.util;

import com.google.common.collect.Sets;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.entity.Organization;
import com.zxy.product.system.entity.OrganizationImportEntity;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Set;

public class ImportExportUtil {
    private static final String CODE_PATTERN = "^[a-zA-Z0-9_]{1,15}$";

    public static Validator<String> organizationNameValidator() {
        return (v, vc, previous) -> {
            boolean status = false;
            if (!StringUtils.hasText(v)) {
                vc.error(ErrorCode.OrganizationNameRequired);
            } else if (v.length() > 100) {
                vc.error(ErrorCode.OrganizationNameTooLong);
            } else {
                status = true;
            }
            return status;
        };
    }
    public static Validator<String> organizationCodeValidator() {
        return (v, vc, previous) -> {
            boolean status = false;
            if (!StringUtils.hasText(v)) {
                vc.error(ErrorCode.OrganizationCodeRequired);
            } else if (v.length() > 15) {
                vc.error(ErrorCode.OrganizationCodeTooLong);
            } else if (!v.matches(CODE_PATTERN)){
                vc.error(ErrorCode.OrganizationImportCodeNotMatch);
            } else {
                status = true;
            }
            return status;
        };
    }

	 public static Validator<String> parentOrganizationCodeValidator() {
        return (v, vc, previous) -> {
            boolean status = false;
            if (!StringUtils.hasText(v)) {
                vc.error(ErrorCode.ParentOrganizationCodeRequired);
            } else if (v.length() > 15) {
                vc.error(ErrorCode.ParentOrganizationCodeTooLong);
            } else if (!v.matches(CODE_PATTERN)){
                vc.error(ErrorCode.ParentOrganizationImportCodeNotMatch);
            } else {
                status = true;
            }
            return status;
        };
    }
    public static Validator<String> organizationLevelValidator() {
        Set<String> levelSet = Sets.newHashSet();
        levelSet.add("机构");
        levelSet.add("部门");
        return (v, vc, previous) -> {
            if (v != null && !levelSet.contains(v)) {
                vc.error(ErrorCode.OrganizationLevelError);
                return false;
            }
            return true;
        };
    }

    public static Validator<String> organizationStatusValidator() {
        Set<String> statusSet = Sets.newHashSet();
        statusSet.add("启用");
        statusSet.add("禁用");
        return (v, vc, previous) -> {
            if (v != null && !statusSet.contains(v)) {
                vc.error(ErrorCode.OrganizationStatusError);
                return false;
            }
            return true;
        };
    }
    public static OrganizationImportEntity getBySheetRow(Reader.Row row) {
        final String status = "禁用";
        final String level = "机构";
        OrganizationImportEntity o = new OrganizationImportEntity();
        o.setRowIndex(row.getIndex());

        o.setName(row.get(0, String.class));
        o.setCode(row.get(1, String.class));
        o.setParentCode(row.get(2, String.class));
        o.setLevelName(row.get(3, String.class));
        o.setStatusName(row.get(4, String.class));
        o.setLevel(level.equalsIgnoreCase(o.getLevelName()) ? Organization.LEVEL_BRANCH : Organization.LEVEL_DEPARTMENT);
        o.setStatus(status.equalsIgnoreCase(o.getStatusName()) ? Organization.STATUS_DISABLED : Organization.STATUS_ENABLED);
        return o;
    }

    /*
     * 为Excel打上水印工具函数 请自行确保参数值，以保证水印图片之间不会覆盖。 在计算水印的位置的时候，并没有考虑到单元格合并的情况，请注意
     *
     * @param wb
     *            Excel Workbook
     * @param sheet
     *            需要打水印的Excel
     * @param waterRemarkPath
     *            水印地址，classPath，目前只支持png格式的图片，
     *            因为非png格式的图片打到Excel上后可能会有图片变红的问题，且不容易做出透明效果。
     *            同时请注意传入的地址格式，应该为类似："\\excelTemplate\\test.png"
     * @param startXCol
     *            水印起始列
     * @param startYRow
     *            水印起始行
     * @param betweenXCol
     *            水印横向之间间隔多少列
     * @param betweenYRow
     *            水印纵向之间间隔多少行
     * @param XCount
     *            横向共有水印多少个
     * @param YCount
     *            纵向共有水印多少个
     * @param waterRemarkWidth
     *            水印图片宽度为多少列
     * @param waterRemarkHeight
     *            水印图片高度为多少行
     * @throws IOException
     */
    public static void putWaterRemarkToExcel(Workbook wb, Sheet sheet, String waterRemarkPath, int startXCol,
                                             int startYRow, int betweenXCol, int betweenYRow, int XCount, int YCount, int waterRemarkWidth,
                                             int waterRemarkHeight, String content) throws IOException {

        // 加载图片
        ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
        BufferedImage bufferImg = createWaterMark(content);
        ImageIO.write(bufferImg, "png", byteArrayOut);

        // 开始打水印
        Drawing drawing = sheet.createDrawingPatriarch();
//        Drawing drawingPatriarch = sheet.getDrawingPatriarch();
        // 按照共需打印多少行水印进行循环
        for (int yCount = 0; yCount < YCount; yCount++) {
            // 按照每行需要打印多少个水印进行循环
            for (int xCount = 0; xCount < XCount; xCount++) {
                // 创建水印图片位置
                int xIndexInteger = startXCol + (xCount * waterRemarkWidth) + (xCount * betweenXCol);
                int yIndexInteger = startYRow + (yCount * waterRemarkHeight) + (yCount * betweenYRow);
                /*
                 * 参数定义： 第一个参数是（x轴的开始节点）； 第二个参数是（是y轴的开始节点）； 第三个参数是（是x轴的结束节点）；
                 * 第四个参数是（是y轴的结束节点）； 第五个参数是（是从Excel的第几列开始插入图片，从0开始计数）；
                 * 第六个参数是（是从excel的第几行开始插入图片，从0开始计数）； 第七个参数是（图片宽度，共多少列）；
                 * 第8个参数是（图片高度，共多少行）；
                 */
                ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, xIndexInteger,
                        yIndexInteger, xIndexInteger + waterRemarkWidth, yIndexInteger + waterRemarkHeight);

                Picture pic = drawing.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_PNG));
                pic.resize();
            }
        }
    }

    public static BufferedImage createWaterMark(String content) {
        Integer width = 1000;
        Integer height = 500;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);// 获取bufferedImage对象
        String fontType = "黑体";
        Integer fontStyle = Font.BOLD;
        Integer fontSize = 48;
        Font font = new Font(fontType, fontStyle, fontSize);
        Graphics2D g2d = image.createGraphics(); // 获取Graphics2d对象
        image = g2d.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
        g2d.dispose();
        g2d = image.createGraphics();
        g2d.setColor(new Color(0, 0, 0, 90)); //设置字体颜色和透明度
        g2d.setStroke(new BasicStroke(1)); // 设置字体
        g2d.setFont(font); // 设置字体类型  加粗 大小
        g2d.rotate(Math.toRadians(18), (double) image.getWidth() / 2, (double) image.getHeight() / 2);//设置倾斜度
        FontRenderContext context = g2d.getFontRenderContext();
        Rectangle2D bounds = font.getStringBounds(content, context);
        double x = 0;
        double y = 180;
        double ascent = -bounds.getY();
        double baseY = y + ascent;
        // 写入水印文字原定高度过小，所以累计写水印，增加高度
        g2d.drawString(content, (int) x, (int) baseY);
        // 设置透明度
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
        // 释放对象
        g2d.dispose();
        return image;
    }

    public static void createWaterMark(String content, String path) throws IOException {
        Integer width = 300;
        Integer height = 200;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);// 获取bufferedImage对象
        String fontType = "黑体";
        Integer fontStyle = Font.BOLD;
        Integer fontSize = 48;
        Font font = new Font(fontType, fontStyle, fontSize);
        Graphics2D g2d = image.createGraphics(); // 获取Graphics2d对象
        image = g2d.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
        g2d.dispose();
        g2d = image.createGraphics();
        g2d.setColor(new Color(0, 0, 0, 90)); //设置字体颜色和透明度
        g2d.setStroke(new BasicStroke(1)); // 设置字体
        g2d.setFont(font); // 设置字体类型  加粗 大小
        g2d.rotate(Math.toRadians(-10), (double) image.getWidth() / 2, (double) image.getHeight() / 2);//设置倾斜度
        FontRenderContext context = g2d.getFontRenderContext();
        Rectangle2D bounds = font.getStringBounds(content, context);
        double x = (width - bounds.getWidth()) / 2;
        double y = (height - bounds.getHeight()) / 2;
        double ascent = -bounds.getY();
        double baseY = y + ascent;
        // 写入水印文字原定高度过小，所以累计写水印，增加高度
        g2d.drawString(content, (int) x, (int) baseY);
        // 设置透明度
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
        // 释放对象
        g2d.dispose();
        ImageIO.write(image, "png", new File(path));
    }

}
