package com.zxy.product.examstu.web.util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpClientUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);
	
	public static String getJsonContent(String getUrl, String token) {
		LOGGER.info("get Url:"+getUrl);
		CloseableHttpClient httpclient =HttpClients.createDefault();
		String json = "";
        try {
            // 创建httpget.
            HttpGet httpget = new HttpGet(getUrl);
            httpget.setHeader("Authorization","Bearer__"+token);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();//设置请求和传输超时时间
            httpget.setConfig(requestConfig);
            // 执行get请求.
            CloseableHttpResponse response = httpclient.execute(httpget);
            try {
                // 获取响应实体
            	LOGGER.info("statusLine:" + response.getStatusLine());
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                	json = EntityUtils.toString(entity);
                	LOGGER.debug("entity json:"+json);
                }
            } catch(Exception e){
            	LOGGER.error("Get response error", e);
            } finally {
                response.close();
            }
        } catch (ClientProtocolException e) {
            LOGGER.error(e.getMessage());
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
        	LOGGER.error("call remote url error", e);
        }  finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
		return json;
	}

    public static String postJsonContent(String postUrl, Map<String,String> paraStr) {
    	LOGGER.info("post Url:"+postUrl);
        CloseableHttpClient httpclient =HttpClients.createDefault();
        String json = "";
        try {
            //创建http post请求
            HttpPost httpPost = new HttpPost(postUrl);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);

            if (!paraStr.isEmpty()){
                List<NameValuePair> nvps = new ArrayList<>();
                for (Map.Entry<String,String> entry:paraStr.entrySet()){
                    nvps.add(new BasicNameValuePair(entry.getKey(),entry.getValue()));
                }
                httpPost.setEntity(new UrlEncodedFormEntity(nvps,"UTF-8"));
            }

            CloseableHttpResponse response = httpclient.execute(httpPost);
            try {
                // 获取响应实体
            	LOGGER.info("statusLine:" + response.getStatusLine());
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    json = EntityUtils.toString(entity);
                    LOGGER.debug("entity json:"+json);
                }
            } catch(Exception e){
            	LOGGER.error("Get response error", e);
            } finally {
                response.close();
            }
        } catch (ClientProtocolException e) {
            LOGGER.error(e.getMessage());
        } catch (ParseException e) {
            LOGGER.error(e.getMessage());
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
        } catch (Exception e) {
        	LOGGER.error("call remote url error", e);
        } finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
        return json;
    }
	
}
