package com.zxy.product.examstu.web.config;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import com.zxy.product.examstu.web.log.DefaultLogStorage;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import com.zxy.common.restful.log.support.AbstractLogStorage;

/**
 * @user tianjun
 * @date 16/7/12
 */
//@Configuration
//@EnableScheduling
public class ScheduConfig implements SchedulingConfigurer {

	@Bean(destroyMethod="shutdown")
	public Executor taskScheduler() {
		return Executors.newScheduledThreadPool(10);
	}

	@Bean
	public AbstractLogStorage<Map<String, Object>> logStorage(){
		return new DefaultLogStorage();
	}

	@Override
	public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
		scheduledTaskRegistrar.setScheduler(taskScheduler());
		scheduledTaskRegistrar.addCronTask(logStorage(),"0/5 * * * * ?");
	}
}
