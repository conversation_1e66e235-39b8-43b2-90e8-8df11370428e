package com.zxy.product.examstu.web.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.MappingJsonFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class JsonUtils {
	
	public static <T> List<T> toList(String json, Class<T> clazz) {  
        if (json == null || json.equals("")) {  
            return null;  
        }  
        ObjectMapper mapper = new ObjectMapper();
        List<T> list = new ArrayList<T>();  
        try {  
            JavaType type = mapper.getTypeFactory().constructParametricType(List.class, clazz);
            list = mapper.readValue(json, type);  
            return list;  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
  
        return list;  
    }  
	
	public static String toJson(Object object) {  
        String json = "";  
        try {  
            if (object == null) {  
                return "";  
            }  
            StringWriter sw = new StringWriter();  
            JsonGenerator gen = new JsonFactory().createGenerator(sw);
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(Include.NON_NULL);
            mapper.setDateFormat(new SimpleDateFormat(DateUtil.YYYY_MM_DD));
            mapper.writeValue(gen, object);  
  
            json = sw.toString();  
            sw.close();  
        } catch (Exception ex) {  
            ex.printStackTrace();  
            json = "";  
        }  
        return json;  
    }  
	
	public static <T> T toObject(String json, Class<T> clazz) {  
        T obj = null;  
        try {  
            if (json == null || json.equals("")) {  
                return null;  
            }  
            JsonFactory jsonFactory = new MappingJsonFactory();
            JsonParser jsonParser = jsonFactory.createParser(json);
            ObjectMapper mapper = new ObjectMapper();
            obj = mapper.readValue(jsonParser, clazz);  
  
        } catch (Exception ex) {  
            ex.printStackTrace();  
            obj = null;  
        }  
        return obj;  
  
    }  
}
