package com.zxy.product.examstu.web.config;

import java.net.InetSocketAddress;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.csource.common.MyException;
import org.csource.fastdfs.ClientGlobal;
import org.csource.fastdfs.TrackerGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.zxy.common.restful.multipart.support.pool.DefaultTrackerServerConnectionPool;
import com.zxy.common.restful.multipart.support.pool.DefaultTrackerServerFactory;

/**
 * <AUTHOR>
 *
 */
@Configuration
public class FastDFSConfig implements EnvironmentAware{

    private Logger logger = LoggerFactory.getLogger(FastDFSConfig.class);

    private int connectTimeout;
    private int networkTimeout;
    private String charset;
    private int trackerHttpPort;
    private boolean antiStealToken;
    private String secretKey;
    private String trackerServers;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setNetworkTimeout(int networkTimeout) {
        this.networkTimeout = networkTimeout;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public void setTrackerHttpPort(int trackerHttpPort) {
        this.trackerHttpPort = trackerHttpPort;
    }

    public void setAntiStealToken(boolean antiStealToken) {
        this.antiStealToken = antiStealToken;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public void setTrackerServers(String trackerServers) {
        this.trackerServers = trackerServers;
    }

    @Override
    public void setEnvironment(Environment env) {
        connectTimeout = env.getProperty("spring.fastdfs.connect-timeout", Integer.class, 30);
        networkTimeout = env.getProperty("spring.fastdfs.network-timeout", Integer.class, 60);
        charset = env.getProperty("spring.fastdfs.charset", String.class, "utf-8");
        trackerHttpPort = env.getProperty("", Integer.class, 20000);
        antiStealToken = env.getProperty("spring.fastdfs.anti-steal-token", boolean.class);
        secretKey = env.getProperty("spring.fastdfs.secret-key");
        trackerServers = env.getProperty("spring.fastdfs.tracker-servers", String.class);
    }

    @Bean
    public boolean afterPropertiesSet() throws MyException {
        logger.info("load fastdfs config: ");
        logger.info("[connectTimeout="+connectTimeout+"]");
        logger.info("[networkTimeout="+networkTimeout+"]");
        logger.info("[charset="+charset+"]");
        logger.info("[trackerHttpPort="+trackerHttpPort+"]");
        logger.info("[antiStealToken="+antiStealToken+"]");
        logger.info("[secretKey="+secretKey+"]");
        logger.info("[trackerServers="+trackerServers+"]");

        ClientGlobal.g_connect_timeout = connectTimeout * 1000;
        ClientGlobal.g_network_timeout = networkTimeout * 1000;
        ClientGlobal.g_charset = charset;
        ClientGlobal.g_tracker_http_port = trackerHttpPort;
        ClientGlobal.g_anti_steal_token = antiStealToken;
        if(antiStealToken){
            ClientGlobal.g_secret_key = secretKey;
        }
        ClientGlobal.g_tracker_group = trackerGroup();
        return true;
    }
    
    @Bean
    public DefaultTrackerServerFactory defaultTrackerServerFactory() {
        return new DefaultTrackerServerFactory();
    }

    @Bean
    public DefaultTrackerServerConnectionPool defaultTrackerServerConnectionPool(DefaultTrackerServerFactory defaultTrackerServerFactory, Environment env) {
        GenericObjectPoolConfig config = new GenericObjectPoolConfig();
        config.setMaxTotal(env.getProperty("spring.fastdfs.max-total", Integer.class, 5));
        config.setMaxIdle(env.getProperty("spring.fastdfs.max-idle", Integer.class, 5));
        config.setJmxEnabled(false);
        return new DefaultTrackerServerConnectionPool(defaultTrackerServerFactory, config);
    }

    private TrackerGroup trackerGroup() throws MyException{
        String[] parts;
        String[] szTrackerServers = trackerServers.split(",");
        InetSocketAddress[] tracker_servers = new InetSocketAddress[szTrackerServers.length];
        for (int i=0; i<szTrackerServers.length; i++)
        {
            parts = szTrackerServers[i].split("\\:", 2);
            if (parts.length != 2)
            {
                throw new MyException("the value of item \"tracker_server\" is invalid, the correct format is host:port");
            }
            tracker_servers[i] = new InetSocketAddress(parts[0].trim(), Integer.parseInt(parts[1].trim()));
        }
        return new TrackerGroup(tracker_servers);
    }

}
