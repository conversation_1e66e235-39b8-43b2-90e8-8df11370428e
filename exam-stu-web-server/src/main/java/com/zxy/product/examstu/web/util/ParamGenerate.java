package com.zxy.product.examstu.web.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ParamGenerate {

    static final Map<String,String> DEFAULT_MAP = new HashMap<>();

    static {
        DEFAULT_MAP.put("Integer", null);
        DEFAULT_MAP.put("String", null);
        DEFAULT_MAP.put("Long", null);
    }

//    public static void main(String[] args) {
//        execute();
//    }

    public static void execute() {
        ParamItem[] vaild = new ParamItem[]{
            new ParamItem("name,publisherId,ownedOrganizationId,publishOrganizationId,paperClassId", true),
            new ParamItem(Integer.class, "duration,passScore,credit,type", true),
            new ParamItem(Integer.class, "applicantNeedAudit,isOpenPractice,showAnswerRule", true),
            new ParamItem(Long.class, "startTime,endTime", true),
            new ParamItem(Long.class, "applicantEndTime", false),
            new ParamItem("coverId,certificateId,examNotes"),
            new ParamItem(Integer.class, "supportApp,isAllowSwitch,allowSwitchTimes,isShowAnswerImmed", false),
            new ParamItem(Integer.class, "isAllowPause,isAllowAddRoom,getCreditRule,allowExamTimes", false)
        };

        String params = ParamItem.toParamString(vaild);

        String gets = ParamItem.toGetString(vaild, "requestContext");

        String methodParams = ParamItem.toMethodString(vaild);

        String setParams = ParamItem.getSetString(vaild, "exam", "requestContext");

        String updateSetting = ParamItem.getUpdateSetting(vaild, "exam");

        System.out.println(params);
        System.out.println(gets);
        System.out.println(methodParams);
        System.out.println(setParams);
        System.out.println(updateSetting);

    }

    static class ParamItem {
        private String type;
        private List<String> params;
        private boolean isRequired;

        /**
         * @param type 参数类型
         * @param params 参数名称集合
         * @param isRequired 是否必须
         */
        public ParamItem(String type, String[] params, boolean isRequired) {
            this.type = type;
            this.params = Arrays.asList(params);
            this.isRequired = isRequired;
        }

        public ParamItem(Class<?> type, String[] params, boolean isRequired) {
            this(type.getSimpleName(), params, isRequired);
        }


        public ParamItem(Class<?> type, String params, boolean isRequired) {
            this(type, params.split(","), isRequired);
        }

        public ParamItem(String[] params, boolean isRequired) {
            this(String.class, params, isRequired);
        }

        public ParamItem(String[] params) {
            this(params, false);
        }

        public ParamItem(String paramsStr, boolean isRequired) {
            this(String.class, paramsStr, isRequired);
        }

        public ParamItem(String paramStr) {
            this(paramStr, false);
        }

        public String toParamsString() {
            StringBuffer result = new StringBuffer();
            params.forEach(p -> {
                result.append(String.format("@Param(name = \"%s\", type = %s.class", p, type));
                if (isRequired) {
                    result.append(", required = true");
                }
                result.append(")\n");
            });
            return result.toString();
        }

        public String toGetString(String paramName) {
            StringBuffer result = new StringBuffer();
            params.forEach(p -> {
                result.append(paramName + ".get");
                if (!isRequired) {
                    result.append("Optional");
                }
                result.append("(\"" + p + "\", " + type + ".class),\n");
            });
            return result.toString();
        }

        public String getUpdateSet(String beanName) {
            StringBuffer result = new StringBuffer();
            params.forEach(p -> {
                if (isRequired) {
                    result.append(beanName + "." + getSetName(p));
                    result.append("(" + p);
                } else {
                    result.append(p + ".ifPresent(" + beanName + "::" + getSetName(p));
                }
                result.append(");\n");
            });
            return result.toString();
        }

        public String getSetName(String p) {
            return "set" + p.substring(0,1).toUpperCase() + p.substring(1);
        }


        public static String getUpdateSetting(ParamItem[] vaild, String beanName) {
            StringBuffer sb = new StringBuffer();
            Arrays.asList(vaild).forEach(p -> {
                sb.append(p.getUpdateSet(beanName));
            });
            return sb.toString();
        }

        public String toSetString(String beanName, String paramName) {
            StringBuffer result = new StringBuffer();
            params.forEach(p -> {
                String setName = getSetName(p);
                String paramGet = "(\"" + p + "\", " + type + ".class)";
                if (isRequired) {
                    result.append(beanName + "." + setName);
                    result.append("(");
                    result.append(paramName + ".get");
                    result.append(paramGet);
                } else {
                    String def = DEFAULT_MAP.get(type);
                    if (def == null) {
                        result.append(paramName + ".getOptional");
                        result.append(paramGet);
                        result.append(".ifPresent(");
                        result.append(beanName + "::" + setName);
                    } else {
                        result.append(beanName + "." + setName);
                        result.append("(");
                        result.append(paramName + ".getOptional");
                        result.append(paramGet + ".orElse(" + def + ")");
                    }
                }
                result.append(");\n");
            });
            return result.toString();
        }

        public String toMethodParamsString() {
            StringBuffer result = new StringBuffer();
            params.forEach(p -> {
                if(isRequired)
                    result.append(type + " " + p + ", ");
                else
                    result.append("Optional<"+type+">" + " " + p + ", ");
            });
            return result.toString();

        }

        public static String toParamString(ParamItem[] vaild) {
            StringBuffer sb = new StringBuffer();
            Arrays.asList(vaild).forEach(p -> {
                sb.append(p.toParamsString());
            });
            return sb.toString();
        }

        public static String toGetString(ParamItem[] vaild, String paramName) {
            StringBuffer sb = new StringBuffer();
            Arrays.asList(vaild).forEach(p -> {
                sb.append(p.toGetString(paramName));
            });
            return sb.toString();
        }

        public static String toMethodString(ParamItem[] vaild) {
            StringBuffer sb = new StringBuffer();
            Arrays.asList(vaild).forEach(p -> {
                sb.append(p.toMethodParamsString());
            });
            String[] arrays = sb.toString().split(",");
            StringBuffer result = new StringBuffer();
            List<Integer> size = new ArrayList<>();
            size.add(1);
            Arrays.asList(arrays).forEach(name -> {
                if (result.length() > size.size() * 120) {
                    size.add(1);
                    result.append("\n");
                }
                if (name.trim().length() > 0)
                    result.append(name + ",");
            });
            return result.substring(0, result.lastIndexOf(","))+"\n";
        }

        public static String getSetString(ParamItem[] vaild, String beanName, String paramName) {
            StringBuffer result = new StringBuffer();
            Arrays.asList(vaild).forEach(p -> {
                result.append(p.toSetString(beanName, paramName));
            });
            return result.toString();
        }
    }
}
