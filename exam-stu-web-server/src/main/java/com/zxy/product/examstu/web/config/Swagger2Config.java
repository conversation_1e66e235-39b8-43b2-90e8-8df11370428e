package com.zxy.product.examstu.web.config;



/**
@EnableSwagger2 //Enable swagger 2.0 spec
@ComponentScan
@Configuration
public class Swagger2Config {

//    @Bean
//    public Docket examApi() {
//        return new Docket(DocumentationType.SWAGGER_2)
//                .groupName("exam-api")
//                .apiInfo(apiInfo())
//                .select()
//                .paths(examPaths())
//                .build()
//                .ignoredParameterTypes(ApiIgnore.class)
//                .enableUrlTemplating(true);
//    }

    @Bean
    public Docket examFrontApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("exam-front-api")
                .apiInfo(apiInfo())
                .select()
                .paths(examFrontPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket questionApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("question-api")
                .apiInfo(apiInfo())
                .select()
                .paths(questionPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket examRecordApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("exam-record-api")
                .apiInfo(apiInfo())
                .select()
                .paths(examRecordPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket paperApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("paper-class-api")
                .apiInfo(apiInfo())
                .select()
                .paths(paperClassPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket questionDepotApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("question-depot-api")
                .apiInfo(apiInfo())
                .select()
                .paths(questionDepotPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket announcementApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("announcement-api")
                .apiInfo(apiInfo())
                .select()
                .paths(announcementPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }
    @Bean
    public Docket authExamApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("auth-exam-api")
                .apiInfo(apiInfo())
                .select()
                .paths(authExamPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }
    @Bean
    public Docket certificateRecordApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("certificate-record-api")
                .apiInfo(apiInfo())
                .select()
                .paths(certificateRecordPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket equipmentTypeApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("equipment-type-api")
                .apiInfo(apiInfo())
                .select()
                .paths(equipmentTypePaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket invigilatorApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("invigilator-api")
                .apiInfo(apiInfo())
                .select()
                .paths(invigilatorPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    @Bean
    public Docket signAuthApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("sign-auth-api")
                .apiInfo(apiInfo())
                .select()
                .paths(signAuthPaths())
                .build()
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }

    private Predicate<String> signAuthPaths() {
        return regex("/sign-auth/.*");
    }

    private Predicate<String> invigilatorPaths() {
        return regex("/invigilator/.*");
    }

    private Predicate<String> equipmentTypePaths() {
        return regex("/equipment-type/.*");
    }

    private Predicate<String> certificateRecordPaths() {
        return regex("/certificate-record/.*");
    }

    private Predicate<String> authExamPaths() {
        return regex("/auth-exam/.*");
    }

    private Predicate<String> announcementPaths() {
        return regex("/announcement/.*");
    }

    private Predicate<String> questionDepotPaths() {
        return regex("/question-depot/.*");
    }

    private Predicate<String> paperClassPaths() {
        return regex("/paper-class/.*");
    }

    private Predicate<String> examRecordPaths() {
        return regex("/exam-record/.*");
    }

    private Predicate<String> questionPaths() {
        return regex("/question/.*");
    }

//    private Predicate<String> examPaths() {
//        return regex("/exam/");
//    }
    private Predicate<String> examFrontPaths() {
        return regex("/exam/front/.*");
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("考试项目API")
                .description("如下是考试相关的api")
                .version("1.0")
                .build();
    }

    @Bean
    SecurityScheme apiKey() {
        return new ApiKey("api_key", "api_key", "header");
    }

    
}
 */
