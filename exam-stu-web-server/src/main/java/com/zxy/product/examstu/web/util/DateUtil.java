package com.zxy.product.examstu.web.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DateUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static Long dateStringYYYYMMDD2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD);
    }

    public static Long dateStringYYYYMMDDHHMMSS2Long(String date) {
        return dateString2Long(date, YYYY_MM_DD_HH_MM_SS);
    }

    public static Long dateStringYYYYMMDDHHMM2Long(String date) {
    	return dateString2Long(date, YYYY_MM_DD_HH_MM);
    }

    public static Long dateString2Long(String date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date d = null;
        try {
            d = sdf.parse(date);
        } catch (ParseException e) {
            LOGGER.error("string to date error", e);
        }

        return d.getTime();
    }

    /**
     * Long类型日期转String类型
     * @param dateLong
     * @param format
     * @return
     */
    public static String dateLongToString(Long dateLong,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(dateLong);
        return sdf.format(date);
    }
    public static Optional<Long> dateStringYYYYMMDDHHMMSS2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDDHHMMSS2Long(d);
        });
    }
    public static Optional<Long> dateStringYYYYMMDD2OptionalLong(Optional<String> date) {
        return date.map(d -> {
            return dateStringYYYYMMDD2Long(d);
        });
    }
}
