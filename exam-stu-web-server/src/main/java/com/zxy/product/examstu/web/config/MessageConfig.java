package com.zxy.product.examstu.web.config;

import com.zxy.common.message.CommonMessageConverter;
import com.zxy.common.message.consumer.MessageException;
import com.zxy.common.serialize.Serializer;
import com.zxy.common.serialize.hessian.HessianSerializer;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.message.provider.MessageSenderFactory;
import org.springframework.util.ErrorHandler;
//import com.zxy.product.examstu.web.listener.SubmitListener;

import java.util.Arrays;

/**
 * @user tianjun
 * @date 16/6/21
 */
@Configuration
public class MessageConfig implements BeanFactoryAware, EnvironmentAware {


    private static final Logger LOGGER = LoggerFactory.getLogger(MessageConfig.class);

    private String examSubmitQueenName;

    private DefaultListableBeanFactory beanFactory;
    private Environment env;
    private MessageSender messageSender;
    @Override
    @Autowired
    public void setBeanFactory(BeanFactory beanFactory) {
        if (beanFactory instanceof DefaultListableBeanFactory) {
            this.beanFactory = (DefaultListableBeanFactory) beanFactory;
        }
    }

    @Bean
    public Serializer serializer() {
        return new HessianSerializer();
    }

    @Bean
    public CommonMessageConverter commonMessageConverter(Serializer serializer) {
        CommonMessageConverter converter = new CommonMessageConverter();
        converter.setSerializer(serializer);
        return converter;
    }

    @Bean
    public MessageSenderFactory messageSenderFactory(){
        return new MessageSenderFactory();
    }

    @Bean
    public MessageSender messageSender(MessageSenderFactory messageSenderFactory, Environment env){
        return messageSenderFactory.create(env.getProperty("spring.rabbitmq.default-exchange"));
    }


    @Bean
    public MessageSender examMessageSender(MessageSenderFactory messageSenderFactory, Environment env){
        return messageSenderFactory.create(env.getProperty("spring.rabbitmq.exam.web.excange"));
    }


    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
        examSubmitQueenName = "exam-force-submit-" + System.currentTimeMillis();
    }


    @Bean
    DirectExchange exchange(Environment env) {
        return new DirectExchange(env.getProperty("spring.rabbitmq.default-exchange"));
    }

    @Bean
    TopicExchange examTopicExchange(Environment env) {
        return new TopicExchange(env.getProperty("spring.rabbitmq.exam.web.excange"));
    }


//    @Bean
//    public SimpleMessageListenerContainer examSubmitListenerContainer(TopicExchange examTopicExchange, ConnectionFactory connectionFactory, SubmitListener submitListener) {
//        Queue queue = new Queue(examSubmitQueenName, true, true, false);
//        this.beanFactory.registerSingleton(examSubmitQueenName, queue);
//        Arrays.stream(submitListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(examSubmitQueenName + "#" + type, BindingBuilder.bind(queue).to(examTopicExchange).with(String.valueOf(type)));
//        });
//        return createListener(connectionFactory, submitListener, examSubmitQueenName);
//    }

    private SimpleMessageListenerContainer createListener(ConnectionFactory connectionFactory, MessageListener listener, String queue) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(queue);
        container.setMessageListener(listener);
        container.setPrefetchCount(env.getProperty("spring.rabbitmq.listener.simple.prefetch", Integer.class, 1));
        container.setConcurrentConsumers(env.getProperty("spring.rabbitmq.listener.simple.concurrency", Integer.class, 1));
        container.setMaxConcurrentConsumers(env.getProperty("spring.rabbitmq.listener.simple.max-concurrency", Integer.class, 1));
        container.setErrorHandler(new ErrorHandler() {
            @Override
            public void handleError(Throwable throwable) {
                if (causeChainContainsARADRE(throwable)) {
                    StringBuilder errorMessage = new StringBuilder(env.getProperty("application.env.name", String.class, "dev9"));
                    errorMessage.append("环境异步监听服务出错: ");
                    errorMessage.append(throwable.getCause().getMessage());
                    LOGGER.error("message listener shutdown: " + throwable.getCause().getMessage());
                    // 发送邮件消息
                    messageSender.send(MessageTypeContent.SEND_MESSAGE_WARNING_EMAIL, (Object)errorMessage.toString(),
                                       MessageHeaderContent.SUBJECT, "exam project " + container.getClass().getSimpleName() + "服务挂起");
                    // 停止监听
                    container.shutdown();
                }
            }
            private boolean causeChainContainsARADRE(Throwable t) {
                for(Throwable cause = t.getCause(); cause != null; cause = cause.getCause()) {
                    if(cause instanceof MessageException) {
                        return true;
                    }
                }
                return false;
            }
        });
        return container;
    }
}
