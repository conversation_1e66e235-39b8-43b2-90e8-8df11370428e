package com.zxy.product.examstu.web.util;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.content.FlowLimitEnum;
import com.zxy.product.examstu.dto.exam.online.ExamMemory;
import com.zxy.product.examstu.web.config.ExamOnlineConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.zxy.product.course.content.CourseLimitConstants.HumanWhiteMemberIds;
import static com.zxy.product.exam.content.ErrorCode.TooManyExamineePleaseComeBackLaterEnsureYourExamExperience;
import static com.zxy.product.examstu.content.ExamLimitConstants.*;
import static com.zxy.product.examstu.content.MessageHeaderContent.*;
import static com.zxy.product.examstu.content.MessageTypeContent.EXAM_DISTRIBUTE_LIMIT_VIEW;

/**
 * 考试在线限流限频校验
 * <AUTHOR>
 * @date 2025年03月13日 11:16
 */
@Component
public class ExamFlowVerify {
    private Redis redis;
    private Cache onlineExamCache;
    private MessageSender messageSender;

    @Autowired
    private void setRedis( Redis redis ){ this.redis=redis; }

    @Autowired
    public void setOnlineExamCache( CacheService cacheService ){ this.onlineExamCache=cacheService.create(CacheKeyApplication,CacheKeyModule); }

    @Autowired
    public void setMessageSender( MessageSender messageSender ){ this.messageSender=messageSender; }

    /**考试在线超出限流阈值，触发限流，提示用户*/
    public void verify(Integer region,String examId, String memberId){
        ExamMemory examMemory=ExamOnlineConfig.examMemory;

        if( !examMemory.getMainSwitch() || !examMemory.getApplicationSwitch() ){
            return; //总开关或考试开关关闭时，则不做限流操作
        }

        if( !examMemory.getMaxOnlineSwitch() ){
            return; // 考试最大在线未开启限流，则不做限流操作
        }

        // 考试最大在线缓存Key
        String cacheKey = CacheKeyOnline + FlowLimitEnum.ExamSubmit.getBusinessType();

        if( examMemory.getMaxOnlineSwitch() ){
            // 考试开启最大在线，校验是否超出阈值
            Integer maxOnline = examMemory.getMaxOnline();
            Long number = onlineExamCache.increment( cacheKey, 0L );
            this.doWhiteListVerify( memberId, maxOnline, Math.toIntExact(number));
        }

        // 考试最大在线缓存递增且发送MQ执行流水添加
        onlineExamCache.increment( cacheKey,1L );
        messageSender.send( EXAM_DISTRIBUTE_LIMIT_VIEW,
                EXAM_ONLINE_REGION, String.valueOf(region), EXAM_ONLINE_MEMBER_ID, memberId,
                EXAM_ONLINE_BUSINESS_ID, examId );
    }

    /**
     * 执行白名单校验
     * @param memberId 用户Id
     * @param maxOnline 当前后台设定的在线人数阈值
     * @param cacheOnline 缓存中自增的在线人数
     */
    private void doWhiteListVerify( String memberId, Integer maxOnline, Integer cacheOnline ){
        if( maxOnline<=cacheOnline ){
            TooManyExamineePleaseComeBackLaterEnsureYourExamExperience.throwIf( !redis.process( ew1-> ew1.sismember( HumanWhiteMemberIds, memberId ) ) );
        }
    }

}
