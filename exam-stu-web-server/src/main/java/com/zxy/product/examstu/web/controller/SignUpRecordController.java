package com.zxy.product.examstu.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.examstu.api.SignUpRecordService;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.SignupRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 报名历史记录
 * <AUTHOR>
 * @date 2017年10月25日 下午5:19:54
 *
 */
@Controller
@RequestMapping("/signup-record")
public class SignUpRecordController {

    private SignUpRecordService signUpRecordService;
    private MemberService memberService;

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setSignUpRecordService(SignUpRecordService signUpRecordService) {
        this.signUpRecordService = signUpRecordService;
    }

    /** 考试报名历史记录集合 */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "examId", type = String.class)
    @JSON("id,memberId,auditStatus,createTime")
//    @Permitted
    public List<SignupRecord> findList(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        Integer examRegion = memberService.findExamRegion(memberId);
        List<SignupRecord> signupRecords = signUpRecordService.findList(
                examRegion,
                memberId,
                requestContext.getOptionalString("examId"));
        return signupRecords;
    }


}
