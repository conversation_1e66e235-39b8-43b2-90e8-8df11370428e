package com.zxy.product.examstu.web.config;

import com.alibaba.fastjson.JSON;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.content.SwitchEnum;
import com.zxy.product.system.entity.InternalSwitch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 开关配置类
 *
 * <AUTHOR>
 * @date 2024年03月22日 14:08
 */
@Component
@EnableScheduling
public class SwitchConfig {
    private static final Logger logger= LoggerFactory.getLogger(SwitchConfig.class);
    private static Map<String,Integer> switchMap;
    private InternalSwitchService switchService;

    @Autowired
    public void setSwitchService(InternalSwitchService switchService){
        this.switchService=switchService;
    }

    /**初始化公共开关Map，默认全部关闭*/
    @PostConstruct
    public void initializeSwitch(){
        switchMap = Arrays.stream(SwitchEnum.values())
                .collect(Collectors.toMap(SwitchEnum::getKey, ew1 -> InternalSwitch.switchStatusOff));
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    public void schedulePopulatedMap() {
        switchMap = switchService.buildSwitchMap();
        logger.info("获取公共开关Map开始执行{}", JSON.toJSONString(switchMap));
    }

    /**
     * 获取开关状态
     *
     * @param switchEnum 开关枚举
     * @return 开关状态
     */
    public static boolean getSwitchStatus(SwitchEnum switchEnum){
        return Optional.ofNullable(switchEnum)
                .filter(ew1->Objects.equals(InternalSwitch.switchStatusNO,switchMap.get(ew1.getKey())))
                .map(ew2->Boolean.TRUE)
                .orElse(Boolean.FALSE);
    }
}