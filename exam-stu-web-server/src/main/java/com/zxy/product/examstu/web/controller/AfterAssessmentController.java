package com.zxy.product.examstu.web.controller;



import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.examstu.api.AfterAssessmentService;

import com.zxy.product.exam.entity.ExamRegist;
import com.zxy.product.exam.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

/**
 *
 * ClassName: AfterAssessmentController <br/>
 * Reason: 认证考试后评估报表 <br/>
 * date: 2017年11月7日 上午10:01:08 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@Controller
@RequestMapping("/after-assessment")
public class AfterAssessmentController {

	private AfterAssessmentService afterAssessmentService;
	@Autowired
	public void setAfterAssessmentService(AfterAssessmentService afterAssessmentService) {
		this.afterAssessmentService = afterAssessmentService;
	}

	/**
	 * 空接口-用于运维健康检测
	 */
	@RequestMapping("/health-check")
	@JSON("*")
	public Map<String, String> healthCheck() {
		return ImmutableMap.of("status", "ok");
	}

	/**
    *
    * frontExamPersonalDetail:前端考试个人后评估详情列表. <br/>
    */
   @RequestMapping(value = "/front-exam-personal-detail" , method = RequestMethod.GET)
   @Param(name = "id" ,required=true) //考试id
   @JSON("organization.(id,name)")
   @JSON("member.(id,name,fullName)")
   @JSON("correctRate,userRateJson")
   public ExamRegist frontExamPersonalDetail(RequestContext requestContext, Subject<Member> subject) {
	   Integer examRegion = subject.examRegion();
	   return afterAssessmentService.frontExamPersonalDetailList(examRegion,
               requestContext.getString("id"), subject.getCurrentUserId());

   }



}
