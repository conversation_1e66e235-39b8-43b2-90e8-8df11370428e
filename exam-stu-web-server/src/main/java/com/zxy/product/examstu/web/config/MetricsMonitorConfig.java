package com.zxy.product.examstu.web.config;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;

import org.springframework.boot.actuate.metrics.dropwizard.DropwizardMetricServices;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.codahale.metrics.MetricFilter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.graphite.Graphite;
import com.codahale.metrics.graphite.GraphiteReporter;
import com.codahale.metrics.jvm.GarbageCollectorMetricSet;
import com.codahale.metrics.jvm.MemoryUsageGaugeSet;
import com.codahale.metrics.jvm.ThreadStatesGaugeSet;

/**
 * <AUTHOR>
 *
 */
//@Configuration
public class MetricsMonitorConfig {

    @Bean
    public GraphiteReporter graphiteReporter(MetricRegistry registry, DropwizardMetricServices service, Environment env) {
        String name = env.getProperty("spring.application.name");
        try {
            String ip = InetAddress.getLocalHost().getHostAddress();
            name += "." + ip.replaceAll("\\.", "-");
        } catch (Exception e) {
            name += ".error-127-0-0-1";
        }

        Graphite graphite = new Graphite(
                new InetSocketAddress(env.getProperty("graphite.server"), env.getProperty("graphite.port", int.class)));
        GraphiteReporter reporter = GraphiteReporter.forRegistry(registry)
              .prefixedWith(name)
              .convertRatesTo(TimeUnit.SECONDS)
              .convertDurationsTo(TimeUnit.MILLISECONDS)
              .filter(MetricFilter.ALL)
              .build(graphite);
        registry.registerAll(new MemoryUsageGaugeSet());
        registry.registerAll(new GarbageCollectorMetricSet());
        registry.registerAll(new ThreadStatesGaugeSet());

        reporter.start(5, TimeUnit.SECONDS);
        return reporter;
    }

}
