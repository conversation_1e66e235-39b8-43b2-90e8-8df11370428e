package com.zxy.product.examstu.web.controller;


import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;

import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.examstu.api.ProfessionLevelService;
import com.zxy.product.examstu.api.ProfessionService;
import com.zxy.product.examstu.api.PaperClassService;
import com.zxy.product.examstu.content.ErrorCode;
import com.zxy.product.examstu.api.*;

import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.web.util.DateUtil;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.entity.RuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * 考试相关api----学员端
 */
@Controller
@RequestMapping("/exam/front")
public class ExamFrontController{
	private static final Logger logger=LoggerFactory.getLogger(ExamFrontController.class);

	private static final Logger LOGGER = LoggerFactory.getLogger(ExamFrontController.class);

	private final static String CLOUD_PROFESSION = "cloud-profession";

	private final static String CLOUD_LEVEL = "cloud-level";

	private final static String AUDIENT_CACHE = "audient-cache";

	private final static String ENTER_EXAM_CODE_CACHE = "enter-exam-code-cache";

	private final static String FILTER_ANSWER_QUESTION_ATTR = "-1";




	private RuleConfigService ruleConfigService;

	private ExamService examService;

	private Cache examCache;

	private Cache examRecordCache;

	private AudienceObjectService audienceObjectService;

	private PaperClassService paperClassService;

	private Cache cloudProfessionCache;

	private ProfessionService professionService;

	private Cache cloudLevelCache;

	private ProfessionLevelService professionLevelService;

	private SignUpService signUpService;

	private ExamRecordFaceService examRecordFaceService;

	private StrongBaseService strongBaseService;

	private ExamRecordService examRecordService;

	private PaperInstanceService  paperInstanceService;

	private Cache audientCache;

	private Cache enterExamCodeCache;

	private Cache gridNoSignupCache;

	private MessageSender messageSender;

	private Cache paperCache;

	private Cache questionCache;

	private QuestionCopyService questionCopyService;

	private Cache paperInstanceQuestionCopyCache;

	private AnswerRecordService answerRecordService;

	private GridCourseService gridCourseService;

	private CourseStudyProgressService courseStudyProgressService;

	private MemberService memberService;

	private MarkConfigService markConfigService;


	@Autowired
	public void setMarkConfigService(MarkConfigService markConfigService) {
		this.markConfigService = markConfigService;
	}

	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Autowired
	public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
		this.courseStudyProgressService = courseStudyProgressService;
	}


	@Autowired
	public void setGridCourseService(GridCourseService gridCourseService) {
		this.gridCourseService = gridCourseService;
	}


	@Autowired
	public void setAnswerRecordService(AnswerRecordService answerRecordService) {
		this.answerRecordService = answerRecordService;
	}

	@Autowired
	public void setQuestionCopyService(QuestionCopyService questionCopyService) {
		this.questionCopyService = questionCopyService;
	}

	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}


	@Autowired
	public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
		this.paperInstanceService = paperInstanceService;
	}

	@Autowired
	public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}

	@Autowired
	public void setStrongBaseService(StrongBaseService strongBaseService) {
		this.strongBaseService = strongBaseService;
	}


	@Autowired
	public void setExamRecordFaceService(ExamRecordFaceService examRecordFaceService) {
		this.examRecordFaceService = examRecordFaceService;
	}

	@Autowired
	public void setSignUpService(SignUpService signUpService) {
		this.signUpService = signUpService;
	}

	@Autowired
	public void setProfessionLevelService(ProfessionLevelService professionLevelService) {
		this.professionLevelService = professionLevelService;
	}

	@Autowired
	public void setProfessionService(ProfessionService professionService) {
		this.professionService = professionService;
	}


	@Autowired
	public void setPaperClassService(PaperClassService paperClassService) {
		this.paperClassService = paperClassService;
	}

	@Autowired
	public void setAudienceObjectService(AudienceObjectService audienceObjectService) {
		this.audienceObjectService = audienceObjectService;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.examCache = cacheService.create("cacheExam", "examEntity");
		this.examRecordCache = cacheService.create("cacheExamRecord", "examRecordEntity");
		this.cloudProfessionCache = cacheService.create("cacheCloudProfession", CLOUD_PROFESSION);
		this.cloudLevelCache = cacheService.create("cacheCloudLevel", CLOUD_LEVEL);
		this.audientCache = cacheService.create("cacheAudience", AUDIENT_CACHE);
		this.enterExamCodeCache = cacheService.create("cacheEnterExamCode", ENTER_EXAM_CODE_CACHE);
		this.paperCache = cacheService.create("cachePaperInstance", "paperInstanceEntity");
		this.questionCache = cacheService.create("cacheQuestionCopy", "questionCopyEntity");
		this.paperInstanceQuestionCopyCache = cacheService.create("cachePaperInstanceQuestionCopy", "paperInstanceQuestionCopyEntity");
		this.gridNoSignupCache = cacheService.create("grid-no-signup", "time");

	}

	@Autowired
	public void setExamService(ExamService examService) {
		this.examService = examService;
	}


	@Autowired
	public void setRuleConfigService(RuleConfigService ruleConfigService) {
		this.ruleConfigService = ruleConfigService;
	}


	/**
	 * 判断用户是否拥有考试受众权限
	 * @param context
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/is-audient", method = RequestMethod.GET)
	@Param(name = "examId", required = true)
	@JSON("*")
//	@Permitted()
	public Map<String, Object> isAudient(RequestContext context, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		//考试撤销判断
		Exam exam = getExamFromCacheOrDB(examRegion, context.getString("examId"));
		if (exam != null && exam.getStatus() != null && Objects.equals(exam.getStatus(), Exam.STATUS_NOT_PUBLISH)) throw new UnprocessableException(ErrorCode.ExamStatusError);
		//受众权限判断
		boolean isAudient = audienceObjectService.isAudient(examRegion, context.getString("examId"), subject.getCurrentUserId());
		if (!isAudient) throw new UnprocessableException(ErrorCode.IsNotAudient);
		return ImmutableMap.of("isGrant", isAudient ? 1 : 0);
	}




	@RequestMapping(value = "/validate-exam", method = RequestMethod.GET)
	@Param(name = "examId", type = String.class, required = true)
	@JSON("*")
	public Map<String, Object> validateExam(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		Exam exam = getExamFromCacheOrDB(examRegion, requestContext.getString("examId"));
		ExamRecord  examRecord = getExamRecordFromCacheOrDB(examRegion, exam, subject, false);
		return ImmutableMap.of("validate", 1);
	}

	/**
	 * 缓存获取考试的来源
	 * @return
	 */
	@RequestMapping(value = "/get-source-type", method= RequestMethod.GET)
	@Param(name="examId", type=String.class)
	@JSON("sourceType")
	public Exam getSourceTypeByExamId(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		Optional<String> examId = requestContext.getOptionalString("examId");
		return examId.map(s -> getExamFromCacheOrDB(examRegion, s)).orElse(null);
	}


	/**
	 * 网格长考试验证： 网格长认证考试不报名时，进入考试需要判断是否满足默认条件：1，完成学习。
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/validate-grid-exam", method = RequestMethod.GET)
	@Param(name = "examId", required = true)
	@JSON("*")
	public Map<String, Object> validateGridExam(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		String examId = requestContext.get("examId", String.class);
		String memberId = subject.getCurrentUserId();
		Long time = gridNoSignupCache.get(examId+"#"+memberId, Long.class);

		HashMap<String, Object> map = new HashMap<>();
		if (time != null) {
			map.put("gridValidate", 0);
			map.put("time", ((SignUp.CACHE_TIME*1000)-(System.currentTimeMillis() - time)));
			return map;
		}

		Exam exam = getExamFromCacheOrDB(examRegion, examId);
		// 网格长认证考试不报名时，进入考试需要判断是否满足默认条件：1，完成学习。
		if (Exam.EXAM_GRID_TYPE.equals(exam.getType()) && Exam.EXAM_NEED_APPLICANT_NO.equals(exam.getNeedApplicant())) {
			if (!finishGridCondition(examRegion, exam.getId(), memberId)) {
				// 不满足时有2个小时的缓存时间
				map.put("gridValidate", 0);
				map.put("time", SignUp.CACHE_TIME*1000);
				gridNoSignupCache.set(examId+"#"+memberId, System.currentTimeMillis(), SignUp.CACHE_TIME);
				return map;
			}
		}
		return ImmutableMap.of("gridValidate", 1);
	}
	/**
	 * 普通考试报名验证
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/validate-exam-two", method = RequestMethod.GET)
	@Param(name = "examId", required = true)
	@JSON("*")
	public Map<String, Object> validateExamTwo(RequestContext requestContext, Subject<Member> subject) {
		String examId = requestContext.get("examId", String.class);
		String memberId = subject.getCurrentUserId();
		Long time = gridNoSignupCache.get(examId+"#"+memberId, Long.class);

		HashMap<String, Object> map = new HashMap<>();
		if (time != null) {
			map.put("validate", 0);
			map.put("time", ((SignUp.CACHE_TIME*1000)-(System.currentTimeMillis() - time)));
			return map;
		}
		return ImmutableMap.of("validate", 1);
	}

	private Boolean finishGridCondition(Integer examRegion, String examId, String memberId) {
		// 是否获得chbn证书
//		Boolean haveChbnCertificate = chbnKnowledgeEmpowermentService.haveChbnCertificate(Exam.ACTIVITY_TYPE_CHBN, memberId);
		// 是否完成学习
		List<String> courseIds = gridCourseService.findGridCourseByExamId(examRegion, examId).stream().map(GridCourse::getCourseId).collect(Collectors.toList());
		Boolean finishAllCourse = courseStudyProgressService.finishAllCourse(memberId, courseIds);
		return finishAllCourse;
	}



	/**
	 * 考试答题记录信息
	 * @param requestContext
	 * @param member
	 * @return
	 */
	@RequestMapping(value = "/answer-record", method = RequestMethod.GET)
	@Param(name = "examId")
	@JSON("id,answer,score,isRight,questionId")
	@JSON("questionCopy.(type)")
//	@Permitted()
	public List<AnswerRecord> getAnswerRecords (RequestContext requestContext, Subject<Member> member) {
		Integer examRegion = member.examRegion();
		Exam exam = getExamFromCacheOrDB(examRegion, requestContext.getString("examId"));
		ExamRecord record = getExamRecordFromCacheOrDB(examRegion, exam, member, false);
		return answerRecordService.findByExamRecordId(examRegion, record.getId(), exam.getId());
	}


	/**
	 * 查看考试详情
	 * 1.考试基本信息
	 * 2.试卷信息
	 * 3.考试记录信息
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/score-detail", method = RequestMethod.GET)
	@Param(name = "examRecordId", type = String.class, required = true)
	@Param(name = "examId", type = String.class, required = true)
	@Param(name = "memberId", type = String.class)
	@JSON("id,name,startTime,endTime,duration,passScore,type,applicantNeedAudit,examNotes")
	@JSON("applicantEndTime,coverId,certificateId,examNotes,supportApp,isAllowSwitch,indefinite")
	@JSON("allowSwitchTimes,isAllowPause,isAllowAddRoom,allowExamTimes,paperSortRule,paperShowRule,showAnswerRule")
	@JSON("paper.(questionNum,totalScore,isSubjective)")
	@JSON("paper.questions.(id,type,content,parentId,score,errorRate,parsing,parsingText)")
	@JSON("paper.questions.questionAttrCopys.(id,name,value,type)")
	@JSON("paper.questions.answerRecord.(id,answer,score,isRight)")
	@JSON("paper.questions.subs.(id,type,content,parentId,score,errorRate,parsing,parsingText)")
	@JSON("paper.questions.subs.answerRecord.(id,answer,score,isRight)")
	@JSON("paper.questions.subs.questionAttrCopys.(id,name,value,type,createTime)")
	@JSON("examRecord.(id,score,status,orderContent)")
	@JSON("examRecord.member.(id,name,fullName)")
//	@Permitted()
	public Exam getWithScoreDetail(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		String examRecordId = requestContext.getString("examRecordId");
		String examId = requestContext.getString("examId");
		logger.info("getWithScoreDetail获取入参{},{},{}",examRegion,examRecordId,examId);
		ExamRecord examRecord = examRecordService.get(examRegion, examRecordId, examId);

		Optional.ofNullable(examRecord).orElseThrow(() -> new UnprocessableException(ErrorCode.ErrorViewScoreDetail));

		Exam exam = getExamFromCacheOrDB(examRegion, examRecord.getExamId());

		// 该考试记录未提交前不可查看详情
		if (examRecord.getStatus() == ExamRecord.STATUS_DOING) {
			throw new UnprocessableException(ErrorCode.ExamRecordScoreDetailError);
		}

		// 考试不可查看详情时
		if (exam != null
				&& (exam.getShowAnswerRule() == Exam.SHOW_ANSWER_RULE_4
				|| exam.getShowAnswerRule() == Exam.SHOW_ANSWER_RULE_5)) {
			throw new UnprocessableException(ErrorCode.ExamRecordScoreDetailError);
		}

		PaperInstance paperInstance = paperInstanceService.getSimpleData(examRegion, examRecord.getPaperInstanceId());
		if (paperInstance != null) {
			PaperClass paperClass = paperClassService.getSimplePaperClassInfo(examRegion, paperInstance.getPaperClassId());
			paperInstance.setQuestions(questionCopyService.findQuestionsByPaperAndExamRecord(examRegion, paperInstance.getId(), examRecord.getId(), examRecord.getExamId()));
			if (paperClass != null) {
				paperInstance.setTotalScore(paperClass.getTotalScore());
				paperInstance.setQuestionNum(paperClass.getQuestionNum());
			}
		}
		exam.setExamRecord(examRecord);
		exam.setPaper(paperInstance);
		sendOtherModuleExamRecordUpdateStatus(exam.getSourceType(), examRecord.getId(), examRecord.getExamId());
		return exam;
	}


	/**
	 * 查看考试详情-后台管理
	 * 1.考试基本信息
	 * 2.试卷信息
	 * 3.考试记录信息
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/score-detail-for-manage", method = RequestMethod.GET)
	@Param(name = "examRecordId", type = String.class, required = true)
	@Param(name = "examId", type = String.class, required = true)
	@Param(name = "memberId", type = String.class, required = true) // 该考试记录的学员id
	@JSON("id,name,startTime,endTime,duration,passScore,type,applicantNeedAudit,examNotes")
	@JSON("applicantEndTime,coverId,certificateId,examNotes,supportApp,isAllowSwitch,indefinite")
	@JSON("allowSwitchTimes,isAllowPause,isAllowAddRoom,allowExamTimes,paperSortRule,paperShowRule,showAnswerRule")
	@JSON("paper.(questionNum,totalScore,isSubjective)")
	@JSON("paper.questions.(id,type,content,parentId,score,errorRate)")
	@JSON("paper.questions.questionAttrCopys.(id,name,value,type)")
	@JSON("paper.questions.answerRecord.(id,answer,score,isRight)")
	@JSON("paper.questions.subs.(id,type,content,parentId,score,errorRate)")
	@JSON("paper.questions.subs.answerRecord.(id,answer,score,isRight)")
	@JSON("paper.questions.subs.questionAttrCopys.(id,name,value,type,createTime)")
	@JSON("examRecord.(id,score,status,orderContent)")
	@JSON("examRecord.member.(id,name,fullName)")
//	@Permitted()
	public Exam getWithScoreDetailForManage(RequestContext requestContext, Subject<Member> subject) {
		String memberId = requestContext.getString("memberId");
		Integer examRegion = memberService.findExamRegion(memberId);
		String examRecordId = requestContext.getString("examRecordId");
		String examId = requestContext.getString("examId");
		ExamRecord examRecord = examRecordService.get(examRegion, examRecordId, examId);

		Optional.ofNullable(examRecord).orElseThrow(() -> new UnprocessableException(com.zxy.product.exam.content.ErrorCode.ErrorViewScoreDetail));

		Exam exam = getExamFromCacheOrDB(examRegion, examRecord.getExamId());

		// 该考试记录未提交前不可查看详情
		if (examRecord != null
				&& examRecord.getStatus() == ExamRecord.STATUS_DOING) {
			throw new UnprocessableException(com.zxy.product.exam.content.ErrorCode.ExamRecordScoreDetailError);
		}

		PaperInstance paperInstance = paperInstanceService.getSimpleData(examRegion, examRecord.getPaperInstanceId());
		if (paperInstance != null) {
			PaperClass paperClass = paperClassService.getSimplePaperClassInfo(examRegion, paperInstance.getPaperClassId());
			paperInstance.setQuestions(questionCopyService.findQuestionsByPaperAndExamRecord(examRegion, paperInstance.getId(), examRecord.getId(), examRecord.getExamId()));
			if (paperClass != null) {
				paperInstance.setTotalScore(paperClass.getTotalScore());
				paperInstance.setQuestionNum(paperClass.getQuestionNum());
			}
		}
		exam.setExamRecord(examRecord);
		exam.setPaper(paperInstance);
		sendOtherModuleExamRecordUpdateStatus(exam.getSourceType(), examRecord.getId(), examRecord.getExamId());
		return exam;
	}



	/**
	 * 活动首页-考试列表信息
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/activity-list", method = RequestMethod.GET)
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name", type=String.class)
	@Param(name="type", type=Integer.class)
	@Param(name="searchStatus", type=Integer.class)
	@Param(name="clientType", type=String.class, required=true)
	@Param(name="topicId", type=String.class)
	@JSON("recordCount")
	@JSON("items.(id,name,examNotes,startTime,endTime,joinNumber,type)")
//	@Permitted()
	public PagedResult<Exam> findActivityList(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		return examService.findActivityList(
				examRegion,
				requestContext.get("page",Integer.class),
				requestContext.get("pageSize",Integer.class),
				requestContext.getOptional("name",String.class),
				requestContext.getOptional("type",Integer.class),
				subject.getCurrentUserId(),
				requestContext.getOptionalInteger("searchStatus"),
				requestContext.getString("clientType"),
				requestContext.getOptionalString("topicId")
		);
	}

	/**
	 * 我的档案-考试
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/archivr-list", method = RequestMethod.GET)
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name", type=String.class)
	@Param(name="type", type=Integer.class)
	@Param(name="searchStatus", type=Integer.class)
	@Param(name="startTimeOrderBy", type=Integer.class)
	@Param(name="startTime", type=Long.class)
	@Param(name="endTime", type=Long.class)
	@JSON("recordCount")
	@JSON("more")
	@JSON("items.(id,name,type,examNotes,startTime,endTime,passScore,allowExamTimes,examTimes,isShowAnswerImmed,needApplicant,applicantStartTime,applicantEndTime,showAnswerRule)")
	@JSON("items.(applicantNeedAudit,status,hasCert,certificateId,showScoreTime)")
	@JSON("items.examRecord.(id,status,score,totalScore,submitTime)")
	@JSON("items.signUp.(id,status)")
	@JSON("items.examRegist.(id,memberId,examTimes,isOperation)")
	@JSON("items.examRegist.certificateRecord.(id)")
//	@Permitted()
	public Map<String,Object> findArchiveList(RequestContext requestContext,  Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
		Optional<Long> startTime = requestContext.getOptional("startTime",Long.class);
		String examRegistStringTable = ExamRegist.STRING_EXAM_REGIST;
		String examRecordStringTable = ExamRecord.STRING_EXAM_RECORD;
		if (startTime.isPresent()) {
			examRegistStringTable += "_"+ DateUtil.dateLongToString(startTime.get(), "yyyy");
			examRecordStringTable += "_"+ DateUtil.dateLongToString(startTime.get(), "yyyy");
		}
		int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
		return examService.findArchiveListByExamRegist(
				examRegion,
				requestContext.get("page",Integer.class),
				requestContext.get("pageSize",Integer.class),
				subject.getCurrentUserId(),
				requestContext.getOptional("startTime",Long.class),
				requestContext.getOptional("endTime",Long.class),
				userIds,
				examRegistStringTable,
				examRecordStringTable,
				pageSwitch == 1
		);
	}


	/**
	 * 根据考试ID,检查考试密码，个人码
	 * type: { 1：通过，2：考试密码错误，3：个人密码错误，4：考试密码个人密码均错误，5：超出考试次数}
	 * @return
	 */
	@RequestMapping(value = "/check-code", method = RequestMethod.POST)
	@JSON("type")
	@Param(name="examId", type=String.class, required = true)
	@Param(name="password", type=String.class)
	@Param(name="personalCode", type=Integer.class)
	@Param(name="needCheckPersonalCode", type=Integer.class)
//	@Permitted()
	public Map<String, Integer> checkPasswordCode(RequestContext requestContext,  Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		// 定义错误码
		int noError = 1, passwordError = 2, personalCodeError = 3, bothCodeError = 4, overExamTimeError = 5;

		String examId = requestContext.getString("examId");
		String password = requestContext.getOptionalString("password").orElse("");
		Integer personalCode = requestContext.getOptionalInteger("personalCode").orElse(null);
		Integer needCheckPersonal = requestContext.getOptionalInteger("needCheckPersonalCode").orElse(0);
		List<Integer> flags = new ArrayList<>();

		Exam exam = getExamFromCacheOrDB(examRegion, examId);

		// 考试密码验证
		if (exam.getIsSetPassword() == Exam.EXAM_YES) {
			if (password.equals("") || !password.equals(exam.getPassword())) {
				flags.add(passwordError);
			}
		}
		// 个人密码验证
		if (needCheckPersonal == Exam.EXAM_YES && exam.getIsSetPersonalCode() == Exam.EXAM_YES) {
			ExamRecord examRecord = examRecordCache.get(ExamRecord.getExamRecordKey(examId, subject.getCurrentUserId()), () -> {
				return examRecordService.getNewestRecord(examRegion, examId, subject.getCurrentUserId());
			});
			if (personalCode != null && !(personalCode.equals(examRecord.getPersonalCode()))) {
				flags.add(personalCodeError);
			}
		}
		if (flags.size() > 1) return ImmutableMap.of("type", bothCodeError);

		if (flags.size() == 1) return ImmutableMap.of("type", flags.get(0));

		// 验证是否超出考试次数
		if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1 == exam.getStrongBaseFlag() && strongBaseService.authType(examRegion, exam.getId())) {
			Boolean again = strongBaseService.examAgain(examRegion, exam.getId(), subject.getCurrentUserId());
			if (!again) return ImmutableMap.of("type", overExamTimeError);
		} else {
			boolean flag = examService.isOverExamTime(examRegion, requestContext.getString("examId"), subject.getCurrentUserId());
			if (flag) return ImmutableMap.of("type", overExamTimeError);
		}
		enterExamCodeCache.set(ExamRecord.getExamRecordKey(examId, subject.getCurrentUserId()), "1",  60);
		return ImmutableMap.of("type", noError);

	}



	/**
	 * 个人中心-我的考试
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/person-center-list", method = RequestMethod.GET)
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name", type=String.class)
	@Param(name="type", type=Integer.class)
	@Param(name="organizationId", type=String.class)
	@Param(name="searchStatus", type=Integer.class)
	@Param(name="startTimeOrderBy", type=Integer.class)
	@Param(name = "year", type = String.class)
	@Param(name = "all", type = String.class, value = "传值查询所有考试")
	@JSON("recordCount")
	@JSON("more")
	@JSON("items.(id,name,type,examNotes,startTime,endTime,passScore,allowExamTimes,examedTimes,isShowAnswerImmed,needApplicant,applicantStartTime,applicantEndTime,isSetPassword,isSetPersonalCode)")
	@JSON("items.(applicantNeedAudit,status,hasCert,certificateId,showAnswerRule,showScoreTime,duration,faceEnter,faceMonitor,strongBaseFlag,examAgain)")
	@JSON("items.examRecord.(id,memberId,status,score,totalScore,submitTime,isReset,startTime,topScoreRecordId,currentTime,faceStatus,participateTime)")
	@JSON("items.signUp.(id,status)")
	@JSON("items.examRegist.(passStatus)")
	@JSON("items.certificateRecord.(id)")
	@JSON("items.paperClass.(totalScore)")
//	@Permitted()
	public Map<String, Object> findPersonCenterList(RequestContext requestContext,  Subject<Member> subject) {
		List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
		int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
		Optional<String> all = requestContext.getOptional("all", String.class);
		if (all.isPresent()) {
			return examService.findNewPersonCenterListByRegist(
				subject.examRegion(),
				requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class),
				requestContext.getOptional("name", String.class),
				Optional.empty(),
				subject.getCurrentUserId(),
				requestContext.getOptionalInteger("searchStatus"),
				requestContext.getOptionalInteger("startTimeOrderBy"),
				userIds,
				requestContext.getOptional("organizationId", String.class),
				requestContext.getOptional("year", String.class),
				false, // false 为 不是认证考试
				true,
				all
			);
		}

		return examService.findNewPersonCenterListByRegist(
				subject.examRegion(),
				requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class),
				requestContext.getOptional("name", String.class),
				requestContext.getOptional("type", Integer.class),
				subject.getCurrentUserId(),
				requestContext.getOptionalInteger("searchStatus"),
				requestContext.getOptionalInteger("startTimeOrderBy"),
				userIds,
				requestContext.getOptional("organizationId", String.class),
				requestContext.getOptional("year", String.class),
				false, // false 为 不是认证考试
				pageSwitch == 1,
				all
        );

	}

	/**
	 * 我的认证考试（个人中心）
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value = "/person-center-my-list" , method = RequestMethod.GET)
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name", type=String.class)
	@Param(name="type", type=Integer.class)
	@Param(name="organizationId", type=String.class) // grid为网格长认证考试
	@Param(name="searchStatus", type=Integer.class)
	@Param(name="startTimeOrderBy", type=Integer.class)
	@Param(name = "year", type = String.class)
	@JSON("recordCount")
	@JSON("more")
	@JSON("items.(id,examBatch,name,type,examNotes,startTime,endTime,passScore,allowExamTimes,examedTimes,isShowAnswerImmed,needApplicant,applicantStartTime,applicantEndTime,isSetPassword,isSetPersonalCode,admissionTicket,duration)")
	@JSON("items.(applicantNeedAudit,status,hasCert,certificateId,showAnswerRule,showScoreTime,faceEnter,faceMonitor)")
	@JSON("items.examRecord.(id,status,score,totalScore,submitTime,isReset,startTime,topScoreRecordId,currentTime,memberId,faceStatus)")
	@JSON("items.signUp.(id,status)")
	@JSON("items.paperClass.(totalScore)")
	@JSON("items.examRegist.(passStatus)")
//	@Permitted()
	public Map<String, Object> findPersonCenterMyList(RequestContext requestContext,  Subject<Member> subject) {
		List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
		int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
		return examService.findNewPersonCenterListByRegist(
				subject.examRegion(),
				requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class),
				requestContext.getOptional("name", String.class),
				requestContext.getOptional("type", Integer.class),
				subject.getCurrentUserId(),
				requestContext.getOptionalInteger("searchStatus"),
				requestContext.getOptionalInteger("startTimeOrderBy"),
				userIds,
				requestContext.getOptional("organizationId", String.class),
				requestContext.getOptional("year", String.class),
				true, // true 为认证考试
				pageSwitch == 1,
				Optional.empty()
        );
	}


	/**
	 * APP-个人中心-我的考试
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/app-person-center-list", method = RequestMethod.GET)
	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name", type=String.class)
	@Param(name="type", type=Integer.class) // 1,正式 2,非正式 3,认证
	@Param(name="organizationId", type=String.class)
	@Param(name="searchStatus", type=Integer.class)
	@Param(name="startTimeOrderBy", type=Integer.class)
	@Param(name = "year", type = String.class)
	@JSON("recordCount")
	@JSON("items.(id,name,examBatch,type,examNotes,startTime,endTime,passScore,allowExamTimes,examedTimes,isShowAnswerImmed,needApplicant,applicantStartTime,applicantEndTime,isSetPassword,isSetPersonalCode)")
	@JSON("items.(applicantNeedAudit,status,hasCert,certificateId,showAnswerRule,showScoreTime)")
	@JSON("items.examRecord.(id,memberId,status,score,totalScore,submitTime,isReset,startTime,topScoreRecordId,currentTime,participateTime)")
	@JSON("items.signUp.(id,status)")
	@JSON("items.certificateRecord.(id)")
	@JSON("items.paperClass.(totalScore)")
	@JSON("items.examRegist.(passStatus)")
//	@Permitted()
	public PagedResult<Exam> findAppPersonCenterList(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
		Optional<Integer> type = requestContext.getOptional("type",Integer.class);

		// 正式考试 和 非正式 考试
		if (type.isPresent() && (type.get() == 1 || type.get() == 2)) {
			return examService.findPersonCenterListByRegist(
					examRegion,
					requestContext.get("page",Integer.class),
					requestContext.get("pageSize",Integer.class),
					requestContext.getOptional("name",String.class),
					requestContext.getOptional("type",Integer.class),
					subject.getCurrentUserId(),
					requestContext.getOptionalInteger("searchStatus"),
					requestContext.getOptionalInteger("startTimeOrderBy"),
					userIds,
					requestContext.getOptional("organizationId",String.class),
					requestContext.getOptional("year", String.class),
					false // false 为 不是认证考试
			);
		}
		// 认证考试
		else if (type.isPresent() && (type.get() == 3)) {
			return examService.findPersonCenterListByRegist(
					examRegion,
					requestContext.get("page",Integer.class),
					requestContext.get("pageSize",Integer.class),
					requestContext.getOptional("name",String.class),
					Optional.empty(),
					subject.getCurrentUserId(),
					requestContext.getOptionalInteger("searchStatus"),
					requestContext.getOptionalInteger("startTimeOrderBy"),
					userIds,
					requestContext.getOptional("organizationId",String.class),
					requestContext.getOptional("year", String.class),
					true // true 为 是认证考试
			);
		}
		// 全部考试
		else {
			return examService.findAllPersonCenterListByRegist(
					examRegion,
					requestContext.get("page",Integer.class),
					requestContext.get("pageSize",Integer.class),
					requestContext.getOptional("name",String.class),
					requestContext.getOptional("type",Integer.class),
					subject.getCurrentUserId(),
					requestContext.getOptionalInteger("searchStatus"),
					requestContext.getOptionalInteger("startTimeOrderBy"),
					userIds,
					requestContext.getOptional("organizationId",String.class),
					requestContext.getOptional("year", String.class)
			);
		}

	}



	@Param(name="page", type=Integer.class, required = true)
	@Param(name="pageSize", type=Integer.class, required = true)
	@Param(name="name")
	@Param(name="sourceType", type=Integer.class,required = true)
	@Param(name = "year")
	@Param(name = "startTimeOrderBy",type = Integer.class,required = true)
	@JSON("recordCount")
	@JSON("items.paperClass.(totalScore)")
	@JSON("items.(id,examBatch,name,type,examNotes,startTime,endTime,passScore,allowExamTimes,examedTimes,isShowAnswerImmed,needApplicant,applicantStartTime,applicantEndTime,isSetPassword,isSetPersonalCode,admissionTicket,duration)")
	@JSON("items.(applicantNeedAudit,status,hasCert,certificateId,showAnswerRule,showScoreTime,faceEnter,faceMonitor)")
	@JSON("items.examRecord.(id,status,score,totalScore,submitTime,isReset,startTime,topScoreRecordId,currentTime,memberId,faceStatus)")
	@JSON("items.examRegist.(passStatus)")
	@JSON("more")
//	@Permitted()
	@RequestMapping(value = "/other-exam-records" , method = RequestMethod.GET)
	public Map<String, Object> otherExamRecords(RequestContext requestContext,  Subject<Member> subject){
		Integer examRegion = subject.examRegion();
		List<String> userIds = Collections.singletonList(subject.getCurrentUserId());
		int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
		return examService.otherExamRecords(
				subject.examRegion(),
				requestContext.get("page",Integer.class),
				requestContext.get("pageSize",Integer.class),
				requestContext.getOptional("name",String.class),
				requestContext.getOptional("sourceType",Integer.class),
				userIds,
				subject.getCurrentUserId(),
				requestContext.getOptional("year", String.class),
				requestContext.getOptional("startTimeOrderBy",Integer.class),
				pageSwitch == 1
		);
	}



	/**
	 * 验证是否能评卷
	 * 多个老师评同分试卷
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/validate-mark-paper", method = RequestMethod.GET)
	@Param(name = "examRecordId", required=true)
	@Param(name = "examId", required=true)
	@Param(name = "memberId", required=true) // 考生的memberID
	@JSON("*")
//	@Permitted()
	public Map<String, String> validateMarkPaper(RequestContext requestContext, Subject<Member> subject) {
		String memberId = requestContext.getString("memberId");
		Integer examRegion = memberService.findExamRegion(memberId);
		if (markConfigService.validateMarkPaper(
				examRegion,
				requestContext.getString("examRecordId"),
				subject.getCurrentUserId(),
				requestContext.getString("examId"))) {

			return ImmutableMap.of("status", "1");
		}
		return ImmutableMap.of("status", "0");
	}


	/**
	 * 验证是否能审核试卷
	 * 多个老师评同分试卷
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/validate-audit-paper", method = RequestMethod.GET)
	@Param(name = "examRecordId", required=true)
	@Param(name = "examId", required=true)
	@Param(name = "memberId", required=true) // 考生的memberID
	@JSON("*")
//	@Permitted()
	public Map<String, String> validateAuditPaper(RequestContext requestContext, Subject<Member> subject) {
		String memberId = requestContext.getString("memberId");
		Integer examRegion = memberService.findExamRegion(memberId);
		if (markConfigService.validateAuditPaper(
				examRegion,
				requestContext.getString("examRecordId"),
				subject.getCurrentUserId(),
				requestContext.getString("examId"))) {

			return ImmutableMap.of("status", "1");
		}
		return ImmutableMap.of("status", "0");
	}


	@RequestMapping(value="/find-by-audient", method = RequestMethod.GET)
	@Param(name="ids", required = true)
	@JSON("id,name,startTime,endTime,duration,passScore,type,needApplicant,applicantNeedAudit,showAnswerRule,applicantNumber,certificateId,previousStatus")
	@JSON("applicantStartTime,applicantEndTime,coverId,certificateId,examNotes,supportApp,parentId,status,joinNumber,isSetPassScore,paperClassId,anonymityMark")
	@JSON("isOpenPractice,paperShowRule,paperSortRule,isAllowSwitch,isShowAnswerImmed,allowSwitchTimes,isAllowPause")
	@JSON("isAllowAddRoom,allowExamMuchTimes,allowExamTimes,hasCert,sendToCenter,isOverByPassExam")
	public List<Exam> findByOwnAudient(RequestContext context, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		List<Exam> exams =  examService.findExamByAudient(
				examRegion,
				context.getString("ids").split(","),
				subject.getCurrentUserId()
		);
		return exams == null ? new ArrayList<>() : exams;
	}

	/*
	 * 根据考试ID，登录用户id获取最新考试记录
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/user-record/{id}", method = RequestMethod.GET)
	@JSON("id,name,createTime,startTime,endTime,examNotes,duration,passScore,type,needApplicant,applicantNeedAudit,isOverByPassExam,organizationId")
	@JSON("applicantStartTime,applicantEndTime,status,allowExamTimes,allowSwitchTimes,isShowAnswerImmed,examedTimes,sourceType,sendToCenter,clientType")
	@JSON("isSetPassword,isSetPersonalCode,needFillOutInfo,showAnswerRule,showScoreTime,strongBaseFlag,examAgain")
	@JSON("signUp.(id,status)")
	@JSON("paperClass.(totalScore)")
	@JSON("profession.(id,name)")
	@JSON("level.(id,levelName)")
	@JSON("member.(email,phoneNumber)")
	@JSON("examRecord.(id,status,startTime,endTime,submitTime,isFinished,isReset,topScoreRecordId,currentTime)")
	@JSON("finishEnterFace")
	@JSON("faceStatus")
	@JSON("faceEnter,faceMonitor,face2Enter,face2Monitor")
	@Param(name="id", type=String.class, required = true)
	@Param(name="examRecordId")
//	@Permitted()
	public Exam getExamWithUserRecord(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		String examId = requestContext.getString("id");
		Optional<String> examRecordId = requestContext.getOptionalString("examRecordId");
		Exam exam = getExamFromCacheOrDB(examRegion, examId);
		// 考试活动判断用户权限（app扫码报名受众过滤）
		if (exam.getSourceType() == 1) {
			//考试撤销判断
			if (exam.getStatus() == Exam.STATUS_NOT_PUBLISH) throw new UnprocessableException(ErrorCode.ExamStatusError);
			//受众权限判断
			boolean isAudient = audienceObjectService.isAudient(examRegion, examId, subject.getCurrentUserId());
			if (!isAudient) throw new UnprocessableException(ErrorCode.IsNotAudient);
		}
		PaperClass paperClass = paperClassService.getSimplePaperClassInfo(examRegion, exam.getPaperClassId());
		exam.setPaperClass(paperClass);

		Exam signUpAndExamRecord;
		if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
			Profession profession = getCloudProfession(examRegion, exam.getProfessionId());
			exam.setProfession(profession);
			ProfessionLevel level = getCloudLevel(examRegion, exam.getLevelId());
			exam.setLevel(level);
			Member member = getMember(examRegion, subject.getCurrentUserId());
			exam.setMember(member);
			signUpAndExamRecord =  examService.getCloudSignUpAndExamRecord(examRegion, requestContext.getString("id"), subject.getCurrentUserId());
		} else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
			Member member = getMember(examRegion, subject.getCurrentUserId());
			exam.setMember(member);
			signUpAndExamRecord =  examService.getGridSignUpAndExamRecord(examRegion, requestContext.getString("id"), subject.getCurrentUserId());
		}
		else {
			signUpAndExamRecord =  examService.getSignUpAndExamRecord(examRegion, requestContext.getString("id"), subject.getCurrentUserId());
		}

		exam.setExamRecord(signUpAndExamRecord.getExamRecord());
		exam.getExamRecord().setCurrentTime(System.currentTimeMillis());
		exam.setSignUp(signUpAndExamRecord.getSignUp());
		exam.setExamedTimes(signUpAndExamRecord.getExamedTimes());
		//开启人脸考试设置人脸检测状态
		if(Exam.FACE_ENTER_YES.equals(exam.getFaceMonitor()) || Exam.FACE_MONITOR_YES.equals(exam.getFaceEnter())) {
			//根据是否传了examRecordId判断是否为重新考试
			//examRecordId="noRecordId",去人脸监考表查询examRecordId为""的数据（有时间改成bool值）
			String latestRecordId = examRecordId.orElseGet(() -> exam.getExamRecord().getId());
			latestRecordId="noRecordId".equals(latestRecordId)?"":latestRecordId;
			List<ExamRecordFace> resp = examRecordFaceService.getListByRecordId(examRegion, subject.getCurrentUserId(), examId, latestRecordId, 0, Optional.empty());
			if(CollectionUtils.isEmpty(resp)){
				exam.setFinishEnterFace(false);
			}else{
				ExamRecordFace recordFace = resp.get(0);
				//当人脸状态为正常或标记正常，不用再次进行人脸检测
				exam.setFinishEnterFace(ExamRecordFace.STRING_EXAM_RECORD_FACE_STATUS_NORMAL.equals(recordFace.getStatus())||
						ExamRecordFace.STRING_EXAM_RECORD_FACE_TYPE_TAG_NORMAL.equals(recordFace.getStatus()));
				//如果是重新考试，record记录还未生成，人脸状态取人脸监考表的状态
				//否则取record表的状态
				if(examRecordId.isPresent()){
					exam.setFaceStatus(recordFace.getStatus());
				}else {
					exam.setFaceStatus(exam.getExamRecord().getFaceStatus());
				}
			}
		}
		// 如果该考试为强基的考试，需要查询是否还有考试机会
		if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1 == exam.getStrongBaseFlag()) {
			if (strongBaseService.authType(examRegion, exam.getId())) {
				exam.setExamAgain(strongBaseService.examAgain(examRegion, exam.getId(),subject.getCurrentUserId()));
			} else {
				// 如果为模拟考试，构造前端正常次数逻辑
				exam.setStrongBaseFlag(Exam.STRONG_BASE_FLAG_0);
				exam.setExamAgain(null);
			}
		}
		return exam;
	}



	@RequestMapping(value = "/over-exam-time" , method = RequestMethod.POST)
	@Param(name = "examId", required = true)
	@JSON("status")
	public Map<String, Integer> isOverExamTime(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		// 获取考试允许考试次数
		Exam exam = getExamFromCacheOrDB(examRegion, requestContext.getString("examId"));
		// 获取用户最新考试记录
		ExamRecord examRecord = getExamRecordFromCacheOrDB(examRegion, exam.getId(), subject.getCurrentUserId());

		if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1.equals(exam.getStrongBaseFlag())) {
			if (strongBaseService.authType(examRegion, exam.getId())) {
				return ImmutableMap.of("status", !strongBaseService.examAgain(examRegion, exam.getId(),subject.getCurrentUserId()) ? Exam.EXAM_NO : Exam.EXAM_YES);
			}
		}
		Integer allowExamTimes = exam.getAllowExamTimes();

		// 判断考试次数是否超过最大限制
		boolean flag =  isOverExamTimes(allowExamTimes, examRecord);
		return ImmutableMap.of("status", flag ? Exam.EXAM_NO : Exam.EXAM_YES);

	}

	/**
	 * 判断考试次数是否超过最大限制
	 * @return
	 */
	private boolean isOverExamTimes(Integer allowExamTimes, ExamRecord examRecord) {
		// 未开考，考试不限次数
		if (allowExamTimes == null || allowExamTimes == 0 || examRecord == null) return false;

		Integer examTimes = examRecord.getExamTimes();
		if(examTimes == null || examTimes ==0) {
			return false;
		}

		return (examTimes.intValue() + 1)  > allowExamTimes.intValue()
				&& (examRecord.getStatus() != null &&
				ExamRecord.STATUS_TIME_EXCEPTION.intValue() < examRecord.getStatus().intValue());
	}


	/**
	 * 考卷信息(进入考试)
	 * 1.考试基本信息
	 * 2.试卷实例信息
	 * 3.考试记录信息
	 * @param requestContext
	 * @param member
	 * @return
	 */
	@RequestMapping(value = "/exam-paper", method = RequestMethod.GET)
	@Param(name = "examId", type = String.class)
	@JSON("id,name,startTime,endTime,duration,passScore,type,examNotes")
	@JSON("supportApp,isAllowSwitch,isShowAnswerImmed,indefinite")
	@JSON("allowSwitchTimes,allowExamTimes,paperSortRule,paperShowRule,showAnswerRule,isSetPersonalCode,isPermitViewCode,showScoreTime")
	@JSON("paper.(encryptContent,questionNum,totalScore,isSubjective)")
	@JSON("paper.questions.(id,type,content,parentId,score,sequence,difficulty)")
	@JSON("paper.questions.questionAttrCopys.(id,name,value,type)")
	@JSON("paper.questions.subs.(id,type,content,parentId,score,difficulty)")
	@JSON("paper.questions.subs.questionAttrCopys.(id,name,value,type)")
	@JSON("paper.answerRecords.(id,answer,score,isRight,questionId)")
	@JSON("paper.answerRecords.questionCopy.(type)")
	@JSON("examRecord.(id,startTime,endTime,isReset,lastCacheTime,lastSubmitTime,currentTime,status,orderContent,switchTimes,personalCode,paperInstanceId,faceStatus)")
	@JSON("examRecord.member.(id,name,fullName)")
	@JSON("faceMonitor,faceEnter,indefinite")
//	@Permitted()
	public Exam enterExam (RequestContext requestContext, Subject<Member> member) {
		Integer examRegion = member.examRegion();
		Long st = System.currentTimeMillis();
		//最新考试信息
		Exam newestExam = getExamFromCacheOrDB(examRegion, requestContext.getString("examId"));
		//考试未开始，不能进入
		if (newestExam.getStartTime() != null && st < newestExam.getStartTime()) {
			throw new UnprocessableException(ErrorCode.ExamNoStart);
		}
		if (Exam.STATUS_NOT_PUBLISH == newestExam.getStatus() || Exam.STATUS_PUBLISHING == newestExam.getStatus()) {
			throw new UnprocessableException(ErrorCode.ExamNoStart);
		}
		//考试已结束，不能进入
		if (newestExam.getEndTime() != null && newestExam.getDuration() != null
				&& st > (newestExam.getEndTime() + (newestExam.getDuration() * 60000))) {
			throw new UnprocessableException(ErrorCode.ExamIsOver);
		}

		//考试记录信息
		ExamRecord record = getExamRecordFromCacheOrDB(examRegion, newestExam, member, true);

		//试卷信息
		PaperInstance paper = getPaperByCache(examRegion, newestExam.getId(), record.getPaperInstanceId());
		//试题信息
		getQuestionsByCache(examRegion, paper, newestExam, record);
		//答题记录信息
		getAnswerRecordsByExamRecordId(examRegion, paper, record);
		//如果用户在考试撤销之前已经拥有考试信息，就继续沿用自己的考试信息(对于正在进行中的考试记录不影响修改后的考试信息)
		Exam realReturnToUserExam = getRealReturnToUserExam(newestExam, record);
		realReturnToUserExam.setPaper(paper);
		realReturnToUserExam.setExamRecord(record);

		Long et = System.currentTimeMillis();
		LOGGER.info("enterExam, pay time:{}", (et - st) + "ms");

		return realReturnToUserExam;
	}

	/**
	 * 撤销考试，不影响正在进行中的考试记录的考试信息
	 * 从缓存获取的examRecord里面应该持有创建时所对应的考试信息
	 * @param newestExam
	 * @param record
	 * @return
	 */
	private Exam getRealReturnToUserExam(Exam newestExam, ExamRecord record) {
		return Optional.ofNullable(record.getExam()).map(exam -> exam).orElseGet(() -> {
			record.setExam(newestExam);
			examRecordCache.set(ExamRecord.getExamRecordKey(
							record.getExamId(),record.getMemberId()),
					record, Exam.CACHE_TIME);
			return newestExam;
		});
	}

	/**
	 * 根据考试记录获取答题记录
	 * 条件：超时异常请求试卷
	 * @param paper
	 * @param record
	 */
	private void getAnswerRecordsByExamRecordId(Integer examRegion, PaperInstance paper, ExamRecord record) {
		if (record.getEndTime() < System.currentTimeMillis()) {
			paper.setAnswerRecords(answerRecordService.findByExamRecordId(examRegion, record.getId(), record.getExamId()));
		}
		paper.setAnswerRecords(null);
	}

	/**
	 * 缓存获取试卷信息
	 * @param examId
	 * @param paperInstanceId
	 * @return
	 */
	private PaperInstance getPaperByCache(Integer examRegion, String examId, String paperInstanceId) {
		PaperInstance paper = paperCache.get(examId + paperInstanceId, () -> {
			return paperInstanceService.get(examRegion, paperInstanceId, examId);
		}, Exam.CACHE_TIME);
		return paper;
	}

	/**
	 * 缓存获取试题信息
	 * @param paper
	 * @param exam
	 * @param record
	 */
	private void getQuestionsByCache(Integer examRegion, PaperInstance paper, Exam exam, ExamRecord record) {
		packQuestionsFromCacheOrDBToPaper(examRegion, paper, exam);
		if (paper.getQuestions().isEmpty()) findQuestionsByPaperId(examRegion, paper, record, exam.getId());
		updateQuestionAnswerToNegativeOne(paper);
	}

	/**
	 * 把试题里面含有答案的字段都统一变为 “-1”
	 * @param paper
	 */
	private void updateQuestionAnswerToNegativeOne(PaperInstance paper) {
		paper.getQuestions().stream().forEach(q -> {
			filterQuestionAttrWithNoAnswer(q);
		});
	}

	/**
	 * 过滤  答案属性
	 * 把答案都变为-1
	 * 1.单选，多选，把questionAttrCopy.type=0的type变为-1
	 * 2.判断，填空，问答，questionAttrCopy.value 变为-1
	 * 3.排序 questionAttrCopy.type=0 的 value 变为-1
	 * @param questionCopy
	 * @return
	 */
	private QuestionCopy filterQuestionAttrWithNoAnswer(QuestionCopy questionCopy) {
		//单选
		if (questionCopy.getType() == Question.SINGLE_CHOOSE || questionCopy.getType() == Question.MULTIPLE_CHOOSE) {
			questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithChoose(questionCopy.getQuestionAttrCopys()));
		}
		//判断，问答，填空
		if (questionCopy.getType() == Question.JDUGEMENT || questionCopy.getType() == Question.QUESTION_ANWSER || questionCopy.getType() == Question.SENTENCE_COMPLETION) {
			questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithJdugement(questionCopy.getQuestionAttrCopys()));
		}

		//阅读
		if (questionCopy.getType() == Question.READING_COMPREHENSION) {
			questionCopy.setSubs(questionCopy.getSubs().stream().map(t -> {
				if (t.getType() == Question.SINGLE_CHOOSE || t.getType() == Question.MULTIPLE_CHOOSE) {
					t.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithChoose(t.getQuestionAttrCopys()));
				}
				if (t.getType() == Question.QUESTION_ANWSER) {
					t.setQuestionAttrCopys(new ArrayList<>());
				}
				return t;
			}).collect(Collectors.toList()));
		}
		//排序
		if (questionCopy.getType() == Question.SORTING) {
			questionCopy.setQuestionAttrCopys(filterQuestionAttrNoAnswerWithSort(questionCopy.getQuestionAttrCopys()));
		}

		return questionCopy;
	}

	/**
	 * 排序
	 * 把questionAttrCopys type=0的记录 的name 变为-1
	 * @param questionAttrCopys
	 * @return
	 */
	private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithSort(List<QuestionAttrCopy> questionAttrCopys) {
		return questionAttrCopys.stream().map(t -> {
			if (Integer.valueOf(t.getType()) == QuestionAttr.ANSWER_TYPE) {
				try {
					t.setName(FILTER_ANSWER_QUESTION_ATTR);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			return t;
		}).collect(Collectors.toList());
	}

	/**
	 * 填空，问答，判断
	 * 把name,value =-1
	 * @param questionAttrCopys
	 * @return
	 */
	private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithJdugement(List<QuestionAttrCopy> questionAttrCopys) {
		return questionAttrCopys.stream().map(t -> {
			try {
				t.setName(FILTER_ANSWER_QUESTION_ATTR);
				t.setValue(FILTER_ANSWER_QUESTION_ATTR);
			} catch (Exception e) {
				e.printStackTrace();
			}

			return t;
		}).collect(Collectors.toList());
	}


	/**
	 * 单选多选
	 * questionAttrCopy.type=0 -> type=-1
	 * @param questionAttrCopys
	 * @return
	 */
	private List<QuestionAttrCopy> filterQuestionAttrNoAnswerWithChoose(List<QuestionAttrCopy> questionAttrCopys) {
		return questionAttrCopys.stream().map(t -> {
			try {
				t.setType(FILTER_ANSWER_QUESTION_ATTR);
			} catch (Exception e) {
				e.printStackTrace();
			}
			return t;
		}).collect(Collectors.toList());
	}


	private void findQuestionsByPaperId(Integer examRegion, PaperInstance paper, ExamRecord record, String examId) {
		List<QuestionCopy> questions = questionCopyService.findQuestionsByPaperId(examRegion,
				paper.getId(), record.getId(), examId);
		Collections.sort(questions);
		paper.getQuestions().addAll(questions);
	}

	/**
	 * 缓存取出考试对应全部题目
	 * 组装试卷实例对应的试题
	 * @param paper
	 * @param exam
	 */
	private void packQuestionsFromCacheOrDBToPaper(Integer examRegion, PaperInstance paper, Exam exam) {

		//从缓存或数据库查询该考试对应的所有题目
		List<QuestionCopy> questionCopys = questionCache.get(exam.getId(), () -> {
			return questionCopyService.findQuestionCopysByExamId(exam.getId());
		}, Exam.CACHE_TIME);
		//试题映射
		Map<String, QuestionCopy> questionCopyMap = questionCopys.stream()
				.collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
		//子题目映射
		Map<String, List<QuestionCopy>> subsMap = questionCopys.stream()
				.filter(f -> f.getParentId() != null)
				.collect(Collectors.groupingBy(QuestionCopy::getParentId));

		//根据paperId查出试卷试题的顺序(随机组卷使用)
		List<PaperInstanceQuestionCopy> paperInstanceQuestionCopies = paperInstanceQuestionCopyCache.get(paper.getId(), () -> {
			return paperInstanceService.findPaperInstanceQuestionCopiesByPaperId(examRegion, paper.getId(), exam.getId());
		}, Exam.CACHE_TIME);

		Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopiesMap = paperInstanceQuestionCopies.stream().collect(
				Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

		//根据paper持有的paperQuestionCopyIds组装对应的试题
		List<QuestionCopy> paperQuestions = paper.getQuestionCopyIds().stream().map(id -> {
			QuestionCopy questionCopy = questionCopyMap.get(id);
			//设置对应的默认试题顺序
			if (questionCopy != null && paperInstanceQuestionCopiesMap.get(questionCopy.getId()) != null)
				questionCopy.setSequence(paperInstanceQuestionCopiesMap.get(questionCopy.getId()).getSequence());
			return questionCopy;
		}).collect(Collectors.toList());

		// 组装阅读题
		try {
			if (paperQuestions != null && paperQuestions.size() > 0)
				paperQuestions.stream().filter(f -> f !=null && Question.READING_COMPREHENSION == f.getType()).forEach(q -> {
					if (subsMap != null) {
						List<QuestionCopy> subs = subsMap.get(q.getId());
						if (subs != null) {
							subs.forEach(s -> {
								if (paperInstanceQuestionCopiesMap.get(s.getId()) != null)
									s.setSequence(paperInstanceQuestionCopiesMap.get(s.getId()).getSequence());
							});
						}
						if (q!=null){
							q.setSubs(subs);
						}
					}
				});
			Collections.sort(paperQuestions);
			paper.setQuestions(new ArrayList<>());
			paper.getQuestions().addAll(paperQuestions);
		} catch (Exception e2) {
			e2.printStackTrace();
		}

	}



	/**
	 * 缓存获取考试记录信息
	 * 缓存没有，获取db
	 * db没有，根据条件生成或返回异常代码
	 * @param exam
	 * @param member
	 * @return
	 */
	private ExamRecord getExamRecordFromCacheOrDB(Integer examRegion, Exam exam, Subject<Member> member, boolean checkCode) {
		ExamRecord record = examRecordCache.get(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()), () -> {

			ExamRecord er = examRecordService.getNewestRecord(examRegion, exam.getId(), member.getCurrentUserId());

			if (isOtherModuleExam(exam)) return getOtherModuleExamRecord(examRegion, er, exam, member.getCurrentUserId());

			return validateExamRecord(examRegion, exam, er, member.getCurrentUserId());

		}, Exam.CACHE_TIME);

		//每次提交试卷，考试记录不会清除缓存，考试记录的提交试卷会更新
		//多次考的时候，再次考试，先从缓存获取考试记录，判断提交时间是否已更新

		// 线上出现examRecord的status为null的情况，在此增加判断，并清空缓存，让用户重新进入
		if (record == null || record.getStatus() == null) {
			examRecordCache.clear(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()));
			throw new UnprocessableException(ErrorCode.ExamRecordStatusRequired);
		}

		if (alreadySubmited(record)) {

			record = validateByCacheRecord(examRegion, exam, record, member);

			examRecordCache.set(ExamRecord.getExamRecordKey(exam.getId(), member.getCurrentUserId()), record, Exam.CACHE_TIME);
		}

		if (checkCode && !isOtherModuleExam(exam) && needCheckCode(exam)  && !checkCodeFromCache(exam.getId(), member.getCurrentUserId())) {
			//由于密码错误进不去，必须更新以前记录为非当前记录
			examRecordService.updateBeforeRecordBeNoCurrent(examRegion, record);
			throw new UnprocessableException(ErrorCode.ExamPasswordOrPersonalCodeError);
		}

		//指定考试，报名考试，手动添加考生
		//需要更新开始时间，结束时间，试题排序等字段，个人密码
		// checkCode 为true,代表是在enterExam方法调用该获取考试记录的方法，为false, 其他方法调用，有一些方法不需再执行多一次
		if (checkCode) {
			updateExamRecordFieldValue(examRegion, record, exam);
		}

//		//异步生成考试记录，试卷发生异常的补偿逻辑
//		//有可能异步挂了，撤销考试再发布时候考试记录删掉，但缓存还有
//		//考试记录所关联的试卷已经被删掉等
//		//以上的场景，在examListener生成试卷逻辑的方法加了事务后不会发生
//		//如下方法先保留
//		return asyncErrorCompensation(exam, record, member);
		return record;
	}


	/**
	 * 指定考试/报名考试
	 * 考试记录
	 * a.开始时间
	 * b.结束时间 = 开始时间 + 时长
	 * c.当前时间
	 * @param record
	 */
	private void updateExamRecordFieldValue(Integer examRegion, ExamRecord record, Exam exam) {
		Optional.ofNullable(record.getStartTime()).orElseGet(() -> {

			record.setStatus(ExamRecord.STATUS_DOING);
			record.setStartTime(System.currentTimeMillis());

			Optional.ofNullable(record.getEndTime()).orElseGet(() -> {
				record.setEndTime(System.currentTimeMillis() + (1000 * 60 * exam.getDuration()));
				return record.getEndTime();
			});

			Optional.ofNullable(record.getOrderContent()).orElseGet(() -> {
				String orderContent = paperInstanceService.createNewQuestionOrder(examRegion,
						Optional.empty(), record.getPaperInstanceId(), exam.getPaperSortRule());
				record.setOrderContent(orderContent);
				return record.getOrderContent();
			});

			Optional<Integer> personalCode = getPersonalCode(exam);

			examRecordService.update(examRegion, record.getId(), Optional.of(record.getStartTime()),
					Optional.of(record.getEndTime()), Optional.of(ExamRecord.STATUS_DOING),
					Optional.empty(), null, Optional.empty(), null,
					Optional.empty(), Optional.ofNullable(record.getOrderContent()), personalCode, exam.getId());

			messageSender.send(MessageTypeContent.EXAM_RECORD_UPDATE_STARTING,
					ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID, record.getId(),
					MessageHeaderContent.EXAM_ID, exam.getId(),
					MessageHeaderContent.MEMBER_ID, record.getMemberId());

			record.setPersonalCode(personalCode.orElse(null));
			return record.getStartTime();
		});

		//其他业务模块的考试每次进入考试都要发出消息，监听进度
		sendOtherModuleExamRecordUpdateStatus(exam.getSourceType(), record.getId(), exam.getId());

		//currentTime:当前服务器的时间
		//第一次请求考试信息的时候，以startTime为currentTime
		//之后如果用户刷新试卷信息，直接返回服务器当前时间
		Optional.ofNullable(record.getStartTime()).map(startTime -> {
			Optional.ofNullable(record.getCurrentTime()).map(ct -> {
				record.setCurrentTime(System.currentTimeMillis());
				return ct;
			}).orElseGet(() -> {
				record.setCurrentTime(record.getStartTime());
				return record.getCurrentTime();
			});
			return startTime;
		}).orElseGet(() -> {
			record.setCurrentTime(System.currentTimeMillis());
			return record.getCurrentTime();
		});

		if (!isOtherModuleExam(exam) && record.getStartTime() != null) {
			Optional<Integer> personalCode = getPersonalCode(exam);
			examRecordService.update(examRegion, record.getId(), Optional.empty(),
					Optional.empty(), Optional.empty(),
					Optional.empty(), null, Optional.empty(), null,
					Optional.empty(), Optional.empty(), personalCode, exam.getId());
			record.setPersonalCode(personalCode.orElse(null));
		}

		//存储缓存
		record.setExamId(exam.getId());
		examRecordCache.set(ExamRecord.getExamRecordKey(
				record.getExamId(), record.getMemberId()), record, Exam.CACHE_TIME);
	}

	/**
	 * 发送其他模块考试的状态变更
	 * @param sourceType
	 * @param examRecordId
	 * @param examId
	 */
	private void sendOtherModuleExamRecordUpdateStatus(Integer sourceType, String examRecordId, String examId) {
		if (sourceType != Exam.EXAM_ACTIVITY_SOURCE_TYPE)
			messageSender.send(
					MessageTypeContent.OTHER_MODULE_EXAM_RECORD_UPDATE_STATUS,
					MessageHeaderContent.ID, examRecordId,
					MessageHeaderContent.EXAM_ID,examId);
	}


	private Optional<Integer> getPersonalCode(Exam exam) {
		if (!isOtherModuleExam(exam)
				&& (exam.getIsSetPersonalCode() != null
				&& exam.getIsSetPersonalCode() == Exam.EXAM_YES)) {
			return randomThreeCode();
		}
		return Optional.empty();
	}

	private Optional<Integer> randomThreeCode() {
		List<String> str = new ArrayList<>();
		for (int i = 0; i < 3; i++) {
			Integer temp = new Random().nextInt(9);
			temp = temp == 0 ? (temp + 1) : temp;
			str.add(String.valueOf(temp));
		}
		return Optional.of(Integer.valueOf(str.stream().collect(Collectors.joining(""))));
	}



	private boolean needCheckCode(Exam exam) {
		return (exam.getIsSetPersonalCode() != null && exam.getIsSetPersonalCode() == Exam.EXAM_YES)
				|| (exam.getIsSetPassword() != null && exam.getIsSetPassword() == Exam.EXAM_YES);
	}

	private boolean checkCodeFromCache(String examId, String memberId) {
		String value = enterExamCodeCache.get(ExamRecord.getExamRecordKey(examId, memberId), String.class);
		if (value != null && value.equals("1")) {
			enterExamCodeCache.clear(ExamRecord.getExamRecordKey(examId, memberId));
			return true;
		}
		return false;
	}

	private boolean alreadySubmited(ExamRecord record) {
		return record.getSubmitTime() != null
				|| record.getStatus() >= ExamRecord.STATUS_TO_BE_OVER;
	}

	private ExamRecord validateByCacheRecord(Integer examRegion, Exam exam, ExamRecord record, Subject<Member> member) {
		if (isOtherModuleExam(exam)) return getOtherModuleExamRecord(examRegion, record, exam, member.getCurrentUserId());
		return validateExamRecord(examRegion, exam, record, member.getCurrentUserId());
	}

	/**
	 * 验证考试记录是否符合条件
	 * 1.考试撤销返回异常
	 * 2.非受众范围 返回异常
	 * 3.如果考试记录为空，
	 * 		a.是否需要报名
	 *      b.创建记录
	 * 4.考试活动超时，重置记录不用考虑此范围
	 * 5.考试记录已完成，判断是否能继续多次考，创建记录
	 * @param exam
	 * @param examRecord
	 * @return
	 */
	private ExamRecord validateExamRecord(Integer examRegion, Exam exam, ExamRecord examRecord, String memberId) {
		// 撤销
		validateExamCanceled(exam);

		//是否受众范围内
		validateAuident(examRegion, examRecord, exam, memberId);

		//考试记录为空的场景判断
		if (valdateExamRecordForNull(examRegion, examRecord, exam, memberId)) {
			return createNewExamRecord(examRegion, examRecord, exam, memberId);
		}

		//是否超时
		validateOverTime(examRecord, exam);

		//判断是否超出考试次数限制
		if (validateOverLimitExamTimes(examRegion, examRecord, exam, memberId)) {
			return createNewExamRecord(examRegion, examRecord, exam, memberId);
		}

		return examRecord;
	}

	private void validateOverTime(ExamRecord er, Exam exam) {
		Long nowtime = System.currentTimeMillis();
		if (er.getIsReset() == null || er.getIsReset() != ExamRecord.IS_RESET) {
			if (exam.getSourceType() == Exam.EXAM_ACTIVITY_SOURCE_TYPE) {
				if (!validateExamTimeRange(nowtime, exam, er)) {
					throw new ValidationException(ErrorCode.OverExam);
				}
			}
		}
	}

	private boolean validateExamTimeRange(Long nowTime, Exam exam, ExamRecord er) {
		if (er.getStartTime() == null) {
			return exam.getStartTime() <= nowTime && exam.getEndTime() >= nowTime;
		}
		return exam.getStartTime() <= nowTime;
	}

	private boolean valdateExamRecordForNull(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
		return Optional.ofNullable(er).map(e -> false).orElseGet(() -> {
			//指定考试
			if (specifyExam(exam))
				throw new UnprocessableException(ErrorCode.IsNotAudient);
			// 报名考试验证
			if (!applicantExamNeedApproving(examRegion, exam, memberId, er))
				throw new UnprocessableException(ErrorCode.ExamApplicantNoPass);

			return true;
		});
	}

	/**
	 * 判断是否指定考试
	 * @param exam
	 * @return
	 */
	private boolean specifyExam(Exam exam) {
		return exam.getSendToCenter() != null && exam.getSendToCenter() == Exam.EXAM_YES;
	}

	/**
	 * 报名非审核 只要有数据就通过
	 * 报名需审核 有数据并且通过状态
	 * @param exam
	 * @param memberId
	 * @return
	 */
	private boolean applicantExamNeedApproving(Integer examRegion, Exam exam, String memberId, ExamRecord examRecord) {
		if (exam.getNeedApplicant() == Exam.EXAM_YES) {
			return signUpService.getOptional(examRegion, exam.getId(), memberId).map(t -> {
				return t.getStatus() != null && t.getStatus().intValue() == SignUp.STATUS_PASSED;
			}).orElse(false);
		}
		return true;
	}

	private void validateExamCanceled(Exam exam) {
		if (exam.getStatus() == Exam.STATUS_NOT_PUBLISH)
			throw new UnprocessableException(ErrorCode.ExamStatusError);
	}

	private void validateAuident(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
		if (!noNeedCheckIsAuient(er) && !isAudient(examRegion, exam.getId(), memberId))
			throw new UnprocessableException(ErrorCode.IsNotAudient);
	}

	/**
	 * 是否拥有受众
	 * ps********************后期要监听撤销后更改受众对象的更改对应的cacheMap
	 * @param examId
	 * @param memberId
	 * @return
	 */
	private boolean isAudient(Integer examRegion, String examId, String memberId) {
		Map<String, String> audientMap = audientCache.get(examId, () -> {
			return new HashMap<>();
		}, Exam.CACHE_TIME);
		if (audientMap.get(memberId) != null) return true;

		boolean isAudient = audienceObjectService.isAudient(examRegion, examId, memberId);
		if (isAudient) {
			audientMap.put(memberId, "true");
			audientCache.set(examId, audientMap, Exam.CACHE_TIME);
		}
		return isAudient;
	}

	/**
	 * 不需要检查受众权限的情况
	 *a .开考了，还没提交结果的
	 *b' 考试撤销，切换了受众对象，之前的记录在个人中心，还能再考
	 * @param er
	 * @return
	 */
	private boolean noNeedCheckIsAuient(ExamRecord er) {
		return er != null && er.getStatus() >= ExamRecord.STATUS_DOING &&
				er.getStatus() <= ExamRecord.STATUS_TIME_EXCEPTION;
	}

	/**
	 * 其他模块的考试
	 * 课程 专题 直播 班级等
	 * @param exam
	 * @return
	 */
	private boolean isOtherModuleExam(Exam exam) {
		return exam.getSourceType() != Exam.EXAM_ACTIVITY_SOURCE_TYPE;
	}

	/**
	 * 考试记录-其他模块
	 * @param er
	 * @param exam
	 * @param memberId
	 * @return
	 */
	private ExamRecord getOtherModuleExamRecord(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
		if (er == null) return createNewExamRecord(examRegion, er, exam, memberId);
		return validateOtherModuleExamReocrd(examRegion, er, exam, memberId);
	}

	/**
	 * 其他模块的 考试 跟考试活动的验证有所区别，分开写验证
	 * @param memberId
	 * @return
	 */
	private ExamRecord validateOtherModuleExamReocrd(Integer examRegion, ExamRecord examRecord, Exam exam, String memberId) {
		if (otherModuleExamMore(examRegion, examRecord, exam, memberId)) return createNewExamRecord(examRegion, examRecord, exam, memberId);
		return examRecord;
	}

	/**
	 * 课程 专题的考试需要再考
	 * 1：允许考试次数null-->是否及格才算完成->是， 未及格
	 * 2：允许考试次数有-->0 || x < total
	 * @param er
	 * @param exam
	 * @return
	 */
	private boolean otherModuleExamMore(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
		if (exam.getCreateTime() != null && exam.getCreateTime() > 1541001600000l
				&& exam.getAllowExamTimes() != null) {
			//判断是否超出考试次数限制
			if (validateOverLimitExamTimes(examRegion, er, exam, memberId)) {
				return true;
			}
			return false;
		}
		return exam.getIsOverByPassExam() != null
				&& Exam.IS_OVER_BY_PASS_EXAM == exam.getIsOverByPassExam()
				&& er.getStatus() == ExamRecord.STATUS_NOT_PASS;
	}

	private boolean validateOverLimitExamTimes(Integer examRegion, ExamRecord er, Exam exam, String memberId) {
		if (er.getStatus() >= ExamRecord.STATUS_TO_BE_OVER || er.getSubmitTime() != null) {
			//是否能再次考试
			if (isCanExamAgain(examRegion, exam, er, memberId)) {
				return true;
			}
			throw new ValidationException(ErrorCode.CanNotExamMore);
		}
		return false;
	}

	/**
	 * 是否能继续考多次
	 *
	 * 0.考试组内的正式考试共享允许考试次数，每次考试（仅认证考试），消耗一次考试次数，直到剩余允许考试次数为0后，不再允许继续考试，若不填写则为不限制考试次数；
	 * 1.allowExamTimes=0，无限次考
	 * 2.允许参考次数大于已考次数，可以继续考
	 * @param exam
	 * @param er
	 * @return
	 */
	private boolean isCanExamAgain(Integer examRegion, Exam exam, ExamRecord er, String memberId) {

		// 如果考试是强基计划认证专区用的，判断考试组的次数是否满足
		if (exam.getStrongBaseFlag() != null && Exam.STRONG_BASE_FLAG_1 == exam.getStrongBaseFlag()) {
			// 只有考试组中是认证考试类型并且已发布才看是否满足考试组的次数
			if (strongBaseService.authType(examRegion, exam.getId())) {
				// 查询是否允许再次考试
				return strongBaseService.examAgain(examRegion, exam.getId(), memberId);
			}
		}

		int examedTime = Optional.ofNullable(er.getExamTimes()).orElseGet(() -> {
			return examRecordService.calculateExamTimes(examRegion, exam.getId(), er.getMemberId());
		});
		return exam.getAllowExamTimes() == Exam.NO_LIMIT_TIME
				|| (exam.getAllowExamTimes() != Exam.NO_LIMIT_TIME && exam.getAllowExamTimes() > examedTime);
	}

	/**
	 * 创建考试记录
	 * 1.更新历史记录isCurrent=0
	 * 2.把旧的考试记录试卷id传给新记录(保持考多次用同一份试卷)
	 * 3.如果每次试卷随机，创建考试记录是随机抽取试卷
	 * @param examRecord
	 * @param exam
	 * @param memberId
	 * @return
	 */
	private ExamRecord createNewExamRecord(Integer examRegion, ExamRecord examRecord, Exam exam, String memberId) {

		Long startTime = System.currentTimeMillis();
		Long endTime = System.currentTimeMillis() + (1000 * 60 * exam.getDuration());
		if (Exam.RANDOM_TYPE_TWO.equals(exam.getRandomType())) {
			// 其他模块，随机组卷，多次试卷随机，生成examRecord时，随机抽取一张试卷
			if (examRecord != null) {
				examRecord.setPaperInstanceId(paperInstanceService.getWithRandomByExamId(examRegion, exam.getId()).getId());
			}
		}
		ExamRecord newExamRecord = examRecordService.insert(examRegion,
				exam.getId(), getPaperInstanceId(examRegion, examRecord, exam), memberId, ExamRecord.STATUS_DOING,
				Optional.ofNullable(startTime), Optional.ofNullable(endTime),
				Optional.ofNullable(exam.getPaperSortRule()), Optional.ofNullable(getExamRecordExamTimes(examRecord)), getPersonalCode(exam));
		return newExamRecord;
	}

	private String getPaperInstanceId(Integer examRegion, ExamRecord examRecord, Exam exam) {
		return Optional.ofNullable(examRecord).map(e -> {
			return e.getPaperInstanceId();
		}).orElseGet(() -> {
			return paperInstanceService.getWithRandomByExamId(examRegion, exam.getId()).getId();
		});
	}

	private Integer getExamRecordExamTimes(ExamRecord examRecord) {
		return Optional.ofNullable(examRecord).map(e -> {
			return e.getExamTimes();
		}).orElse(null);
	}


	private ExamRecord getExamRecordFromCacheOrDB(Integer examRegion, String examId, String memberId) {
		String recordKey = ExamRecord.getExamRecordKey(examId, memberId);
		return examRecordCache.get(recordKey, () -> {
			return examRecordService.getNewestRecord(examRegion, examId, memberId);
		}, Exam.CACHE_TIME);
	}


	private Member getMember(Integer examRegion, String currentUserId) {
		return signUpService.getMember(examRegion, currentUserId);
	}

	private Profession getCloudProfession(Integer examRegion, String professionId) {
		return cloudProfessionCache.get(professionId, () -> {
			return professionService.getCloudProfession(examRegion, professionId);
		}, Exam.CACHE_TIME);
	}

	private ProfessionLevel getCloudLevel(Integer examRegion, String levelId) {
		return cloudLevelCache.get(levelId, () -> {
			return professionLevelService.getCloudLevel(examRegion, levelId);
		}, Exam.CACHE_TIME);
	}

	/**
	 * 缓存获取考试信息
	 * @param examId
	 * @return
	 */
	private Exam getExamFromCacheOrDB(Integer examRegion, String examId) {
		Exam exam = new Exam();
		logger.info("getExamFromCacheOrDB获取入参{},{}",examRegion,examId);
		try {
			exam = examCache.get(examId, () -> {
				return Optional.ofNullable(
								examService.getSimpleData(examRegion, examId))
						.orElseThrow(() ->
								new UnprocessableException(ErrorCode.ExamNullError));
			}, Exam.CACHE_TIME);

		} catch (Exception e) {
			examCache.clear(examId);
			exam = examCache.get(examId, () -> {
				return Optional.ofNullable(
								examService.getSimpleData(examRegion, examId))
						.orElseThrow(() ->
								new UnprocessableException(ErrorCode.ExamNullError));
			}, Exam.CACHE_TIME);
		}

		return exam;
	}

	@RequestMapping(value = "/refresh-face-record", method = RequestMethod.POST)
	@Param(name = "examId", required = true)
	@Param(name = "examRecordId", required = true)
	@JSON("*")
	public Map<String, Object> refreshFaceRecord(RequestContext requestContext, Subject<Member> member){
		Integer examRegion = member.examRegion();
		examRecordFaceService.refreshFaceRecord(
				examRegion,
				requestContext.getString("examId"),
				member.getCurrentUserId(),
				requestContext.getString("examRecordId"));
		return ImmutableMap.of("result", "success");
	}



	/**
	 * 根据ids查询考试和试卷信息
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value = "basic-by-ids", method = RequestMethod.GET)
	@Param(name = "ids", type = String.class, required =true)
	@JSON("id,name,organizationId,startTime,endTime,examedTimes,createTime,isOverByPassExam,sourceType,allowExamTimes,passScore,showAnswerRule,coverId,coverIdPath")
	@JSON("paperClass.(id,type)")
	@JSON("examRecord.(id,status,score)")
	public List<Exam> findBasicByIds(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		return examService.findBasicByIdsByMemberId(examRegion, requestContext.getString("ids").split(","), subject.getCurrentUserId());
	}


	/**
	 * 根据日期查询当天的考试列表
	 */
	@RequestMapping(value = "/exam-day", method= RequestMethod.GET)
	@JSON("id,name,startTime,needFillOutInfo,endTime,duration,applicantStartTime,applicantEndTime,needApplicant,applicantNeedAudit,type,haveExamTimes")
	@JSON("signUp.(id,status,memberId)")
	@JSON("examRecord.(currentTime)")
	@JSON("paperClass.(id)")
	@Param(name="organizationId", type=String.class, required = true)
	@Param(name="date", type=String.class, required = true)
	public List<Exam> findExamDay(RequestContext requestContext, Subject<Member> subject) {
		Integer examRegion = subject.examRegion();
		return examService.findExamDay(
				examRegion,
				requestContext.getString("organizationId"),
				DateUtil.dateStringYYYYMMDD2Long(requestContext.getString("date")),
				DateUtil.dateStringYYYYMMDDHHMMSS2Long(requestContext.getString("date")+" 23:59:59"),
				subject.getCurrentUserId()
		);
	}



}
