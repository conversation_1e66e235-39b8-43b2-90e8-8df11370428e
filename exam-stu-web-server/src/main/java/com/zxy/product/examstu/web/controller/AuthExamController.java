package com.zxy.product.examstu.web.controller;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;

import com.zxy.product.examstu.api.ExamService;
import com.zxy.product.exam.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@Controller
@RequestMapping("/auth-exam")
public class AuthExamController {



    private ExamService examService;


    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }



    /**
     * 认证考试列表
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/signup-list" , method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "proId", type = String.class)
    @Param(name = "subProId", type = String.class)
    @Param(name = "levelId", type = String.class)
    @Param(name = "equipmentId", type = String.class)
    @Param(name = "startTimeOrderBy", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,type,needApplicant,applicantNeedAudit,needFillOutInfo,examBatch,name,startTime,endTime,applicantStartTime,applicantEndTime,applicantNumber,status,haveExamTimes)")
    @JSON("items.signUp.(id,status,memberId)")
    @JSON("items.examRecord.(currentTime)")
    @JSON("items.paperClass.(id)")
    public PagedResult<Exam> findExamList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findAuthExamList(
                examRegion,
                requestContext.get("organizationId",String.class),
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("proId", String.class),
                requestContext.getOptional("subProId", String.class),
                requestContext.getOptional("levelId", String.class),
                requestContext.getOptional("equipmentId", String.class),
                requestContext.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId()
        );
    }

    /**
     * 需要有准考证的考试列表
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/my-ticket-list" , method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "startTimeOrderBy", type = Integer.class)
    @Param(name = "year", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,examBatch,name,startTime,endTime,passScore,status)")
    @JSON("items.paperClass.(totalScore)")
    public PagedResult<Exam> findTicketExamList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findTicketExamList(
                examRegion,
                requestContext.get("organizationId",String.class),
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId(),
                requestContext.getOptional("year", String.class)

        );
    }

    /**
     * 我的准考证信息
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/ticket-detail" , method = RequestMethod.GET)
    @Param(name = "examId", type = String.class, required = true)
    @JSON("id,examBatch,name,startTime,endTime,duration,examNotes")
    @JSON("signUpAuth.(signUpId,memberId,professionId,subProfessionId,equipmentTypeId,workDepart,workTime,isGroupExpert,isProvinExpert,otherExamAppraisal,awardSituation,crossCondition,applyLevel,applyProfession,applySubProfession,applySupplier)")
    @JSON("member.(id,name,fullName,majorPositionId,status,organizationId,majorPositionName,headPortrait,headPortraitPath,phoneNumber,email,identityNumber)")
    @JSON("profession.(id,code,name,parentId,organizationId)")
    @JSON("subProfession.(id,code,name,parentId,organizationId)")
    @JSON("equipmentType.(id,code,name)")
    @JSON("level.(id,subProfessionId,levelCode,levelName,level,validDate)")
//    @JSON("invigilatorList.(id,name,fullName)")
    @JSON("organization.(id,name,shortName,code,companyId)")
    @JSON("company.(id,name,shortName,code,companyId)")
    @JSON("examRegist.(ticket)")
    public Exam findTicketDetail(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findTicketDetail(
                examRegion,
                requestContext.get("examId",String.class),
                subject.getCurrentUserId()
                );
    }

    /**
     * 我的认证考试列表
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/my-exam-list" , method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "startTimeOrderBy", type = Integer.class)
    @Param(name = "year", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,examBatch,name,startTime,endTime,passScore)")
    @JSON("items.paperClass.(id,totalScore)")
    @JSON("items.examRecord.(status)")
    @JSON("items.signUp.(status)")
    public PagedResult<Exam> findMyExamList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findMyExamList(
                examRegion,
                requestContext.get("organizationId",String.class),
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId(),
                requestContext.getOptional("year", String.class)
        );
    }

    /**
     * 报名审核列表
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/audit-list" , method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "startTimeOrderBy", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,examBatch,name,status,startTime,endTime,auditStartTime,auditEndTime,preApproval)")
    @JSON("items.signUp.(id,status)")
    public PagedResult<Exam> findAuditList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findAuditList(
                examRegion,
                requestContext.get("organizationId",String.class),
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId()
                );
    }

    /**
     * 我的成绩列表
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/my-score" , method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "startTimeOrderBy", type = Integer.class)
    @Param(name = "year", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,examBatch,name,startTime,endTime,duration,passScore,allowExamTimes,showAnswerRule,showScoreTime)")
    @JSON("items.examRecord.(id,status,startTime,endTime,lastSubmitTime,score)")
    @JSON("items.examRegist.(passStatus)")
    @JSON("items.paperClass.(id,totalScore)")
    public PagedResult<Exam> findMyScoreList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return examService.findMyScoreList(
                examRegion,
                requestContext.get("organizationId",String.class),
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId(),
                requestContext.getOptional("year", String.class)
                );
    }

}
