package com.zxy.product.examstu.web.util;

import com.zxy.product.exam.entity.GridFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件处理工具类
 *
 */
public class FileUtil {
    private static final String ZIP_EXT = ".zip";

    private FileUtil() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 创建单个文件
     *
     * @param destFileName 目标文件名
     * @return 创建成功返回true，已存在或失败返回false
     */
    public static boolean createFile(String destFileName) {
        File file = new File(destFileName);
        if (file.exists()) {
            LOGGER.error("文件已存在！");
            return false;
        }
        if (destFileName.endsWith(File.separator)) {
            LOGGER.error("创建单个文件{}失败，目标文件不能为目录！", destFileName);
            return false;
        }
        if (!file.getParentFile().exists() && !file.getParentFile().mkdirs()) {
            LOGGER.error("创建目标文件所在的目录失败！");
            return false;
        }
        try {
            return file.createNewFile();
        } catch (IOException e) {
            LOGGER.error("创建文件失败！", e);
            return false;
        }
    }

    /**
     * 压缩指定文件目录
     *
     * @param srcFilePath 待压缩的文件路径
     */
    public static void zipDir(String srcFilePath) {
        File srcFile = new File(srcFilePath);
        if (srcFile.exists() && srcFile.isDirectory()) {
            String zipName = srcFile.getName() + ZIP_EXT;
            File targetFile = new File(srcFile.getParent(), zipName);
            cleanUp(targetFile);
            try (FileOutputStream fos = new FileOutputStream(targetFile);
                 ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(fos))) {
                File[] subFiles = srcFile.listFiles();
                if (subFiles != null) {
                    for (File subFile : subFiles) {
                        addEntry(subFile, zos);
                    }
                }
            } catch (IOException e) {
                LOGGER.error("压缩包添加文件失败！", e);
            }
        }
    }

    public static void zipDirWithName(String srcFilePath, String name) {
        File srcFile = new File(srcFilePath);
        if (srcFile.exists() && srcFile.isDirectory()) {
            String zipName = name + GridFile.ZIP_EXT;
            File targetFile = new File(srcFile.getParent(), zipName);
            cleanUp(targetFile);
            try (FileOutputStream fos = new FileOutputStream(targetFile);
                 ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(fos))) {
                File[] subFiles = srcFile.listFiles();
                if (subFiles != null) {
                    for (File subFile : subFiles) {
                        addEntry(subFile, zos);
                    }
                }
            } catch (IOException e) {
                LOGGER.error("压缩包添加文件失败！", e);
            }
        }
    }

    public static void cleanUp(File targetFile) {
        if (!targetFile.exists()) {
            return;
        }
        if (targetFile.isDirectory()) {
            String[] childs = targetFile.list();
            for (String file : childs == null ? new String[0] : childs) {
                deleteFile(new File(targetFile, file));
            }
        }
        deleteFile(targetFile);
    }

    private static void deleteFile(File file) {
        if (!file.exists()) {
            return;
        }
        try {
            Path dirPath = Paths.get(file.toURI());
            Path currentPath = Paths.get(dirPath.toString());
            Files.delete(currentPath);
        } catch (IOException e) {
            LOGGER.error("删除文件失败！", e);
        }
    }

    /**
     * 添加文件
     *
     * @param source 要添加进zip的文件
     * @param zos    zip文件输出流
     */
    private static void addEntry(File source, ZipOutputStream zos)
            throws IOException {
        byte[] buffer = new byte[GridFile.BUFFER_SIZE];
        try (FileInputStream fis = new FileInputStream(source);
             BufferedInputStream bis = new BufferedInputStream(fis, buffer.length)) {
            int read;
            zos.putNextEntry(new ZipEntry(source.getName()));
            while ((read = bis.read(buffer, 0, buffer.length)) != -1) {
                zos.write(buffer, 0, read);
            }
            zos.closeEntry();
        }
    }

    public static void convertByte(byte[] bytes, String fileName) {
        if (bytes == null || bytes.length == 0) {
            return;
        }
        try (OutputStream os = new FileOutputStream(fileName)) {
            os.write(bytes, 0, bytes.length);
        } catch (Exception e) {
            LOGGER.error("生成文件失败！", e);
        }
    }





}
