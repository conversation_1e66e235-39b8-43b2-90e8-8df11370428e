spring.application.name=exam-stu-web-server

dubbo.application.name=exam-stu-web-server
dubbo.application.version=1
dubbo.registry.address=zookeeper://localhost:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


# rabbitmq
spring.rabbitmq.host=**************
spring.rabbitmq.port=30007
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.exam.web.excange=amq.topic
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

graphite.server=**************
graphite.port=30004

# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = mw9.zhixueyun.com:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456
spring.fastdfs.max-total = 5
spring.fastdfs.max-idle = 5

# redis
spring.redis.cluster = false
spring.redis.cluster.nodes = *************:30006
#spring.redis.cluster.nodes= ***************:30006
spring.redis.timeout=10000

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

server.context-path=/api/v1/exam-stu

exam.message.queue.exam.submit = zxy-exam-record-member-submit
#exam.message.queue.exam.time.expand = zxy-exam-time-expand
#exam.message.queue.paperinstance.cache = zxy-exam-paperinstance-cache
server.port=8082

sync.paper.password =qpgd6f3LX0hXaikP
sync.correction.url =http://net.chinamobile.com/exam/web/paper/syncPaperErrorInfo.action
#\u751F\u6210\u62A5\u9519\u63A5\u53E3token\u7684\u5E38\u91CF
sync.correction.token.constant=connectexampaper

#isActive swagger
swagger.show=false
