package com.zxy.product.examstu.async.config;


import com.zxy.product.examstu.content.DataSourceEnum;
import org.jooq.impl.DataSourceConnectionProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DynamicDataSourceConfig {

    private static final Logger logger= LoggerFactory.getLogger(DynamicDataSourceConfig.class);

    @Bean(name = "examDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.exam")
    @Primary
    public DataSource examDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "northDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.north")
    public DataSource northDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "southDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.south")
    public DataSource southDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public DataSourceConnectionProvider dataSourceConnectionProvider(DynamicDataSource dynamicDataSource){
        return new DataSourceConnectionProvider(dynamicDataSource);
    }

    @Bean
    public DynamicDataSource myDataSource(@Qualifier("examDataSource") DataSource examDataSource,
                                          @Qualifier("northDataSource") DataSource northDataSource,
                                          @Qualifier("southDataSource") DataSource southDataSource) {
        logger.error("考试数据源{}", examDataSource);
        logger.error("考试北库数据源{}",northDataSource);
        logger.error("考试南库数据源{}",southDataSource);
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceEnum.NORTH.getName(), northDataSource);
        targetDataSources.put(DataSourceEnum.SOUTH.getName(), southDataSource);
        return new DynamicDataSource(examDataSource, targetDataSources);
    }
}
