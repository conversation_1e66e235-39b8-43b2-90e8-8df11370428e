package com.zxy.product.examstu.async;


import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.examstu.async.config.*;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Schema;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * @user tianjun
 * @date 16/6/22
 */
@SpringBootApplication(exclude = {MongoDataAutoConfiguration.class, MongoAutoConfiguration.class})
@Import({
        MessageConfig.class, DynamicDataSourceConfig.class, JooqAutoConfiguration.class,
        RPCClientConfig.class, CommonDaoConfig.class, MongoConfig.class, CacheConfig.class})
public class ExamStuAsyncServiceMain {

    @Bean
    public Schema schema() {
        return Exam.EXAM_SCHEMA; // jOOQ生成代码的根目录下与数据库同名的类
    }

	public static void main(String[] args) throws Exception {
		SpringApplication.run(ExamStuAsyncServiceMain.class, args);
	}
}
