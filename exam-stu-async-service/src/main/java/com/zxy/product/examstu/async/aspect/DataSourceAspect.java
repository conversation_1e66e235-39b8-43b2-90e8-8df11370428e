package com.zxy.product.examstu.async.aspect;

import com.zxy.product.examstu.content.DataSourceEnum;
import com.zxy.product.examstu.async.config.DynamicDataSource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Aspect
@Component
public class DataSourceAspect implements Ordered {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    // 用于缓存方法对应的事务注解信息，减少反射获取的开销，优化缓存获取逻辑以应对高并发
    private final ConcurrentHashMap<Method, TransactionalAnnotationInfo> transactionAnnotationCache = new ConcurrentHashMap<>();

    // 用于控制数据源切换的并发访问，确保同一时刻只有一个线程能进行数据源切换操作
    private final ReentrantLock dataSourceSwitchLock = new ReentrantLock();

    // 用于记录事务调用层次的ThreadLocal变量
    private static final ThreadLocal<Integer> transactionCallLevel = new ThreadLocal<>();

    @Autowired
    private ApplicationContext applicationContext;

    // 切点定义，拦截带有特定DataSource注解（类级别或方法级别）的方法
    @Pointcut("@within(com.zxy.product.examstu.annotation.DataSource) || @annotation(com.zxy.product.examstu.annotation.DataSource)")
    public void dataSourcePointCut() {}

    @Around("dataSourcePointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 进入方法时，事务调用层次递增
        transactionCallLevel.set(transactionCallLevel.get() == null? 1 : transactionCallLevel.get() + 1);

        aroundProcess(point);
        try {
            Object result = point.proceed();
            logger.info("切面加日志：方法 [{}] 执行完成，入参：{}，出参：{}，耗时：{} 毫秒",
                    point.getSignature().toLongString(), Arrays.toString(point.getArgs()), result,
                    System.currentTimeMillis() - startTime);
            return result;
        } catch (Throwable e) {
            logger.error("方法执行出现异常，异常信息：{}", e.getMessage(), e);
            String dataSourceWhenException = DynamicDataSource.getDataSource();
            logger.error("出现异常时的数据源状态：{}", dataSourceWhenException);
            // 这里可以根据业务需求添加一些补救措施，比如尝试回滚数据源切换等
            throw e;
        } finally {
            int currentLevel = transactionCallLevel.get();
            logger.info("切面加日志：finally进入时的当前层数，{}", currentLevel);
            if (currentLevel > 0) {
                // 方法执行结束，事务调用层次递减
                transactionCallLevel.set(currentLevel - 1);
            }
            if (isLastMethodInChain(point) && currentLevel == 0) {
                DynamicDataSource.clearDataSource();
                logger.info("切面加日志：清理数据源");
                String dataSource5 = DynamicDataSource.getDataSource();
                logger.info("切面加日志：清理数据源之后的数据源，{}", dataSource5);
            }
        }
    }

    // 具体数据源动态切换执行，添加锁控制并发冲突，优化了参数获取方式，添加错误处理增强
    private void aroundProcess(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = point.getArgs();

        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            try {
                params.put(parameterNames[i], args[i]);
            } catch (ArrayIndexOutOfBoundsException e) {
                logger.error("参数获取出现异常，参数名: {}, 可能参数个数不匹配，请检查方法签名和参数传递情况", parameterNames[i], e);
                continue;
            }
        }

        Object region = params.get("examRegion");
        Object examId = params.get("examId");

        String dataSource3 = DynamicDataSource.getDataSource();
        logger.info("切面加日志：进入切面时的默认数据源，dataSource : {}  ", dataSource3);

        if (Objects.isNull(region)) {
            logger.info("切面加日志：region是空时： examId:{}, examRegion : {}, dataSource : {}  ", examId, region, DynamicDataSource.getDataSource());
        }

        if (!Objects.isNull(region)) {
            String dataSource4 = DynamicDataSource.getDataSource();
            logger.info("切面加日志：region是非空时, examId:{}, examRegion : {}, 当前数据源 : {}  ", examId, region, dataSource4);
            String type = DataSourceEnum.dataSourceMap.get((Integer) region);
            dataSourceSwitchLock.lock();
            try {
                DynamicDataSource.setDataSource(type);
                String dataSource2 = DynamicDataSource.getDataSource();
                logger.info("切面加日志：region是非空时, type:{}, 切换后的数据源是:{}", type, dataSource2);
            } catch (Exception e) {
                logger.error("数据源切换出现异常：", e);
            } finally {
                dataSourceSwitchLock.unlock();
            }
        }
    }

    // 判断当前被拦截的方法是否是调用链中的最后一个方法，综合考虑更多事务特性和配置情况（适配4.3.29版本，去掉超时时间判断）
    private boolean isLastMethodInChain(ProceedingJoinPoint point) {
        TransactionalAnnotationInfo annotationInfo = getTransactionAnnotationInfo(point);
        if (annotationInfo == null) {
            // 如果方法没有配置事务，直接认为可以清理数据源（这里可根据业务逻辑调整判断逻辑）
            return true;
        }

        TransactionStatus status = TransactionAspectSupport.currentTransactionStatus();
        if (status == null) {
            // 当前线程不存在活跃事务
            return true;
        }

        int currentLevel = transactionCallLevel.get();
        logger.info("切面加日志：isLastMethodInChain的当前层数，{}", currentLevel);

        Propagation propagation = annotationInfo.getPropagation();
        switch (propagation) {
            case REQUIRED:
                return isLastMethodInChainForRequired(annotationInfo, status) && currentLevel == 1;
            case REQUIRES_NEW:
                return isLastMethodInChainForRequiresNew(annotationInfo, status) && currentLevel == 1;
            case NESTED:
                return isLastMethodInChainForNested(annotationInfo, status) && currentLevel == 1;
            default:
                return false;
        }
    }

    // 获取方法上的@Transactional注解中的传播行为、只读属性以及隔离级别等关键事务属性信息，优化缓存获取逻辑
    private TransactionalAnnotationInfo getTransactionAnnotationInfo(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        TransactionalAnnotationInfo cachedInfo = transactionAnnotationCache.get(method);
        if (cachedInfo == null) {
            synchronized (transactionAnnotationCache) {
                cachedInfo = transactionAnnotationCache.get(method);
                if (cachedInfo == null) {
                    Transactional transactional = method.getAnnotation(Transactional.class);
                    if (transactional!= null) {
                        cachedInfo = new TransactionalAnnotationInfo(
                                transactional.propagation(),
                                transactional.readOnly(),
                                transactional.isolation()
                        );
                    }
                    if (cachedInfo!= null) {
                        transactionAnnotationCache.put(method, cachedInfo);
                    }
                }
            }
        }
        return cachedInfo;
    }

    // 针对REQUIRED事务传播行为判断是否是最后一个方法的逻辑抽取，适配4.3.29版本，通过异常判断事务是否回滚
    private boolean isLastMethodInChainForRequired(TransactionalAnnotationInfo annotationInfo, TransactionStatus status) {
        boolean readOnly = annotationInfo.isReadOnly();

        // 通过检查事务状态中的rollbackOnly属性来判断是否应该回滚
        if (status.isRollbackOnly()) {
            return true;
        }

        return readOnly || status.isCompleted();
    }

    // 针对REQUIRES_NEW事务传播行为判断是否是最后一个方法的逻辑抽取，适配4.3.29版本，通过异常判断事务是否回滚
    private boolean isLastMethodInChainForRequiresNew(TransactionalAnnotationInfo annotationInfo, TransactionStatus status) {
        // 通过检查事务状态中的rollbackOnly属性来判断是否应该回滚
        if (status.isRollbackOnly()) {
            return true;
        }

        return status.isCompleted();
    }

    // 针对NESTED事务传播行为判断是否是最后一个方法的逻辑抽取，适配4.3.29版本，通过异常判断事务是否回滚
    private boolean isLastMethodInChainForNested(TransactionalAnnotationInfo annotationInfo, TransactionStatus status) {
        boolean readOnly = annotationInfo.isReadOnly();

        // 通过检查事务状态中的rollbackOnly属性来判断是否应该回滚
        if (status.isRollbackOnly()) {
            return true;
        }

        if (readOnly) {
            return status.isCompleted() &&!TransactionAspectSupport.currentTransactionStatus().hasSavepoint();
        }
        return false;
    }

    // 内部类，用于封装从@Transactional注解中获取到的传播行为、只读属性以及隔离级别等关键事务属性信息
    class TransactionalAnnotationInfo {
        private Propagation propagation;
        private boolean readOnly;
        private Isolation isolation;

        public TransactionalAnnotationInfo(Propagation propagation, boolean readOnly, Isolation isolation) {
            this.propagation = propagation;
            this.readOnly = readOnly;
            this.isolation = isolation;
        }

        public Propagation getPropagation() {
            return propagation;
        }

        public boolean isReadOnly() {
            return readOnly;
        }

        public Isolation getIsolation() {
            return isolation;
        }
    }

    @Override
    public int getOrder() {
        return 1;
    }

}