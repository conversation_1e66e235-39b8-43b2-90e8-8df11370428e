package com.zxy.product.examstu.async.task;

import com.zxy.common.cache.redis.Redis;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordProcessService;
import com.zxy.product.examstu.content.DataSourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.UUID;

@Component
@EnableAsync
public class AnswerRecordProcessTask {

    private static final Logger logger= LoggerFactory.getLogger(AnswerRecordProcessTask.class);
    private static final String REDIS_LOCK = "exam-stu-async-task#clean-last-week-data#redis-lock";


    private Redis redis;

    private AnswerRecordProcessService answerRecordProcessService;

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setAnswerRecordProcessService(AnswerRecordProcessService answerRecordProcessService) {
        this.answerRecordProcessService = answerRecordProcessService;
    }

    @Async
    @Scheduled(cron = " 0 0 1 * * ?")
    @DataSource
    public void doCleanLastWeekData() {
        final String lockKey = REDIS_LOCK;
        final String lockValue = UUID.randomUUID().toString();
        final int lockExpireSeconds = 60;
        try {
            String lockResult = redis.process(x -> x.set(lockKey, lockValue, "NX", "EX", lockExpireSeconds));

            if (!StringUtils.isEmpty(lockResult) && "OK".equalsIgnoreCase(lockResult)) {
                long startTime = System.currentTimeMillis();

                logger.info("清除考试流水表数据，开始时间{}", startTime);
                try {
                    answerRecordProcessService.cleanLastWeekData(DataSourceEnum.NORTH.getType());

                    long middleTime = System.currentTimeMillis();

                    logger.info("清除考试流水表数据 north库数据清除结束，持续时间{}", middleTime - startTime);

                    answerRecordProcessService.cleanLastWeekData(DataSourceEnum.SOUTH.getType());

                    long endTime = System.currentTimeMillis();

                    logger.info("清除考试流水表数据 south库数据清除结束，持续时间{}", endTime - middleTime);
                    logger.info("结束时间{}，总耗时{}", endTime, (endTime - startTime));
                }finally {
                    releaseLockSafely(lockKey, lockValue);
                }
            }
        } catch (Exception e) {
            logger.error("清除考试流水表数据处理失败：{}", e.getMessage());
        }finally {
            releaseLockSafely(lockKey, lockValue);
        }
    }

    private void releaseLockSafely(String lockKey, String lockValue) {
        try {
            String currentValue = redis.process(x -> x.get(lockKey));
            if (lockValue.equals(currentValue)) {
                redis.process(x -> x.del(lockKey));
                logger.info("清除考试流水表数据 成功释放redis锁：{}", lockKey);
            }
        } catch (Exception e) {
            logger.error("清除考试流水表数据 释放redis锁异常：", e);
        }
    }

}
