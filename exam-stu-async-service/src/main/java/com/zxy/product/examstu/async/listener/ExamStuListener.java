package com.zxy.product.examstu.async.listener;


import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.examstu.api.SignUpService;
import com.zxy.product.examstu.api.ToDoService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.examstu.api.ExamRecordService;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 *
 */
@Component
public class ExamStuListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamStuListener.class);

    private TransactionTemplate transactionTemplate;

	private ExamRecordService examRecordService;

	private SignUpService signUpService;

	private ToDoService toDoService;

	@Autowired
	public void setToDoService(ToDoService toDoService) {
		this.toDoService = toDoService;
	}

	@Autowired
	public void setSignUpService(SignUpService signUpService) {
		this.signUpService = signUpService;
	}

	@Autowired
	public void setExamRecordService(ExamRecordService examRecordService) {
		this.examRecordService = examRecordService;
	}


    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}
    @Override
    protected void onMessage(Message message) {

		LOGGER.info("ExamStuListener/:{}", message);

    	Integer type = message.getType();

    	 transactionTemplate.execute(new TransactionCallbackWithoutResult() {
 			@Override
 			protected void doInTransactionWithoutResult(TransactionStatus status) {
 				switch (type) {
 				case MessageTypeContent.EXAM_STU_UPDATE_RECORD_ORDER:
					// 更新未开始的考试记录的试题顺序
					updateOrderContentOfNoStartExamRecords(message);
 					break;
				case MessageTypeContent.EXAM_STU_CHANGE_APPLICANT_EXAM:
					// 报名转非报名修改报名记录数据
					changeApplicantExam(message);
					break;
				case MessageTypeContent.EXAM_STU_DELETE_RECORD:
					// 报名转非报名修改报名记录数据
					deleteExamRecordBySenderToCenterExam(message);
					break;
				case MessageTypeContent.EXAM_STU_DELETE_TO_DO:
					// 评卷老师修改，删除不存在的旧评卷老师todo数据
					deleteToDoByUpdateMarkConfig(message);
					break;
					//更新超时异常记录
				case MessageTypeContent.EXAM_RECORD_BE_EXCEPTION:
					doUpdateExamRecordException();
					break;
					//考试结束后，检查考试记录有无问题
				case MessageTypeContent.EXAM_STU_CHECK_EXAM_RECORD:
					checkExamRecord(message);
					break;
 				default:
 					break;
 				}
 			}
    	 });
    }

	private void checkExamRecord(Message message) {
		String examId = message.getHeader(MessageHeaderContent.ID);
		examRecordService.checkExamRecord(Exam.NORTH, examId);
		examRecordService.checkExamRecord(Exam.SOURCE, examId);
	}


	private void doUpdateExamRecordException() {
		examRecordService.doUpdateException(Exam.NORTH);
		examRecordService.doUpdateException(Exam.SOURCE);
	}


	/**
	 * 更新未开始的考试记录的试题顺序
	 */
	private void updateOrderContentOfNoStartExamRecords(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String paperSortRule = message.getHeader(MessageHeaderContent.PAPER_SORT_RULE);
		examRecordService.updateOrderContentOfNoStartExamRecords(Exam.NORTH, examId, Integer.valueOf(paperSortRule));
		examRecordService.updateOrderContentOfNoStartExamRecords(Exam.SOURCE, examId, Integer.valueOf(paperSortRule));
	}

	/**
	 * 报名转非报名修改报名记录数据*
	 */
	private void changeApplicantExam(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		signUpService.changeApplicantExam(Exam.NORTH,examId);
		signUpService.changeApplicantExam(Exam.SOURCE,examId);

	}

	/**
	 * * 推送转非推送，删除待考试的考试记录
	 * @param message
	 */
	private void deleteExamRecordBySenderToCenterExam(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		examRecordService.deleteExamRecordBySenderToCenterExam(Exam.NORTH,examId);
		examRecordService.deleteExamRecordBySenderToCenterExam(Exam.SOURCE,examId);
	}

	/**
	 * 评卷老师修改，删除不存在的旧评卷老师todo数据
	 */
	private void deleteToDoByUpdateMarkConfig(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String oldMemberIds = message.getHeader(MessageHeaderContent.PARAMS);
		if (!StringUtils.isEmpty(oldMemberIds)) {
			List<String> memberIds = Arrays.asList(oldMemberIds.split(","));
			if (!CollectionUtils.isEmpty(memberIds)) {
				toDoService.deleteToDoByUpdateMarkConfig(Exam.NORTH, examId, memberIds);
				toDoService.deleteToDoByUpdateMarkConfig(Exam.SOURCE, examId, memberIds);
			}
		}
	}


	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_STU_UPDATE_RECORD_ORDER,
			MessageTypeContent.EXAM_STU_CHANGE_APPLICANT_EXAM,
			MessageTypeContent.EXAM_STU_DELETE_RECORD,
			MessageTypeContent.EXAM_STU_DELETE_TO_DO,
			MessageTypeContent.EXAM_STU_CHECK_EXAM_RECORD,
				MessageTypeContent.EXAM_RECORD_BE_EXCEPTION
		};
	}



}
