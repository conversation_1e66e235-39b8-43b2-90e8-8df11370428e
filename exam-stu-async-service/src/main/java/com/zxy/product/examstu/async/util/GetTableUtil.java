package com.zxy.product.examstu.async.util;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.entity.*;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR>
 */
@Component
public class GetTableUtil {

    private Cache examRecordFaceTimeCache;
    private CommonDao<Exam> examDao;

    private Cache examCreateTimeCache;

    private Cache examRecordTimeCache;

    private Cache examRegistTimeCache;

    private Cache paperInstanceQuestionCopyTimeCache;

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.examRecordFaceTimeCache = cacheService.create("fenbiao", "exam-record-face-time");
        this.examCreateTimeCache = cacheService.create("fenbiao", "exam-create-time");
        this.examRecordTimeCache = cacheService.create("fenbiao", "exam-record-time");
        this.examRegistTimeCache = cacheService.create("fenbiao", "exam-regist-time");
        this.paperInstanceQuestionCopyTimeCache = cacheService.create("fenbiao", "paper-instance-question-copy-Time");
    }

    /**
     * 答案记录表
     */
    public TableImpl<?> getAnswerRecordTable(String answerRecordStringTable) {
        if (AnswerRecord.STRING_ANSWER_RECORD_2019.equals(answerRecordStringTable))
            return ANSWER_RECORD_2019;
        if (AnswerRecord.STRING_ANSWER_RECORD_2020.equals(answerRecordStringTable))
            return ANSWER_RECORD_2020;
        if (AnswerRecord.STRING_ANSWER_RECORD_2021.equals(answerRecordStringTable))
            return ANSWER_RECORD_2021;
        if (AnswerRecord.STRING_ANSWER_RECORD_2022.equals(answerRecordStringTable))
            return ANSWER_RECORD_2022;
        if (AnswerRecord.STRING_ANSWER_RECORD_2023.equals(answerRecordStringTable))
            return ANSWER_RECORD_2023;
        if (AnswerRecord.STRING_ANSWER_RECORD_2024.equals(answerRecordStringTable))
            return ANSWER_RECORD_2024;
        if (AnswerRecord.STRING_ANSWER_RECORD_2025.equals(answerRecordStringTable))
            return ANSWER_RECORD_2025;
        if (AnswerRecord.STRING_ANSWER_RECORD_2026.equals(answerRecordStringTable))
            return ANSWER_RECORD_2026;
        if (AnswerRecord.STRING_ANSWER_RECORD_2027.equals(answerRecordStringTable))
            return ANSWER_RECORD_2027;
        if (AnswerRecord.STRING_ANSWER_RECORD_2028.equals(answerRecordStringTable))
            return ANSWER_RECORD_2028;
        if (AnswerRecord.STRING_ANSWER_RECORD_2029.equals(answerRecordStringTable))
            return ANSWER_RECORD_2029;
        if (AnswerRecord.STRING_ANSWER_RECORD_2030.equals(answerRecordStringTable))
            return ANSWER_RECORD_2030;
        return ANSWER_RECORD;
    }

    /**
     * 考试记录表
     */
    public TableImpl<?> getExamRecordTable(String examRecordStringTable) {
        if (ExamRecord.STRING_EXAM_RECORD.equals(examRecordStringTable))
            return EXAM_RECORD;
        if (ExamRecord.STRING_EXAM_RECORD_2017.equals(examRecordStringTable))
            return EXAM_RECORD_2017;
        if (ExamRecord.STRING_EXAM_RECORD_2018.equals(examRecordStringTable))
            return EXAM_RECORD_2018;
        if (ExamRecord.STRING_EXAM_RECORD_2019.equals(examRecordStringTable))
            return EXAM_RECORD_2019;
        if (ExamRecord.STRING_EXAM_RECORD_2020.equals(examRecordStringTable))
            return EXAM_RECORD_2020;
        if (ExamRecord.STRING_EXAM_RECORD_2021.equals(examRecordStringTable))
            return EXAM_RECORD_2021;
        if (ExamRecord.STRING_EXAM_RECORD_2022.equals(examRecordStringTable))
            return EXAM_RECORD_2022;
        if (ExamRecord.STRING_EXAM_RECORD_2023.equals(examRecordStringTable))
            return EXAM_RECORD_2023;
        if (ExamRecord.STRING_EXAM_RECORD_2024.equals(examRecordStringTable))
            return EXAM_RECORD_2024;
        if (ExamRecord.STRING_EXAM_RECORD_2025.equals(examRecordStringTable))
            return EXAM_RECORD_2025;
        if (ExamRecord.STRING_EXAM_RECORD_2026.equals(examRecordStringTable))
            return EXAM_RECORD_2026;
        if (ExamRecord.STRING_EXAM_RECORD_2027.equals(examRecordStringTable))
            return EXAM_RECORD_2027;
        if (ExamRecord.STRING_EXAM_RECORD_2028.equals(examRecordStringTable))
            return EXAM_RECORD_2028;
        if (ExamRecord.STRING_EXAM_RECORD_2029.equals(examRecordStringTable))
            return EXAM_RECORD_2029;
        if (ExamRecord.STRING_EXAM_RECORD_2030.equals(examRecordStringTable))
            return EXAM_RECORD_2030;
        return EXAM_RECORD;
    }

    /**
     * 考试注册表
     */
    public TableImpl<?> getExamRegistTable(String examRegistStringTable) {
        if (ExamRegist.STRING_EXAM_REGIST.equals(examRegistStringTable))
            return EXAM_REGIST;
        if (ExamRegist.STRING_EXAM_REGIST_2017.equals(examRegistStringTable))
            return EXAM_REGIST_2017;
        if (ExamRegist.STRING_EXAM_REGIST_2018.equals(examRegistStringTable))
            return EXAM_REGIST_2018;
        if (ExamRegist.STRING_EXAM_REGIST_2019.equals(examRegistStringTable))
            return EXAM_REGIST_2019;
        if (ExamRegist.STRING_EXAM_REGIST_2020.equals(examRegistStringTable))
            return EXAM_REGIST_2020;
        if (ExamRegist.STRING_EXAM_REGIST_2021.equals(examRegistStringTable))
            return EXAM_REGIST_2021;
        if (ExamRegist.STRING_EXAM_REGIST_2022.equals(examRegistStringTable))
            return EXAM_REGIST_2022;
        if (ExamRegist.STRING_EXAM_REGIST_2023.equals(examRegistStringTable))
            return EXAM_REGIST_2023;
        if (ExamRegist.STRING_EXAM_REGIST_2024.equals(examRegistStringTable))
            return EXAM_REGIST_2024;
        if (ExamRegist.STRING_EXAM_REGIST_2025.equals(examRegistStringTable))
            return EXAM_REGIST_2025;
        if (ExamRegist.STRING_EXAM_REGIST_2026.equals(examRegistStringTable))
            return EXAM_REGIST_2026;
        if (ExamRegist.STRING_EXAM_REGIST_2027.equals(examRegistStringTable))
            return EXAM_REGIST_2027;
        if (ExamRegist.STRING_EXAM_REGIST_2028.equals(examRegistStringTable))
            return EXAM_REGIST_2028;
        if (ExamRegist.STRING_EXAM_REGIST_2029.equals(examRegistStringTable))
            return EXAM_REGIST_2029;
        if (ExamRegist.STRING_EXAM_REGIST_2030.equals(examRegistStringTable))
            return EXAM_REGIST_2030;
        return EXAM_REGIST;
    }


    /**
     * 试卷实例与试题副本关联表
     */
    public TableImpl<?> getPaperInstanceQuestionCopyTable(String paperInstanceQuestionCopyStringTable) {
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2017.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2017;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2018.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2018;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2019.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2019;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2020.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2020;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2021.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2021;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2022.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2022;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2023.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2023;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2024.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2024;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2025.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2025;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2026.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2026;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2027.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2027;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2028.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2028;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2029.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2029;
        if (PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2030.equals(paperInstanceQuestionCopyStringTable))
            return PAPER_INSTANCE_QUESTION_COPY_2030;
        return PAPER_INSTANCE_QUESTION_COPY;
    }


    public TableImpl<?> getExamRecordFaceTable(String examRecordFaceStringTable) {
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2017.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2017;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2018.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2018;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2019.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2019;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2020.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2020;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2021.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2021;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2022.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2022;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2023.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2023;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2024.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2024;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2025.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2025;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2026.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2026;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2027.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2027;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2028.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2028;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2029.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2029;
        if (ExamRecordFace.STRING_EXAM_RECORD_FACE_2030.equals(examRecordFaceStringTable))
            return EXAM_RECORD_FACE_2030;
        return EXAM_RECORD_FACE;
    }


    /**
     * 根据examId查询考试创建时间，选择需要操作的考试人脸记录表
     */
    public String getExamRecordFaceStringTable(String examId) {
        if (examId != null) {
            return examRecordFaceTimeCache.get(examId, () -> {
                List<Exam> examList = getExamList(examId);
                if (examList != null && !examList.isEmpty()) {
                    if (Exam.EXAM_ACTIVITY_SOURCE_TYPE.equals(examList.get(0).getSourceType())) {
                        // 考试模块的考试按照考试的开始时间分表
                        Long startTime = examList.get(0).getStartTime();
                        // 2017年以前的数据放到examRecordFace
                        if (startTime == null || startTime < Exam.TIME_2017)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE;
                        // 2017-2018 的数据放到examRecordFace_2017
                        if (startTime < Exam.TIME_2018)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2017;
                        // 2018-2019 的数据放到examRecordFace_2018
                        if (startTime < Exam.TIME_2019)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2018;
                        // 2019-2020 的数据放到examRecordFace_2019
                        if (startTime < Exam.TIME_2020)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2019;
                        // 2020-2021 的数据放到examRecordFace_2020
                        if (startTime < Exam.TIME_2021)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2020;
                        // 2021-2022 的数据放到examRecordFace_2021
                        if (startTime < Exam.TIME_2022)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2021;
                        // 2022-2023 的数据放到examRecordFace_2022
                        if (startTime < Exam.TIME_2023)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2022;
                        // 2023-2024 的数据放到examRecordFace_2023
                        if (startTime < Exam.TIME_2024)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2023;
                        // 2024-2025 的数据放到examRecordFace_2024
                        if (startTime < Exam.TIME_2025)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2024;
                        // 2025-2026 的数据放到examRecordFace_2025
                        if (startTime < Exam.TIME_2026)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2025;
                        // 2026-2027 的数据放到examRecordFace_2026
                        if (startTime < Exam.TIME_2027)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2026;
                        // 2027-2028 的数据放到examRecordFace_2027
                        if (startTime < Exam.TIME_2028)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2027;
                        // 2028-2029 的数据放到examRecordFace_2028
                        if (startTime < Exam.TIME_2029)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2028;
                        // 2029-2030 的数据放到examRecordFace_2029
                        if (startTime < Exam.TIME_2030)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2029;
                        // 2030-2031 的数据放到examRecordFace_2030
                        if (startTime < Exam.TIME_2031)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2030;
                        return ExamRecordFace.STRING_EXAM_RECORD_FACE;
                    } else {
                        // 其他考试模块的考试按照考试的创建时间分表
                        Long createTime = examList.get(0).getCreateTime();
                        // 2017年以前的数据放到examRecordFace
                        if (createTime == null || createTime < Exam.TIME_2017)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE;
                        // 2017-2018 的数据放到examRecordFace_2017
                        if (createTime < Exam.TIME_2018)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2017;
                        // 2018-2019 的数据放到examRecordFace_2018
                        if (createTime < Exam.TIME_2019)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2018;
                        // 2019-2020 的数据放到examRecordFace_2019
                        if (createTime < Exam.TIME_2020)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2019;
                        // 2020-2021 的数据放到examRecordFace_2020
                        if (createTime < Exam.TIME_2021)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2020;
                        // 2021-2022 的数据放到examRecordFace_2021
                        if (createTime < Exam.TIME_2022)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2021;
                        // 2022-2023 的数据放到examRecordFace_2022
                        if (createTime < Exam.TIME_2023)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2022;
                        // 2023-2024 的数据放到examRecordFace_2023
                        if (createTime < Exam.TIME_2024)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2023;
                        // 2024-2025 的数据放到examRecordFace_2024
                        if (createTime < Exam.TIME_2025)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2024;
                        // 2025-2026 的数据放到examRecordFace_2025
                        if (createTime < Exam.TIME_2026)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2025;
                        // 2026-2027 的数据放到examRecordFace_2026
                        if (createTime < Exam.TIME_2027)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2026;
                        // 2027-2028 的数据放到examRecordFace_2027
                        if (createTime < Exam.TIME_2028)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2027;
                        // 2028-2029 的数据放到examRecordFace_2028
                        if (createTime < Exam.TIME_2029)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2028;
                        // 2029-2030 的数据放到examRecordFace_2029
                        if (createTime < Exam.TIME_2030)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2029;
                        // 2030-2031 的数据放到examRecordFace_2030
                        if (createTime < Exam.TIME_2031)
                            return ExamRecordFace.STRING_EXAM_RECORD_FACE_2030;
                        return ExamRecordFace.STRING_EXAM_RECORD_FACE;
                    }
                }
                return ExamRecordFace.STRING_EXAM_RECORD_FACE;
            }, Exam.CACHE_TIME);
        }
        return ExamRecordFace.STRING_EXAM_RECORD_FACE;
    }

    private List<Exam> getExamList(String examId) {
        return examDao.execute(e -> e.select(
                        Fields.start()
                                .add(EXAM.SOURCE_TYPE)
                                .add(EXAM.CREATE_TIME)
                                .add(EXAM.START_TIME)
                                .end()
                )
                .from(EXAM)
                .where(EXAM.ID.eq(examId))
                .fetch(r -> {
                    Exam exam = new Exam();
                    exam.setSourceType(r.getValue(EXAM.SOURCE_TYPE));
                    exam.setCreateTime(r.getValue(EXAM.CREATE_TIME));
                    exam.setStartTime(r.getValue(EXAM.START_TIME));
                    return exam;
                }));
    }


    /**
     * 根据examId查询考试创建时间，选择需要操作的答题记录表
     */
    public String getAnswerRecordStringTable(String examId) {
        if (examId != null) {

            return examCreateTimeCache.get(examId, () -> {
                List<Long> createTimeList = examDao.execute(e -> e.select(
                                Fields.start()
                                        .add(EXAM.CREATE_TIME)
                                        .end()
                        )
                        .from(EXAM)
                        .where(EXAM.ID.eq(examId))
                        .fetch(EXAM.CREATE_TIME));
                if (createTimeList != null && !createTimeList.isEmpty()) {
                    // 2019年以前的数据放到answerRecord
                    if (createTimeList.get(0) == null || createTimeList.get(0) < Exam.CREATE_TIME_2019)
                        return AnswerRecord.STRING_ANSWER_RECORD;
                    // 2019-2020 的数据放到answerRecord_2019
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2019 && createTimeList.get(0) < Exam.CREATE_TIME_2020)
                        return AnswerRecord.STRING_ANSWER_RECORD_2019;
                    // 2020-2021 的数据放到answerRecord_2020
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2020 && createTimeList.get(0) < Exam.CREATE_TIME_2021)
                        return AnswerRecord.STRING_ANSWER_RECORD_2020;
                    // 2021-2022 的数据放到answerRecord_2021
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2021 && createTimeList.get(0) < Exam.CREATE_TIME_2022)
                        return AnswerRecord.STRING_ANSWER_RECORD_2021;
                    // 2022-2023 的数据放到answerRecord_2022
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2022 && createTimeList.get(0) < Exam.CREATE_TIME_2023)
                        return AnswerRecord.STRING_ANSWER_RECORD_2022;
                    // 2023-2024 的数据放到answerRecord_2023
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2023 && createTimeList.get(0) < Exam.CREATE_TIME_2024)
                        return AnswerRecord.STRING_ANSWER_RECORD_2023;
                    // 2024-2025 的数据放到answerRecord_2024
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2024 && createTimeList.get(0) < Exam.CREATE_TIME_2025)
                        return AnswerRecord.STRING_ANSWER_RECORD_2024;
                    // 2025-2026 的数据放到answerRecord_2025
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2025 && createTimeList.get(0) < Exam.CREATE_TIME_2026)
                        return AnswerRecord.STRING_ANSWER_RECORD_2025;
                    // 2026-2027 的数据放到answerRecord_2026
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2026 && createTimeList.get(0) < Exam.CREATE_TIME_2027)
                        return AnswerRecord.STRING_ANSWER_RECORD_2026;
                    // 2027-2028 的数据放到answerRecord_2027
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2027 && createTimeList.get(0) < Exam.CREATE_TIME_2028)
                        return AnswerRecord.STRING_ANSWER_RECORD_2027;
                    // 2028-2029 的数据放到answerRecord_2028
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2028 && createTimeList.get(0) < Exam.CREATE_TIME_2029)
                        return AnswerRecord.STRING_ANSWER_RECORD_2028;
                    // 2029-2030 的数据放到answerRecord_2029
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2029 && createTimeList.get(0) < Exam.CREATE_TIME_2030)
                        return AnswerRecord.STRING_ANSWER_RECORD_2029;
                    // 2030-2031 的数据放到answerRecord_2030
                    if (createTimeList.get(0) >= Exam.CREATE_TIME_2030 && createTimeList.get(0) < Exam.CREATE_TIME_2031)
                        return AnswerRecord.STRING_ANSWER_RECORD_2030;
                    return AnswerRecord.STRING_ANSWER_RECORD;
                }
                return AnswerRecord.STRING_ANSWER_RECORD;
            }, Exam.CACHE_TIME);
        }
        return AnswerRecord.STRING_ANSWER_RECORD;
    }

    /**
     * 根据examId查询考试创建时间，选择需要操作的考试记录表
     */
    public String getExamRecordStringTable(String examId) {
        if (examId != null) {
            return examRecordTimeCache.get(examId, () -> {
                List<Exam> examList = getExamList(examId);
                if (examList != null && !examList.isEmpty()) {
                    if (Exam.EXAM_ACTIVITY_SOURCE_TYPE.equals(examList.get(0).getSourceType())) {
                        // 考试模块的考试按照考试的开始时间分表
                        Long startTime = examList.get(0).getStartTime();
                        // 2017年以前的数据放到examRecord
                        if (startTime == null || startTime < Exam.TIME_2017)
                            return ExamRecord.STRING_EXAM_RECORD;
                        // 2017-2018 的数据放到examRecord_2017
                        if (startTime < Exam.TIME_2018)
                            return ExamRecord.STRING_EXAM_RECORD_2017;
                        // 2018-2019 的数据放到examRecord_2018
                        if (startTime < Exam.TIME_2019)
                            return ExamRecord.STRING_EXAM_RECORD_2018;
                        // 2019-2020 的数据放到examRecord_2019
                        if (startTime < Exam.TIME_2020)
                            return ExamRecord.STRING_EXAM_RECORD_2019;
                        // 2020-2021 的数据放到examRecord_2020
                        if (startTime < Exam.TIME_2021)
                            return ExamRecord.STRING_EXAM_RECORD_2020;
                        // 2021-2022 的数据放到examRecord_2021
                        if (startTime < Exam.TIME_2022)
                            return ExamRecord.STRING_EXAM_RECORD_2021;
                        // 2022-2023 的数据放到examRecord_2022
                        if (startTime < Exam.TIME_2023)
                            return ExamRecord.STRING_EXAM_RECORD_2022;
                        // 2023-2024 的数据放到examRecord_2023
                        if (startTime < Exam.TIME_2024)
                            return ExamRecord.STRING_EXAM_RECORD_2023;
                        // 2024-2025 的数据放到examRecord_2024
                        if (startTime < Exam.TIME_2025)
                            return ExamRecord.STRING_EXAM_RECORD_2024;
                        // 2025-2026 的数据放到examRecord_2025
                        if (startTime < Exam.TIME_2026)
                            return ExamRecord.STRING_EXAM_RECORD_2025;
                        // 2026-2027 的数据放到examRecord_2026
                        if (startTime < Exam.TIME_2027)
                            return ExamRecord.STRING_EXAM_RECORD_2026;
                        // 2027-2028 的数据放到examRecord_2027
                        if (startTime < Exam.TIME_2028)
                            return ExamRecord.STRING_EXAM_RECORD_2027;
                        // 2028-2029 的数据放到examRecord_2028
                        if (startTime < Exam.TIME_2029)
                            return ExamRecord.STRING_EXAM_RECORD_2028;
                        // 2029-2030 的数据放到examRecord_2029
                        if (startTime < Exam.TIME_2030)
                            return ExamRecord.STRING_EXAM_RECORD_2029;
                        // 2030-2031 的数据放到examRecord_2030
                        if (startTime < Exam.TIME_2031)
                            return ExamRecord.STRING_EXAM_RECORD_2030;
                        return ExamRecord.STRING_EXAM_RECORD;
                    } else {
                        // 其他考试模块的考试按照考试的创建时间分表
                        Long createTime = examList.get(0).getCreateTime();
                        // 2017年以前的数据放到examRecord
                        if (createTime == null || createTime < Exam.TIME_2017)
                            return ExamRecord.STRING_EXAM_RECORD;
                        // 2017-2018 的数据放到examRecord_2017
                        if (createTime < Exam.TIME_2018)
                            return ExamRecord.STRING_EXAM_RECORD_2017;
                        // 2018-2019 的数据放到examRecord_2018
                        if (createTime < Exam.TIME_2019)
                            return ExamRecord.STRING_EXAM_RECORD_2018;
                        // 2019-2020 的数据放到examRecord_2019
                        if (createTime < Exam.TIME_2020)
                            return ExamRecord.STRING_EXAM_RECORD_2019;
                        // 2020-2021 的数据放到examRecord_2020
                        if (createTime < Exam.TIME_2021)
                            return ExamRecord.STRING_EXAM_RECORD_2020;
                        // 2021-2022 的数据放到examRecord_2021
                        if (createTime < Exam.TIME_2022)
                            return ExamRecord.STRING_EXAM_RECORD_2021;
                        // 2022-2023 的数据放到examRecord_2022
                        if (createTime < Exam.TIME_2023)
                            return ExamRecord.STRING_EXAM_RECORD_2022;
                        // 2023-2024 的数据放到examRecord_2023
                        if (createTime < Exam.TIME_2024)
                            return ExamRecord.STRING_EXAM_RECORD_2023;
                        // 2024-2025 的数据放到examRecord_2024
                        if (createTime < Exam.TIME_2025)
                            return ExamRecord.STRING_EXAM_RECORD_2024;
                        // 2025-2026 的数据放到examRecord_2025
                        if (createTime < Exam.TIME_2026)
                            return ExamRecord.STRING_EXAM_RECORD_2025;
                        // 2026-2027 的数据放到examRecord_2026
                        if (createTime < Exam.TIME_2027)
                            return ExamRecord.STRING_EXAM_RECORD_2026;
                        // 2027-2028 的数据放到examRecord_2027
                        if (createTime < Exam.TIME_2028)
                            return ExamRecord.STRING_EXAM_RECORD_2027;
                        // 2028-2029 的数据放到examRecord_2028
                        if (createTime < Exam.TIME_2029)
                            return ExamRecord.STRING_EXAM_RECORD_2028;
                        // 2029-2030 的数据放到examRecord_2029
                        if (createTime < Exam.TIME_2030)
                            return ExamRecord.STRING_EXAM_RECORD_2029;
                        // 2030-2031 的数据放到examRecord_2030
                        if (createTime < Exam.TIME_2031)
                            return ExamRecord.STRING_EXAM_RECORD_2030;
                        return ExamRecord.STRING_EXAM_RECORD;
                    }
                }
                return ExamRecord.STRING_EXAM_RECORD;
            }, Exam.CACHE_TIME);
        }
        return ExamRecord.STRING_EXAM_RECORD;
    }

    /**
     * 根据examId查询考试创建时间，选择需要操作的考试注册表
     */
    public String getExamRegistStringTable(String examId) {
        if (examId != null) {
            return examRegistTimeCache.get(examId, () -> {
                List<Exam> examList = getExamList(examId);
                if (examList != null && !examList.isEmpty()) {
                    if (Exam.EXAM_ACTIVITY_SOURCE_TYPE.equals(examList.get(0).getSourceType())) {
                        // 考试模块的考试按照考试的开始时间分表
                        Long startTime = examList.get(0).getStartTime();
                        // 2017年以前的数据放到examRegist
                        if (startTime == null || startTime < Exam.TIME_2017)
                            return ExamRegist.STRING_EXAM_REGIST;
                        // 2017-2018 的数据放到examRecord_2017
                        if (startTime < Exam.TIME_2018)
                            return ExamRegist.STRING_EXAM_REGIST_2017;
                        // 2018-2019 的数据放到examRecord_2018
                        if (startTime < Exam.TIME_2019)
                            return ExamRegist.STRING_EXAM_REGIST_2018;
                        // 2019-2020 的数据放到examRecord_2019
                        if (startTime < Exam.TIME_2020)
                            return ExamRegist.STRING_EXAM_REGIST_2019;
                        // 2020-2021 的数据放到examRecord_2020
                        if (startTime < Exam.TIME_2021)
                            return ExamRegist.STRING_EXAM_REGIST_2020;
                        // 2021-2022 的数据放到examRecord_2021
                        if (startTime < Exam.TIME_2022)
                            return ExamRegist.STRING_EXAM_REGIST_2021;
                        // 2022-2023 的数据放到examRecord_2022
                        if (startTime < Exam.TIME_2023)
                            return ExamRegist.STRING_EXAM_REGIST_2022;
                        // 2023-2024 的数据放到examRecord_2023
                        if (startTime < Exam.TIME_2024)
                            return ExamRegist.STRING_EXAM_REGIST_2023;
                        // 2024-2025 的数据放到examRecord_2024
                        if (startTime < Exam.TIME_2025)
                            return ExamRegist.STRING_EXAM_REGIST_2024;
                        // 2025-2026 的数据放到examRecord_2025
                        if (startTime < Exam.TIME_2026)
                            return ExamRegist.STRING_EXAM_REGIST_2025;
                        // 2026-2027 的数据放到examRecord_2026
                        if (startTime < Exam.TIME_2027)
                            return ExamRegist.STRING_EXAM_REGIST_2026;
                        // 2027-2028 的数据放到examRecord_2027
                        if (startTime < Exam.TIME_2028)
                            return ExamRegist.STRING_EXAM_REGIST_2027;
                        // 2028-2029 的数据放到examRecord_2028
                        if (startTime < Exam.TIME_2029)
                            return ExamRegist.STRING_EXAM_REGIST_2028;
                        // 2029-2030 的数据放到examRecord_2029
                        if (startTime < Exam.TIME_2030)
                            return ExamRegist.STRING_EXAM_REGIST_2029;
                        // 2030-2031 的数据放到examRecord_2030
                        if (startTime < Exam.TIME_2031)
                            return ExamRegist.STRING_EXAM_REGIST_2030;

                        return ExamRegist.STRING_EXAM_REGIST;
                    } else {
                        // 其他考试模块的考试按照考试的创建时间分表
                        Long createTime = examList.get(0).getCreateTime();
                        // 2017年以前的数据放到examRecord
                        if (createTime == null || createTime < Exam.TIME_2017)
                            return ExamRegist.STRING_EXAM_REGIST;
                        // 2017-2018 的数据放到examRecord_2017
                        if (createTime < Exam.TIME_2018)
                            return ExamRegist.STRING_EXAM_REGIST_2017;
                        // 2018-2019 的数据放到examRecord_2018
                        if (createTime < Exam.TIME_2019)
                            return ExamRegist.STRING_EXAM_REGIST_2018;
                        // 2019-2020 的数据放到examRecord_2019
                        if (createTime < Exam.TIME_2020)
                            return ExamRegist.STRING_EXAM_REGIST_2019;
                        // 2020-2021 的数据放到examRecord_2020
                        if (createTime < Exam.TIME_2021)
                            return ExamRegist.STRING_EXAM_REGIST_2020;
                        // 2021-2022 的数据放到examRecord_2021
                        if (createTime < Exam.TIME_2022)
                            return ExamRegist.STRING_EXAM_REGIST_2021;
                        // 2022-2023 的数据放到examRecord_2022
                        if (createTime < Exam.TIME_2023)
                            return ExamRegist.STRING_EXAM_REGIST_2022;
                        // 2023-2024 的数据放到examRecord_2023
                        if (createTime < Exam.TIME_2024)
                            return ExamRegist.STRING_EXAM_REGIST_2023;
                        // 2024-2025 的数据放到examRecord_2024
                        if (createTime < Exam.TIME_2025)
                            return ExamRegist.STRING_EXAM_REGIST_2024;
                        // 2025-2026 的数据放到examRecord_2025
                        if (createTime < Exam.TIME_2026)
                            return ExamRegist.STRING_EXAM_REGIST_2025;
                        // 2026-2027 的数据放到examRecord_2026
                        if (createTime < Exam.TIME_2027)
                            return ExamRegist.STRING_EXAM_REGIST_2026;
                        // 2027-2028 的数据放到examRecord_2027
                        if (createTime < Exam.TIME_2028)
                            return ExamRegist.STRING_EXAM_REGIST_2027;
                        // 2028-2029 的数据放到examRecord_2028
                        if (createTime < Exam.TIME_2029)
                            return ExamRegist.STRING_EXAM_REGIST_2028;
                        // 2029-2030 的数据放到examRecord_2029
                        if (createTime < Exam.TIME_2030)
                            return ExamRegist.STRING_EXAM_REGIST_2029;
                        // 2030-2031 的数据放到examRecord_2030
                        if (createTime < Exam.TIME_2031)
                            return ExamRegist.STRING_EXAM_REGIST_2030;
                        return ExamRegist.STRING_EXAM_REGIST;
                    }
                }
                return ExamRegist.STRING_EXAM_REGIST;
            }, Exam.CACHE_TIME);
        }
        return ExamRegist.STRING_EXAM_REGIST;
    }


    /**
     * 根据examId查询考试创建时间，选择需要操作的答题记录表
     */
    public String getPaperInstanceQuestionCopyStringTable(String examId) {
        if (examId != null) {
            return paperInstanceQuestionCopyTimeCache.get(examId, () -> {
                List<Long> createTimeList = examDao.execute(e -> e.select(
                                Fields.start()
                                        .add(EXAM.CREATE_TIME)
                                        .end()
                        )
                        .from(EXAM)
                        .where(EXAM.ID.eq(examId))
                        .fetch(EXAM.CREATE_TIME));
                if (createTimeList != null && !createTimeList.isEmpty()) {
                    // 按照考试的创建时间分表
                    Long createTime = createTimeList.get(0);
                    // 2017年以前的数据放到paperInstanceQuestionCopy
                    if (createTime == null || createTime < Exam.TIME_2017)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY;
                    // 2017-2018 的数据放到paperInstanceQuestionCopy_2017
                    if (createTime < Exam.TIME_2018)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2017;
                    // 2018-2019 的数据放到paperInstanceQuestionCopy_2018
                    if (createTime < Exam.TIME_2019)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2018;
                    // 2019-2020 的数据放到paperInstanceQuestionCopy_2019
                    if (createTime < Exam.TIME_2020)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2019;
                    // 2020-2021 的数据放到paperInstanceQuestionCopy_2020
                    if (createTime < Exam.TIME_2021)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2020;
                    // 2021-2022 的数据放到paperInstanceQuestionCopy_2021
                    if (createTime < Exam.TIME_2022)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2021;
                    // 2022-2023 的数据放到paperInstanceQuestionCopy_2022
                    if (createTime < Exam.TIME_2023)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2022;
                    // 2023-2024 的数据放到paperInstanceQuestionCopy_2023
                    if (createTime < Exam.TIME_2024)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2023;
                    // 2024-2025 的数据放到paperInstanceQuestionCopy_2024
                    if (createTime < Exam.TIME_2025)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2024;
                    // 2025-2026 的数据放到paperInstanceQuestionCopy_2025
                    if (createTime < Exam.TIME_2026)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2025;
                    // 2026-2027 的数据放到paperInstanceQuestionCopy_2026
                    if (createTime < Exam.TIME_2027)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2026;
                    // 2027-2028 的数据放到paperInstanceQuestionCopy_2027
                    if (createTime < Exam.TIME_2028)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2027;
                    // 2028-2029 的数据放到paperInstanceQuestionCopy_2028
                    if (createTime < Exam.TIME_2029)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2028;
                    // 2029-2030 的数据放到paperInstanceQuestionCopy_2029
                    if (createTime < Exam.TIME_2030)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2029;
                    // 2030-2031 的数据放到paperInstanceQuestionCopy_2030
                    if (createTime < Exam.TIME_2031)
                        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY_2030;
                    return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY;
                }
                return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY;
            }, Exam.CACHE_TIME);
        }
        return PaperInstanceQuestionCopy.STRING_PAPER_INSTANCE_QUESTION_COPY;
    }
}
