package com.zxy.product.examstu.async.config;

import com.zxy.common.message.CommonMessageConverter;
import com.zxy.common.message.consumer.MessageException;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.message.provider.MessageSenderFactory;
import com.zxy.common.serialize.Serializer;
import com.zxy.common.serialize.hessian.HessianSerializer;
import com.zxy.product.examstu.async.listener.*;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.ErrorHandler;

import java.util.Arrays;


@Configuration
public class MessageConfig implements BeanFactoryAware, EnvironmentAware {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageConfig.class);

    private String examStuRecordQueueName;

    private String examStuQueueName;

    private String examStuTodoQueueName;

    private String answerRecordStuQueueName;

    private String submitPaperQueueName;

    private String markPaperQueueName;

    private String examRecordQueueName;

    private String examOnlineDistributeQueueName;

    private DefaultListableBeanFactory beanFactory;

    private Environment env;
    private MessageSender messageSender;


    @Override
    public void setEnvironment(Environment env) {
    	this.env = env;
        examStuRecordQueueName = env.getProperty("examstu.message.queue.examstu.record.async");
        examStuQueueName = env.getProperty("examstu.message.queue.exam.stu.async");
        examStuTodoQueueName = env.getProperty("examstu.message.queue.exam.to.do");
        answerRecordStuQueueName = env.getProperty("examstu.message.queue.answer.record.stu.async");
        submitPaperQueueName = env.getProperty("examstu.message.queue.submit.paper.async");
        markPaperQueueName = env.getProperty("examstu.message.queue.mark.paper.async");
        examRecordQueueName = env.getProperty("examstu.message.queue.exam.record.async");
        examOnlineDistributeQueueName=env.getProperty("examstu.message.queue.exam.online.distribute.async");
    }


    @Override
	public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
    	if (beanFactory instanceof DefaultListableBeanFactory) {
    		this.beanFactory = (DefaultListableBeanFactory) beanFactory;
    	}
	}

    @Bean
    public MessageSenderFactory messageSenderFactory(){
        return new MessageSenderFactory();
    }

    @Bean
    public MessageSender messageSender(MessageSenderFactory messageSenderFactory, Environment env){
        messageSender = messageSenderFactory.create(env.getProperty("spring.rabbitmq.default-exchange"));
        return messageSender;
    }

    @Bean
    DirectExchange exchange(Environment env) {
        return new DirectExchange(env.getProperty("spring.rabbitmq.default-exchange"));
    }

    @Bean
    public Serializer serializer() {
        return new HessianSerializer();
    }

    @Bean
    public CommonMessageConverter commonMessageConverter(Serializer serializer) {
        CommonMessageConverter converter = new CommonMessageConverter();
        converter.setSerializer(serializer);
        return converter;
    }


    @Bean
    public SimpleMessageListenerContainer examRegistContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ExamRegistListener examRegistListner) {
    	Queue queue = new Queue(examStuRecordQueueName, true);
    	this.beanFactory.registerSingleton(examStuRecordQueueName, queue);
    	Arrays.stream(examRegistListner.getTypes()).forEach(type -> {
    		this.beanFactory.registerSingleton(examStuRecordQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
    	});
    	return createListener(connectionFactory, examRegistListner, examStuRecordQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer examStuContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ExamStuListener examStuListener) {
        Queue queue = new Queue(examStuQueueName, true);
        this.beanFactory.registerSingleton(examStuQueueName, queue);
        Arrays.stream(examStuListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(examStuQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, examStuListener, examStuQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer answerRecordStuContainer(DirectExchange exchange, ConnectionFactory connectionFactory, AnswerRecordStuListener answerRecordStuListener) {
        Queue queue = new Queue(answerRecordStuQueueName, true);
        this.beanFactory.registerSingleton(answerRecordStuQueueName, queue);
        Arrays.stream(answerRecordStuListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(answerRecordStuQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, answerRecordStuListener, answerRecordStuQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer submitPaperContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubmitPaperListener submitPaperListener) {
        Queue queue = new Queue(submitPaperQueueName, true);
        this.beanFactory.registerSingleton(submitPaperQueueName, queue);
        Arrays.stream(submitPaperListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(submitPaperQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, submitPaperListener, submitPaperQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer markPaperContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MarkPaperStuListener markPaperListener) {
        Queue queue = new Queue(markPaperQueueName, true);
        this.beanFactory.registerSingleton(markPaperQueueName, queue);
        Arrays.stream(markPaperListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(markPaperQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, markPaperListener, markPaperQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer examStuTodoContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ToDoListener toDoListener) {
        Queue queue = new Queue(examStuTodoQueueName, true);
        this.beanFactory.registerSingleton(examStuTodoQueueName, queue);
        Arrays.stream(toDoListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(examStuTodoQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, toDoListener, examStuTodoQueueName);
    }

//    @Bean
//    public SimpleMessageListenerContainer examRecordContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ExamRecordListener examRecordListener) {
//        Queue queue = new Queue(examRecordQueueName, true);
//        this.beanFactory.registerSingleton(examRecordQueueName, queue);
//        Arrays.stream(examRecordListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(examRecordQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
//        });
//        return createListener(connectionFactory, examRecordListener, examRecordQueueName);
//    }


    @Bean
    public SimpleMessageListenerContainer examOnlineDistributeContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ExamOnlineDistributeListener examOnlineDistributeListener) {
        Queue queue = new Queue(examOnlineDistributeQueueName, true);
        this.beanFactory.registerSingleton(examOnlineDistributeQueueName, queue);
        Arrays.stream(examOnlineDistributeListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(examOnlineDistributeQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, examOnlineDistributeListener, examOnlineDistributeQueueName);
    }


    private SimpleMessageListenerContainer createListener(ConnectionFactory connectionFactory, MessageListener listener, String queue) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(queue);
        container.setMessageListener(listener);
        container.setPrefetchCount(env.getProperty("spring.rabbitmq.listener.simple.prefetch", Integer.class, 1));
        container.setConcurrentConsumers(env.getProperty("spring.rabbitmq.listener.simple.concurrency", Integer.class, 1));
        container.setMaxConcurrentConsumers(env.getProperty("spring.rabbitmq.listener.simple.max-concurrency", Integer.class, 1));
        container.setErrorHandler(new ErrorHandler() {
            @Override
            public void handleError(Throwable throwable) {
                if (causeChainContainsARADRE(throwable)) {
                    StringBuilder errorMessage = new StringBuilder(env.getProperty("application.env.name", String.class, "dev9"));
                    errorMessage.append("环境异步监听服务出错: ");
                    errorMessage.append(throwable.getCause().getMessage());
                    LOGGER.error("message listener shutdown: " + throwable.getCause().getMessage());
                    // 发送邮件消息
                    messageSender.send(MessageTypeContent.SEND_MESSAGE_WARNING_EMAIL, (Object)errorMessage.toString(),
                            MessageHeaderContent.SUBJECT, "exam stu project " + listener.getClass().getSimpleName() + "服务挂起");
                    // 停止监听
                    container.shutdown();
                }
            }
            private boolean causeChainContainsARADRE(Throwable t) {
                for(Throwable cause = t.getCause(); cause != null; cause = cause.getCause()) {
                    if(cause instanceof MessageException) {
                        return true;
                    }
                }
                return false;
            }
        });
        return container;
    }

}
