package com.zxy.product.examstu.async.listener;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.examstu.async.helper.ExamRegistListenerHelper;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
@Component
public class ExamRegistListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamRegistListener.class);

    private TransactionTemplate transactionTemplate;

    private ExamRegistListenerHelper examRegistListenerHelper;

	private MemberService memberService;


	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Autowired
    public void setExamRegistListenerHelper(ExamRegistListenerHelper examRegistListenerHelper) {
        this.examRegistListenerHelper = examRegistListenerHelper;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}

    //ps,未开始转考试中，考试中转已结束的两种情况没有做对应的消息监听
    @Override
    protected void onMessage(Message message) {

		LOGGER.info("ExamRegistListener/:{}", message);

    	Integer type = message.getType();


    	 transactionTemplate.execute(new TransactionCallbackWithoutResult() {
 			@Override
 			protected void doInTransactionWithoutResult(TransactionStatus status) {
 				switch (type) {
 				case MessageTypeContent.EXAM_SIGNUP_INSERT:
 					doSignUpRegisting(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_PASS:
 					doSignUpPassed(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_REFUSE:
 					doSignUpRefused(message);
 					break;
 				case MessageTypeContent.EXAM_SIGNUP_DELETE:
 					doSignUpCancel(message);
 					break;
 				case MessageTypeContent.EXAM_EXAM_RECORD_INSERT:
 					doExamRecordInsert(message);
 					break;
				case MessageTypeContent.SUBMIT_PAPER_STU:
 					doSubmitPaper(message);
 					break;
 				case MessageTypeContent.EXAM_EXAM_RECORD_DELETE:
 					doExamRecordDeleted(message);
 					break;
 				case MessageTypeContent.EXAM_ADD_USER_TO_EXAM:
 					doExamRecordInsert(message);
 					break;
 				case MessageTypeContent.EXAM_CHANGE_TO_ORDINARY:
 					doExamChangeToOrdinary(message);
 					break;
 				case MessageTypeContent.EXAM_RECORD_SCORE_RESULT:
 					updateTopScore(message);
 					break;
 				case MessageTypeContent.EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER:
 					doExamChangeToNoSpecify(message);
 					break;
 				default:
 					break;
 				}
 			}
    	 });
    }

    private void doExamChangeToNoSpecify(Message message) {
    	String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		examRegistListenerHelper.doExamChangeToNoSpecify(examRegion, examId);
	}

    private void updateTopScore(Message message) {
		String examRecordId = message.getHeader(MessageHeaderContent.ID);
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		examRegistListenerHelper.updateTopScore(examRegion,examRecordId, examId);
	}

	private void doExamChangeToOrdinary(Message message) {
    	String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		examRegistListenerHelper.doExamChangeToOrdinary(examRegion, examId);
	}

	private void doExamRecordDeleted(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		examRegistListenerHelper.doExamRecordDeleted(examRegion, examId, memberId);
	}

	private void doSubmitPaper(Message message) {
		String submitTypeStr = message.getHeader(ExamRecord.SUBMIT_PAPER_TYPE);
		String submitPaperExamId = message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_ID);
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		ExamRecord.SubmitType submitType = null;
		if (submitTypeStr != null) {
			submitType = ExamRecord.SubmitType.valueOf(submitTypeStr);
		}
		String examRecordId = message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID);
		examRegistListenerHelper.doSubmitPaper(examRegion,submitType, examRecordId, submitPaperExamId);
	}

	private void doExamRecordInsert(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		Integer status = Integer.valueOf(message.getHeader(MessageHeaderContent.STATUS));
		Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
		if (StringUtils.isEmpty(memberIds)) {
			return;
		}
		examRegistListenerHelper.doExamRecordInsert(examRegion, examId, memberIds, status);
	}

	private void doSignUpCancel(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		String examRegion = message.getHeader(MessageHeaderContent.EXAM_REGION);
		examRegistListenerHelper.doSignUpCancel(Integer.valueOf(examRegion), examId, memberIds);
	}

	private void doSignUpRefused(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String memberIds = message.getHeader(MessageHeaderContent.IDS);
		String examRegion = message.getHeader(MessageHeaderContent.EXAM_REGION);
		examRegistListenerHelper.doSignUpRefused(Integer.valueOf(examRegion), examId, memberIds);
	}

	private void doSignUpPassed(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String ids = message.getHeader(MessageHeaderContent.IDS);
		String examRegion = message.getHeader(MessageHeaderContent.EXAM_REGION);
		examRegistListenerHelper.doSignUpPassed(Integer.valueOf(examRegion), examId, ids);
	}

	/**
	 * 报名
	 * @param message
	 */
	private void doSignUpRegisting(Message message) {
		String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
		String ids = message.getHeader(MessageHeaderContent.IDS);
		if (!org.springframework.util.StringUtils.isEmpty(ids) && CollectionUtils.isNotEmpty(Arrays.asList(ids.split(",")))) {
			String memberId = Arrays.asList(ids.split(",")).get(0);
			Integer examRegion = memberService.findExamRegion(memberId);
			examRegistListenerHelper.doSignUpRegisting(examRegion, examId, ids);
		}
	}

	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_SIGNUP_INSERT,
			MessageTypeContent.EXAM_SIGNUP_PASS,
			MessageTypeContent.EXAM_SIGNUP_REFUSE,
			MessageTypeContent.EXAM_SIGNUP_DELETE,
			MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
			MessageTypeContent.SUBMIT_PAPER_STU,
			MessageTypeContent.EXAM_EXAM_RECORD_DELETE,
			MessageTypeContent.EXAM_ADD_USER_TO_EXAM,
			MessageTypeContent.EXAM_CHANGE_TO_ORDINARY,
			MessageTypeContent.EXAM_RECORD_SCORE_RESULT,
			MessageTypeContent.EXAM_RECORD_BE_DELETE_BY_SEND_TO_CENTER

		};
	}


}
