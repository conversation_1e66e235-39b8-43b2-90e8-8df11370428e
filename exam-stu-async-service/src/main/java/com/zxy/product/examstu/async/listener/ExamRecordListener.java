package com.zxy.product.examstu.async.listener;


import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.examstu.async.helper.ExamRegistListenerHelper;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
@Component
public class ExamRecordListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExamRecordListener.class);

    private TransactionTemplate transactionTemplate;

    private ExamRegistListenerHelper examRegistListenerHelper;

	private MemberService memberService;


	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Autowired
    public void setExamRegistListenerHelper(ExamRegistListenerHelper examRegistListenerHelper) {
        this.examRegistListenerHelper = examRegistListenerHelper;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}

    //ps,未开始转考试中，考试中转已结束的两种情况没有做对应的消息监听
    @Override
	protected void onMessage(Message message) {

		LOGGER.info("ExamRecordListener/:{}", message);

		Integer type = message.getType();
		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus status) {
				switch (type) {
					case MessageTypeContent.EXAM_RECORD_STU_INSERT:
						doExamRecordInsert(message);
						break;
					default:
						break;
				}
			}
		});
	}
	private void doExamRecordInsert(Message message) {
		ExamRecord examRecord = JSONObject.parseObject(message.getHeader(MessageHeaderContent.PARAMS),ExamRecord.class);
		if (examRecord == null || StringUtils.isEmpty(examRecord.getMemberId())){
			return;
		}
		Integer examRegion = memberService.findExamRegion(examRecord.getMemberId());
		String examId = examRecord.getExamId();
		examRegistListenerHelper.doExamRecordInsert(examRegion, examId, examRecord);
	}


	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_RECORD_STU_INSERT
		};
	}


}
