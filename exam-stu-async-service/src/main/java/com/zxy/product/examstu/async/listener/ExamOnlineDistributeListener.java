package com.zxy.product.examstu.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamOnlineLog;
import com.zxy.product.examstu.api.ExamOnlineService;
import com.zxy.product.examstu.api.ExamService;
import com.zxy.product.examstu.content.FlowLimitEnum;
import com.zxy.product.examstu.dto.exam.online.MsgParameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.zxy.product.examstu.content.ExamLimitConstants.*;
import static com.zxy.product.examstu.content.MessageHeaderContent.*;
import static com.zxy.product.examstu.content.MessageTypeContent.*;

/**
 * 考试限流任务分发队列相关监听类
 *
 * <AUTHOR>
 * @date 2025年02月13日 6:59
 */
@Component
public class ExamOnlineDistributeListener extends AbstractMessageListener {
    private static final Logger logger = LoggerFactory.getLogger(ExamOnlineDistributeListener.class);
    private ExamService examService;
    private ExamOnlineService examOnlineService;

    @Autowired
    public void setExamService(ExamService examService) { this.examService = examService; }

    @Autowired
    public void setExamOnlineService(ExamOnlineService examOnlineService) { this.examOnlineService = examOnlineService; }

    @Override
    protected void onMessage(Message message) {
        int type = message.getType();
        logger.info("考试限流任务分发队列相关监听类开始，消息类型{}", type);
        String businessId = message.getHeader(EXAM_ONLINE_BUSINESS_ID);
        String memberId = message.getHeader(EXAM_ONLINE_MEMBER_ID);
        String region = message.getHeader(EXAM_ONLINE_REGION);
        logger.info("当前考试所属数据库{}", region);
        Map<Integer, Runnable> runnableMap = new HashMap<>(3);
        runnableMap.put(EXAM_DISTRIBUTE_LIMIT_VIEW,        () -> view(Integer.valueOf(region), businessId, memberId));
        runnableMap.put(EXAM_DISTRIBUTE_LIMIT_RELEASE, () -> release(Integer.valueOf(region), businessId, memberId));
        runnableMap.get(type).run();
    }

    /**
     * 构建组装MQ消息体
     * @param businessId 业务Id
     * @param memberId   用户Id
     * @return MQ消息体
     */
    private MsgParameter doMsgParameter(String businessId, String memberId, Integer region) {
        Exam exam = examService.getSimpleData(1, businessId);
        MsgParameter msgParameter = new MsgParameter();
        msgParameter.setBusinessId(businessId);
        msgParameter.setMemberId(memberId);
        msgParameter.setExamStartTime(exam.getStartTime());
        msgParameter.setExamPersistentTime(exam.getDuration() * 60 * 1000L);
        msgParameter.setExamRegion(region);
        return msgParameter;
    }

    /**
     * 限流：前置校验
     *
     * @param businessId 业务Id
     * @param memberId   当前用户Id
     */
    public void view(Integer region, String businessId, String memberId) {
        MsgParameter msgParameter = doMsgParameter(businessId, memberId, region);
        Optional<ExamOnlineLog> onlineLogOpt = examOnlineService.doSingleOnlineLogOpt(region, msgParameter.getBusinessId(), msgParameter.getMemberId());
        if (!onlineLogOpt.isPresent()) {
            ExamOnlineLog examOnlineLog = examOnlineService.insertOnlineLog(
                    region, msgParameter.getBusinessId(), msgParameter.getMemberId(),
                    msgParameter.getExamStartTime() + msgParameter.getExamPersistentTime());
            logger.info("添加前置通知流水成功，添加数据{}", examOnlineLog);
        }
    }


    /**
     * 限流：后置释放
     *
     * @param businessId 业务Id
     * @param memberId   用户Id
     */
    public void release(Integer region, String businessId, String memberId) {
        logger.info("释放前置通知流水，考试{}库", region);
        examOnlineService.releaseOnlineLog(region, businessId, memberId);
    }

    @Override
    public int[] getTypes() {
        return new int[]{EXAM_DISTRIBUTE_LIMIT_VIEW, EXAM_DISTRIBUTE_LIMIT_RELEASE};
    }
}
