package com.zxy.product.examstu.async.listener;

import com.alibaba.fastjson.JSONArray;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.async.helper.AnswerRecordListenerHelper;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
@Component
public class MarkPaperStuListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(MarkPaperStuListener.class);

	private AnswerRecordListenerHelper answerRecordListenerHelper;

	@Autowired
	public void setAnswerRecordListenerHelper(AnswerRecordListenerHelper answerRecordListenerHelper) {
		this.answerRecordListenerHelper = answerRecordListenerHelper;
	}

	@Override
    protected void onMessage(Message message) {

		LOGGER.info("MarkPaperStuListener/:{}", message);

    	Integer type = message.getType();

    	switch (type) {
		case MessageTypeContent.EXAM_STU_ANSWER_RECORD_UPDATE:
			List<AnswerRecord> answerRecords = JSONArray.parseArray(message.getHeader(MessageHeaderContent.PARAMS),AnswerRecord.class);
			String member = message.getHeader(MessageHeaderContent.MEMBER_ID);
			String exam = message.getHeader(MessageHeaderContent.EXAM_ID);
			String examRecordId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
			updateAnswerRecord(answerRecords, member, exam, examRecordId);
			break;
		default:
			break;
		}
    }

	private void updateAnswerRecord(List<AnswerRecord> answerRecords,String memberId,String examId, String examRecordId) {

		if (CollectionUtils.isEmpty(answerRecords)) {
			return;
		}

		Integer examRegion = answerRecordListenerHelper.memberBelongSouth(memberId);

		answerRecordListenerHelper.updateAnswerRecord(examRegion, examId, answerRecords);

		//  判断主观题是否评分完毕
		if (answerRecordListenerHelper.markPaperCompleted(examRegion, examRecordId, examId)) {
			answerRecordListenerHelper.confirmExamRecord(examRegion, examRecordId, examId);
		}

	}

	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_STU_ANSWER_RECORD_UPDATE
		};
	}

}
