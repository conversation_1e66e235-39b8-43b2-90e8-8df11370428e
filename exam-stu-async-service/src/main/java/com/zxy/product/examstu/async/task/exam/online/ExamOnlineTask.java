package com.zxy.product.examstu.async.task.exam.online;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamOnlineService;
import com.zxy.product.examstu.content.DataSourceEnum;
import com.zxy.product.examstu.content.FlowLimitEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import static com.zxy.product.examstu.content.ExamLimitConstants.*;

/**
 * 限流：定时任务处理（考试在线人数）
 * <AUTHOR>
 * @date 2025年2月17日 16:00
 */
@Component
@EnableAsync
public class ExamOnlineTask {
    private static final Logger logger= LoggerFactory.getLogger(ExamOnlineTask.class);
    private Cache cache;
    private ExamOnlineService examOnlineService;

    @Autowired
    public void setCache(CacheService cacheService){ this.cache=cacheService.create(CacheKeyApplication,CacheKeyModule); }

    @Autowired
    public void setExamOnlineService( ExamOnlineService examOnlineService ){ this.examOnlineService=examOnlineService; }


    /**修正||覆写缓存中各业务类型的最大在线人数，直接COUNT*/
    @Async
    @Scheduled(cron = "0 */10 * * * ?  ")
    @DataSource
    public void correctFlowLimitOnline(){
        logger.info("修正||覆写缓存中各类型的最大在线人数开始");
        Integer northCount = examOnlineService.doSelectCount(DataSourceEnum.NORTH.getType());
        Integer southCount = examOnlineService.doSelectCount(DataSourceEnum.SOUTH.getType());
        String cacheKey=CacheKeyOnline + FlowLimitEnum.ExamSubmit.getBusinessType();
        Long cacheExamNumber = cache.increment(cacheKey, 0L);
        long examCount=northCount+southCount;
        Long examIncrement = cache.increment(cacheKey, examCount - cacheExamNumber);
        logger.info("修改||覆写缓存中各类型的最大在线人数结束，统计考试在线人数{}",examIncrement);
    }
}
