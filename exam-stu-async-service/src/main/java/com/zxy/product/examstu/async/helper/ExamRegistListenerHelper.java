package com.zxy.product.examstu.async.helper;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.async.util.GetTableUtil;
import com.zxy.product.examstu.content.DataSourceEnum;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.annotation.DataSource;
import org.jooq.Field;
import org.jooq.InsertValuesStepN;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import static com.zxy.product.exam.jooq.Tables.*;

@Component
public class ExamRegistListenerHelper {

    private CommonDao<Exam> examDao;

    private CommonDao<ExamRegist> examRegistDao;

    private CommonDao<ExamRecord> examRecordDao;

    private CommonDao<SignUp> signUpDao;

    private CommonDao<CloudSignup> cloudSignUpDao;

    private CommonDao<GridSignup> gridSignUpDao;

    public final static int PAGE_SIZE = 2000;

    private Cache examRegistTimeCache;


    private MessageSender messageSender;

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }


    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }



    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.examRegistTimeCache = cacheService.create("fenbiao", "exam-regist-time");
    }

    @Autowired
    public void setGridSignUpDao(CommonDao<GridSignup> gridSignUpDao) {
        this.gridSignUpDao = gridSignUpDao;
    }

    @Autowired
    public void setCloudSignUpDao(CommonDao<CloudSignup> cloudSignUpDao) {
        this.cloudSignUpDao = cloudSignUpDao;
    }

    @Autowired
    public void setSignUpDao(CommonDao<SignUp> signUpDao) {
        this.signUpDao = signUpDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }

    @Autowired
    public void setExamRegistDao(CommonDao<ExamRegist> examRegistDao) {
        this.examRegistDao = examRegistDao;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @DataSource
    public void doSignUpRegisting(Integer examRegion, String examId, String ids) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        //先删除
        List<String> examRegistIds = examRegistDao.execute(x -> x.select(
                Fields.start()
                .add(examRegistTable.field("f_id", String.class)).end())
                .from(examRegistTable)
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                .and(examRegistTable.field("f_member_id", String.class).in(ids.split(",")))
                .fetch(examRegistTable.field("f_id", String.class))
            );

        examRegistDao.execute(dslContext ->
        dslContext.delete(examRegistTable).where(
                examRegistTable.field("f_id", String.class).in(examRegistIds)
                ).execute());

        examRegistIds.forEach(id->
                messageSender.send(
                        MessageTypeContent.DELETE_EXAM_DATA,
                        MessageHeaderContent.ID,id,
                        MessageHeaderContent.PARAMS,DeleteDataExam.EXAM_REGIST)
        );

        //后新增
        examDao.getOptional(examId).ifPresent(exam -> {
            Arrays.stream(ids.split(",")).forEach(memberId -> {
                ExamRegist examRegist = new ExamRegist();
                examRegist.forInsert();
                examRegist.setExamId(examId);
                examRegist.setMemberId(memberId);
                examRegist.setExamTimes(0);
                examRegist.setStatus(getStatusBySignUp(exam, memberId));
                examRegist.setType(ExamRegist.TYPE_SIGN_UP);
                examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);

                examRegistDao.execute(e ->
                e.insertInto(examRegistTable, examRegistTable.field("f_id", String.class),
                        examRegistTable.field("f_top_score", Integer.class),
                        examRegistTable.field("f_top_score_record_id", String.class),
                        examRegistTable.field("f_exam_times", Integer.class),
                        examRegistTable.field("f_member_id", String.class),
                        examRegistTable.field("f_status", Integer.class),
                        examRegistTable.field("f_exam_id", String.class),
                        examRegistTable.field("f_pass_status", Integer.class),
                        examRegistTable.field("f_certificate_issue", Integer.class),
                        examRegistTable.field("f_create_time", Long.class),
                        examRegistTable.field("f_type", Integer.class)
                        )
                        .values(
                                examRegist.getId(), examRegist.getTopScore(),
                                examRegist.getTopScoreRecordId(),examRegist.getExamTimes(),
                                examRegist.getMemberId(),examRegist.getStatus(),
                                examRegist.getExamId(),examRegist.getPassStatus(),
                                examRegist.getCertificateIssue(),examRegist.getCreateTime(),
                                examRegist.getType()
                                )
                        .execute()
                );

            });
        });
    }

    /**
     * 判断报名状态
     * 待审核
     * 被拒绝
     * 审核通过
     *      开考中
     *      未开始
     * @param exam
     * @param memberId
     * @return
     */
    private Integer getStatusBySignUp(Exam exam, String memberId) {
        if (Exam.EXAM_CLOUD_TYPE.equals(exam.getType())) {
            return cloudSignUpDao.fetchOne(CLOUD_SIGNUP.EXAM_ID.eq(exam.getId()), CLOUD_SIGNUP.MEMBER_ID.eq(memberId)).map(cloudSignUp -> {
                if (cloudSignUp.getStatus() != null && cloudSignUp.getStatus().intValue() == SignUp.STATUS_APPROVE) return ExamRegist.STATUS_APPROVING;
                if (cloudSignUp.getStatus() != null && cloudSignUp.getStatus().intValue() == SignUp.STATUS_REFUSE) return ExamRegist.STATUS_BE_REFUSE;
                if (cloudSignUp.getStatus() != null && cloudSignUp.getStatus().intValue() == SignUp.STATUS_APPROVE
                        && exam.getStartTime() != null && System.currentTimeMillis() > exam.getStartTime()) {
                    return ExamRegist.STATUS_EXAM_STARTING;
                }
                return ExamRegist.STATUS_NO_BEGIN;
            }).orElse(ExamRegist.STATUS_NO_BEGIN);
        } else if (Exam.EXAM_GRID_TYPE.equals(exam.getType())) {
            return gridSignUpDao.fetchOne(GRID_SIGNUP.EXAM_ID.eq(exam.getId()), GRID_SIGNUP.MEMBER_ID.eq(memberId)).map(gridSignUp -> {
                if (gridSignUp.getStatus() != null && gridSignUp.getStatus().intValue() == SignUp.STATUS_APPROVE) return ExamRegist.STATUS_APPROVING;
                if (gridSignUp.getStatus() != null && gridSignUp.getStatus().intValue() == SignUp.STATUS_REFUSE) return ExamRegist.STATUS_BE_REFUSE;
                if (gridSignUp.getStatus() != null && gridSignUp.getStatus().intValue() == SignUp.STATUS_APPROVE
                        && exam.getStartTime() != null && System.currentTimeMillis() > exam.getStartTime()) {
                    return ExamRegist.STATUS_EXAM_STARTING;
                }
                return ExamRegist.STATUS_NO_BEGIN;
            }).orElse(ExamRegist.STATUS_NO_BEGIN);
        }
        else {
            return signUpDao.fetchOne(SIGNUP.EXAM_ID.eq(exam.getId()), SIGNUP.MEMBER_ID.eq(memberId)).map(signUp -> {
                if (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_APPROVE) return ExamRegist.STATUS_APPROVING;
                if (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_REFUSE) return ExamRegist.STATUS_BE_REFUSE;
                if (signUp.getStatus() != null && signUp.getStatus().intValue() == SignUp.STATUS_APPROVE
                        && exam.getStartTime() != null && System.currentTimeMillis() > exam.getStartTime()) {
                    return ExamRegist.STATUS_EXAM_STARTING;
                }
                return ExamRegist.STATUS_NO_BEGIN;
            }).orElse(ExamRegist.STATUS_NO_BEGIN);
        }
    }

    @DataSource
    public void deleteFirstIfOneId(Integer examRegion, String memberIds, String examId) {
        String[] arr = memberIds.split(",");
        if (arr.length == 1) {
            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
            List<String> examRegistIds = examRegistDao.execute(x -> x.select(
                    Fields.start()
                    .add(examRegistTable.field("f_id", String.class)).end())
                    .from(examRegistTable)
                    .where(examRegistTable.field("f_exam_id", String.class).eq(examId))
                    .and(examRegistTable.field("f_member_id", String.class).eq(memberIds))
                    .fetch(examRegistTable.field("f_id", String.class))
                );

            examRegistDao.execute(dslContext ->
            dslContext.delete(examRegistTable).where(
                    examRegistTable.field("f_id", String.class).in(examRegistIds)
                    ).execute());


            examRegistIds.forEach(id->
                    messageSender.send(
                            MessageTypeContent.DELETE_EXAM_DATA,
                            MessageHeaderContent.ID,id,
                            MessageHeaderContent.PARAMS,DeleteDataExam.EXAM_REGIST)
            );
        }
    }

    @DataSource
    public void doSignUpPassed(Integer examRegion, String examId, String ids) {
        examDao.getOptional(examId).ifPresent(exam -> {
            if (System.currentTimeMillis() > exam.getStartTime()) {
                updateRegistExamStarting(examRegion, examId, ids);
            } else {
                updateRegistExamNoBegin(examRegion, examId, ids);
            }
        });
    }

    private void updateRegistExamStarting(Integer examRegion, String examId, String memberIds) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examDao.execute(e -> e.batch(Arrays.stream(memberIds.split(",")).map(memberId -> {
            return e.update(examRegistTable)
                    .set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_EXAM_STARTING)
                    .where(examRegistTable.field("f_exam_id", String.class).eq(examId), examRegistTable.field("f_member_id", String.class).eq(memberId));
        }).collect(Collectors.toList())).execute());
    }

    private void updateRegistExamNoBegin(Integer examRegion, String examId, String memberIds) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examDao.execute(e -> e.batch(Arrays.stream(memberIds.split(",")).map(memberId -> {
            return e.update(examRegistTable)
                    .set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_NO_BEGIN)
                    .where(examRegistTable.field("f_exam_id", String.class).eq(examId), examRegistTable.field("f_member_id", String.class).eq(memberId));
        }).collect(Collectors.toList())).execute());
    }

    @DataSource
    public void doSignUpRefused(Integer examRegion, String examId, String memberIds) {
        updateRegistSignUpRefuse(examRegion, examId, memberIds);
    }

    private void updateRegistSignUpRefuse(Integer examRegion, String examId, String memberIds) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examDao.execute(e -> e.batch(Arrays.stream(memberIds.split(",")).map(memberId -> {
            return e.update(examRegistTable).set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_BE_REFUSE)
                    .where(examRegistTable.field("f_exam_id", String.class).eq(examId), examRegistTable.field("f_member_id", String.class).eq(memberId));
        }).collect(Collectors.toList())).execute());
    }

    @DataSource
    public void doSignUpCancel(Integer examRegion, String examId, String memberIds) {
        deleteRegistSignUpCancel(examRegion, examId, memberIds);
    }

    private void deleteRegistSignUpCancel(Integer examRegion, String examId, String memberIds) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<String> examRegistIds = examRegistDao.execute(e -> e.select(examRegistTable.field("f_id", String.class))
                .from(examRegistTable)
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId),
                        examRegistTable.field("f_member_id", String.class).in(Arrays.asList(memberIds.split(","))))
                .fetch(examRegistTable.field("f_id", String.class)));
        examDao.execute(e -> e.batch(Arrays.stream(memberIds.split(",")).map(memberId -> {
            return e.deleteFrom(examRegistTable).where(examRegistTable.field("f_exam_id", String.class).eq(examId), examRegistTable.field("f_member_id", String.class).eq(memberId));
        }).collect(Collectors.toList())).execute());

        examRegistIds.forEach(id->
                messageSender.send(
                        MessageTypeContent.DELETE_EXAM_DATA,
                        MessageHeaderContent.ID,id,
                        MessageHeaderContent.PARAMS,DeleteDataExam.EXAM_REGIST)
        );
    }

    @DataSource
    public void doExamRecordInsert(Integer examRegion, String examId, String memberIds, Integer status) {
        if (StringUtils.isEmpty(memberIds)) {
            return;
        }

        if (ExamRecord.STATUS_TO_BE_STARTED.equals(status)) {
            examDao.getOptional(examId).ifPresent(exam -> {
                //非报名,新增注册数据
                Integer registStatus = System.currentTimeMillis() > exam.getStartTime()
                        ? ExamRegist.STATUS_EXAM_STARTING : ExamRegist.STATUS_NO_BEGIN;
                if (exam.getNeedApplicant() == null ||  !Exam.EXAM_YES.equals(exam.getNeedApplicant())) {

                    //清除分表缓存
                    examRegistTimeCache.clear(examId);
                    List<ExamRegist> examRegists = Arrays.stream(memberIds.split(",")).map(memberId -> {
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.forInsert();
                        examRegist.setExamId(examId);
                        examRegist.setMemberId(memberId);
                        examRegist.setExamTimes(0);
                        examRegist.setStatus(registStatus);
                        examRegist.setType(getType(exam));
                        examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);
                        return examRegist;
                    }).collect(Collectors.toList());

                    // 批量审核，南北库各删除一遍，防止regist表数据重复
                    deleteFirstIfOneId(DataSourceEnum.SOUTH.getType(), memberIds, examId);
                    deleteFirstIfOneId(DataSourceEnum.NORTH.getType(), memberIds, examId);

                    TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

                    // 分批插入数据库
                    List<List<ExamRegist>> examRegistList = Lists.partition(examRegists, PAGE_SIZE);
                    for (List<ExamRegist> temp : examRegistList) {
                        Field<?> [] fileds = {
                                examRegistTable.field("f_id", String.class),
                                examRegistTable.field("f_top_score", Integer.class),
                                examRegistTable.field("f_top_score_record_id", String.class),
                                examRegistTable.field("f_exam_times", Integer.class),
                                examRegistTable.field("f_member_id", String.class),
                                examRegistTable.field("f_status", Integer.class),
                                examRegistTable.field("f_exam_id", String.class),
                                examRegistTable.field("f_pass_status", Integer.class),
                                examRegistTable.field("f_certificate_issue", Integer.class),
                                examRegistTable.field("f_create_time", Long.class),
                                examRegistTable.field("f_type", Integer.class)
                            };
                        examRegistDao.execute(e->{
                              InsertValuesStepN<?> step = e.insertInto(examRegistTable,fileds);
                              temp.forEach(examRegist->{
                                  step.values(
                                          examRegist.getId(), examRegist.getTopScore(),
                                          examRegist.getTopScoreRecordId(),examRegist.getExamTimes(),
                                          examRegist.getMemberId(),examRegist.getStatus(),
                                          examRegist.getExamId(),examRegist.getPassStatus(),
                                          examRegist.getCertificateIssue(),examRegist.getCreateTime(),
                                          examRegist.getType()
                                      );
                              });
                              return step.execute();
                          });

                    }
                }
                else {
                    //报名考试直接添加考生
                    doExamRecordInsertByApplicantExam(examRegion, exam, registStatus, memberIds);
                }

            });
        }

        if (status == ExamRecord.STATUS_DOING) {
            insertExamRegistByRecordDoing(examRegion, examId, memberIds);
        }

    }
    @DataSource
    public void doExamRecordInsert(Integer examRegion, String examId,ExamRecord examRecord) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRecordStringTable(examId));
        examRecordDao.execute(e -> e.insertInto(examRecordTable, examRecordTable.field("f_id", String.class),
                                                examRecordTable.field("f_member_id", String.class),
                                                examRecordTable.field("f_organization_id", String.class),
                                                examRecordTable.field("f_start_time", Long.class),
                                                examRecordTable.field("f_end_time", Long.class),
                                                examRecordTable.field("f_last_submit_time", Long.class),
                                                examRecordTable.field("f_score", Integer.class),
                                                examRecordTable.field("f_client_type", Integer.class),
                                                examRecordTable.field("f_status", Integer.class),
                                                examRecordTable.field("f_exam_id", String.class),
                                                examRecordTable.field("f_paper_instance_id", String.class),
                                                examRecordTable.field("f_exam_number", Integer.class),
                                                examRecordTable.field("f_create_time", Long.class),
                                                examRecordTable.field("f_submit_time", Long.class),
                                                examRecordTable.field("f_duration", Long.class),
                                                examRecordTable.field("f_is_reset", Integer.class),
                                                examRecordTable.field("f_is_current", Integer.class),
                                                examRecordTable.field("f_is_finished", Integer.class),
                                                examRecordTable.field("f_exception_order", Integer.class),
                                                examRecordTable.field("f_order_content", String.class),
                                                examRecordTable.field("f_exam_times", Integer.class),
                                                examRecordTable.field("f_switch_times", Integer.class),
                                                examRecordTable.field("f_personal_code", Integer.class),
                                                examRecordTable.field("f_user_ip", String.class),
                                                examRecordTable.field("f_no_answer_count", Integer.class),
                                                examRecordTable.field("f_answered_count", Integer.class),
                                                examRecordTable.field("f_client_version", String.class))
                                    .values(examRecord.getId(),
                                            examRecord.getMemberId(),
                                            examRecord.getOrganizationId(),
                                            examRecord.getStartTime(),
                                            examRecord.getEndTime(),
                                            examRecord.getLastSubmitTime(),
                                            examRecord.getScore(),
                                            examRecord.getClientType(),
                                            examRecord.getStatus(),
                                            examRecord.getExamId(),
                                            examRecord.getPaperInstanceId(),
                                            examRecord.getExamNumber(),
                                            examRecord.getCreateTime(),
                                            examRecord.getSubmitTime(),
                                            examRecord.getDuration(),
                                            examRecord.getIsReset(),
                                            examRecord.getIsCurrent(),
                                            examRecord.getIsFinished(),
                                            examRecord.getExceptionOrder(),
                                            examRecord.getOrderContent(),
                                            examRecord.getExamTimes(),
                                            examRecord.getSwitchTimes(),
                                            examRecord.getPersonalCode(),
                                            examRecord.getUserIp(),
                                            examRecord.getNoAnswerCount(),
                                            examRecord.getAnsweredCount(),
                                            examRecord.getClientVersion()).execute());

        messageSender.send(
                com.zxy.product.exam.content.MessageTypeContent.EXAM_EXAM_RECORD_INSERT,
                com.zxy.product.exam.content.MessageHeaderContent.IDS, examRecord.getMemberId(),
                com.zxy.product.exam.content.MessageHeaderContent.EXAM_ID, examId,
                com.zxy.product.exam.content.MessageHeaderContent.STATUS, String.valueOf(ExamRecord.STATUS_TO_BE_STARTED));
    }

    private Integer getType(Exam exam) {
        if (exam.getNeedApplicant() != null && exam.getNeedApplicant() == Exam.EXAM_YES) {
            return ExamRegist.TYPE_SIGN_UP;
        } else if (exam.getSendToCenter() != null && exam.getSendToCenter() == Exam.EXAM_YES) {
            return ExamRegist.TYPE_SPEC_EXAM;
        }
        return ExamRegist.TYPE_ORDINARY_EXAM;
    }

    private void doExamRecordInsertByApplicantExam(Integer examRegion, Exam exam, Integer registStatus, String memberIds) {
        String[] arr = memberIds.split(",");

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(exam.getId()));

        List<ExamRegist> examRegists = examRegistDao.execute(e -> e.select(examRegistTable.field("f_id", String.class),
                examRegistTable.field("f_member_id", String.class))
                .from(examRegistTable)
                .where(examRegistTable.field("f_exam_id", String.class).eq(exam.getId()),
                        examRegistTable.field("f_member_id", String.class).in(arr))
                .and(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_NO_BEGIN))
                .fetch(r -> {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                    examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                    return examRegist;
                }));

        String existedMemberIds = examRegists.stream().map(ExamRegist::getMemberId).collect(Collectors.joining(","));
        List<String> afterFilterExistedMemberIds = Arrays.stream(arr).filter(t -> {
            return existedMemberIds.indexOf(t) < 0;
        }).collect(Collectors.toList());
        List<ExamRegist> inserts = afterFilterExistedMemberIds.stream().map(memberId -> {
            ExamRegist examRegist = new ExamRegist();
            examRegist.forInsert();
            examRegist.setExamId(exam.getId());
            examRegist.setMemberId(memberId);
            examRegist.setExamTimes(0);
            examRegist.setStatus(registStatus);
            examRegist.setType(getType(exam));
            examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);
            return examRegist;
        }).collect(Collectors.toList());
        String mIds = inserts.stream().map(ExamRegist::getMemberId).collect(Collectors.joining(","));
        deleteFirstIfOneId(examRegion, mIds, exam.getId());

        Field<?> [] fileds = {
                examRegistTable.field("f_id", String.class),
                examRegistTable.field("f_top_score", Integer.class),
                examRegistTable.field("f_top_score_record_id", String.class),
                examRegistTable.field("f_exam_times", Integer.class),
                examRegistTable.field("f_member_id", String.class),
                examRegistTable.field("f_status", Integer.class),
                examRegistTable.field("f_exam_id", String.class),
                examRegistTable.field("f_pass_status", Integer.class),
                examRegistTable.field("f_certificate_issue", Integer.class),
                examRegistTable.field("f_create_time", Long.class),
                examRegistTable.field("f_type", Integer.class)
            };
        examRegistDao.execute(e->{
              InsertValuesStepN<?> step = e.insertInto(examRegistTable,fileds);
              inserts.forEach(examRegist->{
                  step.values(
                          examRegist.getId(), examRegist.getTopScore(),
                          examRegist.getTopScoreRecordId(),examRegist.getExamTimes(),
                          examRegist.getMemberId(),examRegist.getStatus(),
                          examRegist.getExamId(),examRegist.getPassStatus(),
                          examRegist.getCertificateIssue(),examRegist.getCreateTime(),
                          examRegist.getType()
                      );
              });
              return step.execute();
          });
    }

    @DataSource
    public void insertExamRegistByRecordDoing(Integer examRegion, String examId, String memberIds) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examDao.getOptional(examId).ifPresent(exam -> {
            Arrays.stream(memberIds.split(",")).forEach(memberId -> {
                Integer count = examRegistDao.execute(e -> e.select(DSL.count(examRegistTable.field("f_id", String.class)))
                        .from(examRegistTable).where(examRegistTable.field("f_member_id", String.class).eq(memberId),
                                examRegistTable.field("f_exam_id", String.class).eq(examId)).fetchOne(DSL.count(examRegistTable.field("f_id", String.class))));

                if (count == 0) {
                    ExamRegist examRegist = new ExamRegist();
                    examRegist.forInsert();
                    examRegist.setExamId(examId);
                    examRegist.setMemberId(memberId);
                    examRegist.setExamTimes(0);
                    examRegist.setStatus(ExamRegist.STATUS_EXAM_STARTING);
                    examRegist.setType(getType(exam));
                    examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);

                    deleteFirstIfOneId(examRegion, memberIds, examId);
                    examRegistDao.execute(e ->
                    e.insertInto(examRegistTable, examRegistTable.field("f_id", String.class),
                            examRegistTable.field("f_top_score", Integer.class),
                            examRegistTable.field("f_top_score_record_id", String.class),
                            examRegistTable.field("f_exam_times", Integer.class),
                            examRegistTable.field("f_member_id", String.class),
                            examRegistTable.field("f_status", Integer.class),
                            examRegistTable.field("f_exam_id", String.class),
                            examRegistTable.field("f_pass_status", Integer.class),
                            examRegistTable.field("f_certificate_issue", Integer.class),
                            examRegistTable.field("f_create_time", Long.class),
                            examRegistTable.field("f_type", Integer.class)
                            )
                            .values(
                                    examRegist.getId(), examRegist.getTopScore(),
                                    examRegist.getTopScoreRecordId(),examRegist.getExamTimes(),
                                    examRegist.getMemberId(),examRegist.getStatus(),
                                    examRegist.getExamId(),examRegist.getPassStatus(),
                                    examRegist.getCertificateIssue(),examRegist.getCreateTime(),
                                    examRegist.getType()
                                    )
                            .execute()
                    );
                }

            });

        });
    }

    @DataSource
    public void doSubmitPaper(Integer examRegion,ExamRecord.SubmitType submitType, String examRecordId, String submitPaperExamId) {
        if (submitType == ExamRecord.SubmitType.Hand) {

            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(submitPaperExamId));
            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(submitPaperExamId));

            List<ExamRecord> examRecordList = examRecordDao.execute(e ->
            e.select(Fields.start()
                .add(examRecordTable.fields())
                .end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
                ).fetch(r -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                    examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                    examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                    examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                    examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
                    examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                    examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                    examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
                    examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                    examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
                    examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
                    examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
                    examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                    examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
                    examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                    examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
                    examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
                    examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
                    examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
                    return examRecord;
                });
            if (examRecordList == null || examRecordList.size() == 0)
                return;
            ExamRecord examRecord = examRecordList.get(0);

            examRegistDao.execute(dslContext ->
                dslContext.update(examRegistTable)
                    .set(examRegistTable.field("f_status", Integer.class),ExamRegist.STATUS_FINISHED)
                    .set(examRegistTable.field("f_exam_times", Integer.class), examRegistTable.field("f_exam_times", Integer.class).add(1))
                .where(examRegistTable.field("f_exam_id", String.class).eq(examRecord.getExamId()))
                .and(examRegistTable.field("f_member_id", String.class).eq(examRecord.getMemberId()))
                .execute());

        }
    }


    @DataSource
    public void doExamRecordDeleted(Integer examRegion, String examId, String memberId) {
        //初始化exam-regist,只允许一次
//        initExamRegist();

        Integer type = examDao.execute(dsl ->
        dsl.select(Fields.start().add(EXAM.TYPE).end())
                .from(EXAM)
                .where(EXAM.ID.eq(examId))
                .fetchOne(EXAM.TYPE)
        );
        if (Exam.EXAM_CLOUD_TYPE.equals(type)) {
            cloudSignUpDao.fetchOne(CLOUD_SIGNUP.EXAM_ID.eq(examId), CLOUD_SIGNUP.MEMBER_ID.eq(memberId)).map(cloudSignup -> {
                updateRegistSignUpRefuse(examRegion, examId, memberId);
                return cloudSignup;
            }).orElseGet(() -> {
                deleteRegistSignUpCancel(examRegion, examId, memberId);
                return null;
            });
        }else if (Exam.EXAM_GRID_TYPE.equals(type)) {
            gridSignUpDao.fetchOne(GRID_SIGNUP.EXAM_ID.eq(examId), GRID_SIGNUP.MEMBER_ID.eq(memberId)).map(gridSignup -> {
                updateRegistSignUpRefuse(examRegion, examId, memberId);
                return gridSignup;
            }).orElseGet(() -> {
                deleteRegistSignUpCancel(examRegion, examId, memberId);
                return null;
            });
        }else {
            signUpDao.fetchOne(SIGNUP.EXAM_ID.eq(examId), SIGNUP.MEMBER_ID.eq(memberId)).map(signUp -> {
                updateRegistSignUpRefuse(examRegion, examId, memberId);
                return signUp;
            }).orElseGet(() -> {
                deleteRegistSignUpCancel(examRegion, examId, memberId);
                return null;
            });
        }
    }

    @DataSource
    public void doExamChangeToOrdinary(Integer examRegion, String examId) {
        examDao.getOptional(examId).ifPresent(exam -> {
            if (exam.getStartTime() != null && System.currentTimeMillis() > exam.getStartTime()) {
                updateRegistBeStarting(examRegion, examId);
            }
            if (exam.getStartTime() != null && System.currentTimeMillis() < exam.getStartTime()) {
                updateRegistBeNoBegin(examRegion, examId);
            }
        });
    }

    private void updateRegistBeNoBegin(Integer examRegion, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examRegistDao.execute(e -> e.update(examRegistTable).set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_NO_BEGIN)
                .where(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_APPROVING),examRegistTable.field("f_exam_id", String.class).eq(examId)).execute());
    }

    private void updateRegistBeStarting(Integer examRegion, String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        examRegistDao.execute(e -> e.update(examRegistTable).set(examRegistTable.field("f_status", Integer.class), ExamRegist.STATUS_EXAM_STARTING)
                .where(examRegistTable.field("f_status", Integer.class).eq(ExamRegist.STATUS_APPROVING),examRegistTable.field("f_exam_id", String.class).eq(examId)).execute());
    }
    @DataSource
    public void updateTopScore(Integer examRegion,String examRecordId, String examId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        List<String> list = examRecordDao.execute(e -> e.select(
                Fields.start()
                .add(examRecordTable.field("f_member_id", String.class)).end())
                .from(examRecordTable)
                .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
                .fetch(examRecordTable.field("f_member_id", String.class))
            );
        if (list == null || list.size() == 0)
            return;
        String memberId = list.get(0);

        List<ExamRecord> examRecordList = examRecordDao.execute(e ->
        e.select(Fields.start()
            .add(examRecordTable.fields())
            .end())
            .from(examRecordTable)
            .where(examRecordTable.field("f_exam_id", String.class).eq(examId))
            .and(examRecordTable.field("f_member_id", String.class).eq(memberId))
            ).fetch(r -> {
                ExamRecord examRecord = new ExamRecord();
                examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
                examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
                examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
                examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
                examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
                examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
                examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
                examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
                examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
                examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
                examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
                examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
                return examRecord;
        });
        if (examRecordList == null || examRecordList.size() == 0)
            return;
        ExamRecord examRecord = examRecordList.stream().filter(t -> t.getScore() != null).max(Comparator.comparingInt(ExamRecord ::getScore)).orElse(null);
        if (examRecord == null)
            return;

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        List<ExamRegist> examRegists = examRegistDao.execute(e ->
        e.select(Fields.start()
            .add(examRegistTable.fields())
            .end())
            .from(examRegistTable)
            .where(examRegistTable.field("f_exam_id", String.class).eq(examRecord.getExamId()))
            .and(examRegistTable.field("f_member_id", String.class).eq(examRecord.getMemberId()))
            ).fetch(r -> {
                ExamRegist examRegist = new ExamRegist();
                examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
                examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));
                examRegist.setCreateTime(r.getValue(examRegistTable.field("f_create_time", Long.class)));
                examRegist.setType(r.getValue(examRegistTable.field("f_type", Integer.class)));
                return examRegist;
            });

        if (examRegists == null || examRegists.size() == 0)
            return;
        if (examRegists.size() > 1) {
            List<String> examRegistIds = examRegistDao.execute(x -> x.select(
                    Fields.start()
                    .add(examRegistTable.field("f_id", String.class)).end())
                    .from(examRegistTable)
                    .where(examRegistTable.field("f_id", String.class).ne(examRegists.get(0).getId()))
                    .and(examRegistTable.field("f_exam_id", String.class).eq(examRecord.getExamId()))
                    .and(examRegistTable.field("f_member_id", String.class).eq(examRecord.getMemberId()))
                    .fetch(examRegistTable.field("f_id", String.class))
                );
            examRegistDao.execute(dslContext ->
            dslContext.delete(examRegistTable).where(
                    examRegistTable.field("f_id", String.class).in(examRegistIds)
                    ).execute());
            examRegistIds.forEach(id ->{
                                      messageSender.send(
                                              MessageTypeContent.DELETE_EXAM_DATA,
                                              MessageHeaderContent.ID, id,
                                              MessageHeaderContent.PARAMS, DeleteDataExam.EXAM_REGIST);
                                  });
        }

        compareTopScore(examRegion,examRegists.get(0), examRecord);

    }

    @DataSource
    private void compareTopScore(Integer examRegion,ExamRegist examRegist, ExamRecord examRecord) {
        if (examRegist != null) {
            examRegistDao.execute(e -> {
                // 及格状态：0及格 1不及格 2已完成
                // 已完成
                Integer registPassStatus = examRegist.getPassStatus() == null ? ExamRegist.PASS_STATUS_FINISH : examRegist.getPassStatus();
                if (ExamRecord.STATUS_PASS.equals(examRecord.getStatus())) {
                    // 及格
                    registPassStatus =  ExamRegist.PASS_STATUS_YES;
                } else if (!ExamRegist.PASS_STATUS_YES.equals(examRegist.getPassStatus())
                        && ExamRecord.STATUS_NOT_PASS.equals(examRecord.getStatus())) {
                    // 初次不及格
                    registPassStatus =  ExamRegist.PASS_STATUS_NO;
                }

                TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examRegist.getExamId()));

                return e.update(examRegistTable)
                        .set(examRegistTable.field("f_top_score", Integer.class), examRecord.getScore())
                        .set(examRegistTable.field("f_top_score_record_id", String.class), examRecord.getId())
                        .set(examRegistTable.field("f_pass_status", Integer.class), registPassStatus)
                        .where(examRegistTable.field("f_id", String.class).eq(examRegist.getId())).execute();
            });

        }
    }

    @DataSource
    public void doExamChangeToNoSpecify(Integer examRegion, String examId) {
        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<String> examRegistIds = examRegistDao.execute(e -> e.select(examRegistTable.field("f_id", String.class)).from(examRegistTable)
                .leftJoin(examRecordTable).on(examRegistTable.field("f_exam_id", String.class).eq(examRecordTable.field("f_exam_id", String.class)))
                .and(examRegistTable.field("f_member_id", String.class).eq(examRecordTable.field("f_member_id", String.class)))
                .and(examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT))
                .where(examRegistTable.field("f_exam_id", String.class).eq(examId), examRecordTable.field("f_id", String.class).isNull()).fetch(examRegistTable.field("f_id", String.class)));

        examRegistDao.execute(dslContext ->
        dslContext.delete(examRegistTable).where(
                examRegistTable.field("f_id", String.class).in(examRegistIds)
                ).execute());

        examRegistIds.forEach(id->
                messageSender.send(
                        MessageTypeContent.DELETE_EXAM_DATA,
                        MessageHeaderContent.ID,id,
                        MessageHeaderContent.PARAMS,DeleteDataExam.EXAM_REGIST)
        );
    }




}
