package com.zxy.product.examstu.async.task.exam.online;

import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.ExamOnlineService;
import com.zxy.product.examstu.content.DataSourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 限流：定时任务处理（考试在线人数）
 * <AUTHOR>
 * @date 2025年2月17日 16:00
 */
@Component
@EnableAsync
public class CleanOnlineTask {
    private static final Logger logger= LoggerFactory.getLogger(CleanOnlineTask.class);
    private ExamOnlineService examOnlineService;

    @Autowired
    public void setExamOnlineService(ExamOnlineService examOnlineService){ this.examOnlineService = examOnlineService; }

    @Async
    @Scheduled(cron = "0 0 1 * * *")
    @DataSource
    public void cleanCourseOnline(){ this.doCleanOnline(); }

    @DataSource
    public void doCleanOnline(){
        long startMarker = System.currentTimeMillis();
        logger.info("考试清理无效数据定时任务开始，开始时间{}", startMarker);
        examOnlineService.cleanExamOnline(DataSourceEnum.NORTH.getType());
        examOnlineService.cleanExamOnline(DataSourceEnum.SOUTH.getType());
        long endMarker = System.currentTimeMillis();
        logger.info("考试清理无效数据定时任务结束，结束时间{}，持续时间{}", endMarker , ( endMarker - startMarker ) );
    }
}
