package com.zxy.product.examstu.async.listener;

import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.api.ExamStuRecordService;
import com.zxy.product.examstu.api.MarkConfigService;
import com.zxy.product.examstu.api.ToDoService;
import com.zxy.product.examstu.async.helper.ToDoListenerHelper;
import com.zxy.product.examstu.content.DataSourceEnum;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.system.entity.MsgCountItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 *
 */
@Component
public class ToDoListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(ToDoListener.class);
    private ToDoService toDoService;
    private MessageSender messageSender;
    private MarkConfigService markConfigService;
	private ToDoListenerHelper toDoListenerHelper;
	private ExamStuRecordService examStuRecordService;

	@Autowired
	public void setToDoListenerHelper(ToDoListenerHelper toDoListenerHelper) {
		this.toDoListenerHelper = toDoListenerHelper;
	}

	@Autowired
    public void setMarkConfigService(MarkConfigService markConfigService) {
		this.markConfigService = markConfigService;
	}

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}


	@Autowired
	public void setToDoService(ToDoService toDoService) {
		this.toDoService = toDoService;
	}

	@Autowired
	public void setExamStuRecordService(ExamStuRecordService examStuRecordService) {
		this.examStuRecordService = examStuRecordService;
	}

	@Override
    protected void onMessage(Message message) {
        LOGGER.info("ToDoListener:" + message.toString());
    	Integer type = message.getType();

    	switch (type) {
		case MessageTypeContent.CREATE_TO_DO:
			String examRecordId = message.getHeader(MessageHeaderContent.ID);
			String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
			Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));
			createToDo(examRegion, examRecordId, examId);
			break;
		//消息红点待办计算
		case MessageTypeContent.EXAM_MARK_PAPER_INSERT:
			sendMessageForRedPoint(message);
			break;
		//更新评卷配置 重新算消息红点
		case MessageTypeContent.UPDATE_MARK_CONFIG:
			sendMessageForRedPoint(message);
			break;
		case MessageTypeContent.EXAM_STU_TODO_UPDATE:
			List<ToDo> updateToDos = getParams(message);
			if (CollectionUtils.isEmpty(updateToDos)){
				return;
			}
			toDosUpdate(updateToDos);
			break;
		case MessageTypeContent.EXAM_STU_TODO_DELETE:
			List<ToDo> deleteToDos = getParams(message);
			if (CollectionUtils.isEmpty(deleteToDos)){
				return;
			}
			toDosDelete(deleteToDos);
			break;
		case MessageTypeContent.EXAM_STU_TODO_UPDATE_ENTITY:
			List<ToDo> params = getParams(message);
			if (CollectionUtils.isEmpty(params)) {
				return;
			}
			update(params);
			break;
		case  MessageTypeContent.EXAM_STU_TODO_UPDATE_AUDITED:
			String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
			String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);

			List<String> southMemberIds = toDoListenerHelper.memberBelongSouth(Collections.singletonList(memberId));

			if (!CollectionUtils.isEmpty(southMemberIds)) {
				toDoService.updateAuditTime(DataSourceEnum.SOUTH.getType(), businessId, memberId);
			} else {
				toDoService.updateAuditTime(DataSourceEnum.NORTH.getType(), businessId, memberId);
			}
		default:
			break;
		}

    }

	private void update(List<ToDo> toDoList ) {
		Map<String, List<ToDo>> memberIdsToToDoMap = getToDoMap(toDoList);

		// 获取 southMemberIds
		List<String> memberIds = new ArrayList<>(memberIdsToToDoMap.keySet());
		List<String> southMemberIds = toDoListenerHelper.memberBelongSouth(memberIds);

		// 获取包含在 southMemberIds 中的 to_do 列表
		List<ToDo> southMemberTodos = getSouthToDoList(memberIdsToToDoMap, southMemberIds);

		// 获取North 中的 to_do 列表
		List<ToDo> northMemberTodos = getNorthToDoList(memberIdsToToDoMap, southMemberIds);

		if (!CollectionUtils.isEmpty(northMemberTodos)){
			toDoService.update(DataSourceEnum.NORTH.getType(), northMemberTodos);
		}

		if (!CollectionUtils.isEmpty(southMemberTodos)){
			toDoService.update(DataSourceEnum.SOUTH.getType(), northMemberTodos);
		}
	}

	private List<ToDo> getParams(Message message) {
		String updateToDoParams = message.getHeader(MessageHeaderContent.PARAMS);
		if (updateToDoParams.startsWith("[")) {
			return JSONObject.parseArray(updateToDoParams, ToDo.class);
		} else if (updateToDoParams.startsWith("{")) {
			ToDo toDo = JSONObject.parseObject(updateToDoParams, ToDo.class);
			return Collections.singletonList(toDo);
		}else{
			return Collections.EMPTY_LIST;
		}
	}

	private void sendMessageForRedPoint(Message message) {
    	String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
    	List<MarkConfig> markConfis = markConfigService.find(Exam.NORTH, examId);
    	List<String> receiverIds = markConfis.stream().map(MarkConfig::getMemberId).collect(Collectors.toSet()).stream().collect(Collectors.toList());
    	sendMessageForRedPoint(calculateTodoAmount(receiverIds));
	}

	public void createToDo(Integer examRegion,String examRecordId,String examId) {
		toDoListenerHelper.createToDo(examRegion, examRecordId, examId);
	}


	/**
	 * 发送消息红点消息
	 * @param toToAmounts
	 */
	private void sendMessageForRedPoint(List<Map<String, Object>> toToAmounts) {
		toToAmounts.forEach(t -> {
			if (t.get("count") != null) {
				messageSender.send(com.zxy.product.system.content.MessageTypeContent.MSG_COUNT_ITEM,
						com.zxy.product.system.content.MessageHeaderContent.MEMBER_ID, String.valueOf(t.get("memberId")),
						com.zxy.product.system.content.MessageHeaderContent.BUSINESS_TYPE, MsgCountItem.TYPE_MARK_PAPER + "",
						com.zxy.product.system.content.MessageHeaderContent.COUNT, String.valueOf(t.get("count")));
			}
		});

	}

	/**
	 * 统计消息红点
	 * @param receiverIds
	 * @return
	 */
	private List<Map<String, Object>> calculateTodoAmount(List<String> receiverIds) {
		List<ToDo> toDos = toDoService.findNoFinishedToDoByMemberIds(Exam.NORTH, receiverIds);
		toDos.addAll(toDoService.findNoFinishedToDoByMemberIds(Exam.SOURCE, receiverIds));
		Map<String, List<ToDo>> map = getToDoMap(toDos);
		return receiverIds.stream().map(id -> {
			Map<String, Object> item = new HashMap<>();
			if (map.get(id) == null) {
				item.put("memberId", id);
				item.put("count", 0);
			} else {
				item.put("memberId", id);
				item.put("count", map.get(id).size());
			}
			return item;
		}).collect(Collectors.toList());

	}
	
	private void toDosUpdate(List<ToDo> toDos){

		Map<String, List<ToDo>> memberIdsToToDoMap = getToDoMap(toDos);

		// 获取 southMemberIds
		List<String> memberIds = new ArrayList<>(memberIdsToToDoMap.keySet());
		List<String> southMemberIds = toDoListenerHelper.memberBelongSouth(memberIds);

		// 获取包含在 southMemberIds 中的 to_do 列表
		List<ToDo> southMemberTodos = getSouthToDoList(memberIdsToToDoMap, southMemberIds);

		// 获取North 中的 to_do 列表
		List<ToDo> northMemberTodos = getNorthToDoList(memberIdsToToDoMap, southMemberIds);

		if (!CollectionUtils.isEmpty(northMemberTodos)){
			toDoService.updateToDo(DataSourceEnum.NORTH.getType(), northMemberTodos);
		}

		if (!CollectionUtils.isEmpty(southMemberTodos)){
			toDoService.updateToDo(DataSourceEnum.SOUTH.getType(), northMemberTodos);
		}
	}

	private void updateEntity(List<ToDo> toDos){
		Map<String, List<ToDo>> memberIdsToToDoMap = getToDoMap(toDos);

		// 获取 southMemberIds
		List<String> memberIds = new ArrayList<>(memberIdsToToDoMap.keySet());
		List<String> southMemberIds = toDoListenerHelper.memberBelongSouth(memberIds);

		// 获取包含在 southMemberIds 中的 to_do 列表
		List<ToDo> southMemberTodos = getSouthToDoList(memberIdsToToDoMap, southMemberIds);

		// 获取North 中的 to_do 列表
		List<ToDo> northMemberTodos = getNorthToDoList(memberIdsToToDoMap, southMemberIds);

		if (!CollectionUtils.isEmpty(northMemberTodos)){
			toDoService.updateToDo(DataSourceEnum.NORTH.getType(), northMemberTodos);
		}
	};

	private void toDosDelete(List<ToDo> toDos){

		Map<String, List<ToDo>> memberIdsToToDoMap = getToDoMap(toDos);

		// 获取 southMemberIds
		List<String> memberIds = new ArrayList<>(memberIdsToToDoMap.keySet());
		List<String> southMemberIds = toDoListenerHelper.memberBelongSouth(memberIds);

		// 获取包含在 southMemberIds 中的 to_do 列表
		List<ToDo> southMemberTodos = getSouthToDoList(memberIdsToToDoMap, southMemberIds);

		// 获取North 中的 to_do 列表
		List<ToDo> northMemberTodos = getNorthToDoList(memberIdsToToDoMap, southMemberIds);

		if (!CollectionUtils.isEmpty(northMemberTodos)){
			toDoService.updateToDo(DataSourceEnum.NORTH.getType(), northMemberTodos);
		}

		if (!CollectionUtils.isEmpty(southMemberTodos)){
			toDoService.updateToDo(DataSourceEnum.SOUTH.getType(), northMemberTodos);
		}
	}

	private Map<String, List<ToDo>> getToDoMap(List<ToDo> toDos) {

		Map<String, List<ToDo>> memberIdsToToDoMap = toDos.stream().collect(Collectors.groupingBy(ToDo::getMemberId));

		return memberIdsToToDoMap;
	}

	private List<ToDo> getSouthToDoList(Map<String, List<ToDo>> memberIdsToToDoMap, List<String> southMemberIds) {
		return memberIdsToToDoMap.entrySet().stream()
								 .filter(entry -> southMemberIds.contains(entry.getKey()))
								 .flatMap(entry -> entry.getValue().stream())
								 .collect(Collectors.toList());
	}

	private List<ToDo> getNorthToDoList(Map<String, List<ToDo>> memberIdsToToDoMap, List<String> southMemberIds) {
		return memberIdsToToDoMap.entrySet().stream()
								 .filter(entry -> !southMemberIds.contains(entry.getKey()))
								 .flatMap(entry -> entry.getValue().stream())
								 .collect(Collectors.toList());
	}

	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.CREATE_TO_DO,
			MessageTypeContent.EXAM_MARK_PAPER_INSERT,
			MessageTypeContent.UPDATE_MARK_CONFIG,
			MessageTypeContent.EXAM_STU_TODO_UPDATE,
			MessageTypeContent.EXAM_STU_TODO_DELETE,
			MessageTypeContent.EXAM_STU_TODO_UPDATE_ENTITY,
			MessageTypeContent.EXAM_STU_TODO_UPDATE_AUDITED

		};
	}


}
