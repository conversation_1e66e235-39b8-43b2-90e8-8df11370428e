package com.zxy.product.examstu.async.helper;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.GenseeWebCastService;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.GenseeWebCast;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.examstu.api.ExamNoticeService;
import com.zxy.product.examstu.api.ToDoService;
import com.zxy.product.examstu.async.util.GetTableUtil;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.entity.ClassInfo;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

@Component
public class ToDoListenerHelper {

    public final static String ORGID = "organizationId";
    public final static String CONTENT_PARARMS = "contentParams";
    private CommonDao<Exam> examDao;
    private CommonDao<ExamRecord> examRecordDao;
    private AnswerRecordService answerRecordService;
    private MessageSender messageSender;
    private ToDoService toDoService;
    private ExamNoticeService examNoticeService;
    private GenseeWebCastService genseeWebCastService;
    private CourseInfoService courseinfoService;
    private ClassInfoService classInfoService;
    private MemberService memberService;
    private GetTableUtil getTableUtil;


    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }


    @Autowired
    public void setGenseeWebCastService(GenseeWebCastService genseeWebCastService) {
        this.genseeWebCastService = genseeWebCastService;
    }

    @Autowired
    public void setCourseinfoService(CourseInfoService courseinfoService) {
        this.courseinfoService = courseinfoService;
    }

    @Autowired
    public void setExamNoticeService(ExamNoticeService examNoticeService) {
        this.examNoticeService = examNoticeService;
    }


    @Autowired
    public void setToDoService(ToDoService toDoService) {
        this.toDoService = toDoService;
    }


    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }


    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @DataSource
    public void createToDo(Integer examRegion,String examRecordId,String examId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        List<ExamRecord> examRecordList = examRecordDao.execute(e ->
                e.select(Fields.start()
                                .add(examRecordTable.fields())
                                .end())
                        .from(examRecordTable)
                        .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
        ).fetch(r -> {
            ExamRecord examRecord = new ExamRecord();
            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
            examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
            examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
            examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
            examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
            examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
            examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
            examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
            examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
            examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
            examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
            examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
            examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
            examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
            examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
            examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
            examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
            examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
            examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
            examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
            examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
            examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
            examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
            examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
            examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
            return examRecord;
        });
        if (examRecordList == null || examRecordList.size() == 0)
            return;
        ExamRecord examRecord = examRecordList.get(0);

        examDao.getOptional(examRecord.getExamId()).map(exam -> {
            List<AnswerRecord> answerRecordList = answerRecordService.findIncludeQuestionByExamRecordId(examRegion,examRecordId, examRecord.getExamId());
            insertMarkPaperToDo(examRegion,examRecord, exam, answerRecordList.stream().filter(a ->
                    (a != null && a.getQuestionCopy() != null && a.getQuestionCopy().getType() != null && a.getQuestionCopy().getType() == Question.SENTENCE_COMPLETION) ||
                            (a != null && a.getQuestionCopy() != null && a.getQuestionCopy().getType() != null &&a.getQuestionCopy().getType() == Question.QUESTION_ANWSER)
            ).collect(Collectors.toList()));
            return null;
        });

    }

    /**
     * 阅卷待办
     * a.生成待办记录
     * b.发送评卷通知
     * c.发送待办消息红点统计
     * @param
     */
    private void insertMarkPaperToDo(Integer examRegion,ExamRecord examRecord, Exam exam, List<AnswerRecord> answerRecordList) {

        List<MarkConfig> markConfis = findMarkConfigs(examRecord.getExamId());
        List<String> receiverIds = markConfis.stream().map(MarkConfig::getMemberId).collect(Collectors.toSet()).stream().collect(Collectors.toList());

        Map<String,Integer> memberMap = new HashMap<String, Integer>();

        //过滤掉不需要推送待办的老师
        List<String> afterFilteredNoNeedPushingTearchIds = filterNoNeedPushing(receiverIds, answerRecordList, markConfis, memberMap);

        if (markConfis != null && markConfis.size() > 0 && afterFilteredNoNeedPushingTearchIds.size() > 0) {

            toDoService.insert(examRegion,
                    examRecord.getId(),
                    afterFilteredNoNeedPushingTearchIds,
                    ToDo.MARK_PAPER_TYPE,
                    memberMap
            );

            Map<String, Object> map = getModuleInfo(exam);
            //发消息到管理端处理examNotice的插入
            examNoticeService.insert(
                    ExamNotice.BUSINESS_TYPE_EXAM,
                    examRecord.getId(),
                    ExamNotice.TYPE_MARK_PAPER,
                    String.valueOf(map == null ? "" : map.get(ORGID)),
                    getTemplateCode(exam),
                    Optional.ofNullable(map == null ? null : (String[])map.get(CONTENT_PARARMS)),
                    Optional.ofNullable(afterFilteredNoNeedPushingTearchIds.stream().collect(Collectors.joining(",")))
            );
            // 发送消息红点，每个老师待办评卷的数量
            // 性能测试 暂时注释
            messageSender.send(MessageTypeContent.UPDATE_MARK_CONFIG,
                    MessageHeaderContent.EXAM_ID, examRecord.getExamId());
        }
    }

    /**
     * 根据考试id查询评卷老师
     * @param examId
     * @return
     */
    private List<MarkConfig> findMarkConfigs(String examId) {
        List<MarkConfig> markConfigs = examRecordDao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(MARK_CONFIG.ID)
                                        .add(MARK_CONFIG.MEMBER_ID)
                                        .add(MARK_CONFIG.TYPE)
                                        .add(MARK_CONFIG.TYPE_ID)
                                        .add(QUESTION.ID)
                                        .add(QUESTION.TYPE)
                                        .end()
                        )
                        .from(MARK_CONFIG)
                        .leftJoin(QUESTION).on(QUESTION.ID.eq(MARK_CONFIG.TYPE_ID))
                        .where(MARK_CONFIG.EXAM_ID.eq(examId)).fetch(r -> {
                            MarkConfig markConfig = r.into(MarkConfig.class);
                            Question question = r.into(Question.class);
                            markConfig.setQuestion(question);
                            return markConfig;
                        })
        );
        List<MarkConfig> readingMarkConfigs = markConfigs.stream().filter(m -> {
            if (m.getQuestion() != null) {
                return m.getQuestion().getType() == Question.READING_COMPREHENSION;
            }
            return false;
        }).collect(Collectors.toList());

        List<String> parentIds = readingMarkConfigs.stream().map(m -> m.getQuestion().getId()).collect(Collectors.toList());
        List<Question> subs = examRecordDao.execute(e -> e.select(QUESTION.ID,QUESTION.PARENT_ID).from(QUESTION).where(QUESTION.PARENT_ID.in(parentIds)).fetchInto(Question.class));
        Map<String, List<Question>> subMaps = subs.stream().collect(Collectors.groupingBy(Question::getParentId));

        markConfigs.forEach(m -> {
            if (m.getQuestion() != null && subMaps.get(m.getTypeId()) != null) {
                m.getQuestion().setSubs(subMaps.get(m.getTypeId()));
            }
        });
        return markConfigs;
    }

    /**
     * 过滤那些不需要评卷的待办
     * ##如果该老师对应的题目都未答就不需要评卷
     * @param receiverIds
     * @param answerRecordList
     * @param markConfis
     * @param map
     * @return
     */
    private List<String> filterNoNeedPushing(List<String> receiverIds,  List<AnswerRecord> answerRecordList, List<MarkConfig> markConfis, Map<String, Integer> map) {
        return receiverIds.stream().filter(r -> {
            return checkQuestionNeedToMark(answerRecordList, markConfis, r, map);
        }).collect(Collectors.toList());
    }

    private boolean checkQuestionNeedToMark(List<AnswerRecord> answerRecordList, List<MarkConfig> markConfis, String memberId, Map<String, Integer> map) {
        List<AnswerRecord> answerRecords = getAnswerRecordByMarkConfig(answerRecordList, markConfis, memberId, map);
        if (answerRecords != null && answerRecords.size() > 0) {
            return answerRecords.stream().filter(a -> a.getAnswer() != null && !a.getAnswer().equals("")).collect(Collectors.toList()).size() > 0;
        }
        return false;
    }

    private List<AnswerRecord> getAnswerRecordByMarkConfig(List<AnswerRecord> answerRecordList, List<MarkConfig> markConfis, String memberId, Map<String, Integer> map) {
        if (markConfis != null && markConfis.size() > 0) {

            Integer type = markConfis.get(0).getType();

            String typeIds = markConfis.stream().filter(m -> memberId.equals(m.getMemberId()))
                    .map(MarkConfig::getTypeId).collect(Collectors.joining(","));

            List<MarkConfig> memberMarkConfigs = markConfis.stream().filter(m -> m.getMemberId().equals(memberId))
                    .collect(Collectors.toList());

            switch (type) {
                case MarkConfig.PAPER:
                    // 填空题的数量
                    List<AnswerRecord> sentenceCompletionList = answerRecordList.stream().filter(a -> (a != null && a.getQuestionCopy() != null && a.getQuestionCopy().getType() != null && a.getQuestionCopy().getType() == Question.SENTENCE_COMPLETION)).collect(Collectors.toList());
                    // 问答题的数量
                    List<AnswerRecord> questionAnwserList = answerRecordList.stream().filter(a -> (a != null && a.getQuestionCopy() != null && a.getQuestionCopy().getType() != null && a.getQuestionCopy().getType() == Question.QUESTION_ANWSER)).collect(Collectors.toList());

                    if (sentenceCompletionList != null && sentenceCompletionList.size() > 0 && sentenceCompletionList.size() == answerRecordList.size()) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_1);
                    } else if (questionAnwserList != null && questionAnwserList.size() > 0 && questionAnwserList.size() == answerRecordList.size()) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_0);
                    } else {
                        map.put(memberId, ToDo.INCLUDE_TYPE_2);
                    }
                    return answerRecordList;

                case MarkConfig.QUESTION_TYPE:
                    if (typeIds.indexOf(String.valueOf(Question.SENTENCE_COMPLETION)) > -1 && typeIds.indexOf(String.valueOf(Question.QUESTION_ANWSER)) > -1) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_2);
                    } else if (typeIds.indexOf(String.valueOf(Question.SENTENCE_COMPLETION)) > -1 && typeIds.indexOf(String.valueOf(Question.QUESTION_ANWSER)) == -1) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_1);
                    } else {
                        map.put(memberId, ToDo.INCLUDE_TYPE_0);
                    }
                    return answerRecordList.stream().filter(a -> {
                        if (a.getQuestionCopy().getParentId() != null) {
                            return typeIds.indexOf(String.valueOf(Question.READING_COMPREHENSION)) > -1;
                        }
                        return typeIds.indexOf(String.valueOf(a.getQuestionCopy().getType())) > -1;
                    }).collect(Collectors.toList());

                case MarkConfig.QUESTION:
                    boolean sentenceCompletionFlag = false;
                    boolean questionAnswerFlag = false;

                    List<AnswerRecord> answerRecordlist = answerRecordList.stream().filter(a -> {
                        if (a.getQuestionCopy().getParentId() != null) {
                            return readingMarkConfigChecking(a, memberMarkConfigs);
                        }
                        return typeIds.indexOf(String.valueOf(a.getQuestionCopy().getQuestion().getId())) > -1;
                    }).collect(Collectors.toList());

                    for (AnswerRecord a : answerRecordlist) {
                        if (sentenceCompletionFlag == false && Question.SENTENCE_COMPLETION == a.getQuestionCopy().getType()) {
                            sentenceCompletionFlag = true;
                        }
                        if (questionAnswerFlag == false && Question.QUESTION_ANWSER == a.getQuestionCopy().getType()) {
                            questionAnswerFlag = true;
                        }
                    }

                    if (sentenceCompletionFlag && questionAnswerFlag) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_2);
                    } else if (sentenceCompletionFlag && !questionAnswerFlag) {
                        map.put(memberId, ToDo.INCLUDE_TYPE_1);
                    } else {
                        map.put(memberId, ToDo.INCLUDE_TYPE_0);
                    }
                    return answerRecordlist;
                default:
                    break;
            }
        }
        return new ArrayList<>();
    }

    private boolean readingMarkConfigChecking(AnswerRecord answerRecord, List<MarkConfig> memberMarkConfigs) {
        String subQuestionId = answerRecord.getQuestionCopy().getQuestion().getId();

        return memberMarkConfigs.stream().filter(m -> {
            List<Question> subs = m.getQuestion().getSubs();
            if (subs != null && subs.size() > 0) {
                return subs.stream().map(Question::getId).collect(Collectors.joining(",")).indexOf(subQuestionId) > -1;
            }
            return false;
        }).collect(Collectors.toList()).size() > 0;
    }

    /**
     * 各个模块的业务信息(通知模板)
     * @param exam
     * @return
     */
    private Map<String, Object> getModuleInfo(Exam exam) {
        if (exam.getSourceType() == Exam.EXAM_ACTIVITY_SOURCE_TYPE) {

            return ImmutableMap.of("organizationId", exam.getOrganizationId(), "contentParams", new String[]{exam.getName()});

        } else if (exam.getSourceType() == Exam.EXAM_COURSE_SOURCE_TYPE) {

            Optional<CourseInfo> optional = courseinfoService.getCourseBasicByExamId(exam.getId());
            if (optional.isPresent()) return ImmutableMap.of(ORGID, optional.get().getOrganizationId(), CONTENT_PARARMS, new String[]{optional.get().getName()});
            return ImmutableMap.of(ORGID, "1", CONTENT_PARARMS, new String[]{"课程"});

        } else if (exam.getSourceType() == Exam.EXAM_SUBJECT_SOURCE_TYPE) {

            Optional<CourseInfo> optional = courseinfoService.getCourseBasicByExamId(exam.getId());
            if (optional.isPresent()) return ImmutableMap.of(ORGID, optional.get().getOrganizationId(), CONTENT_PARARMS, new String[]{optional.get().getName()});
            return ImmutableMap.of(ORGID, "1", CONTENT_PARARMS, new String[]{"专题"});

        } else if (exam.getSourceType() == Exam.EXAM_LIVE_SOURCE_TYPE) {

            Optional<GenseeWebCast> optional = genseeWebCastService.findGenseeByExamId(exam.getId());
            if (optional.isPresent()) return ImmutableMap.of(ORGID, optional.get().getOrganizationId(), CONTENT_PARARMS, new String[]{optional.get().getSubject()});
            return ImmutableMap.of(ORGID, "1", CONTENT_PARARMS, new String[]{"直播"});

        } else if (exam.getSourceType() == Exam.EXAM_CLASS_SOURCE_TYPE) {
            Optional<ClassInfo> optional = classInfoService.getClassInfoByExamId(exam.getId());
            if (optional.isPresent()) return ImmutableMap.of(ORGID, optional.get().getOrganizationId(), CONTENT_PARARMS, new String[]{optional.get().getClassName()});
            return ImmutableMap.of(ORGID, "1", CONTENT_PARARMS, new String[]{"班级"});
        }
        return null;
    }

    public List<String> memberBelongSouth(List<String> memberId){
        return memberService.findSouthMemberIds(memberId);
    }

    private String getTemplateCode(Exam exam) {
        Map<Integer, String> map = ImmutableMap.of(
                Exam.EXAM_ACTIVITY_SOURCE_TYPE,
                com.zxy.product.system.content.MessageConstant.EXAM_EVALUATION_DEL,
                Exam.EXAM_COURSE_SOURCE_TYPE,
                com.zxy.product.system.content.MessageConstant.COURSE_EXAM_EVALUATION,
                Exam.EXAM_SUBJECT_SOURCE_TYPE,
                com.zxy.product.system.content.MessageConstant.SUBJECT_EXAM_SUBMIT,
                Exam.EXAM_LIVE_SOURCE_TYPE,
                com.zxy.product.system.content.MessageConstant.LIVE_EXAM_DEAL,
                Exam.EXAM_CLASS_SOURCE_TYPE,
                com.zxy.product.system.content.MessageConstant.CLASS_EVALUATION
        );
        return map.get(exam.getSourceType());
    }

}
