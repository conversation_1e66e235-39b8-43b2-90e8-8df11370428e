package com.zxy.product.examstu.async.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.async.helper.AnswerRecordListenerHelper;
import com.zxy.product.examstu.content.MessageConstant;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.human.api.TaskService;
import com.zxy.product.human.entity.Task;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.entity.PointRule;
import com.zxy.product.system.entity.RuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.exam.entity.Exam.CACHE_TIME;
import static com.zxy.product.exam.jooq.Tables.EXAM;
import static com.zxy.product.exam.jooq.Tables.PAPER_CLASS;

/**
 * <AUTHOR>
 *
 */
@Component
public class AnswerRecordStuListener extends AbstractMessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(AnswerRecordStuListener.class);

	private static final Integer PER_SIZE = 1000;

	private static final String RIGHT_ANSWER_CACHE = String.join("#", "exam-stu","paper", "right-answer-cache");

	private ExamStuRecordService examStuRecordService;

	private AnswerRecordService answerRecordService;

	private QuestionCopyStuService questionCopyStuService;

	private TransactionTemplate transactionTemplate;

	private Redis redis;

	private MessageSender messageSender;

	private AnswerRecordStuService answerRecordStuService;

	private Cache examPaperCache;

	private CommonDao<Exam> examDao;

	private TaskService taskService;

	private RuleConfigService ruleConfigService;

	private CommonDao<Organization> organizationDao;

	private ExamService examService;

	private ExamNoticeService examNoticeService;

	private Cache examPaperRightAnswerCache;

	private AnswerRecordListenerHelper answerRecordListenerHelper;


	@Autowired
	public void setAnswerRecordListenerHelper(AnswerRecordListenerHelper answerRecordListenerHelper) {
		this.answerRecordListenerHelper = answerRecordListenerHelper;
	}

	@Autowired
	public void setExamNoticeService(ExamNoticeService examNoticeService) {
		this.examNoticeService = examNoticeService;
	}

	@Autowired
	public void setExamStuRecordService(ExamStuRecordService examStuRecordService) {
		this.examStuRecordService = examStuRecordService;
	}

	@Autowired
	public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
		this.transactionTemplate = transactionTemplate;
	}

	@Autowired
	public void setAnswerRecordService(AnswerRecordService answerRecordService) {
		this.answerRecordService = answerRecordService;
	}

	@Autowired
	public void setRedis(Redis redis) {
		this.redis = redis;
	}

	@Autowired
	public void setQuestionCopyStuService(QuestionCopyStuService questionCopyStuService) {
		this.questionCopyStuService = questionCopyStuService;
	}

	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

	@Autowired
	public void setAnswerRecordStuService(AnswerRecordStuService answerRecordStuService) {
		this.answerRecordStuService = answerRecordStuService;
	}

	@Autowired
	public void setExamDao(CommonDao<Exam> examDao) {
		this.examDao = examDao;
	}

	@Autowired
	public void setTaskService(TaskService taskService) {
		this.taskService = taskService;
	}

	@Autowired
	public void setRuleConfigService(RuleConfigService ruleConfigService) {
		this.ruleConfigService = ruleConfigService;
	}


	@Autowired
	public void setOrganizationDao(CommonDao<Organization> organizationDao) {
		this.organizationDao = organizationDao;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.examPaperCache = cacheService.create("cacheExamPaper", "examEntity" + "#" + "paperClassEntity");
		this.examPaperRightAnswerCache = cacheService.create("exam-stu", "paper#" + "right-answer-cache");
	}

	@Autowired
	public void setExamService(ExamService examService) {
		this.examService = examService;
	}


	@Override
    protected void onMessage(Message message) {

		LOGGER.info("AnswerRecordStuListener/:{}", message);

    	Integer type = message.getType();

    	switch (type) {
    	//计算试卷得分
		case MessageTypeContent.EXAM_ANSWER_RECORD_UPDATE_STU:
			String examRecordId = message.getHeader(MessageHeaderContent.ID);
			String examId = message.getHeader(MessageHeaderContent.EXAM_ID);
			String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
			Integer examRegion = Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION));

			doAnswerRecordCounting(examRegion, examRecordId, examId, memberId);
			break;
		default:
			break;
		}
    }

	@DataSource
	private void doAnswerRecordCounting(Integer examRegion,String examRecordId, String examId,String memberId) {
		List<String> shouldDeleteAnswerIds = new ArrayList<>();
		List<AnswerRecord> updateList = new ArrayList<>();

		List<AnswerRecord> answerRecordList = answerRecordService.findIncludeQuestionByExamRecordId(examRegion,examRecordId, examId);

		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus status) {
				if (answerRecordList != null) {
					ExamRecord examRecord = examStuRecordService.getExamRecord(examRegion, examRecordId, examId);

					if (examRecord != null) {

						Map<String, QuestionCopy> rightAnswer = getRightAnswer(examRegion, memberId, examId);
						//删除重复的答题记录和其他的答题记录
						answerRecordListenerHelper.deleteRepeatAnswers(examRegion, examRecordId, answerRecordList, examId);

						List<AnswerRecord> answerRecords = answerRecordService.findIncludeQuestionByExamRecordId(examRegion,examRecordId, examId);

						answerRecords.stream().forEach(answerRecord -> {
							if (rightAnswer.containsKey(answerRecord.getQuestionId())) {
								QuestionCopy questionCopy = rightAnswer.get(answerRecord.getQuestionId());

								if (questionCopy != null && questionCopy.getType() != Question.QUESTION_ANWSER
										&& questionCopy.getType() != Question.READING_COMPREHENSION) {
									List<QuestionAttrCopy> questionAttrCopys = questionCopy.getQuestionAttrCopys();

									answerIsRight(answerRecord, questionAttrCopys, questionCopy);
									updateList.add(answerRecord);
									sendMessage(answerRecord);
								} else if (questionCopy != null &&
										Question.READING_COMPREHENSION.equals(questionCopy.getType())) {
									shouldDeleteAnswerIds.add(answerRecord.getId());
									answerRecord.setScore(AnswerRecord.ZERO_SCORE);
									updateList.add(answerRecord);
								} else if (StringUtils.isEmpty(answerRecord.getAnswer())) {
									answerRecord.setScore(AnswerRecord.ZERO_SCORE);
									updateList.add(answerRecord);
								}
							}
						});

						answerRecordStuService.batchUpdateAnswerRecord(examRegion,updateList,examId);

						Exam exam = getExamFromCacheOrDB(examRegion, examRecord.getExamId());

						// 更新任务数据
						updateTaskBySubmitPaper(examRegion,examRecordId,examRecord.getExamId());

						//试卷更新后发送更新考试学习计划
						sendExamStudyPlan(examRecord.getExamId(),examRecord.getMemberId(),examRecord.getSubmitTime());

						answerRecordStuService.deleteReadingAnswer(examRegion,shouldDeleteAnswerIds,examId);
						//判断是否需要评卷
						boolean noNeedMark = getNoNeedMark(answerRecords, updateList);

						boolean haveSentenceCompletion = getHaveSentenceCompletion(answerRecords);

						if (exam != null && examRecord.getPaperInstanceId() != null) {

							// 查询该考生作答了多少道有parentId（阅读理解）的题目，按照parentId查询，能保证只要作答了阅读理解中的一道子题就算这个阅读理解题为已作答
							long parentCount = answerRecords.stream().map(AnswerRecord::getQuestionCopyParentId).filter(questionCopyParentId -> !StringUtils.isEmpty(questionCopyParentId)).distinct().count();
							// 查询该考生作答了除了parentId（阅读理解）以外的其他题目数量
							long noParentCount = answerRecords.stream().filter(answerRecord -> StringUtils.isEmpty(answerRecord.getQuestionCopyParentId())).count();
							// 已答题数
							int answeredCount = Math.toIntExact(parentCount + noParentCount);
							// 试卷的试题总数
							int questionNum = exam.getPaperClass().getQuestionNum() == null ? 0 : exam.getPaperClass().getQuestionNum();
							// 未答题数
							int noAnswerCount = Math.max(0, questionNum - answeredCount);

							examRecord.setAnsweredCount(answeredCount);
							examRecord.setNoAnswerCount(noAnswerCount);

							int rightCount = (int) updateList.stream()
									.filter(answerRecord -> AnswerRecord.RIGHT.equals(answerRecord.getIsRight())).count();

							examRecord.setRightCount(rightCount);
							confirmExamRecord(examRegion,examRecord, exam, noNeedMark);
							//创建待办
							if (!noNeedMark || haveSentenceCompletion) {
								messageSender.send(MessageTypeContent.CREATE_TO_DO,
												   MessageHeaderContent.ID, examRecord.getId(),
												   MessageHeaderContent.EXAM_ID, examId,
										           MessageHeaderContent.EXAM_REGION, String.valueOf(examRegion));
							}
						}
					}
				}
			}
		});
	}

	private void sendMessage(AnswerRecord answerRecord) {
		if (!StringUtils.isEmpty(answerRecord.getQuestionId())){
			saveQuestion(answerRecord.getQuestionId()+","+ answerRecord.getIsRight());
		}
	}

	private void answerIsRight(AnswerRecord answerRecord, List<QuestionAttrCopy> questionAttrCopys, QuestionCopy questionCopy) {
		boolean isRight = questionCopyStuService.compareAnswerInfo(questionAttrCopys, answerRecord.getAnswer(), questionCopy.getType());
		if (isRight) {
			answerRecord.setIsRight(AnswerRecord.RIGHT);
			answerRecord.setScore(questionCopy.getScore());
		}else{
			answerRecord.setIsRight(AnswerRecord.WRONG);
			answerRecord.setScore(AnswerRecord.ZERO_SCORE);
		}
	}


	public Map<String, QuestionCopy> getRightAnswer(Integer examRegion, String memberId, String examId) {

		return examPaperRightAnswerCache.get(examId, () -> {
			return questionCopyStuService.getRightAnswer(examRegion, memberId, examId);
		}, CACHE_TIME);

//		return Optional.ofNullable(redis.process(jedis -> jedis.get(cacheKey)))
//					   .map(data -> {
//						   try {
//							   return (Map)new ObjectMapper().readValue(data, new TypeReference<Map<String, QuestionCopy>>() {});
//						   } catch (IOException e) {
//							   e.printStackTrace();
//						   }
//						   return new HashMap<String, QuestionCopy>();
//					   })
//					   .orElseGet(() -> {
//						   return setCache(examRegion, memberId, examId, cacheKey);
//					   });
	}

	private Map<String, QuestionCopy> setCache(Integer examRegion, String memberId, String examId, String cacheKey) {
		Map<String, QuestionCopy> rightAnswer = questionCopyStuService.getRightAnswer(examRegion, memberId, examId);
		// 将数据存入Redis缓存
		redis.process(jedis -> {
			try {
				String jsonString =  new ObjectMapper().writeValueAsString(rightAnswer);
				jedis.set(cacheKey, jsonString, "NX", "EX", 60 * 60 * 24 * 2);
			} catch (JsonProcessingException e) {
				LOGGER.error("Failed to set cache for examId: {}", examId, e);
			}
			return null;
		});
		return rightAnswer;
	}

	private void saveQuestion(String id) {
		messageSender.send(MessageTypeContent.EXAM_QUESTION_ERROR_RATE_INSERT, MessageHeaderContent.ID, id);
	}

	@DataSource
	public Exam getExamFromCacheOrDB(Integer examRegion,String examId) {
		return examPaperCache.get(examId, () -> examDao.execute(e ->
								e.select(
										 Fields.start()
											   .add(EXAM.ID)
											   .add(EXAM.TYPE)
											   .add(EXAM.SOURCE_TYPE)
											   .add(EXAM.ORGANIZATION_ID)
											   .add(EXAM.NAME)
											   .add(EXAM.PASS_SCORE)
											   .add(EXAM.IS_SET_PASS_SCORE)
											   .add(EXAM.IS_OVER_BY_PASS_EXAM)
											   .add(EXAM.PUBLISH_ORGANIZATION_ID)
											   .add(PAPER_CLASS.QUESTION_NUM)
											   .end()
								 )
								 .from(EXAM)
								 .leftJoin(PAPER_CLASS).on(PAPER_CLASS.ID.eq(EXAM.PAPER_CLASS_ID))
								 .where(EXAM.ID.eq(examId)).fetchOne(r -> {
									 Exam exam = new Exam();
									 exam.setId(r.getValue(EXAM.ID));
									 exam.setType(r.getValue(EXAM.TYPE));
									 exam.setSourceType(r.getValue(EXAM.SOURCE_TYPE));
									 exam.setOrganizationId(r.getValue(EXAM.ORGANIZATION_ID));
									 exam.setName(r.getValue(EXAM.NAME));
									 exam.setPassScore(r.getValue(EXAM.PASS_SCORE));
									 exam.setIsSetPassScore(r.getValue(EXAM.IS_SET_PASS_SCORE));
									 exam.setIsOverByPassExam(r.getValue(EXAM.IS_OVER_BY_PASS_EXAM));
									 exam.setPublishOrganizationId(r.getValue(EXAM.PUBLISH_ORGANIZATION_ID));

									 PaperClass paperClass = new PaperClass();
									 paperClass.setQuestionNum(r.getValue(PAPER_CLASS.QUESTION_NUM));

									 exam.setPaperClass(paperClass);
									 return exam;
								 })
		), CACHE_TIME);
	}

	@DataSource
	public void updateTaskBySubmitPaper(Integer examRegion,String examRecordId, String examId) {

		ExamRecord examRecord = examStuRecordService.getExamRecord(examRegion,examRecordId, examId);
		Optional<Exam> examOpt = examService.getOptionalById(examRegion, examId);
		examOpt.ifPresent(exam -> {
			List<Task> tasks = taskService.findTaskBybusinessIdAndMemberId(exam.getId(), examRecord.getMemberId());
			update(tasks.stream().map(t -> {
				t.setStatus(Task.FINISHED);
				t.setStartTime(exam.getStartTime());
				t.setEndTime(exam.getEndTime());
				return t;
			}).collect(Collectors.toList()));
			LOGGER.info("updateTaskBySubmitPaper, examRecordId:{}", examRecordId);
		});
	}
	public void update(List<Task> tasks) {
		List<List<Task>> bigList = Lists.partition(tasks, PER_SIZE);
		for (List<Task> temp : bigList) {
			List<Task> t = temp.stream().collect(Collectors.toList());
			taskService.update(t);
		}
	}

	/**发送考试学习计划*/
	private void sendExamStudyPlan(String examId, String memberId,Long submitTime){
		messageSender.send(MessageTypeContent.EXAM_STUDY_PLAN_UPDATE,
						   MessageHeaderContent.BUSINESS_ID, examId,
						   MessageHeaderContent.EXAM_MEMBER_ID, memberId,
						   MessageHeaderContent.TIME, Objects.isNull(submitTime) ? (System.currentTimeMillis()+"") : (submitTime+""));
	}

	/**
	 * 如果答题记录数等于通过逻辑自动算分的题目数，那就不需要评卷，直接算出结果
	 * @param answerRecordList 用户该考试全部的答题记录
	 * @param updateList 算分的答题记录，剩下的就是主观题需要评分的题目
	 * @return
	 */
	protected boolean getNoNeedMark(List<AnswerRecord> answerRecordList, List<AnswerRecord> updateList) {
		return answerRecordList.size() == updateList.size();
	}

	protected boolean getHaveSentenceCompletion(List<AnswerRecord> answerRecordList) {
		int needFilterCount = answerRecordList.stream().filter(answerRecord -> {
			QuestionCopy questionCopy = answerRecord.getQuestionCopy();
			return questionCopy != null && questionCopy.getType() != null && Question.SENTENCE_COMPLETION == questionCopy.getType()
					&& answerRecord != null && answerRecord.getAnswer() != null && !("").equals(answerRecord.getAnswer());
		}).collect(Collectors.toList()).size();
		return needFilterCount > 0;
	}

	/**
	 * 确认考试记录结果
	 * @param examRecord
	 * @param exam
	 * @param noNeedMark
	 */
	private void confirmExamRecord(Integer examRegion,ExamRecord examRecord, Exam exam, boolean noNeedMark) {
		//不需评卷
		if (noNeedMark) {
			confirmExamRecord(examRegion,examRecord, exam);
		}
		//需要评卷
		else {
			examStuRecordService.updateExamRecordToBeOver(examRegion,examRecord);
			sendStatusChangedMessage(exam, examRecord);
		}
	}

	public String confirmExamRecord(Integer examRegion,ExamRecord examRecord, Exam exam) {
		List<AnswerRecord> answerRecords = answerRecordService.findByExamRecordId(examRegion,examRecord.getId(), exam.getId());

		Integer totalScore = answerRecords.stream()
										  .filter(f -> f.getScore() != null)
										  .map(AnswerRecord::getScore).reduce(0, (a, b) -> a + b);

		examRecord.setScore(totalScore);

		setExamRecordStatus(exam, examRecord, totalScore);

		examStuRecordService.updateExamRecordInConfirm(examRegion,examRecord.getId(), examRecord.getStatus(),
								  examRecord.getIsFinished(), examRecord.getScore(), examRecord.getExamId(),
								  examRecord.getAnsweredCount(), examRecord.getNoAnswerCount(), examRecord.getRightCount());

		sendStatusChangedMessage(exam, examRecord);

		//发送成绩通知
		insertScoreResultExamNotice(examRecord.getMemberId(), examRecord.getId(),
									exam.getPublishOrganizationId(), exam.getName(), getPlainName(examRecord.getMemberId()));
		if (examRecord.getStatus()==6||examRecord.getStatus()==8){
			messageSender.send(com.zxy.product.system.content.MessageTypeContent.SYSTEM_POINT_CHANGE,
							   com.zxy.product.system.content.MessageHeaderContent.MEMBER_ID, examRecord.getMemberId()
					, com.zxy.product.system.content.MessageHeaderContent.RULE_KEY, PointRule.COMPLETE_EXAM);
		}

		messageSender.send(
				MessageTypeContent.EXAM_RECORD_SCORE_RESULT,
				MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
				MessageHeaderContent.ID, examRecord.getId(),
				MessageHeaderContent.EXAM_ID, examRecord.getExamId());

		return examRecord.getId();
	}

	private void insertScoreResultExamNotice(String memberId, String examRecordId,
											 String publishOrganizationId, String examName, String plainName) {

		examNoticeService.insert(ExamNotice.BUSINESS_TYPE_EXAM, examRecordId, ExamNotice.TYPE_EXAM_SCORE,
								 publishOrganizationId, MessageConstant.EXAM_SCORE,
								 getMessageParams(publishOrganizationId, plainName, examName), Optional.of(memberId));
	}


	/**
	 * 获取模板参数
	 * @param publishOrganizationId
	 * @param plainName
	 * @param examName
	 * @return
	 */
	private Optional<String[]> getMessageParams(String publishOrganizationId, String plainName, String examName) {
		String[] arr = new String[]{
				organizationDao.getOptional(publishOrganizationId)
							   .map(o -> o.getName()).orElse(""), plainName, examName };
		return Optional.of(arr);

	}

	private void setExamRecordStatus(Exam exam, ExamRecord examRecord, Integer totalScore) {
		Integer passScore = exam.getPassScore();
		if (passScore != null  || (exam.getIsSetPassScore() != null &&  exam.getIsSetPassScore() == 1)) {
			examRecord.setStatus(
					(passScore != null && totalScore >= passScore * 100)
							? ExamRecord.STATUS_PASS : ExamRecord.STATUS_NOT_PASS);
		} else {
			examRecord.setStatus(ExamRecord.STATUS_FINISHED);
		}
		examRecord.setIsFinished(isFinished(examRecord, exam));
	}

	private Integer isFinished(ExamRecord examRecord, Exam exam) {
		if (exam.getSourceType() != Exam.EXAM_ACTIVITY_SOURCE_TYPE) {
			return exam.getIsOverByPassExam() == Exam.IS_OVER_BY_PASS_EXAM ?
					(examRecord.getStatus() == ExamRecord.STATUS_PASS
							? ExamRecord.IS_FINISHED : ExamRecord.IS_NOT_FINISHED) : ExamRecord.IS_FINISHED;
		}
		return ExamRecord.IS_FINISHED;
	}

	private void sendStatusChangedMessage(Exam exam, ExamRecord examRecord) {
		if (exam.getSourceType() == Exam.EXAM_ACTIVITY_SOURCE_TYPE) {
			messageSender.send(MessageTypeContent.EXAM_EXAM_RECODD_UPDATE_STATUS,
							   MessageHeaderContent.ID, examRecord.getId(),
							   MessageHeaderContent.EXAM_ID, exam.getId());

		} else {
			messageSender.send(MessageTypeContent.OTHER_MODULE_EXAM_RECORD_UPDATE_STATUS,
							   MessageHeaderContent.ID, examRecord.getId(),
							   MessageHeaderContent.EXAM_ID, exam.getId());
		}
	}

	/**
	 * 获取平台名
	 * @param memberId
	 * @return
	 */
	private String getPlainName(String memberId) {
		return ruleConfigService.getByKey(memberId, RuleConfig.KEY.WEBSITE_TITLE).map(r -> r.getValue()).orElse("");
	}

	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.EXAM_ANSWER_RECORD_UPDATE_STU
		};
	}

}
