package com.zxy.product.examstu.async.helper;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.examstu.api.AnswerRecordService;
import com.zxy.product.examstu.async.util.GetTableUtil;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.exam.entity.*;
import com.zxy.product.system.entity.PointRule;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AnswerRecordListenerHelper {


    private AnswerRecordService answerRecordService;

    private MemberService memberService;

    private GetTableUtil getTableUtil;

    private CommonDao<AnswerRecord> answerRecordDao;
    private CommonDao<ExamRecord> examRecordDao;
    private CommonDao<Exam> examDao;
    private MessageSender messageSender;


    @Autowired
    public void setMessageSender(MessageSender messageSender){
        this.messageSender=messageSender;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }

    @Autowired
    public void setAnswerRecordDao(CommonDao<AnswerRecord> answerRecordDao) {
        this.answerRecordDao = answerRecordDao;
    }

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    public Integer memberBelongSouth(String memberId){
        return memberService.findExamRegion(memberId);
    }

    @DataSource
    public void updateAnswerRecord(Integer examRegion, String examId,List<AnswerRecord> answerRecords){
        answerRecordService.batchUpdate(examRegion, examId,answerRecords);
    }

    @DataSource
    public boolean markPaperCompleted(Integer examRegion, String examRecordId, String examId) {
        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        List<AnswerRecord> answerRecords = answerRecordDao.execute(e ->
                e.select(Fields.start()
                                .add(table.fields())
                                .end())
                        .from(table)
                        .where(table.field("f_score", Integer.class).isNull(), table.field("f_exam_record_id", String.class).eq(examRecordId))
        ).fetch(r->{
            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            return answerRecord;
        });
        return answerRecords.size() == 0;
    }


    @DataSource
    public String confirmExamRecord(Integer examRegion, String examRecordId, String examId) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        ExamRecord examRecord = getSimple(examRegion, examRecordId, examId);
        List<AnswerRecord> answerRecords = getListByExamRecordId(examRegion, examRecordId, examId);

        Integer totalScore = answerRecords.stream()
                .filter(f -> f.getScore() != null)
                .map(AnswerRecord::getScore).reduce(0, (a, b) -> a + b);

        Integer size = answerRecords.stream()
                .filter(f -> f.getIsRight() == AnswerRecord.RIGHT).collect(Collectors.toList()).size();
        examRecord.setRightCount(size);

        examRecord.setScore(totalScore);

        Exam exam = examDao.get(examRecord.getExamId());

        setExamRecordValue(exam, examRecord, totalScore, exam.getPassScore());

        updateExamRecordInConfirm(examRecord.getId(), examRecord.getStatus(),
                                  examRecord.getIsFinished(), examRecord.getScore(), examRecordTable, examRecord.getRightCount());
        if (examRecord.getStatus()==6||examRecord.getStatus()==8){
            messageSender.send(com.zxy.product.system.content.MessageTypeContent.SYSTEM_POINT_CHANGE,
                    com.zxy.product.system.content.MessageHeaderContent.MEMBER_ID,examRecord.getMemberId()
                    , com.zxy.product.system.content.MessageHeaderContent.RULE_KEY, PointRule.COMPLETE_EXAM);
        }

        sendStatusChangedMessage(exam, examRecord);

        messageSender.send(MessageTypeContent.EXAM_RECORD_SCORE_RESULT,
                MessageHeaderContent.EXAM_REGION,String.valueOf(examRegion),
                MessageHeaderContent.ID, examRecord.getId(),
                MessageHeaderContent.EXAM_ID,examRecord.getExamId());

        return examRecordId;
    }

    private void sendStatusChangedMessage(Exam exam, ExamRecord examRecord) {
        if (isExamActivitySouceType(exam)) {
            messageSender.send(MessageTypeContent.EXAM_EXAM_RECODD_UPDATE_STATUS,
                    MessageHeaderContent.ID, examRecord.getId(),
                    MessageHeaderContent.EXAM_ID,exam.getId());
        } else {
            messageSender.send(MessageTypeContent.OTHER_MODULE_EXAM_RECORD_UPDATE_STATUS,
                    MessageHeaderContent.ID, examRecord.getId(),
                    MessageHeaderContent.EXAM_ID,exam.getId());
        }
    }

    private void updateExamRecordInConfirm(String id, Integer status, Integer isFinished, Integer score, TableImpl<?> examRecordTable, Integer rightCount) {
        examRecordDao.execute(dslContext ->
                dslContext.update(examRecordTable)
                        .set(examRecordTable.field("f_status", Integer.class), status)
                        .set(examRecordTable.field("f_is_finished", Integer.class), isFinished)
                        .set(examRecordTable.field("f_score", Integer.class), score)
                        .set(examRecordTable.field("f_right_count", Integer.class), rightCount)
                        .where(examRecordTable.field("f_id", String.class).eq(id))
                        .execute());
    }


    private void setExamRecordValue(Exam exam, ExamRecord examRecord, Integer totalScore, Integer passScore) {
        if (needSetPassScore(exam)) {
            examRecord.setStatus(isPass(passScore, totalScore)
                    ? ExamRecord.STATUS_PASS : ExamRecord.STATUS_NOT_PASS);
        } else {
            examRecord.setStatus(ExamRecord.STATUS_FINISHED);
        }
        examRecord.setIsFinished(isFinished(examRecord, exam));
    }

    /**
     * 判断是否完成
     * @param examRecord
     * @return
     */
    private Integer isFinished(ExamRecord examRecord, Exam exam) {
        if (!isExamActivitySouceType(exam)) {
            return exam.getIsOverByPassExam() == Exam.IS_OVER_BY_PASS_EXAM
                    ? (examRecord.getStatus() == ExamRecord.STATUS_PASS
                    ? ExamRecord.IS_FINISHED : ExamRecord.IS_NOT_FINISHED) : ExamRecord.IS_FINISHED;
        }
        return ExamRecord.IS_FINISHED;
    }

    private boolean isExamActivitySouceType(Exam exam) {
        return exam != null && exam.getSourceType() == Exam.EXAM_ACTIVITY_SOURCE_TYPE;
    }


    private boolean isPass(Integer passScore, Integer totalScore) {
        return passScore != null && totalScore >= passScore * 100;
    }

    private boolean needSetPassScore(Exam exam) {
        return exam.getPassScore() != null  || (exam.getIsSetPassScore() != null &&  exam.getIsSetPassScore() == Exam.EXAM_YES);
    }


    @DataSource
    public List<AnswerRecord> getListByExamRecordId(Integer examRegion, String examRecordId, String examId){

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));

        return answerRecordDao.execute(e ->
                e.select(Fields.start()
                                .add(table.fields())
                                .end())
                        .from(table)
                        .where(table.field("f_exam_record_id", String.class).eq(examRecordId))
        ).fetch(r->{
            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            return answerRecord;
        });
    }

    @DataSource
    public ExamRecord getSimple(Integer examRegion, String examRecordId, String examId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        return examRecordDao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(examRecordTable.fields())
                                        .end())
                        .from(examRecordTable)
                        .where(examRecordTable.field("f_id", String.class).eq(examRecordId)).fetchOne(r -> {
                            ExamRecord examRecord = new ExamRecord();
                            examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                            examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                            examRecord.setOrganizationId(r.getValue(examRecordTable.field("f_organization_id", String.class)));
                            examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                            examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                            examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                            examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                            examRecord.setClientType(r.getValue(examRecordTable.field("f_client_type", Integer.class)));
                            examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                            examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                            examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                            examRecord.setExamNumber(r.getValue(examRecordTable.field("f_exam_number", Integer.class)));
                            examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                            examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                            examRecord.setDuration(r.getValue(examRecordTable.field("f_duration", Long.class)));
                            examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                            examRecord.setIsCurrent(r.getValue(examRecordTable.field("f_is_current", Integer.class)));
                            examRecord.setIsFinished(r.getValue( examRecordTable.field("f_is_finished", Integer.class)));
                            examRecord.setExceptionOrder(r.getValue(examRecordTable.field("f_exception_order", Integer.class)));
                            examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                            examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                            examRecord.setSwitchTimes(r.getValue(examRecordTable.field("f_switch_times", Integer.class)));
                            examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                            examRecord.setUserIp(r.getValue(examRecordTable.field("f_user_ip", String.class)));
                            examRecord.setNoAnswerCount(r.getValue(examRecordTable.field("f_no_answer_count", Integer.class)));
                            examRecord.setAnsweredCount(r.getValue(examRecordTable.field("f_answered_count", Integer.class)));
                            examRecord.setClientVersion(r.getValue(examRecordTable.field("f_client_version", String.class)));
                            return examRecord;
                        })
        );
    }

    @DataSource
    public void deleteRepeatAnswers(Integer examRegion, String examRecordId, List<AnswerRecord> answerRecordList, String examId) {
        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        Map<String, List<AnswerRecord>> map = answerRecordList.stream().filter(m->m.getQuestionId()!=null).collect(Collectors.groupingBy(AnswerRecord::getQuestionId));

        List<AnswerRecord> repeats = new ArrayList<>();
        List<AnswerRecord> records = new ArrayList<>();
        map.keySet().forEach(k -> {
            List<AnswerRecord> questionAnswers = map.get(k);
            if (questionAnswers.size() > 1) {
                records.add(questionAnswers.remove(0));
                repeats.addAll(questionAnswers);
            } else if (questionAnswers.size() == 1){
                records.add(questionAnswers.get(0));
            }
        });

        List<String> answerRecordIds = repeats.stream().map(AnswerRecord::getId).collect(Collectors.toList());

        answerRecordIds.forEach(answerRecordId -> {
            answerRecordDao.execute(dslContext ->
                    dslContext.delete(table).where(
                            table.field("f_id", String.class).eq(answerRecordId)
                    ).execute());
        });

        deleteOtherAnswerRecords(examId, examRecordId);
    }


    public void deleteOtherAnswerRecords(String examId, String examRecordId) {
        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));
        // 防止出现 ANSWER_RECORD 表中有其他试卷的 QUESTION_ID，从而导致分数计算不准确，
        // 先查出这个试卷包含的题目
        List<String> questionCopyIds = answerRecordDao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class))
                                        .end())
                        .from(examRecordTable)
                        .leftJoin(paperInstanceQuestionCopyTable).on(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(examRecordTable.field("f_paper_instance_id", String.class)))
                        .where(examRecordTable.field("f_id", String.class).eq(examRecordId))
                        .fetch(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class))
        );

        // 查出 ANSWER_RECORD 表中 不是这张试卷的 本次考试记录的试题作答记录
        List<String> answerRecordIds = answerRecordDao.execute(ar -> ar.select(Fields.start().add(table.field("f_id", String.class)).end())
                .from(table)
                .where(table.field("f_exam_record_id", String.class).eq(examRecordId))
                .and(
                        (table.field("f_question_id", String.class).notIn(questionCopyIds))
                                .or(table.field("f_question_id", String.class).isNull())
                )
                .fetch(table.field("f_id", String.class))
        );

        // 循环逐条删除 避免死锁
        answerRecordIds.forEach(answerRecordId -> {
            answerRecordDao.execute(dslContext ->
                    dslContext.delete(table).where(
                            table.field("f_id", String.class).eq(answerRecordId)
                    ).execute());
        });

    }

}
