package com.zxy.product.examstu.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.examstu.api.*;
import com.zxy.product.examstu.content.MessageHeaderContent;
import com.zxy.product.examstu.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 *
 */
@Component
public class SubmitPaperListener extends AbstractMessageListener {
	private final static Logger LOGGER = LoggerFactory.getLogger(SubmitPaperListener.class);
	private ExamStuRecordService examStuRecordService;

	@Autowired
	public void setExamStuRecordService(ExamStuRecordService examStuRecordService) {
		this.examStuRecordService = examStuRecordService;
	}


	@Override
    protected void onMessage(Message message) {

		LOGGER.info("SubmitPaperListener/:{}", message);

    	Integer type = message.getType();

    	switch (type) {
    	//交卷
    	case MessageTypeContent.SUBMIT_PAPER_STU:
    		doSubmitPaper(message);
    		break;
		default:
			break;
		}
    }

	/**
	 * 交卷
	 * @param message
	 */
	private void doSubmitPaper(Message message) {
		LOGGER.info("exam/AnswerRecordListener, doSubmitPaper:" + message.toString());
		try {
		    examStuRecordService.submitPaper(
					Integer.valueOf(message.getHeader(MessageHeaderContent.EXAM_REGION)),
					message.getHeader(MessageHeaderContent.MEMBER_ID),
		            message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_RECORD_ID),
		            Integer.valueOf(message.getHeader(ExamRecord.SUBMIT_PAPER_CLIENT_TYPE)),
		            ExamRecord.SubmitType.valueOf(message.getHeader(ExamRecord.SUBMIT_PAPER_TYPE)),
		            Long.valueOf(message.getHeader(ExamRecord.SUBMIT_PAPER_TIME)),
		            message.getHeader(ExamRecord.SUBMIT_USER_IP),
		            Integer.valueOf(message.getHeader(ExamRecord.SUBMIT_PAPER_NO_ANSWER_COUNT)),
		            Integer.valueOf(message.getHeader(ExamRecord.SUBMIT_PAPER_ANSWERED_COUNT)),
		            Integer.valueOf(message.getHeader(ExamRecord.SUBMIT_DETAIL_TYPE)),
		            message.getHeader(ExamRecord.SUBMIT_CLIENT_VERSION),
		            message.getHeader(ExamRecord.SUBMIT_PAPER_EXAM_ID)
		        );
        } catch (Exception e) {
            LOGGER.error("exam/AnswerRecordListener, doSubmitPaper:" + message.toString());
            e.printStackTrace();
        }
	}


	@Override
	public int[] getTypes() {
		return new int[]{
			MessageTypeContent.SUBMIT_PAPER_STU
		};
	}

}
