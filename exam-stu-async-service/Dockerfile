FROM harbor.zhixueyun.com:5000/base/jdk-arthas:v8u221_v2

MAINTAINER J<PERSON> <<EMAIL>>

ENV MAX_HEAP 2048m
ENV MIN_HEAP 2048m

EXPOSE 8080

RUN mkdir /work
WORKDIR /work

RUN mkdir /log
VOLUME /log

ENV PROJECT exam-stu-async-service
ADD ./${PROJECT}-*.jar /work/

CMD java -jar -Xms${MIN_HEAP} -Xmx${MAX_HEAP} \
    ${PROJECT}-*.jar \
    > /log/${PROJECT}-`date +%m%d%H%M`.log  2> /log/${PROJECT}-error-`date +%m%d%H%M`.log